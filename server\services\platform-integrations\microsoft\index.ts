// Re-export all Microsoft service modules
export * from './oauth.service';
export * from './teams.service';
export * from './onedrive.service';
export * from './sharepoint.service';
export * from './content.service';
export * from './microsoft.facade';

// Convenience exports for commonly used classes
export {
  MicrosoftOAuthService,
  type MicrosoftCredentials,
} from './oauth.service';

export {
  MicrosoftTeamsService,
  type TeamsSource,
  type TeamsFileMetadata,
} from './teams.service';

export { MicrosoftOneDriveService } from './onedrive.service';
export { MicrosoftSharePointService } from './sharepoint.service';
export { MicrosoftContentService } from './content.service';
export { MicrosoftServiceFacade } from './microsoft.facade';

// Create and export the default Microsoft service instance
import { MicrosoftServiceFacade } from './microsoft.facade';

// Initialize the Microsoft service facade
export const microsoftService = new MicrosoftServiceFacade();

// Export for backward compatibility
export default microsoftService;
