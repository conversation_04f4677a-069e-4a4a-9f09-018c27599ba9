import type { Express } from "express";

/**
 * Register upload service routes
 */
export function registerUploadRoutes(app: Express) {
  // Upload status endpoint
  app.get('/api/upload/status', async (req, res) => {
    try {
      // Get basic upload status
      res.json({
        status: 'ready',
        maxFileSize: '10MB',
        allowedFormats: ['pdf', 'doc', 'docx', 'txt', 'md'],
        uploadsPath: process.env.UPLOADS_PATH || './uploads'
      });
    } catch (error: any) {
      console.error('Error getting upload status:', error);
      res.status(500).json({ message: 'Failed to get upload status', error: error.message });
    }
  });
} 