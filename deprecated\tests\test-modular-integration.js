#!/usr/bin/env node

/**
 * 🧪 COMPREHENSIVE MODULAR INTEGRATION TESTING
 * 
 * This script performs extensive testing to verify that the modularized
 * integration controller maintains 100% functional parity with the original
 * monolithic controller.
 * 
 * Testing Areas:
 * 1. Method Coverage Verification
 * 2. Route Mapping Validation  
 * 3. Error Handling Preservation
 * 4. Service Import Verification
 * 5. Facade Pattern Testing
 * 6. API Contract Compliance
 */

import { promises as fs } from 'fs';
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';
const COLORS = {
  GREEN: '\x1b[32m',
  RED: '\x1b[31m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

function success(message) {
  log(COLORS.GREEN, `✅ ${message}`);
}

function error(message) {
  log(COLORS.RED, `❌ ${message}`);
}

function warning(message) {
  log(COLORS.YELLOW, `⚠️ ${message}`);
}

function info(message) {
  log(COLORS.BLUE, `ℹ️ ${message}`);
}

function header(message) {
  console.log('\n' + '='.repeat(60));
  log(COLORS.BOLD + COLORS.BLUE, `🧪 ${message}`);
  console.log('='.repeat(60));
}

class ModularIntegrationTester {
  constructor() {
    this.testResults = {
      methodCoverage: [],
      routeMapping: [],
      errorHandling: [],
      serviceImports: [],
      facadePattern: [],
      apiContracts: []
    };
    this.totalTests = 0;
    this.passedTests = 0;
    this.failedTests = 0;
  }

  async runAllTests() {
    header('STARTING COMPREHENSIVE MODULAR INTEGRATION TESTING');
    
    try {
      await this.testServerHealth();
      await this.testMethodCoverage();
      await this.testRouteMapping();
      await this.testErrorHandling();
      await this.testServiceImports();
      await this.testFacadePattern();
      await this.testApiContracts();
      
      await this.generateReport();
    } catch (error) {
      error(`Critical test failure: ${error.message}`);
      process.exit(1);
    }
  }

  async testServerHealth() {
    header('Testing Server Health');
    
    try {
      const response = await fetch(`${BASE_URL}/api/health`);
      if (response.ok) {
        success('Server is running and healthy');
        return true;
      } else {
        throw new Error(`Server health check failed: ${response.status}`);
      }
    } catch (err) {
      error(`Server is not running: ${err.message}`);
      error('Please start the server before running tests');
      process.exit(1);
    }
  }

  async testMethodCoverage() {
    header('Testing Method Coverage');
    
    // Original methods from the monolithic controller
    const originalMethods = [
      'getIntegrations',
      'getIntegration', 
      'createIntegration',
      'updateIntegration',
      'deleteIntegration',
      'getAuthUrl',
      'handleOAuthCallback',
      'getGoogleDriveFolders',
      'getDriveStructure',
      'debugDriveFolders',
      'getTeamsAuthUrl',
      'handleTeamsOAuthCallback',
      'getTeamsSources',
      'getTeamsFolders',
      'getTeamsChannels',
      'testTeamsConnection',
      'testConnection',
      'updateSchedule',
      'getSchedules'
    ];

    info(`Checking coverage of ${originalMethods.length} original methods...`);

    try {
      // Read the modular facade
      const facadeContent = await fs.readFile('server/controllers/integration/index.ts', 'utf-8');
      
      let methodsCovered = 0;
      let methodsNotCovered = [];

      for (const method of originalMethods) {
        if (facadeContent.includes(`async ${method}(`)) {
          methodsCovered++;
          this.recordTest('methodCoverage', `${method}`, 'PASS', 'Method present in facade');
        } else {
          methodsNotCovered.push(method);
          this.recordTest('methodCoverage', `${method}`, 'FAIL', 'Method missing from facade');
        }
      }

      if (methodsNotCovered.length === 0) {
        success(`All ${originalMethods.length} methods are covered in the modular facade`);
      } else {
        error(`${methodsNotCovered.length} methods are missing: ${methodsNotCovered.join(', ')}`);
      }

      return methodsNotCovered.length === 0;
    } catch (err) {
      error(`Method coverage test failed: ${err.message}`);
      return false;
    }
  }

  async testRouteMapping() {
    header('Testing Route Mapping');
    
    const routeTests = [
      { method: 'GET', path: '/api/integrations', description: 'Get all integrations' },
      { method: 'GET', path: '/api/integrations/1', description: 'Get single integration' },
      { method: 'POST', path: '/api/integrations', description: 'Create integration' },
      { method: 'PUT', path: '/api/integrations/1', description: 'Update integration' },
      { method: 'DELETE', path: '/api/integrations/1', description: 'Delete integration' },
      { method: 'GET', path: '/api/integrations/1/auth-url', description: 'Get auth URL' },
      { method: 'GET', path: '/api/integrations/1/oauth/callback', description: 'OAuth callback' },
      { method: 'GET', path: '/api/integrations/1/folders', description: 'Get Google Drive folders' },
      { method: 'GET', path: '/api/integrations/1/drive-structure', description: 'Get Drive structure' },
      { method: 'GET', path: '/api/integrations/1/debug-folders', description: 'Debug Drive folders' },
      { method: 'GET', path: '/api/integrations/1/teams-auth-url', description: 'Get Teams auth URL' },
      { method: 'GET', path: '/api/integrations/1/teams/oauth/callback', description: 'Teams OAuth callback' },
      { method: 'GET', path: '/api/integrations/1/teams-sources', description: 'Get Teams sources' },
      { method: 'GET', path: '/api/integrations/1/teams-folders', description: 'Get Teams folders' },
      { method: 'GET', path: '/api/integrations/1/teams-channels/teamId', description: 'Get Teams channels' },
      { method: 'POST', path: '/api/integrations/1/test-teams-connection', description: 'Test Teams connection' },
      { method: 'POST', path: '/api/integrations/1/test-connection', description: 'Test general connection' },
      { method: 'POST', path: '/api/schedules', description: 'Update schedule' },
      { method: 'GET', path: '/api/schedules', description: 'Get schedules' }
    ];

    let routesPassed = 0;

    for (const route of routeTests) {
      try {
        const testPath = route.path.replace('/1', '/999').replace('/teamId', '/test-team');
        const response = await fetch(`${BASE_URL}${testPath}`, { 
          method: route.method,
          headers: { 'Content-Type': 'application/json' },
          body: route.method === 'POST' ? JSON.stringify({}) : undefined
        });

        // We expect 4xx responses for invalid IDs, not 5xx server errors
        if (response.status < 500) {
          routesPassed++;
          this.recordTest('routeMapping', `${route.method} ${route.path}`, 'PASS', `Route exists (${response.status})`);
        } else {
          this.recordTest('routeMapping', `${route.method} ${route.path}`, 'FAIL', `Server error (${response.status})`);
        }
      } catch (err) {
        this.recordTest('routeMapping', `${route.method} ${route.path}`, 'FAIL', `Request failed: ${err.message}`);
      }
    }

    if (routesPassed === routeTests.length) {
      success(`All ${routeTests.length} routes are properly mapped`);
    } else {
      warning(`${routesPassed}/${routeTests.length} routes are working correctly`);
    }

    return routesPassed === routeTests.length;
  }

  async testErrorHandling() {
    header('Testing Error Handling');
    
    const errorTests = [
      {
        name: 'Invalid integration ID',
        method: 'GET',
        path: '/api/integrations/invalid',
        expectedStatus: 400
      },
      {
        name: 'Non-existent integration',
        method: 'GET', 
        path: '/api/integrations/99999',
        expectedStatus: 404
      },
      {
        name: 'Missing request body',
        method: 'POST',
        path: '/api/integrations',
        expectedStatus: 400
      },
      {
        name: 'Invalid JSON body',
        method: 'POST',
        path: '/api/integrations',
        body: 'invalid json',
        expectedStatus: 400
      }
    ];

    let errorTestsPassed = 0;

    for (const test of errorTests) {
      try {
        const response = await fetch(`${BASE_URL}${test.path}`, {
          method: test.method,
          headers: { 'Content-Type': 'application/json' },
          body: test.body
        });

        if (response.status === test.expectedStatus) {
          errorTestsPassed++;
          this.recordTest('errorHandling', test.name, 'PASS', `Correct error status: ${response.status}`);
        } else {
          this.recordTest('errorHandling', test.name, 'FAIL', `Expected ${test.expectedStatus}, got ${response.status}`);
        }
      } catch (err) {
        this.recordTest('errorHandling', test.name, 'FAIL', `Request failed: ${err.message}`);
      }
    }

    if (errorTestsPassed === errorTests.length) {
      success(`All ${errorTests.length} error handling tests passed`);
    } else {
      warning(`${errorTestsPassed}/${errorTests.length} error handling tests passed`);
    }

    return errorTestsPassed === errorTests.length;
  }

  async testServiceImports() {
    header('Testing Service Imports');
    
    const requiredServices = [
      'googleService',
      'teamsService', 
      'microsoftService',
      'cryptoService',
      'schedulerService',
      'notionService'
    ];

    const moduleFiles = [
      'server/controllers/integration/index.ts',
      'server/controllers/integration/base/base-integration.controller.ts',
      'server/controllers/integration/shared/integration-crud.controller.ts',
      'server/controllers/integration/google/google-oauth.controller.ts',
      'server/controllers/integration/google/google-integration.controller.ts',
      'server/controllers/integration/microsoft/teams-oauth.controller.ts',
      'server/controllers/integration/microsoft/teams-integration.controller.ts'
    ];

    let serviceImportsPassed = 0;
    let totalImportChecks = 0;

    for (const file of moduleFiles) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        
        for (const service of requiredServices) {
          totalImportChecks++;
          if (content.includes(service) || content.includes('await import')) {
            serviceImportsPassed++;
            this.recordTest('serviceImports', `${service} in ${file}`, 'PASS', 'Service accessible');
          } else {
            // Some modules might not need all services - check if they actually use them
            const serviceUsed = content.includes(service.replace('Service', ''));
            if (!serviceUsed) {
              serviceImportsPassed++; // Not used, so not needed
              this.recordTest('serviceImports', `${service} in ${file}`, 'PASS', 'Service not needed in this module');
            } else {
              this.recordTest('serviceImports', `${service} in ${file}`, 'FAIL', 'Service used but not imported');
            }
          }
        }
      } catch (err) {
        this.recordTest('serviceImports', `Reading ${file}`, 'FAIL', `File read error: ${err.message}`);
      }
    }

    if (serviceImportsPassed === totalImportChecks) {
      success(`All service imports are correctly handled`);
    } else {
      warning(`${serviceImportsPassed}/${totalImportChecks} service import checks passed`);
    }

    return serviceImportsPassed === totalImportChecks;
  }

  async testFacadePattern() {
    header('Testing Facade Pattern');
    
    try {
      const facadeContent = await fs.readFile('server/controllers/integration/index.ts', 'utf-8');
      
      // Test that facade properly delegates to modular controllers
      const delegationPatterns = [
        'integrationCrudController',
        'googleOAuthController', 
        'googleIntegrationController',
        'teamsOAuthController',
        'teamsIntegrationController'
      ];

      let delegationsPassed = 0;

      for (const pattern of delegationPatterns) {
        if (facadeContent.includes(pattern)) {
          delegationsPassed++;
          this.recordTest('facadePattern', `Delegation to ${pattern}`, 'PASS', 'Proper delegation found');
        } else {
          this.recordTest('facadePattern', `Delegation to ${pattern}`, 'FAIL', 'Missing delegation');
        }
      }

      // Test that all methods are bound
      const bindPattern = /this\.\w+\s*=\s*this\.\w+\.bind\(this\)/g;
      const bindings = facadeContent.match(bindPattern) || [];
      
      if (bindings.length >= 15) { // Expect at least 15 method bindings
        this.recordTest('facadePattern', 'Method binding', 'PASS', `${bindings.length} methods properly bound`);
        delegationsPassed++;
      } else {
        this.recordTest('facadePattern', 'Method binding', 'FAIL', `Only ${bindings.length} methods bound`);
      }

      if (delegationsPassed === delegationPatterns.length + 1) {
        success('Facade pattern is correctly implemented');
      } else {
        warning(`${delegationsPassed}/${delegationPatterns.length + 1} facade pattern tests passed`);
      }

      return delegationsPassed === delegationPatterns.length + 1;
    } catch (err) {
      error(`Facade pattern test failed: ${err.message}`);
      return false;
    }
  }

  async testApiContracts() {
    header('Testing API Contracts');
    
    // Test that we can create, read, update, delete integrations
    try {
      info('Testing CRUD operations...');
      
      // 1. Test getting integrations
      const getResponse = await fetch(`${BASE_URL}/api/integrations`);
      const getResult = await getResponse.json();
      
      if (getResult.integrations && Array.isArray(getResult.integrations)) {
        this.recordTest('apiContracts', 'GET integrations', 'PASS', 'Returns integrations array');
      } else {
        this.recordTest('apiContracts', 'GET integrations', 'FAIL', 'Invalid response format');
        return false;
      }

      // 2. Test creating an integration
      const createPayload = {
        type: 'google-drive',
        name: 'Test Integration API Contract',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: false,
        syncFilters: {},
        syncSchedule: null
      };

      const createResponse = await fetch(`${BASE_URL}/api/integrations`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(createPayload)
      });

      if (createResponse.status === 201) {
        const createResult = await createResponse.json();
        
        if (createResult.integration && createResult.integration.id) {
          this.recordTest('apiContracts', 'POST integrations', 'PASS', 'Successfully created integration');
          
          const newIntegrationId = createResult.integration.id;
          
          // 3. Test getting the specific integration
          const getOneResponse = await fetch(`${BASE_URL}/api/integrations/${newIntegrationId}`);
          const getOneResult = await getOneResponse.json();
          
          if (getOneResult.integration && getOneResult.integration.id === newIntegrationId) {
            this.recordTest('apiContracts', 'GET single integration', 'PASS', 'Retrieved correct integration');
          } else {
            this.recordTest('apiContracts', 'GET single integration', 'FAIL', 'Failed to retrieve integration');
          }

          // 4. Test updating the integration
          const updatePayload = { name: 'Updated Test Integration' };
          const updateResponse = await fetch(`${BASE_URL}/api/integrations/${newIntegrationId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updatePayload)
          });

          if (updateResponse.ok) {
            this.recordTest('apiContracts', 'PUT integration', 'PASS', 'Successfully updated integration');
          } else {
            this.recordTest('apiContracts', 'PUT integration', 'FAIL', 'Failed to update integration');
          }

          // 5. Test deleting the integration
          const deleteResponse = await fetch(`${BASE_URL}/api/integrations/${newIntegrationId}`, {
            method: 'DELETE'
          });

          if (deleteResponse.ok) {
            this.recordTest('apiContracts', 'DELETE integration', 'PASS', 'Successfully deleted integration');
          } else {
            this.recordTest('apiContracts', 'DELETE integration', 'FAIL', 'Failed to delete integration');
          }

        } else {
          this.recordTest('apiContracts', 'POST integrations', 'FAIL', 'Invalid creation response');
        }
      } else {
        this.recordTest('apiContracts', 'POST integrations', 'FAIL', `Creation failed: ${createResponse.status}`);
      }

      success('API contract testing completed');
      return true;
    } catch (err) {
      error(`API contract test failed: ${err.message}`);
      return false;
    }
  }

  recordTest(category, testName, result, details) {
    this.totalTests++;
    if (result === 'PASS') {
      this.passedTests++;
    } else {
      this.failedTests++;
    }

    this.testResults[category].push({
      name: testName,
      result,
      details
    });

    if (result === 'PASS') {
      success(`${testName}: ${details}`);
    } else {
      error(`${testName}: ${details}`);
    }
  }

  async generateReport() {
    header('COMPREHENSIVE TEST REPORT');
    
    console.log('\n📊 SUMMARY STATISTICS:');
    console.log(`Total Tests: ${this.totalTests}`);
    console.log(`✅ Passed: ${this.passedTests}`);
    console.log(`❌ Failed: ${this.failedTests}`);
    console.log(`📈 Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(2)}%`);

    console.log('\n📋 DETAILED RESULTS BY CATEGORY:');

    for (const [category, tests] of Object.entries(this.testResults)) {
      console.log(`\n🔍 ${category.toUpperCase()}:`);
      const passed = tests.filter(t => t.result === 'PASS').length;
      const total = tests.length;
      console.log(`   Status: ${passed}/${total} tests passed`);
      
      const failed = tests.filter(t => t.result === 'FAIL');
      if (failed.length > 0) {
        console.log('   Failed Tests:');
        failed.forEach(test => {
          console.log(`     ❌ ${test.name}: ${test.details}`);
        });
      }
    }

    // Final determination
    console.log('\n' + '='.repeat(60));
    if (this.failedTests === 0) {
      success('🎉 ALL TESTS PASSED! 🎉');
      success('✨ The modularized integration controller is READY FOR DEPLOYMENT!');
      success('🗑️ You can safely DELETE the original integration.ts file!');
    } else if (this.failedTests <= 2) {
      warning('⚠️ Minor issues found, but modularization is mostly successful');
      warning('🔧 Fix the identified issues before deleting the original file');
    } else {
      error('🚫 CRITICAL ISSUES FOUND');
      error('❌ DO NOT delete the original integration.ts file yet');
      error('🔧 Fix all issues before proceeding');
    }
    console.log('='.repeat(60));

    // Write detailed report to file
    const reportContent = JSON.stringify(this.testResults, null, 2);
    await fs.writeFile('modular-integration-test-report.json', reportContent);
    info('📄 Detailed test report saved to: modular-integration-test-report.json');
  }
}

// Run the tests
const tester = new ModularIntegrationTester();
tester.runAllTests().catch(err => {
  error(`Test execution failed: ${err.message}`);
  process.exit(1);
}); 