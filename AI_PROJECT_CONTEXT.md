# MeetSync-Web-App / GPT Unify - AI Project Context

**Generated on:** 2024-12-16
**Purpose:** Comprehensive context for AI assistants to understand this project across different chat sessions

## Project Overview

This is **MeetSync-Web-App**, also known as **"GPT Unify"** - a comprehensive document AI assistant that syncs and processes documents from various platforms (Google Drive, Microsoft Teams, SharePoint) with AI-powered content extraction, search, and an integrated RAG-powered chat assistant.

### Core Purpose
- Sync and process all types of documents from multiple platforms
- Provide AI-powered content extraction and search capabilities
- Enable RAG-powered chat interactions with document content
- Offer real-time synchronization with incremental updates
- Deliver comprehensive security with end-to-end encryption

## Technical Architecture

### Frontend Stack
- **Framework:** React 18 with TypeScript
- **Build Tool:** Vite
- **UI Library:** shadcn/ui components with Radix UI primitives
- **Styling:** Tailwind CSS with custom animations
- **State Management:** TanStack Query for server state, Zustand for client state
- **Routing:** Wouter for client-side navigation
- **Forms:** React Hook Form with Zod validation

### Backend Stack
- **Runtime:** Node.js with Express.js
- **Language:** TypeScript
- **Database:** PostgreSQL with pgvector extension for vector search
- **ORM:** Drizzle ORM with Drizzle Kit for migrations
- **Session Management:** express-session with connect-pg-simple
- **AI Integration:** OpenAI API for content processing and embeddings
- **Authentication:** OAuth 2.0 (Google, Microsoft) with Passport.js

### Key Services & Features

#### Document Processing
- **PDF Processing:** pdf-parse, pdf2pic, pdfjs-dist
- **Office Documents:** mammoth (Word), node-xlsx (Excel), pptx-parser (PowerPoint)
- **File Type Detection:** file-type library
- **Content Extraction:** Specialized parsers for each document type

#### Platform Integrations
- **Google Drive:** googleapis with Google Auth Library
- **Microsoft Teams/SharePoint:** Microsoft Graph Client with MSAL
- **Notion:** @notionhq/client for Notion workspace integration

#### AI & Vector Search
- **Vector Database:** pgvector extension in PostgreSQL
- **Embeddings:** OpenAI embedding models
- **RAG Implementation:** Custom retrieval-augmented generation
- **Chat Interface:** OpenAI chat completions with context injection

#### Scheduling & Sync
- **Cron Jobs:** node-cron and node-schedule for automated sync
- **Incremental Sync:** Change detection and delta updates
- **Background Processing:** Queue-based document processing

## Project Structure

```
├── client/                    # React frontend application
│   ├── src/
│   │   ├── components/       # Reusable UI components (shadcn/ui based)
│   │   ├── pages/           # Page-level components
│   │   ├── lib/             # Utilities, API client, and helpers
│   │   └── hooks/           # Custom React hooks
│   ├── index.html           # HTML entry point
│   └── components/          # Additional component directory
├── server/                   # Node.js backend
│   ├── controllers/         # API route handlers
│   ├── services/            # Business logic and external service integrations
│   ├── routes/              # Express route definitions
│   ├── core/                # Core application logic
│   ├── storage/             # File storage management
│   ├── scripts/             # Database and utility scripts
│   ├── index.ts             # Main server entry point
│   ├── routes.ts            # Route configuration
│   ├── db.ts                # Database connection setup
│   └── utils.ts             # Server utilities
├── shared/                   # Shared code between client and server
│   ├── types/               # TypeScript type definitions
│   ├── schemas/             # Zod schemas for validation
│   ├── utils/               # Shared utility functions
│   ├── openapi/             # API documentation
│   ├── schema.ts            # Database schema (Drizzle)
│   └── index.ts             # Shared exports
├── migrations/               # Database migration files
├── mcp-servers/             # Model Context Protocol servers
├── scripts/                 # Project-wide utility scripts
├── docs/                    # Documentation
├── docker/                  # Docker configuration files
├── tests/                   # Test files
├── uploads/                 # File upload storage
├── dist/                    # Built application files
└── batch-jobs/              # Background job processing
```

## Key Configuration Files

### Package Dependencies
- **Production:** 115+ dependencies including React, Express, Drizzle, OpenAI
- **Development:** 25+ dev dependencies including TypeScript, Vite, Tailwind
- **Key Libraries:** 
  - UI: @radix-ui/*, lucide-react, framer-motion
  - Backend: express, passport, multer, ws
  - Database: drizzle-orm, pg, @neondatabase/serverless
  - AI: openai, @modelcontextprotocol/sdk

### Environment Configuration
- **Development:** Local PostgreSQL + Redis setup
- **Production:** Neon PostgreSQL with Redis
- **Docker:** Multi-stage builds with development and production variants
- **Environment Variables:** OAuth credentials, database URLs, API keys

## Development Workflow

### Available Scripts
- `npm run dev` - Start both client and server in development mode
- `npm run dev:local` - Local development with local databases
- `npm run build` - Build client, server, and MCP servers
- `npm run migrate` - Run database migrations
- `npm run docker:dev` - Development with Docker Compose

### Database Management
- **Schema:** Defined in `shared/schema.ts` using Drizzle ORM
- **Migrations:** Managed with Drizzle Kit
- **Vector Extensions:** pgvector for semantic search capabilities

## Important Features in Detail

### Document Synchronization
1. **Multi-platform Support:** Google Drive, Microsoft Teams, SharePoint, Notion
2. **Real-time Sync:** Automatic detection of document changes
3. **Incremental Updates:** Process only changed files
4. **Comprehensive Processing:** Extract text, metadata, and embeddings

### AI-Powered Chat
1. **RAG Implementation:** Retrieval-augmented generation with OpenAI
2. **Vector Search:** Semantic search across all synchronized documents
3. **Context Injection:** Include relevant document chunks in chat context
4. **Multi-turn Conversations:** Maintain chat history and context

### Security Features
1. **OAuth Integration:** Secure platform authentication
2. **Credential Encryption:** End-to-end encryption for stored credentials
3. **Session Management:** Secure session handling with PostgreSQL store
4. **Access Control:** Platform-specific permission management

### Integration Architecture
1. **Google Drive:** Full API integration with real-time change detection
2. **Microsoft Graph:** Teams and SharePoint access with proper scoping
3. **Notion:** Workspace and page synchronization
4. **Extensible Design:** Plugin architecture for additional platforms

## Current Development Status

### Implemented Features
- ✅ Basic document processing and sync
- ✅ OAuth authentication for Google and Microsoft
- ✅ Vector search and embeddings
- ✅ Chat interface with RAG
- ✅ Docker deployment setup
- ✅ Database schema and migrations

### Development Areas
- 🔄 MCP (Model Context Protocol) server integration
- 🔄 Enhanced security and encryption
- 🔄 Advanced scheduling and batch processing
- 🔄 UI improvements and user experience

## Deployment Information

### Docker Configuration
- **Multi-environment:** Development, staging, and production configs
- **Services:** Application, PostgreSQL, Redis
- **Volumes:** Persistent storage for uploads and database
- **Networks:** Isolated container networking

### Environment Variables Required
- `DATABASE_URL` - PostgreSQL connection with pgvector
- `OPENAI_API_KEY` - OpenAI API for AI features
- `GOOGLE_CLIENT_ID/SECRET` - Google OAuth credentials
- `MICROSOFT_CLIENT_ID/SECRET` - Microsoft OAuth credentials
- `REDIS_URL` - Redis for session storage
- `ENCRYPTION_KEY` - For sensitive data encryption

## Important Notes for AI Assistants

1. **Project Identity:** Always refer to this as "MeetSync-Web-App" or "GPT Unify"
2. **Core Purpose:** Document AI assistant with multi-platform sync and RAG chat
3. **Tech Stack:** React + TypeScript frontend, Node.js + Express backend, PostgreSQL + pgvector
4. **Key Features:** Document processing, AI chat, vector search, OAuth integrations
5. **Architecture:** Modular design with clear separation between client, server, and shared code
6. **Development:** Active project with Docker support and comprehensive dependency management

## Recent Changes & Context

- Currently on the **MCP branch** (Model Context Protocol integration)
- Recent package.json changes suggest active development
- Focus on AI integration and document processing capabilities
- Comprehensive OAuth integration for multiple platforms
- Vector search implementation with pgvector

This context should be referenced in all future interactions to maintain consistency and understanding of the project's scope, architecture, and current state. 