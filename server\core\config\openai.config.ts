import OpenAI from "openai";
import { environmentConfig } from "./environment.config";

/**
 * OpenAI configuration and client management
 */

export interface OpenAIConfig {
  apiKey: string | null;
  chatModel: string;
  embeddingModel: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  maxRetries: number;
  timeout: number;
}

// OpenAI configuration
export const openaiConfig: OpenAIConfig = {
  apiKey: environmentConfig.openaiApiKey || null,
  chatModel: process.env.OPENAI_CHAT_MODEL || 'gpt-4.1-nano-2025-04-14',
  embeddingModel: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small',
  maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '10000'),
  temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
  topP: parseFloat(process.env.OPENAI_TOP_P || '1.0'),
  frequencyPenalty: parseFloat(process.env.OPENAI_FREQUENCY_PENALTY || '0.0'),
  presencePenalty: parseFloat(process.env.OPENAI_PRESENCE_PENALTY || '0.0'),
  maxRetries: parseInt(process.env.OPENAI_MAX_RETRIES || '3'),
  timeout: parseInt(process.env.OPENAI_TIMEOUT || '60000'),
};

// OpenAI client instance
let openaiClient: OpenAI | null = null;

/**
 * Initialize OpenAI client
 */
export const initializeOpenAI = (): OpenAI | null => {
  if (openaiClient) {
    return openaiClient;
  }

  if (!openaiConfig.apiKey) {
    console.warn('⚠️  OpenAI API key not configured - AI features will be disabled');
    return null;
  }

  try {
    openaiClient = new OpenAI({
      apiKey: openaiConfig.apiKey,
      maxRetries: openaiConfig.maxRetries,
      timeout: openaiConfig.timeout,
    });

    console.log('✅ OpenAI client initialized successfully');
    return openaiClient;

  } catch (error) {
    console.error('❌ Failed to initialize OpenAI client:', error);
    return null;
  }
};

/**
 * Get OpenAI client instance
 */
export const getOpenAIClient = (): OpenAI | null => {
  if (!openaiClient) {
    return initializeOpenAI();
  }
  return openaiClient;
};

/**
 * Check OpenAI API health
 */
export const checkOpenAIHealth = async (): Promise<{ healthy: boolean; error?: string }> => {
  try {
    console.log('Checking OpenAI health...');

    // Check if API key is configured
    if (!openaiConfig.apiKey) {
      console.log('OpenAI API key not configured');
      return { healthy: false, error: 'OpenAI API key not configured' };
    }

    // Try to get or initialize the client
    let client = getOpenAIClient();
    if (!client) {
      console.log('OpenAI client not initialized, attempting to initialize...');
      client = initializeOpenAI();
    }

    if (!client) {
      return { healthy: false, error: 'OpenAI client not initialized' };
    }

    console.log('Testing OpenAI API with embedding request...');

    // Test with a simple embedding request with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('OpenAI health check timeout')), 10000); // 10 second timeout
    });

    const embeddingPromise = client.embeddings.create({
      model: openaiConfig.embeddingModel,
      input: 'test',
    });

    await Promise.race([embeddingPromise, timeoutPromise]);

    console.log('OpenAI health check passed');
    return { healthy: true };

  } catch (error) {
    console.error('OpenAI health check failed:', error);
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown OpenAI error'
    };
  }
};

/**
 * Get chat completion with default settings
 */
export const getChatCompletion = async (
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
  options: Partial<OpenAI.Chat.Completions.ChatCompletionCreateParams> = {}
) => {
  const client = getOpenAIClient();

  if (!client) {
    throw new Error('OpenAI client not available');
  }

  return await client.chat.completions.create({
    model: openaiConfig.chatModel,
    messages,
    max_tokens: openaiConfig.maxTokens,
    temperature: openaiConfig.temperature,
    top_p: openaiConfig.topP,
    frequency_penalty: openaiConfig.frequencyPenalty,
    presence_penalty: openaiConfig.presencePenalty,
    stream: false, // Ensure we get a non-streaming response
    ...options,
  });
};

/**
 * Get embeddings with default settings
 */
export const getEmbeddings = async (
  input: string | string[],
  options: Partial<OpenAI.Embeddings.EmbeddingCreateParams> = {}
): Promise<OpenAI.Embeddings.CreateEmbeddingResponse> => {
  const client = getOpenAIClient();
  
  if (!client) {
    throw new Error('OpenAI client not available');
  }

  return await client.embeddings.create({
    model: openaiConfig.embeddingModel,
    input,
    ...options,
  });
};

/**
 * Get OpenAI configuration info
 */
export const getOpenAIInfo = () => {
  return {
    hasApiKey: !!openaiConfig.apiKey,
    chatModel: openaiConfig.chatModel,
    embeddingModel: openaiConfig.embeddingModel,
    maxTokens: openaiConfig.maxTokens,
    temperature: openaiConfig.temperature,
    isInitialized: !!openaiClient,
  };
};

// Export the client getter as default
export default getOpenAIClient;
