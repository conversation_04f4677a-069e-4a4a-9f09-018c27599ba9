import { fileChunks, type FileChunk, type InsertFileChunk } from "../../../shared/index.js";
import { eq } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config";
import postgres from "postgres";

/**
 * RAG (Retrieval-Augmented Generation) storage operations
 * Handles file chunks and vector similarity search
 */
export class RAGStorage {
  private db: any;
  private memoryFileChunks: Map<number, FileChunk>;
  private fileChunkIdCounter: number;
  private useDatabase: boolean;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memoryFileChunks = new Map();
    this.fileChunkIdCounter = 1;
    // Database connection will be initialized lazily when first needed
  }

  /**
   * Ensure database connection is available
   */
  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        // Silently fall back to memory storage
        this.useDatabase = false;
      }
    }
  }

  // File Chunks
  async getFileChunks(fileId: number): Promise<FileChunk[]> {
    this.validateId(fileId);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(fileChunks).where(eq(fileChunks.fileId, fileId));
      return result;
    } else {
      return Array.from(this.memoryFileChunks.values()).filter(
        chunk => chunk.fileId === fileId
      ).sort((a, b) => a.chunkIndex - b.chunkIndex);
    }
  }

  async createFileChunk(chunk: InsertFileChunk): Promise<FileChunk> {
    this.validateId(chunk.fileId);
    this.validateString(chunk.content, 'content');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(fileChunks).values(chunk).returning();
      return result[0];
    } else {
      const id = this.fileChunkIdCounter++;
      const newChunk: FileChunk = {
        fileId: chunk.fileId,
        chunkIndex: chunk.chunkIndex,
        content: chunk.content,
        id,
        metadata: chunk.metadata || {},
        embedding: chunk.embedding || null,
        tokenCount: chunk.tokenCount || null,
        createdAt: new Date(),
      };
      this.memoryFileChunks.set(id, newChunk);
      return newChunk;
    }
  }

  async deleteFileChunks(fileId: number): Promise<void> {
    this.validateId(fileId);
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(fileChunks).where(eq(fileChunks.fileId, fileId));
    } else {
      const chunksToDelete = Array.from(this.memoryFileChunks.entries()).filter(
        ([_, chunk]) => chunk.fileId === fileId
      );

      for (const [id, _] of chunksToDelete) {
        this.memoryFileChunks.delete(id);
      }
    }
  }

  async deleteChunk(chunkId: number): Promise<void> {
    this.validateId(chunkId);
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(fileChunks).where(eq(fileChunks.id, chunkId));
    } else {
      this.memoryFileChunks.delete(chunkId);
    }
  }

  async searchSimilarChunks(queryEmbedding: number[], enabledSources: string[], limit: number): Promise<any[]> {
    // Validate input parameters
    if (!queryEmbedding || !Array.isArray(queryEmbedding) || queryEmbedding.length === 0) {
      console.log("Invalid or empty queryEmbedding provided:", queryEmbedding);
      return [];
    }

    if (!limit || limit <= 0) {
      limit = 10; // Default limit
    }

    console.log(`Searching for similar chunks with embedding length: ${queryEmbedding.length}, sources: ${enabledSources}, limit: ${limit}`);

    this.ensureDatabase();

    if (!this.useDatabase) {
      console.warn('Vector similarity search requires database storage - returning empty results');
      return [];
    }

    try {
      // Convert the embedding array to pgvector format [1,2,3]
      const embeddingVector = `[${queryEmbedding.join(',')}]`;

      // Check if emails are in enabled sources
      const includeEmails = enabledSources.includes('emails');
      
      let queryText = `
        SELECT
          fc.id,
          fc.file_id as "fileId",
          fc.content,
          fc.chunk_index as "chunkIndex",
          fc.metadata,
          f.file_name as "fileName",
          f.platform,
          1 - (fc.embedding <=> $1::vector) as similarity,
          'file' as source_type
        FROM file_chunks fc
        JOIN files f ON fc.file_id = f.id
        WHERE fc.embedding IS NOT NULL
          AND f.status = 'active'
          AND 1 - (fc.embedding <=> $1::vector) > 0.1
      `;

      if (includeEmails) {
        queryText += `
        UNION ALL
        SELECT
          ec.id,
          ec.email_id as "fileId",
          ec.content,
          ec.chunk_index as "chunkIndex",
          ec.metadata,
          COALESCE(ec.metadata->>'subject', 'Email') as "fileName",
          'emails' as platform,
          1 - (ec.embedding <=> $1::vector) as similarity,
          'email' as source_type
        FROM email_chunks ec
        JOIN emails e ON ec.email_id = e.id
        WHERE ec.embedding IS NOT NULL
          AND e.status = 'active'
          AND 1 - (ec.embedding <=> $1::vector) > 0.1
        `;
      }

      const queryParams: any[] = [embeddingVector];

      // Add platform filtering if sources are specified (excluding emails as it's handled separately)
      const nonEmailSources = enabledSources.filter(s => s !== 'emails');
      if (nonEmailSources.length > 0) {
        // Map source IDs to platform names
        const platformNames: string[] = [];
        for (const sourceId of nonEmailSources) {
          try {
            // Handle special virtual sources
            if (sourceId === 'uploaded-files') {
              platformNames.push('uploaded_files');
              console.log(`Mapped virtual source ${sourceId} -> platform: uploaded_files`);
              continue;
            }

            const integrationId = parseInt(sourceId);
            if (!isNaN(integrationId)) {
              // We need to get integration info - this requires the integration storage
              // For now, we'll use a simple mapping
              const integrationTypeMap: Record<string, string> = {
                'google-drive': 'google_drive',
                'google_drive': 'google_drive',
                'microsoft-teams': 'microsoft_teams',
                'microsoft_teams': 'microsoft_teams',
                'slack': 'slack',
                'uploaded-files': 'uploaded_files',
              };
              
              // This is a simplified approach - in a full implementation,
              // we'd query the integration storage to get the actual type
              platformNames.push('google_drive'); // Default assumption
              console.log(`Mapped integration ${integrationId} -> platform: google_drive (default)`);
            } else {
              platformNames.push(sourceId);
            }
          } catch (e) {
            platformNames.push(sourceId);
          }
        }

        if (platformNames.length > 0) {
          // Update the WHERE clause to only apply to the file part of the query
          queryText = queryText.replace(
            'AND 1 - (fc.embedding <=> $1::vector) > 0.1',
            'AND 1 - (fc.embedding <=> $1::vector) > 0.1 AND f.platform = ANY($2)'
          );
          queryParams.push(platformNames);
        }
      }

      queryText += ` ORDER BY similarity DESC LIMIT $${queryParams.length + 1}`;
      queryParams.push(limit);

      console.log(`Executing vector query with params:`, queryParams.map((p, i) => `$${i + 1}: ${Array.isArray(p) ? `[${p.join(',')}]` : p}`));

      // Execute raw SQL query to avoid Drizzle array formatting issues
      const client = postgres(process.env.DATABASE_URL!);
      const result = await client.unsafe(queryText, queryParams);
      await client.end();

      console.log(`Found ${result.length} similar chunks`);
      return result as any[];

    } catch (error: any) {
      console.error("Error in searchSimilarChunks:", error);
      
      // If there's a vector format error, try alternative approach
      if (error.message?.includes('Vector contents must start') || error.message?.includes('malformed array literal')) {
        console.log("Attempting fallback vector search without filtering...");
        try {
          const client = postgres(process.env.DATABASE_URL!);
          const fallbackResult = await client.unsafe(`
            SELECT
              fc.id,
              fc.file_id as "fileId",
              fc.content,
              fc.chunk_index as "chunkIndex",
              fc.metadata,
              f.file_name as "fileName",
              f.platform,
              0.5 as similarity
            FROM file_chunks fc
            JOIN files f ON fc.file_id = f.id
            WHERE fc.embedding IS NOT NULL
              AND f.status = 'active'
            ORDER BY fc.id DESC
            LIMIT $1
          `, [limit]);
          await client.end();
          console.log(`Fallback search found ${fallbackResult.length} chunks`);
          return fallbackResult as any[];
        } catch (fallbackError) {
          console.error("Fallback search also failed:", fallbackError);
        }
      }
      return [];
    }
  }

  async clearAllChunks(): Promise<void> {
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(fileChunks);
    } else {
      this.memoryFileChunks.clear();
      this.fileChunkIdCounter = 1;
    }
  }

  // Utility methods
  private validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid ID provided');
    }
  }

  private validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      chunkCount: this.useDatabase ? 'unknown' : this.memoryFileChunks.size,
      vectorSearchEnabled: this.useDatabase,
    };
  }
}
