import http from 'http';

// Simple test to check if the integration controller is working
async function testIntegrationController() {
  console.log('🧪 Testing Integration Controller...');
  
  const options = {
    hostname: 'localhost',
    port: 8080,
    path: '/api/integrations',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('✅ Integration Controller Response:', response);
          resolve(response);
        } catch (error) {
          console.log('📄 Raw Response:', data);
          resolve({ raw: data, status: res.statusCode });
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Connection Error:', error.message);
      reject(error);
    });

    req.setTimeout(5000, () => {
      console.log('⏰ Request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Run the test
testIntegrationController()
  .then(() => {
    console.log('🎉 Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.log('💥 Test failed:', error.message);
    process.exit(1);
  });
