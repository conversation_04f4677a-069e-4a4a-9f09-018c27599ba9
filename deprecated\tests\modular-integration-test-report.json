{"methodCoverage": [{"name": "getIntegrations", "result": "PASS", "details": "Method present in facade"}, {"name": "getIntegration", "result": "PASS", "details": "Method present in facade"}, {"name": "createIntegration", "result": "PASS", "details": "Method present in facade"}, {"name": "updateIntegration", "result": "PASS", "details": "Method present in facade"}, {"name": "deleteIntegration", "result": "PASS", "details": "Method present in facade"}, {"name": "getAuthUrl", "result": "PASS", "details": "Method present in facade"}, {"name": "handleOAuthCallback", "result": "PASS", "details": "Method present in facade"}, {"name": "getGoogleDriveFolders", "result": "PASS", "details": "Method present in facade"}, {"name": "getDriveStructure", "result": "PASS", "details": "Method present in facade"}, {"name": "debugDriveFolders", "result": "PASS", "details": "Method present in facade"}, {"name": "getTeamsAuthUrl", "result": "PASS", "details": "Method present in facade"}, {"name": "handleTeamsOAuthCallback", "result": "PASS", "details": "Method present in facade"}, {"name": "getTeamsSources", "result": "PASS", "details": "Method present in facade"}, {"name": "getTeamsFolders", "result": "PASS", "details": "Method present in facade"}, {"name": "getTeamsChannels", "result": "PASS", "details": "Method present in facade"}, {"name": "testTeamsConnection", "result": "PASS", "details": "Method present in facade"}, {"name": "testConnection", "result": "PASS", "details": "Method present in facade"}, {"name": "updateSchedule", "result": "PASS", "details": "Method present in facade"}, {"name": "getSchedules", "result": "PASS", "details": "Method present in facade"}], "routeMapping": [{"name": "GET /api/integrations", "result": "PASS", "details": "Route exists (200)"}, {"name": "GET /api/integrations/1", "result": "PASS", "details": "Route exists (404)"}, {"name": "POST /api/integrations", "result": "PASS", "details": "Route exists (400)"}, {"name": "PUT /api/integrations/1", "result": "PASS", "details": "Route exists (400)"}, {"name": "DELETE /api/integrations/1", "result": "PASS", "details": "Route exists (404)"}, {"name": "GET /api/integrations/1/auth-url", "result": "PASS", "details": "Route exists (404)"}, {"name": "GET /api/integrations/1/oauth/callback", "result": "PASS", "details": "Route exists (404)"}, {"name": "GET /api/integrations/1/folders", "result": "PASS", "details": "Route exists (404)"}, {"name": "GET /api/integrations/1/drive-structure", "result": "PASS", "details": "Route exists (404)"}, {"name": "GET /api/integrations/1/debug-folders", "result": "PASS", "details": "Route exists (404)"}, {"name": "GET /api/integrations/1/teams-auth-url", "result": "PASS", "details": "Route exists (200)"}, {"name": "GET /api/integrations/1/teams/oauth/callback", "result": "PASS", "details": "Route exists (200)"}, {"name": "GET /api/integrations/1/teams-sources", "result": "PASS", "details": "Route exists (400)"}, {"name": "GET /api/integrations/1/teams-folders", "result": "PASS", "details": "Route exists (400)"}, {"name": "GET /api/integrations/1/teams-channels/teamId", "result": "PASS", "details": "Route exists (400)"}, {"name": "POST /api/integrations/1/test-teams-connection", "result": "PASS", "details": "Route exists (400)"}, {"name": "POST /api/integrations/1/test-connection", "result": "PASS", "details": "Route exists (404)"}, {"name": "POST /api/schedules", "result": "PASS", "details": "Route exists (400)"}, {"name": "GET /api/schedules", "result": "PASS", "details": "Route exists (200)"}], "errorHandling": [{"name": "Invalid integration ID", "result": "PASS", "details": "Correct error status: 400"}, {"name": "Non-existent integration", "result": "PASS", "details": "Correct error status: 404"}, {"name": "Missing request body", "result": "PASS", "details": "Correct error status: 400"}, {"name": "Invalid JSON body", "result": "FAIL", "details": "Expected 400, got 500"}], "serviceImports": [{"name": "googleService in server/controllers/integration/index.ts", "result": "PASS", "details": "Service accessible"}, {"name": "teamsService in server/controllers/integration/index.ts", "result": "PASS", "details": "Service accessible"}, {"name": "microsoftService in server/controllers/integration/index.ts", "result": "PASS", "details": "Service accessible"}, {"name": "cryptoService in server/controllers/integration/index.ts", "result": "PASS", "details": "Service accessible"}, {"name": "schedulerService in server/controllers/integration/index.ts", "result": "PASS", "details": "Service accessible"}, {"name": "notionService in server/controllers/integration/index.ts", "result": "PASS", "details": "Service accessible"}, {"name": "googleService in server/controllers/integration/base/base-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "teamsService in server/controllers/integration/base/base-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "microsoftService in server/controllers/integration/base/base-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "cryptoService in server/controllers/integration/base/base-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "schedulerService in server/controllers/integration/base/base-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "notionService in server/controllers/integration/base/base-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "googleService in server/controllers/integration/shared/integration-crud.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "teamsService in server/controllers/integration/shared/integration-crud.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "microsoftService in server/controllers/integration/shared/integration-crud.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "cryptoService in server/controllers/integration/shared/integration-crud.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "schedulerService in server/controllers/integration/shared/integration-crud.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "notionService in server/controllers/integration/shared/integration-crud.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "googleService in server/controllers/integration/google/google-oauth.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "teamsService in server/controllers/integration/google/google-oauth.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "microsoftService in server/controllers/integration/google/google-oauth.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "cryptoService in server/controllers/integration/google/google-oauth.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "schedulerService in server/controllers/integration/google/google-oauth.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "notionService in server/controllers/integration/google/google-oauth.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "googleService in server/controllers/integration/google/google-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "teamsService in server/controllers/integration/google/google-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "microsoftService in server/controllers/integration/google/google-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "cryptoService in server/controllers/integration/google/google-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "schedulerService in server/controllers/integration/google/google-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "notionService in server/controllers/integration/google/google-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "googleService in server/controllers/integration/microsoft/teams-oauth.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "teamsService in server/controllers/integration/microsoft/teams-oauth.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "microsoftService in server/controllers/integration/microsoft/teams-oauth.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "cryptoService in server/controllers/integration/microsoft/teams-oauth.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "schedulerService in server/controllers/integration/microsoft/teams-oauth.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "notionService in server/controllers/integration/microsoft/teams-oauth.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "googleService in server/controllers/integration/microsoft/teams-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "teamsService in server/controllers/integration/microsoft/teams-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "microsoftService in server/controllers/integration/microsoft/teams-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "cryptoService in server/controllers/integration/microsoft/teams-integration.controller.ts", "result": "PASS", "details": "Service accessible"}, {"name": "schedulerService in server/controllers/integration/microsoft/teams-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}, {"name": "notionService in server/controllers/integration/microsoft/teams-integration.controller.ts", "result": "PASS", "details": "Service not needed in this module"}], "facadePattern": [{"name": "Delegation to integrationCrudController", "result": "PASS", "details": "Proper delegation found"}, {"name": "Delegation to googleOAuthController", "result": "PASS", "details": "Proper delegation found"}, {"name": "Delegation to googleIntegrationController", "result": "PASS", "details": "Proper delegation found"}, {"name": "Delegation to teamsOAuthController", "result": "PASS", "details": "Proper delegation found"}, {"name": "Delegation to teamsIntegrationController", "result": "PASS", "details": "Proper delegation found"}, {"name": "Method binding", "result": "PASS", "details": "19 methods properly bound"}], "apiContracts": [{"name": "GET integrations", "result": "PASS", "details": "Returns integrations array"}, {"name": "POST integrations", "result": "PASS", "details": "Successfully created integration"}, {"name": "GET single integration", "result": "PASS", "details": "Retrieved correct integration"}, {"name": "PUT integration", "result": "FAIL", "details": "Failed to update integration"}, {"name": "DELETE integration", "result": "PASS", "details": "Successfully deleted integration"}]}