Schedules:
  get:
    tags:
      - Sync
    summary: Get all schedules
    description: Retrieve all synchronization schedules
    operationId: getSchedules
    responses:
      '200':
        description: Schedules retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                schedules:
                  type: array
                  items:
                    type: object
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  post:
    tags:
      - Sync
    summary: Update schedule
    description: Update or create a synchronization schedule
    operationId: updateSchedule
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              integrationId:
                type: integer
              schedule:
                type: string
                description: Cron expression for the schedule
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

SyncLogs:
  get:
    tags:
      - Sync
    summary: Get sync logs
    description: Retrieve synchronization logs with optional filtering
    operationId: getSyncLogs
    parameters:
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/OffsetParam'
      - name: integrationId
        in: query
        description: Filter by integration ID
        required: false
        schema:
          type: integer
    responses:
      '200':
        $ref: '../components/responses.yaml#/SyncLogsListResponse'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

SyncItems:
  get:
    tags:
      - Sync
    summary: Get sync items
    description: Retrieve sync items for a specific sync log
    operationId: getSyncItems
    parameters:
      - $ref: '../components/parameters.yaml#/SyncLogId'
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/OffsetParam'
    responses:
      '200':
        $ref: '../components/responses.yaml#/SyncItemsListResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

SyncNow:
  post:
    tags:
      - Sync
    summary: Trigger immediate sync
    description: Trigger an immediate synchronization for specified integrations
    operationId: syncNow
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              integrationIds:
                type: array
                items:
                  type: integer
                description: Array of integration IDs to sync
              forceResync:
                type: boolean
                default: false
                description: Force a complete resync
    responses:
      '200':
        description: Sync triggered successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                syncLogIds:
                  type: array
                  items:
                    type: integer
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ReVectorize:
  post:
    tags:
      - Sync
    summary: Re-vectorize all files
    description: Re-process all files to generate new embeddings
    operationId: reVectorizeAll
    responses:
      '200':
        description: Re-vectorization started successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                jobId:
                  type: string
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 