import { z } from "zod";

// Chat role types
export type ChatRole = "user" | "assistant" | "system";

// Chat session types
export interface ChatSession {
  id: string;
  userId: string | null;
  title: string | null;
  enabledSources: Record<string, any> | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface InsertChatSession {
  id?: string;
  userId?: string | null;
  title?: string | null;
  enabledSources?: Record<string, any> | null;
}

// Chat message types
export interface ChatMessage {
  id: number;
  sessionId: string;
  role: string;
  content: string;
  sourcesUsed: Record<string, any> | null;
  relevantChunks: Record<string, any> | null;
  tokenCount: number | null;
  model: string | null;
  createdAt: Date;
}

export interface InsertChatMessage {
  sessionId: string;
  role: string;
  content: string;
  sourcesUsed?: Record<string, any> | null;
  relevantChunks?: Record<string, any> | null;
  tokenCount?: number | null;
  model?: string | null;
}

// Chat API request/response types
export interface ChatMessageRequest {
  content: string;
  enabledSources?: string[];
}

export interface ChatSessionRequest {
  title?: string;
  enabledSources?: string[];
  userId?: string;
}

export interface ChatResponse {
  userMessage: ChatMessage;
  aiMessage: ChatMessage;
  relevantChunks: number;
  sourcesUsed: string[];
}

// RAG-related types
export interface RelevantChunk {
  id: number;
  fileId: number;
  fileName: string;
  content: string;
  similarity: number;
  platform: string;
  metadata?: Record<string, any>;
}

export interface ChatSource {
  id: string;
  name: string;
  type: string;
  platform: string;
  status: string;
}

// Search types
export interface ChatSearchRequest {
  query: string;
  enabledSources?: string[];
  limit?: number;
}

export interface ChatSearchResult {
  results: RelevantChunk[];
  count: number;
  query: string;
  enabledSources: string[];
}

// Validation schemas
export const createChatSessionSchema = z.object({
  title: z.string().optional(),
  enabledSources: z.array(z.string()).optional(),
  userId: z.string().optional(),
});

export const sendChatMessageSchema = z.object({
  content: z.string().min(1),
  enabledSources: z.array(z.string()).optional(),
  openaiConfig: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().min(1).max(4000).optional(),
    topP: z.number().min(0).max(1).optional(),
  }).optional(),
});

export const chatSearchSchema = z.object({
  query: z.string().min(1),
  enabledSources: z.array(z.string()).optional(),
  limit: z.number().min(1).max(100).optional(),
});

export type CreateChatSessionRequest = z.infer<typeof createChatSessionSchema>;
export type SendChatMessageRequest = z.infer<typeof sendChatMessageSchema>;
export type ChatSearchRequestValidated = z.infer<typeof chatSearchSchema>;
