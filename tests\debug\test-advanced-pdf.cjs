require('dotenv').config();

async function testAdvancedPDFProcessing() {
  try {
    console.log('🚀 Testing Advanced PDF Processing Integration...\n');
    
    // Test 1: Direct advanced processor test
    console.log('Test 1: Direct Advanced PDF Processor');
    console.log('===================================');
    
    const { AdvancedPDFProcessor } = await import('../../server/services/pdf-service-advanced.js');
    const processor = new AdvancedPDFProcessor();
    
    // Create a simple test PDF buffer
    const testPDFContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 200
>>
stream
BT
/F1 12 Tf
50 700 Td
(ANANT AGGARWAL) Tj
0 -20 Td
(Software Engineer) Tj
0 -20 Td
(Contact: <EMAIL>) Tj
0 -20 Td
(Phone: (555) 123-4567) Tj
0 -20 Td
(Skills: JavaScript, Python, React, Node.js) Tj
0 -20 Td
(Experience: 3+ years in full-stack development) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000010 00000 n 
0000000053 00000 n 
0000000125 00000 n 
0000000185 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
520
%%EOF`;

    const testBuffer = Buffer.from(testPDFContent);
    const testFileName = 'test-resume.pdf';
    
    console.log('📄 Testing with sample PDF buffer...');
    const result = await processor.processPDF(testBuffer, testFileName);
    
    console.log('Result:', {
      success: result.success,
      processingMethod: result.metadata?.processingMethod,
      confidence: result.metadata?.confidence,
      textLength: result.extractedText?.length || 0,
      hasChunks: (result.chunks?.length || 0) > 0,
      error: result.error
    });
    
    if (result.success && result.extractedText) {
      console.log('✅ Advanced PDF Processor working!');
      console.log('📄 Sample extracted text:', result.extractedText.substring(0, 200));
    } else {
      console.log('❌ Advanced PDF Processor failed:', result.error);
    }
    
    console.log('\n');
    
    // Test 2: PDF Service integration test
    console.log('Test 2: PDF Service Integration');
    console.log('===============================');
    
    const { pdfService } = await import('../../server/services/pdf-service.js');
    
    console.log('📄 Testing PDF service with advanced processing...');
    const serviceResult = await pdfService.extractTextFromPDF(testBuffer, testFileName);
    
    console.log('Service Result:', {
      success: serviceResult.success,
      processingMethod: serviceResult.metadata?.processingMethod,
      confidence: serviceResult.metadata?.confidence,
      textLength: serviceResult.extractedText?.length || 0,
      pageCount: serviceResult.metadata?.pageCount,
      wordCount: serviceResult.metadata?.wordCount,
      hasAdvancedFeatures: !!(serviceResult.metadata?.hasImages || serviceResult.metadata?.hasTables),
      error: serviceResult.error
    });
    
    if (serviceResult.success && serviceResult.extractedText) {
      console.log('✅ PDF Service integration working!');
      console.log('📄 Sample text:', serviceResult.extractedText.substring(0, 200));
      
      if (serviceResult.metadata?.structuredMarkdown) {
        console.log('📝 Has structured markdown output!');
      }
      
      if (serviceResult.metadata?.chunks && serviceResult.metadata.chunks.length > 0) {
        console.log(`🧩 Created ${serviceResult.metadata.chunks.length} chunks for RAG!`);
      }
    } else {
      console.log('❌ PDF Service integration failed:', serviceResult.error);
    }
    
    console.log('\n');
    
    // Test 3: Real file test (if available)
    console.log('Test 3: Real PDF File Test');
    console.log('==========================');
    
    try {
      // Try to use the actual Google Drive integration to test with the resume
      const { GoogleServiceFacade } = await import('../../server/services/google/google.facade.js');
      const { storage } = await import('../../server/storage/index.js');
      const { google } = require('googleapis');
      
      const integrations = await storage.getIntegrations();
      const googleIntegrations = integrations.filter(int => int.type === 'google-drive');
      
      if (googleIntegrations.length > 0 && googleIntegrations[0].credentials) {
        console.log('📋 Found Google Drive integration, testing with real resume...');
        
        const integration = googleIntegrations[0];
        const googleService = new GoogleServiceFacade();
        await googleService.initialize();
        
        const auth = await googleService.getAuthorizedClient(integration.credentials);
        const drive = google.drive({ version: 'v3', auth });
        
        const fileId = '1Zk6ZgZHomhCXxjvHx31a8DMIoiVzrSqx'; // Anant's resume
        
        console.log('📥 Downloading real PDF from Google Drive...');
        const response = await drive.files.get({
          fileId: fileId,
          alt: 'media',
        }, {
          responseType: 'arraybuffer',
        });
        
        if (response.data) {
          const realBuffer = Buffer.from(response.data);
          console.log(`✅ Downloaded ${realBuffer.length} bytes`);
          
          console.log('🚀 Testing advanced processing on real resume...');
          const realResult = await pdfService.extractTextFromPDF(realBuffer, 'Anant A. Resume.pdf');
          
          if (realResult.success && realResult.extractedText) {
            console.log('🎉 SUCCESS: Advanced PDF processing working on real resume!');
            console.log(`📊 Processing method: ${realResult.metadata?.processingMethod}`);
            console.log(`📊 Confidence: ${realResult.metadata?.confidence}`);
            console.log(`📊 Pages: ${realResult.metadata?.pageCount}`);
            console.log(`📊 Words: ${realResult.metadata?.wordCount}`);
            console.log(`📊 Has tables: ${realResult.metadata?.hasTables}`);
            console.log(`📊 Has images: ${realResult.metadata?.hasImages}`);
            console.log(`📊 Chunks created: ${realResult.metadata?.chunks?.length || 0}`);
            
            console.log('\n📄 First 500 characters of extracted text:');
            console.log('================================================');
            console.log(realResult.extractedText.substring(0, 500));
            console.log('================================================');
            
            if (realResult.metadata?.structuredMarkdown) {
              console.log('\n📝 First 300 characters of structured markdown:');
              console.log('================================================');
              console.log(realResult.metadata.structuredMarkdown.substring(0, 300));
              console.log('================================================');
            }
          } else {
            console.log('❌ Advanced processing failed on real file:', realResult.error);
          }
        } else {
          console.log('❌ Failed to download real PDF');
        }
      } else {
        console.log('⚠️ No Google Drive integration found, skipping real file test');
      }
    } catch (realFileError) {
      console.log('⚠️ Real file test error (this is okay):', realFileError.message);
    }
    
    console.log('\n🏁 Advanced PDF Processing Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAdvancedPDFProcessing(); 