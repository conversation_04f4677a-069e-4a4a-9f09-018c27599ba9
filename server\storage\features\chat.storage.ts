import { 
  chatSessions, 
  chatMessages, 
  type ChatSession, 
  type InsertChatSession,
  type ChatMessage,
  type InsertChatMessage 
} from "../../../shared/index.js";
import { eq, desc } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config";
import { generateUUID } from '../../../shared/utils/crypto.utils';

/**
 * Chat storage operations (sessions and messages)
 */
export class ChatStorage {
  private db: any;
  private memoryChatSessions: Map<string, ChatSession>;
  private memoryChatMessages: Map<number, ChatMessage>;
  private chatMessageIdCounter: number;
  private useDatabase: boolean;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memoryChatSessions = new Map();
    this.memoryChatMessages = new Map();
    this.chatMessageIdCounter = 1;
    // Database connection will be initialized lazily when first needed
  }

  /**
   * Ensure database connection is available
   */
  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        // Silently fall back to memory storage
        this.useDatabase = false;
      }
    }
  }

  // Chat Sessions
  async getChatSessions(userId?: string, limit?: number): Promise<ChatSession[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(chatSessions);

      if (userId) {
        baseQuery = baseQuery.where(eq(chatSessions.userId, userId)) as any;
      }

      baseQuery = baseQuery.orderBy(desc(chatSessions.updatedAt)) as any;

      if (limit) {
        baseQuery = baseQuery.limit(limit) as any;
      }

      return await baseQuery;
    } else {
      let sessions = Array.from(this.memoryChatSessions.values());

      if (userId) {
        sessions = sessions.filter(session => session.userId === userId);
      }

      sessions.sort((a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );

      if (limit) {
        sessions = sessions.slice(0, limit);
      }

      return sessions;
    }
  }

  async getChatSession(id: string): Promise<ChatSession | undefined> {
    this.ensureDatabase();

    console.log(`[CHATSTORAGE] Looking for session: ${id}`);

    if (this.useDatabase) {
      try {
        const result = await this.db.select().from(chatSessions).where(eq(chatSessions.id, id));
        if (result.length > 0) {
          console.log(`[CHATSTORAGE] Found session: ${id}`);
          return result[0];
        } else {
          console.log(`[CHATSTORAGE] Session not found: ${id}`);
          return undefined;
        }
      } catch (error) {
        console.error(`[CHATSTORAGE] Error looking for session ${id}:`, error);
        throw error;
      }
    } else {
      const session = this.memoryChatSessions.get(id);
      if (session) {
        console.log(`[CHATSTORAGE] Found session in memory: ${id}`);
      } else {
        console.log(`[CHATSTORAGE] Session not found in memory: ${id}`);
      }
      return session;
    }
  }

  async createChatSession(session: InsertChatSession): Promise<ChatSession> {
    this.ensureDatabase();

    // If no id is provided, generate one
    if (!session.id) {
      session.id = generateUUID();
    }

    console.log(`[CHATSTORAGE] Creating session with ID: ${session.id}`);

    if (this.useDatabase) {
      try {
        const result = await this.db.insert(chatSessions).values(session).returning();
        console.log(`[CHATSTORAGE] Successfully created session: ${result[0].id}`);
        return result[0];
      } catch (error) {
        console.error(`[CHATSTORAGE] Failed to create session ${session.id}:`, error);
        throw error;
      }
    } else {
      const now = new Date();
      const newSession: ChatSession = {
        id: session.id,
        title: session.title ?? null,
        userId: session.userId ?? null,
        enabledSources: session.enabledSources || {},
        createdAt: now,
        updatedAt: now,
      };
      this.memoryChatSessions.set(session.id, newSession);
      console.log(`[CHATSTORAGE] Successfully created session in memory: ${session.id}`);
      return newSession;
    }
  }

  async updateChatSession(id: string, data: Partial<ChatSession>): Promise<ChatSession | undefined> {
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.update(chatSessions)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(chatSessions.id, id))
        .returning();
      return result[0];
    } else {
      const session = this.memoryChatSessions.get(id);
      if (!session) return undefined;

      const updated: ChatSession = {
        ...session,
        ...data,
        updatedAt: new Date(),
      };

      this.memoryChatSessions.set(id, updated);
      return updated;
    }
  }

  async deleteChatSession(id: string): Promise<boolean> {
    this.ensureDatabase();

    if (this.useDatabase) {
      try {
        // Delete messages first (cascade should handle this, but being explicit)
        await this.db.delete(chatMessages).where(eq(chatMessages.sessionId, id));
        // Delete session
        await this.db.delete(chatSessions).where(eq(chatSessions.id, id));
        return true;
      } catch (error) {
        console.error("Error deleting chat session:", error);
        return false;
      }
    } else {
      // Also delete all messages in this session
      const messagesToDelete = Array.from(this.memoryChatMessages.entries()).filter(
        ([_, message]) => message.sessionId === id
      );

      for (const [messageId, _] of messagesToDelete) {
        this.memoryChatMessages.delete(messageId);
      }

      return this.memoryChatSessions.delete(id);
    }
  }

  // Chat Messages
  async getChatMessages(sessionId: string, limit: number = 50): Promise<ChatMessage[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      return await this.db
        .select()
        .from(chatMessages)
        .where(eq(chatMessages.sessionId, sessionId))
        .orderBy(desc(chatMessages.createdAt))
        .limit(limit);
    } else {
      return Array.from(this.memoryChatMessages.values())
        .filter(msg => msg.sessionId === sessionId)
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, limit);
    }
  }

  async createChatMessage(message: InsertChatMessage): Promise<ChatMessage> {
    this.ensureDatabase();

    console.log(`[CHATSTORAGE] Creating message for session: ${message.sessionId}`);

    if (this.useDatabase) {
      try {
        // First, verify the session exists
        const sessionExists = await this.db.select().from(chatSessions).where(eq(chatSessions.id, message.sessionId));
        if (sessionExists.length === 0) {
          throw new Error(`Session ${message.sessionId} does not exist in database`);
        }
        console.log(`[CHATSTORAGE] Session ${message.sessionId} verified to exist`);

        const result = await this.db.insert(chatMessages).values(message).returning();
        console.log(`[CHATSTORAGE] Successfully created message for session: ${message.sessionId}`);
        return result[0];
      } catch (error) {
        console.error(`[CHATSTORAGE] Failed to create message for session ${message.sessionId}:`, error);
        throw error;
      }
    } else {
      const id = this.chatMessageIdCounter++;
      const now = new Date();
      const newMessage: ChatMessage = {
        id,
        sessionId: message.sessionId,
        role: message.role,
        content: message.content,
        sourcesUsed: message.sourcesUsed || [],
        relevantChunks: message.relevantChunks || [],
        tokenCount: message.tokenCount || 0,
        model: message.model || "gpt-4.1-nano",
        createdAt: now,
      };
      this.memoryChatMessages.set(id, newMessage);
      console.log(`[CHATSTORAGE] Successfully created message in memory for session: ${message.sessionId}`);
      return newMessage;
    }
  }

  async clearAllChatData(): Promise<void> {
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(chatMessages);
      await this.db.delete(chatSessions);
    } else {
      this.memoryChatMessages.clear();
      this.memoryChatSessions.clear();
      this.chatMessageIdCounter = 1;
    }
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      sessionCount: this.useDatabase ? 'unknown' : this.memoryChatSessions.size,
      messageCount: this.useDatabase ? 'unknown' : this.memoryChatMessages.size,
    };
  }
}
