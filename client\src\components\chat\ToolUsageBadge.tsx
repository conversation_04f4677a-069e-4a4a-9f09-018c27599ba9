import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Zap, CheckCircle, XCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ToolUsage {
  toolName: string;
  serverName: string;
  timestamp: Date;
  duration?: number;
  success: boolean;
  result?: string;
  error?: string;
}

interface ToolUsageBadgeProps {
  toolUsages: ToolUsage[];
  className?: string;
}

export function ToolUsageBadge({ toolUsages, className }: ToolUsageBadgeProps) {
  if (toolUsages.length === 0) return null;

  const successCount = toolUsages.filter(t => t.success).length;
  const failureCount = toolUsages.length - successCount;

  return (
    <Badge
      variant="secondary"
      className={cn(
        "inline-flex items-center space-x-1.5 px-2.5 py-1 text-xs font-semibold shadow-sm border transition-all duration-200 hover:shadow-md",
        successCount === toolUsages.length
          ? "bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border-blue-200 hover:from-blue-200 hover:to-blue-100"
          : failureCount > 0
          ? "bg-gradient-to-r from-yellow-100 to-orange-50 text-yellow-800 border-yellow-200 hover:from-yellow-200 hover:to-orange-100"
          : "bg-gradient-to-r from-gray-100 to-gray-50 text-gray-800 border-gray-200 hover:from-gray-200 hover:to-gray-100",
        className
      )}
    >
      <div className="flex items-center space-x-1">
        <Zap className="w-3 h-3" />
        <span>
          {toolUsages.length} tool{toolUsages.length !== 1 ? 's' : ''} used
        </span>
      </div>
      {failureCount > 0 && (
        <div className="flex items-center space-x-1">
          <span>•</span>
          <XCircle className="w-3 h-3 text-red-600" />
          <span className="text-red-600 font-bold">{failureCount}</span>
        </div>
      )}
    </Badge>
  );
}
