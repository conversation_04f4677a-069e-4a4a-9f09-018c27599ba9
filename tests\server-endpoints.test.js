#!/usr/bin/env node

/**
 * Test all server endpoints to ensure the modularization is working
 */

import http from 'http';

const BASE_URL = 'http://localhost:8080';

const results = {
  passed: 0,
  failed: 0,
  tests: []
};

function addTest(name, status, message, details = null) {
  results.tests.push({ name, status, message, details });
  if (status === 'PASS') results.passed++;
  else results.failed++;

  const emoji = status === 'PASS' ? '✅' : '❌';
  console.log(`${emoji} ${name}: ${message}`);
  if (details) {
    console.log(`   Details: ${details}`);
  }
}

function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testEndpoint(path, expectedStatus = 200, testName = null) {
  const name = testName || `GET ${path}`;
  try {
    const response = await makeRequest(path);
    if (response.statusCode === expectedStatus) {
      addTest(name, 'PASS', `Status ${response.statusCode}`, response.data ? `Response: ${response.data.substring(0, 100)}...` : null);
      return response;
    } else {
      addTest(name, 'FAIL', `Expected ${expectedStatus}, got ${response.statusCode}`, response.data ? `Response: ${response.data}` : null);
      return null;
    }
  } catch (error) {
    addTest(name, 'FAIL', `Connection failed: ${error.message}`, `Full error: ${error.stack || error}`);
    return null;
  }
}

async function testHealthEndpoints() {
  console.log('\n🏥 Testing Health Endpoints...');
  await testEndpoint('/api/health');
  await testEndpoint('/api/readiness');
  await testEndpoint('/api/liveness');
}

async function testStorageEndpoints() {
  console.log('\n📦 Testing Storage Endpoints...');
  await testEndpoint('/api/integrations');
  await testEndpoint('/api/files');
  await testEndpoint('/api/chat-sessions');
  await testEndpoint('/api/sync-logs');
  await testEndpoint('/api/projects');
}

async function testDiagnosticEndpoints() {
  console.log('\n🔍 Testing Diagnostic Endpoints...');
  await testEndpoint('/api/diagnostic/storage');
  await testEndpoint('/api/diagnostic/database');
  await testEndpoint('/api/diagnostic/services');
}

async function testRAGEndpoints() {
  console.log('\n🧠 Testing RAG Endpoints...');
  await testEndpoint('/api/rag/search?query=test');
  await testEndpoint('/api/rag/status');
}

async function testFileUploadEndpoints() {
  console.log('\n📁 Testing File Upload Endpoints...');
  await testEndpoint('/api/upload/status');
}

async function testChatEndpoints() {
  console.log('\n💬 Testing Chat Endpoints...');
  await testEndpoint('/api/chat/sessions');
}

async function runTests() {
  console.log('🚀 MeetSync Web App - Server Endpoint Validation');
  console.log('=' .repeat(60));
  
  // Check if server is running
  try {
    await testEndpoint('/', 200, 'Server Running');
  } catch (error) {
    console.log('❌ Server is not running on http://localhost:8080');
    console.log('Please start the server with: npm run dev');
    process.exit(1);
  }
  
  await testHealthEndpoints();
  await testStorageEndpoints();
  await testDiagnosticEndpoints();
  await testRAGEndpoints();
  await testFileUploadEndpoints();
  await testChatEndpoints();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 ENDPOINT VALIDATION SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📋 Total Tests: ${results.tests.length}`);
  
  const successRate = Math.round((results.passed / results.tests.length) * 100);
  console.log(`📈 Success Rate: ${successRate}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL ENDPOINT TESTS PASSED! Server is working perfectly!');
    process.exit(0);
  } else {
    console.log('\n💥 Some endpoint tests failed. Please review the issues above.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Fatal test error:', error);
  process.exit(1);
});
