import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  ArrowRightIcon,
  CheckIcon
} from 'lucide-react';
import { IntegrationIcon } from '@/components/ui/integration-icons';
import AccountSelector from './AccountSelector';

interface IntegrationOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  provider: string;
  category: string;
  features: string[];
}

interface IntegrationSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onIntegrationCreate: (type: string, account: any) => void;
}

const integrationOptions: IntegrationOption[] = [
  // Google Services
  {
    id: 'gmail',
    name: 'Gmail',
    description: 'Sync and search your Gmail emails with AI-powered insights',
    icon: <IntegrationIcon type="gmail" size={24} />,
    provider: 'google',
    category: 'Communication',
    features: ['Email sync', 'Smart search', 'Thread analysis', 'Attachment extraction']
  },
  {
    id: 'google_calendar',
    name: 'Google Calendar',
    description: 'Access your calendar events and meeting schedules',
    icon: <IntegrationIcon type="google_calendar" size={24} />,
    provider: 'google',
    category: 'Productivity',
    features: ['Event sync', 'Meeting detection', 'Schedule analysis', 'Smart reminders']
  },
  {
    id: 'google_drive',
    name: 'Google Drive',
    description: 'Sync files and documents from your Google Drive',
    icon: <IntegrationIcon type="google_drive" size={24} />,
    provider: 'google',
    category: 'Storage',
    features: ['File sync', 'Document search', 'Folder organization', 'Content analysis']
  },

  // Microsoft Services
  {
    id: 'microsoft_teams',
    name: 'Microsoft Teams',
    description: 'Access Teams chats, meetings, and shared files',
    icon: <IntegrationIcon type="microsoft_teams" size={24} />,
    provider: 'microsoft',
    category: 'Communication',
    features: ['Teams chat sync', 'Meeting recordings', 'Shared files', 'Channel analysis']
  },
  {
    id: 'outlook',
    name: 'Outlook',
    description: 'Sync Outlook emails, calendar, and contacts',
    icon: <IntegrationIcon type="outlook" size={24} />,
    provider: 'microsoft',
    category: 'Communication',
    features: ['Email sync', 'Calendar events', 'Contact management', 'Meeting insights']
  },
  {
    id: 'onedrive',
    name: 'OneDrive',
    description: 'Access and search your OneDrive files and documents',
    icon: <IntegrationIcon type="onedrive" size={24} />,
    provider: 'microsoft',
    category: 'Storage',
    features: ['File sync', 'Document collaboration', 'Version history', 'Smart search']
  },

  // Communication Platforms
  {
    id: 'slack',
    name: 'Slack',
    description: 'Sync Slack conversations and shared files',
    icon: <IntegrationIcon type="slack" size={24} />,
    provider: 'slack',
    category: 'Communication',
    features: ['Channel sync', 'Direct messages', 'File sharing', 'Thread analysis']
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Connect Discord servers and channels for team communication',
    icon: <IntegrationIcon type="discord" size={24} />,
    provider: 'discord',
    category: 'Communication',
    features: ['Server sync', 'Message history', 'Voice transcripts', 'Community insights']
  },
  {
    id: 'zoom',
    name: 'Zoom',
    description: 'Access Zoom meeting recordings and transcripts',
    icon: <IntegrationIcon type="zoom" size={24} />,
    provider: 'zoom',
    category: 'Communication',
    features: ['Recording sync', 'Auto transcripts', 'Meeting summaries', 'Participant insights']
  },

  // Project Management
  {
    id: 'notion',
    name: 'Notion',
    description: 'Sync Notion pages, databases, and team workspaces',
    icon: <IntegrationIcon type="notion" size={24} />,
    provider: 'notion',
    category: 'Productivity',
    features: ['Page sync', 'Database queries', 'Block content', 'Team collaboration']
  },
  {
    id: 'trello',
    name: 'Trello',
    description: 'Connect Trello boards, cards, and project workflows',
    icon: <IntegrationIcon type="trello" size={24} />,
    provider: 'trello',
    category: 'Productivity',
    features: ['Board sync', 'Card tracking', 'Activity logs', 'Progress analytics']
  },
  {
    id: 'asana',
    name: 'Asana',
    description: 'Sync Asana projects, tasks, and team conversations',
    icon: <IntegrationIcon type="asana" size={24} />,
    provider: 'asana',
    category: 'Productivity',
    features: ['Project sync', 'Task management', 'Team updates', 'Goal tracking']
  },
  {
    id: 'jira',
    name: 'Jira',
    description: 'Connect Jira projects, issues, and development workflows',
    icon: <IntegrationIcon type="jira" size={24} />,
    provider: 'atlassian',
    category: 'Productivity',
    features: ['Issue tracking', 'Sprint planning', 'Workflow automation', 'Release notes']
  },

  // Development Tools
  {
    id: 'github',
    name: 'GitHub',
    description: 'Sync GitHub repositories, issues, and pull requests',
    icon: <IntegrationIcon type="github" size={24} />,
    provider: 'github',
    category: 'Development',
    features: ['Repository sync', 'Code search', 'Issue tracking', 'PR analysis']
  },
  {
    id: 'gitlab',
    name: 'GitLab',
    description: 'Connect GitLab projects, merge requests, and CI/CD pipelines',
    icon: <IntegrationIcon type="gitlab" size={24} />,
    provider: 'gitlab',
    category: 'Development',
    features: ['Project sync', 'Merge requests', 'Pipeline logs', 'Code review']
  },

  // Design Tools
  {
    id: 'figma',
    name: 'Figma',
    description: 'Access Figma files, designs, and team collaboration',
    icon: <IntegrationIcon type="figma" size={24} />,
    provider: 'figma',
    category: 'Design',
    features: ['Design sync', 'Comment threads', 'Version history', 'Component libraries']
  },
  {
    id: 'canva',
    name: 'Canva',
    description: 'Sync Canva designs, templates, and brand assets',
    icon: <IntegrationIcon type="canva" size={24} />,
    provider: 'canva',
    category: 'Design',
    features: ['Design templates', 'Brand kit', 'Team folders', 'Content calendar']
  },

  // Storage Services
  {
    id: 'dropbox',
    name: 'Dropbox',
    description: 'Connect Dropbox files, shared folders, and team spaces',
    icon: <IntegrationIcon type="dropbox" size={24} />,
    provider: 'dropbox',
    category: 'Storage',
    features: ['File sync', 'Shared folders', 'Version history', 'Smart suggestions']
  },

  // AI & Analytics
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'Connect OpenAI models and conversation history',
    icon: <IntegrationIcon type="openai" size={24} />,
    provider: 'openai',
    category: 'AI Platform',
    features: ['Model access', 'Chat history', 'Custom prompts', 'Usage analytics']
  },
  {
    id: 'anthropic',
    name: 'Anthropic Claude',
    description: 'Access Claude conversations and AI-powered workflows',
    icon: <IntegrationIcon type="anthropic" size={24} />,
    provider: 'anthropic',
    category: 'AI Platform',
    features: ['Claude access', 'Conversation sync', 'Custom instructions', 'Usage insights']
  }
];

export default function IntegrationSelector({ 
  isOpen, 
  onClose, 
  onIntegrationCreate 
}: IntegrationSelectorProps) {
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationOption | null>(null);
  const [showAccountSelector, setShowAccountSelector] = useState(false);

  const handleIntegrationSelect = (integration: IntegrationOption) => {
    setSelectedIntegration(integration);
    setShowAccountSelector(true);
  };

  const handleAccountSelected = (account: any) => {
    if (selectedIntegration) {
      onIntegrationCreate(selectedIntegration.id, account);
      setShowAccountSelector(false);
      setSelectedIntegration(null);
      onClose();
    }
  };

  const handleAccountSelectorClose = () => {
    setShowAccountSelector(false);
    setSelectedIntegration(null);
  };

  const groupedIntegrations = integrationOptions.reduce((acc, integration) => {
    if (!acc[integration.category]) {
      acc[integration.category] = [];
    }
    acc[integration.category].push(integration);
    return acc;
  }, {} as Record<string, IntegrationOption[]>);

  return (
    <>
      <Dialog open={isOpen && !showAccountSelector} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Integration</DialogTitle>
            <DialogDescription>
              Choose a service to connect and start syncing your data for AI-powered search and insights.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {Object.entries(groupedIntegrations).map(([category, integrations]) => (
              <div key={category} className="space-y-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">{category}</h3>
                  <Badge variant="outline">{integrations.length} services</Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {integrations.map((integration) => (
                    <Card 
                      key={integration.id}
                      className="cursor-pointer hover:shadow-lg transition-all duration-200 group border-2 hover:border-blue-200"
                      onClick={() => handleIntegrationSelect(integration)}
                    >
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-blue-50 text-blue-600 group-hover:bg-blue-100 transition-colors">
                              {integration.icon}
                            </div>
                            <div>
                              <CardTitle className="text-base">{integration.name}</CardTitle>
                              <div className="flex items-center gap-2 mt-1">
                                <div className="h-4 w-4 flex items-center justify-center">
                                  <IntegrationIcon 
                                    type={integration.provider === 'google' ? 'google' : 
                                          integration.provider === 'microsoft' ? 'microsoft_teams' : 
                                          'slack'} 
                                    size={16} 
                                  />
                                </div>
                                <span className="text-xs text-muted-foreground capitalize">
                                  {integration.provider}
                                </span>
                              </div>
                            </div>
                          </div>
                          <ArrowRightIcon className="h-4 w-4 text-muted-foreground group-hover:text-blue-500 group-hover:translate-x-1 transition-all" />
                        </div>
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        <CardDescription className="mb-4 leading-relaxed">
                          {integration.description}
                        </CardDescription>
                        
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-foreground">Key Features:</h4>
                          <div className="flex flex-wrap gap-1">
                            {integration.features.map((feature, index) => (
                              <Badge 
                                key={index} 
                                variant="secondary" 
                                className="text-xs"
                              >
                                <CheckIcon className="h-3 w-3 mr-1" />
                                {feature}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {selectedIntegration && (
        <AccountSelector
          isOpen={showAccountSelector}
          onClose={handleAccountSelectorClose}
          integrationType={selectedIntegration.id}
          onAccountSelected={handleAccountSelected}
        />
      )}
    </>
  );
} 