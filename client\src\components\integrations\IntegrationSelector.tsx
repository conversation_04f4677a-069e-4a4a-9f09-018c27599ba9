import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  CalendarIcon, 
  MailIcon, 
  HardDriveIcon,
  ArrowRightIcon,
  CheckIcon
} from 'lucide-react';
import AccountSelector from './AccountSelector';

interface IntegrationOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  provider: string;
  category: string;
  features: string[];
}

interface IntegrationSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onIntegrationCreate: (type: string, account: any) => void;
}

const integrationOptions: IntegrationOption[] = [
  {
    id: 'gmail',
    name: 'Gmail',
    description: 'Sync and search your Gmail emails with AI-powered insights',
    icon: <MailIcon className="h-6 w-6" />,
    provider: 'google',
    category: 'Communication',
    features: ['Email sync', 'Smart search', 'Thread analysis', 'Attachment extraction']
  },
  {
    id: 'google_calendar',
    name: 'Google Calendar',
    description: 'Access your calendar events and meeting schedules',
    icon: <CalendarIcon className="h-6 w-6" />,
    provider: 'google',
    category: 'Productivity',
    features: ['Event sync', 'Meeting detection', 'Schedule analysis', 'Smart reminders']
  },
  {
    id: 'google_drive',
    name: 'Google Drive',
    description: 'Sync files and documents from your Google Drive',
    icon: <HardDriveIcon className="h-6 w-6" />,
    provider: 'google',
    category: 'Storage',
    features: ['File sync', 'Document search', 'Folder organization', 'Content analysis']
  }
];

export default function IntegrationSelector({ 
  isOpen, 
  onClose, 
  onIntegrationCreate 
}: IntegrationSelectorProps) {
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationOption | null>(null);
  const [showAccountSelector, setShowAccountSelector] = useState(false);

  const handleIntegrationSelect = (integration: IntegrationOption) => {
    setSelectedIntegration(integration);
    setShowAccountSelector(true);
  };

  const handleAccountSelected = (account: any) => {
    if (selectedIntegration) {
      onIntegrationCreate(selectedIntegration.id, account);
      setShowAccountSelector(false);
      setSelectedIntegration(null);
      onClose();
    }
  };

  const handleAccountSelectorClose = () => {
    setShowAccountSelector(false);
    setSelectedIntegration(null);
  };

  const groupedIntegrations = integrationOptions.reduce((acc, integration) => {
    if (!acc[integration.category]) {
      acc[integration.category] = [];
    }
    acc[integration.category].push(integration);
    return acc;
  }, {} as Record<string, IntegrationOption[]>);

  return (
    <>
      <Dialog open={isOpen && !showAccountSelector} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Integration</DialogTitle>
            <DialogDescription>
              Choose a service to connect and start syncing your data for AI-powered search and insights.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {Object.entries(groupedIntegrations).map(([category, integrations]) => (
              <div key={category} className="space-y-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">{category}</h3>
                  <Badge variant="outline">{integrations.length} services</Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {integrations.map((integration) => (
                    <Card 
                      key={integration.id}
                      className="cursor-pointer hover:shadow-lg transition-all duration-200 group border-2 hover:border-blue-200"
                      onClick={() => handleIntegrationSelect(integration)}
                    >
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-blue-50 text-blue-600 group-hover:bg-blue-100 transition-colors">
                              {integration.icon}
                            </div>
                            <div>
                              <CardTitle className="text-base">{integration.name}</CardTitle>
                              <div className="flex items-center gap-2 mt-1">
                                <img 
                                  src={integration.provider === 'google' 
                                    ? 'https://developers.google.com/identity/images/g-logo.png'
                                    : 'https://upload.wikimedia.org/wikipedia/commons/4/44/Microsoft_logo.svg'
                                  }
                                  alt={integration.provider}
                                  className="h-4 w-4"
                                />
                                <span className="text-xs text-muted-foreground capitalize">
                                  {integration.provider}
                                </span>
                              </div>
                            </div>
                          </div>
                          <ArrowRightIcon className="h-4 w-4 text-muted-foreground group-hover:text-blue-500 group-hover:translate-x-1 transition-all" />
                        </div>
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        <CardDescription className="mb-4 leading-relaxed">
                          {integration.description}
                        </CardDescription>
                        
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-foreground">Key Features:</h4>
                          <div className="flex flex-wrap gap-1">
                            {integration.features.map((feature, index) => (
                              <Badge 
                                key={index} 
                                variant="secondary" 
                                className="text-xs"
                              >
                                <CheckIcon className="h-3 w-3 mr-1" />
                                {feature}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {selectedIntegration && (
        <AccountSelector
          isOpen={showAccountSelector}
          onClose={handleAccountSelectorClose}
          integrationType={selectedIntegration.id}
          onAccountSelected={handleAccountSelected}
        />
      )}
    </>
  );
} 