import React from 'react';
import { Icon } from '@iconify/react';
import { cn } from "@/lib/utils";

// Official brand icon mappings using Iconify
const iconMap: Record<string, string> = {
  // AI & ML Services (2025 trending) - Official brand icons
  'openai': 'simple-icons:openai',
  'anthropic': 'simple-icons:anthropic',
  'claude': 'simple-icons:anthropic',
  'midjourney': 'simple-icons:midjourney',
  'stability': 'simple-icons:stabilityai',
  'stable-diffusion': 'simple-icons:stabilityai',
  'chatgpt': 'simple-icons:openai',
  'gpt': 'simple-icons:openai',
  'ai-assistant': 'material-symbols:robot-2',
  'machine-learning': 'material-symbols:psychology',
  
  // Google Services - Official brand icons
  'google-drive': 'logos:google-drive',
  'google_drive': 'logos:google-drive',
  'googledrive': 'logos:google-drive',
  'drive': 'logos:google-drive',
  'gmail': 'logos:google-gmail',
  'google-calendar': 'logos:google-calendar',
  'google_calendar': 'logos:google-calendar',
  'googlecalendar': 'logos:google-calendar',
  'calendar': 'logos:google-calendar',
  'google': 'logos:google-icon',
  'google-workspace': 'logos:google-workspace',
  
  // Microsoft Services - Official brand icons
  'microsoft': 'logos:microsoft-icon',
  'teams': 'logos:microsoft-teams',
  'microsoft-teams': 'logos:microsoft-teams',
  'microsoft_teams': 'logos:microsoft-teams',
  'outlook': 'logos:microsoft-outlook',
  'onedrive': 'logos:microsoft-onedrive',
  'office365': 'logos:microsoft-office',
  'sharepoint': 'logos:microsoft-sharepoint',
  
  // Communication & Collaboration - Official brand icons
  'slack': 'logos:slack-icon',
  'zoom': 'logos:zoom-icon',
  'discord': 'logos:discord-icon',
  'whatsapp': 'logos:whatsapp-icon',
  'telegram': 'logos:telegram',
  'signal': 'simple-icons:signal',
  
  // Storage & File Management
  'dropbox': 'logos:dropbox',
  'file': 'material-symbols:folder',
  'cloud': 'material-symbols:cloud',
  'database': 'material-symbols:database',
  
  // Project Management (2025 popular) - Official brand icons
  'notion': 'logos:notion-icon',
  'trello': 'logos:trello',
  'asana': 'logos:asana-icon',
  'jira': 'logos:jira',
  'confluence': 'logos:atlassian',
  'project': 'material-symbols:dashboard',
  
  // Development & Code - Official brand icons
  'github': 'logos:github-icon',
  'gitlab': 'logos:gitlab',
  'bitbucket': 'logos:bitbucket',
  'code': 'material-symbols:code',
  
  // Design Tools - Official brand icons
  'figma': 'logos:figma',
  'sketch': 'logos:sketch',
  'adobe-xd': 'logos:adobe-xd',
  'adobexd': 'logos:adobe-xd',
  'invision': 'simple-icons:invision',
  'canva': 'simple-icons:canva',
  'design': 'material-symbols:palette',
  
  // Social Media (2025 platforms) - Official brand icons
  'linkedin': 'logos:linkedin-icon',
  'twitter': 'logos:twitter',
  'x': 'simple-icons:x',
  'facebook': 'logos:facebook',
  'instagram': 'skill-icons:instagram',
  'tiktok': 'logos:tiktok-icon',
  'youtube': 'logos:youtube-icon',
  'twitch': 'logos:twitch',
  'social': 'material-symbols:share',
  
  // E-commerce & Web - Official brand icons
  'shopify': 'logos:shopify',
  'wordpress': 'logos:wordpress-icon',
  'wix': 'simple-icons:wix',
  'squarespace': 'simple-icons:squarespace',
  'ecommerce': 'material-symbols:shopping-cart',
  
  // Analytics & Insights
  'analytics': 'material-symbols:analytics',
  'insights': 'material-symbols:visibility',
  'data': 'material-symbols:database',
  'chart': 'material-symbols:bar-chart',
  
  // Security & Privacy
  'security': 'material-symbols:security',
  'privacy': 'material-symbols:privacy-tip',
  'encryption': 'material-symbols:lock',
  
  // Default fallbacks
  'link': 'material-symbols:link',
  'users': 'material-symbols:group',
  'general': 'material-symbols:bolt',
  'integration': 'material-symbols:extension',
  'automation': 'material-symbols:auto-awesome',
  'workflow': 'material-symbols:settings',
};

interface IntegrationIconProps {
  type: string;
  className?: string;
  size?: number;
  color?: string;
  variant?: 'default' | 'outline' | 'ghost';
}

export function IntegrationIcon({ 
  type, 
  className, 
  size = 20, 
  color,
  variant = 'default'
}: IntegrationIconProps) {
  // First try exact match, then normalized versions
  const exactType = type.toLowerCase();
  const normalizedType = type.toLowerCase().replace(/[\s_-]/g, '-');
  const underscoreType = type.toLowerCase().replace(/[\s-]/g, '_');
  
  const iconName = iconMap[exactType] || 
                   iconMap[normalizedType] || 
                   iconMap[underscoreType] || 
                   iconMap['general'] || 
                   'material-symbols:bolt';
  
  const baseStyles = 'flex-shrink-0';
  
  const variantStyles = {
    default: '',
    outline: 'opacity-70 hover:opacity-100',
    ghost: 'opacity-50 hover:opacity-80'
  };

  return (
    <Icon 
      icon={iconName}
      width={size}
      height={size}
      color={color}
      className={cn(
        baseStyles,
        variantStyles[variant],
        className
      )}
    />
  );
}

export type IntegrationType = keyof typeof iconMap;

// Helper function to check if an integration type is supported
export function isIntegrationTypeSupported(type: string): type is IntegrationType {
  const normalizedType = type.toLowerCase().replace(/[\s_-]/g, '-');
  return normalizedType in iconMap;
}

// Helper function to get icons by category
export function getIconsByCategory(category: 'ai' | 'google' | 'microsoft' | 'communication' | 'storage' | 'project' | 'development' | 'design' | 'social' | 'ecommerce') {
  const categories = {
    ai: ['openai', 'anthropic', 'claude', 'midjourney', 'stability', 'chatgpt', 'gpt', 'ai-assistant', 'machine-learning'],
    google: ['google-drive', 'gmail', 'google-calendar', 'google', 'google-workspace'],
    microsoft: ['microsoft', 'teams', 'microsoft-teams', 'outlook', 'onedrive', 'office365', 'sharepoint'],
    communication: ['slack', 'zoom', 'discord', 'whatsapp', 'telegram', 'signal'],
    storage: ['dropbox', 'google-drive', 'onedrive', 'file', 'cloud', 'database'],
    project: ['notion', 'trello', 'asana', 'jira', 'confluence', 'project'],
    development: ['github', 'gitlab', 'bitbucket', 'code'],
    design: ['figma', 'sketch', 'adobe-xd', 'invision', 'canva', 'design'],
    social: ['linkedin', 'twitter', 'x', 'facebook', 'instagram', 'tiktok', 'youtube', 'twitch', 'social'],
    ecommerce: ['shopify', 'wordpress', 'wix', 'squarespace', 'ecommerce']
  };
  
  return categories[category] || [];
}

// Get logo URL for download/external use
export function getIntegrationLogoUrl(type: string): string {
  const iconName = iconMap[type.toLowerCase()];
  if (iconName) {
    return `https://api.iconify.design/${iconName}.svg`;
  }
  return `https://api.iconify.design/material-symbols:bolt.svg`;
} 