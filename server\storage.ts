// LEGACY COMPATIBILITY LAYER
// This file provides backward compatibility while the codebase migrates to modular storage
// New code should import from './storage/index' directly

// DEPRECATED WARNING
console.log('⚠️  storage.ts compatibility layer loaded - consider migrating to modular imports');

// Re-export the modular storage system  
export { storage } from './storage/index';

// Legacy exports for backward compatibility
export { storage as default } from './storage/index';

// Export the interface separately (TypeScript only)
export type { IStorage } from './storage/index';

// For any code still importing specific storage classes
export { StorageFacade } from './storage/storage.facade';
export { 
  UserStorage,
  IntegrationStorage,
  FileStorage,
  ChatStorage,
  RAGStorage,
  SyncStorage,
  ProjectStorage 
} from './storage/features/index';

// Development logging
console.log(`✅ Legacy storage compatibility layer loaded successfully`);
console.log(`🔄 Using modular storage facade (${process.env.DATABASE_URL ? 'database' : 'memory'} mode)`); 