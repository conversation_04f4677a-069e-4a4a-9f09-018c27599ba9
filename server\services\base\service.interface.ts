/**
 * Base service interface and abstract classes for the service layer
 */

/**
 * Base service interface that all services should implement
 */
export interface IBaseService {
  /**
   * Initialize the service
   */
  initialize?(): Promise<void>;

  /**
   * Get service health status
   */
  getHealthStatus?(): Promise<{
    healthy: boolean;
    status: string;
    details?: Record<string, any>;
  }>;

  /**
   * Get service information
   */
  getServiceInfo?(): {
    name: string;
    version: string;
    description: string;
    dependencies?: string[];
  };

  /**
   * Cleanup resources when service is destroyed
   */
  cleanup?(): Promise<void>;
}

/**
 * Base service class with common functionality
 */
export abstract class BaseService implements IBaseService {
  protected serviceName: string;
  protected version: string;
  protected description: string;
  protected initialized: boolean = false;

  constructor(serviceName: string, version: string = '1.0.0', description: string = '') {
    this.serviceName = serviceName;
    this.version = version;
    this.description = description;
  }

  /**
   * Initialize the service (override in subclasses)
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    console.log(`🚀 Initializing ${this.serviceName} service...`);
    await this.onInitialize();
    this.initialized = true;
    console.log(`✅ ${this.serviceName} service initialized`);
  }

  /**
   * Override this method in subclasses for custom initialization
   */
  protected async onInitialize(): Promise<void> {
    // Default implementation - override in subclasses
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    healthy: boolean;
    status: string;
    details?: Record<string, any>;
  }> {
    try {
      const customHealth = await this.checkHealth();
      return {
        healthy: true,
        status: 'healthy',
        details: {
          initialized: this.initialized,
          serviceName: this.serviceName,
          version: this.version,
          ...customHealth,
        },
      };
    } catch (error) {
      return {
        healthy: false,
        status: 'unhealthy',
        details: {
          initialized: this.initialized,
          serviceName: this.serviceName,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Override this method in subclasses for custom health checks
   */
  protected async checkHealth(): Promise<Record<string, any>> {
    return {};
  }

  /**
   * Get service information
   */
  getServiceInfo(): {
    name: string;
    version: string;
    description: string;
    dependencies?: string[];
  } {
    return {
      name: this.serviceName,
      version: this.version,
      description: this.description,
      dependencies: this.getDependencies(),
    };
  }

  /**
   * Override this method in subclasses to specify dependencies
   */
  protected getDependencies(): string[] {
    return [];
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    console.log(`🧹 Cleaning up ${this.serviceName} service...`);
    await this.onCleanup();
    this.initialized = false;
    console.log(`✅ ${this.serviceName} service cleaned up`);
  }

  /**
   * Override this method in subclasses for custom cleanup
   */
  protected async onCleanup(): Promise<void> {
    // Default implementation - override in subclasses
  }

  /**
   * Validate that the service is initialized
   */
  protected ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error(`${this.serviceName} service is not initialized. Call initialize() first.`);
    }
  }

  /**
   * Log service messages with consistent formatting
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${this.serviceName.toUpperCase()}]`;
    
    switch (level) {
      case 'info':
        console.log(`${prefix} ${message}`, data || '');
        break;
      case 'warn':
        console.warn(`${prefix} ⚠️  ${message}`, data || '');
        break;
      case 'error':
        console.error(`${prefix} ❌ ${message}`, data || '');
        break;
    }
  }

  /**
   * Handle errors consistently across services
   */
  protected handleError(error: any, context: string): never {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.log('error', `Error in ${context}: ${errorMessage}`, error);
    throw error;
  }

  /**
   * Validate required environment variables
   */
  protected validateEnvironment(requiredVars: string[]): void {
    const missing = requiredVars.filter(varName => !process.env[varName]);
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables for ${this.serviceName}: ${missing.join(', ')}`);
    }
  }

  /**
   * Retry logic for operations that might fail temporarily
   */
  protected async retry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000,
    context: string = 'operation'
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        this.log('warn', `${context} failed (attempt ${attempt}/${maxRetries})`, error);
        
        if (attempt < maxRetries) {
          await this.delay(delayMs * attempt); // Exponential backoff
        }
      }
    }
    
    this.handleError(lastError, `${context} after ${maxRetries} attempts`);
  }

  /**
   * Utility method for delays
   */
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate input parameters
   */
  protected validateRequired(value: any, fieldName: string): void {
    if (value === null || value === undefined || value === '') {
      throw new Error(`${fieldName} is required for ${this.serviceName}`);
    }
  }

  /**
   * Validate string parameters
   */
  protected validateString(value: string, fieldName: string, minLength: number = 1): void {
    this.validateRequired(value, fieldName);
    if (typeof value !== 'string' || value.trim().length < minLength) {
      throw new Error(`${fieldName} must be a non-empty string for ${this.serviceName}`);
    }
  }

  /**
   * Validate number parameters
   */
  protected validateNumber(value: number, fieldName: string, min?: number, max?: number): void {
    this.validateRequired(value, fieldName);
    if (typeof value !== 'number' || isNaN(value)) {
      throw new Error(`${fieldName} must be a valid number for ${this.serviceName}`);
    }
    if (min !== undefined && value < min) {
      throw new Error(`${fieldName} must be at least ${min} for ${this.serviceName}`);
    }
    if (max !== undefined && value > max) {
      throw new Error(`${fieldName} must be at most ${max} for ${this.serviceName}`);
    }
  }
}
