#!/usr/bin/env node

/**
 * Process files using your original simple embedding approach
 * Just with smart delays to avoid rate limits
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 Processing files with your original simple approach...\n');

async function processSimple() {
  try {
    // Import your simple service
    const { simpleEmbeddingService } = await import('./server/services/simple-embedding-service.js');
    const { storage } = await import('./server/storage/index.js');
    
    if (!simpleEmbeddingService.isInitialized()) {
      console.error('❌ Simple embedding service not initialized (check OpenAI API key)');
      return;
    }
    
    console.log('✅ Simple embedding service initialized');
    
    // Get all files that need processing
    console.log('\n1️⃣ Finding files that need embeddings...');
    const allFiles = await storage.getFiles();
    console.log(`Found ${allFiles.length} total files in database`);
    
    const filesToProcess = [];
    for (const file of allFiles) {
      const hasEmbeddings = await simpleEmbeddingService.hasEmbeddings(file.id);
      if (!hasEmbeddings && file.fileContent) {
        filesToProcess.push({
          fileId: file.id,
          content: file.fileContent,
          name: file.fileName,
          size: file.fileContent.length
        });
      }
    }
    
    if (filesToProcess.length === 0) {
      console.log('✅ All files already have embeddings!');
      return;
    }
    
    console.log(`\n📋 Files needing processing: ${filesToProcess.length}`);
    filesToProcess.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file.name} (${file.size} chars)`);
    });
    
    console.log('\n2️⃣ Processing files with smart delays...');
    console.log('💡 This uses your original simple approach with rate limiting');
    
    // Process files one at a time
    await simpleEmbeddingService.processMultipleFiles(filesToProcess);
    
    console.log('\n🎉 SUCCESS! All files processed with simple approach');
    console.log('\n📊 What happened:');
    console.log('   ✅ Used your original embedding logic');
    console.log('   ✅ Added smart delays between requests');
    console.log('   ✅ Processed files sequentially (not in parallel)');
    console.log('   ✅ No complex queuing or batch systems');
    
    console.log('\n🚀 Your chat system is now ready!');
    console.log('   - All files are searchable');
    console.log('   - RAG functionality is working');
    console.log('   - No rate limiting issues');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.message.includes('429') || error.message.includes('quota')) {
      console.log('\n💡 Rate limit suggestions:');
      console.log('   1. Wait 10 minutes and try again');
      console.log('   2. Process fewer files at once');
      console.log('   3. Use the batch API for bulk processing');
    }
  }
}

// Run it
processSimple()
  .then(() => {
    console.log('\n✅ Simple processing completed!');
  })
  .catch(error => {
    console.error('\n💥 Simple processing failed:', error.message);
  });
