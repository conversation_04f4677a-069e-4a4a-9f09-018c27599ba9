#!/usr/bin/env node

/**
 * Comprehensive validation script for the modularized storage system
 * Tests all files and components to ensure nothing is broken
 */

// Dynamic imports will be used to avoid module resolution issues

console.log('🔍 Starting comprehensive modularization validation...\n');

const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function addTest(name, status, message, details = null) {
  results.tests.push({ name, status, message, details });
  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else if (status === 'WARN') results.warnings++;
  else if (status === 'INFO') results.passed++; // Treat INFO as passed
  
  const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'INFO' ? 'ℹ️' : '⚠️';
  console.log(`${emoji} ${name}: ${message}`);
  if (details) console.log(`   Details: ${details}`);
}

async function validateStorageFacade() {
  console.log('\n📦 Testing Storage Facade...');
  
  try {
    const { storage } = await import('../server/storage/index.js');

    // Test basic storage operations
    const integrations = await storage.getIntegrations();
    addTest('Storage.getIntegrations()', 'PASS', `Found ${integrations.length} integrations`);

    const files = await storage.getFiles();
    addTest('Storage.getFiles()', 'PASS', `Found ${files.length} files`);

    // Test storage info
    const storageInfo = storage.getStorageInfo();
    addTest('Storage.getStorageInfo()', 'PASS', `Storage type: ${storageInfo.type}`);

    // Test individual storage modules
    const modules = Object.keys(storageInfo.modules);
    addTest('Storage Modules', 'PASS', `All ${modules.length} modules initialized: ${modules.join(', ')}`);

  } catch (error) {
    addTest('Storage Facade', 'FAIL', `Error: ${error.message}`);
  }
}

async function validateControllers() {
  console.log('\n🎮 Testing Controllers...');

  try {
    // Test integration controller
    const { integrationController } = await import('../server/controllers/integration/index.ts');
    if (typeof integrationController.getIntegrations === 'function') {
      addTest('IntegrationController', 'PASS', 'Controller loaded and methods available');
    } else {
      addTest('IntegrationController', 'FAIL', 'Controller methods not available');
    }

    // Test sync controller
    const { syncController } = await import('../server/controllers/sync.ts');
    if (typeof syncController.getSyncLogs === 'function') {
      addTest('SyncController', 'PASS', 'Controller loaded and methods available');
    } else {
      addTest('SyncController', 'FAIL', 'Controller methods not available');
    }

    // Test file upload controller
    const { fileUploadController } = await import('../server/controllers/file-upload.ts');
    if (typeof fileUploadController.uploadFiles === 'function') {
      addTest('FileUploadController', 'PASS', 'Controller loaded and methods available');
    } else {
      addTest('FileUploadController', 'FAIL', 'Controller methods not available');
    }

  } catch (error) {
    addTest('Controllers', 'FAIL', `Error loading controllers: ${error.message}`);
  }
}

async function validateServices() {
  console.log('\n🔧 Testing Services...');

  try {
    // Test RAG service
    const { ragService } = await import('../server/services/rag/index.ts');
    if (ragService && typeof ragService.searchSimilarChunks === 'function') {
      addTest('RAGService', 'PASS', 'Service loaded and methods available');
    } else {
      addTest('RAGService', 'FAIL', 'Service not properly loaded');
    }

    // Test embedding service
    const { embeddingService } = await import('../server/services/embedding-service.ts');
    if (embeddingService && typeof embeddingService.isInitialized === 'function') {
      const isInitialized = embeddingService.isInitialized();
      addTest('EmbeddingService', 'PASS', `Service loaded, initialized: ${isInitialized}`);
    } else {
      addTest('EmbeddingService', 'FAIL', 'Service not properly loaded');
    }

  } catch (error) {
    addTest('Services', 'FAIL', `Error loading services: ${error.message}`);
  }
}

async function validateDatabaseConnections() {
  console.log('\n🗄️ Testing Database Connections...');

  try {
    const { storage } = await import('../server/storage/index.js');

    // Test each storage module's database connection
    const storageInfo = storage.getStorageInfo();

    // Check overall database configuration
    const dbConfigured = process.env.DATABASE_URL || false;
    if (!dbConfigured) {
      addTest('Database Configuration', 'INFO', 'DATABASE_URL not configured - using memory storage (normal for development)');
    } else {
      // Check if individual modules are using database
      let usingDatabase = 0;
      let totalModules = 0;
      
      for (const [moduleName, moduleInfo] of Object.entries(storageInfo.modules)) {
        totalModules++;
        if (moduleInfo.type === 'database') {
          usingDatabase++;
        }
      }
      
             if (usingDatabase === totalModules) {
         addTest('Database Configuration', 'PASS', `All ${totalModules} storage modules using database`);
       } else if (usingDatabase > 0) {
         addTest('Database Configuration', 'PASS', `${usingDatabase}/${totalModules} modules using database (lazy loading - normal behavior)`);
       } else {
         addTest('Database Configuration', 'WARN', 'All modules using memory storage despite DATABASE_URL being set');
       }
    }

    // Test actual database operations
    const testIntegration = await storage.createIntegration({
      type: 'test-validation',
      name: 'Validation Test Integration',
      status: 'disconnected',
      credentials: null,
      config: {},
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: false,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    addTest('Database Write', 'PASS', `Created test integration with ID: ${testIntegration.id}`);

    // Clean up test data
    await storage.deleteIntegration(testIntegration.id);
    addTest('Database Delete', 'PASS', 'Cleaned up test integration');

  } catch (error) {
    addTest('Database Operations', 'FAIL', `Error: ${error.message}`);
  }
}

async function validateBackwardCompatibility() {
  console.log('\n🔄 Testing Backward Compatibility...');

  try {
    const { storage } = await import('../server/storage/index.js');

    // Test that all legacy storage methods still work
    const legacyMethods = [
      'getIntegrations',
      'getFiles',
      'getUsers',
      'getChatSessions',
      'getFileChunks',
      'getSyncLogs',
      'getProjects'
    ];

    for (const method of legacyMethods) {
      if (typeof storage[method] === 'function') {
        addTest(`Legacy ${method}`, 'PASS', 'Method available');
      } else {
        addTest(`Legacy ${method}`, 'FAIL', 'Method not available');
      }
    }

  } catch (error) {
    addTest('Backward Compatibility', 'FAIL', `Error: ${error.message}`);
  }
}

async function validateFileImports() {
  console.log('\n📁 Testing File Imports...');
  
  const criticalFiles = [
    '../server/storage/index.js',
    '../server/controllers/integration/index.js',
    '../server/controllers/sync.js',
    '../server/controllers/file-upload.js',
    '../server/services/rag/index.js',
    '../server/routes/diagnostic.js',
    '../server/seed-data.js'
  ];
  
  for (const file of criticalFiles) {
    try {
      await import(file);
      addTest(`Import ${file}`, 'PASS', 'File imports successfully');
    } catch (error) {
      addTest(`Import ${file}`, 'FAIL', `Import error: ${error.message}`);
    }
  }
}

async function runValidation() {
  console.log('🚀 MeetSync Web App - Modularization Validation');
  console.log('=' .repeat(60));
  
  await validateStorageFacade();
  await validateControllers();
  await validateServices();
  await validateDatabaseConnections();
  await validateBackwardCompatibility();
  await validateFileImports();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 VALIDATION SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️  Warnings: ${results.warnings}`);
  console.log(`📋 Total Tests: ${results.tests.length}`);
  
  const successRate = Math.round((results.passed / results.tests.length) * 100);
  console.log(`📈 Success Rate: ${successRate}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Modularization is working perfectly!');
    process.exit(0);
  } else {
    console.log('\n💥 Some tests failed. Please review the issues above.');
    process.exit(1);
  }
}

// Run the validation
runValidation().catch(error => {
  console.error('💥 Fatal validation error:', error);
  process.exit(1);
});
