import type { ContentExtractionResult, PlatformMetadata } from './file-processor.interface';

// Re-export PlatformMetadata for external use
export type { PlatformMetadata };

/**
 * Interface for content extraction services
 * Handles extracting text content from various file types
 */
export interface IContentExtractor {
  /**
   * Extract content from a file
   * @param fileData Platform-specific file data
   * @param fileName Name of the file
   * @param platformMetadata Platform context information
   * @param enableAI Whether to use AI for enhanced extraction
   * @returns Content extraction result
   */
  extractContent(
    fileData: any,
    fileName: string,
    platformMetadata: PlatformMetadata,
    enableAI?: boolean
  ): Promise<ContentExtractionResult>;

  /**
   * Check if this extractor can handle the given file type
   * @param fileName File name with extension
   * @param mimeType MIME type of the file
   * @returns True if extractor can handle this file type
   */
  canExtract(fileName: string, mimeType?: string): boolean;

  /**
   * Get supported file types
   */
  getSupportedTypes(): string[];
}

/**
 * Interface for embedding processors
 * Handles generating embeddings for RAG functionality
 */
export interface IEmbeddingProcessor {
  /**
   * Process embeddings for a file
   * @param savedFile File record from database
   * @param content Content to vectorize
   * @param platformMetadata Platform context
   * @returns Success status
   */
  processEmbeddings(
    savedFile: any,
    content: string,
    platformMetadata: PlatformMetadata
  ): Promise<boolean>;

  /**
   * Check if embeddings already exist for a file
   * @param fileId File ID
   * @returns True if embeddings exist
   */
  hasEmbeddings(fileId: number): Promise<boolean>;

  /**
   * Check if embedding service is available
   */
  isAvailable(): boolean;
}

/**
 * Base content extractor with common functionality
 */
export abstract class BaseContentExtractor implements IContentExtractor {
  abstract extractContent(
    fileData: any,
    fileName: string,
    platformMetadata: PlatformMetadata,
    enableAI?: boolean
  ): Promise<ContentExtractionResult>;

  abstract canExtract(fileName: string, mimeType?: string): boolean;
  abstract getSupportedTypes(): string[];

  /**
   * Create a successful extraction result
   */
  protected createSuccessResult(
    content: string,
    vectorizationContent?: string,
    metadata?: Record<string, any>,
    extractionMethod: string = 'specialized'
  ): ContentExtractionResult {
    return {
      success: true,
      content,
      vectorizationContent: vectorizationContent || content,
      metadata,
      extractionMethod: extractionMethod as any
    };
  }

  /**
   * Create a failed extraction result
   */
  protected createErrorResult(error: string): ContentExtractionResult {
    return {
      success: false,
      error,
      extractionMethod: 'failed'
    };
  }

  /**
   * Create metadata-only result for files that can't be extracted
   */
  protected createMetadataOnlyResult(
    fileName: string,
    platformMetadata: PlatformMetadata,
    additionalInfo?: Record<string, any>
  ): ContentExtractionResult {
    const metadataContent = this.buildMetadataContent(fileName, platformMetadata, additionalInfo);
    
    return {
      success: true,
      content: metadataContent,
      vectorizationContent: metadataContent,
      metadata: { ...platformMetadata, ...additionalInfo },
      extractionMethod: 'metadata-only'
    };
  }

  /**
   * Build searchable content from metadata
   */
  protected buildMetadataContent(
    fileName: string,
    platformMetadata: PlatformMetadata,
    additionalInfo?: Record<string, any>
  ): string {
    const parts = [
      `File: ${fileName}`,
      `Platform: ${platformMetadata.platform}`,
      `Source: ${platformMetadata.sourceContext}`
    ];

    if (platformMetadata.folderPath) {
      parts.push(`Location: ${platformMetadata.folderPath}`);
    }

    if (platformMetadata.owner) {
      parts.push(`Owner: ${platformMetadata.owner}`);
    }

    if (additionalInfo) {
      Object.entries(additionalInfo).forEach(([key, value]) => {
        if (value && typeof value === 'string') {
          parts.push(`${key}: ${value}`);
        }
      });
    }

    parts.push(`Modified: ${platformMetadata.lastModified.toLocaleDateString()}`);

    return parts.join('\n');
  }

  /**
   * Log extraction information
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const prefix = `[CONTENT-EXTRACTOR]`;
    
    switch (level) {
      case 'info':
        console.log(`${prefix} ${message}`, data || '');
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, data || '');
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data || '');
        break;
    }
  }
}
