import React from 'react';
import { cn } from '../../lib/utils';

interface SourceSelectorProps {
  availableSources: string[];
  enabledSources: string[];
  onToggleSource: (source: string) => void;
}

const sourceConfig = {
  'uploaded-files': {
    label: 'Uploaded Files',
    description: 'Files you have uploaded to the chat',
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    ),
    color: 'blue'
  },
  google_drive: {
    label: 'Google Drive',
    description: 'Files from your Google Drive',
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    ),
    color: 'green'
  },
  microsoft_teams: {
    label: 'Microsoft Teams',
    description: 'Teams chats and meeting transcripts',
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    ),
    color: 'purple'
  },
  emails: {
    label: 'Emails',
    description: 'Email messages from multiple platforms',
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
    color: 'red'
  }
};

const colorClasses = {
  blue: {
    enabled: 'bg-blue-100 text-blue-800 border-blue-200',
    disabled: 'bg-gray-100 text-gray-600 border-gray-200',
    hover: 'hover:bg-blue-50'
  },
  green: {
    enabled: 'bg-green-100 text-green-800 border-green-200',
    disabled: 'bg-gray-100 text-gray-600 border-gray-200',
    hover: 'hover:bg-green-50'
  },
  purple: {
    enabled: 'bg-purple-100 text-purple-800 border-purple-200',
    disabled: 'bg-gray-100 text-gray-600 border-gray-200',
    hover: 'hover:bg-purple-50'
  },
  red: {
    enabled: 'bg-red-100 text-red-800 border-red-200',
    disabled: 'bg-gray-100 text-gray-600 border-gray-200',
    hover: 'hover:bg-red-50'
  }
};

export function SourceSelector({ availableSources, enabledSources, onToggleSource }: SourceSelectorProps) {
  return (
    <div className="p-3 space-y-3">
      {/* Source toggles */}
      <div className="space-y-2">
        {availableSources.map((source) => {
          const config = sourceConfig[source as keyof typeof sourceConfig];
          const isEnabled = enabledSources.includes(source);
          
          if (!config) {
            // Fallback for unknown sources
            return (
              <button
                key={source}
                onClick={() => onToggleSource(source)}
                className={cn(
                  "w-full flex items-center justify-between px-3 py-2 rounded-lg border text-sm transition-colors",
                  isEnabled 
                    ? "bg-blue-50 text-blue-700 border-blue-200" 
                    : "bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100"
                )}
              >
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-gray-400 rounded"></div>
                  <span>{source.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                </div>
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  isEnabled ? "bg-blue-500" : "bg-gray-300"
                )} />
              </button>
            );
          }

          const colors = colorClasses[config.color as keyof typeof colorClasses];
          
          return (
            <button
              key={source}
              onClick={() => onToggleSource(source)}
              className={cn(
                "w-full flex items-center justify-between px-3 py-2 rounded-lg border text-sm transition-colors",
                isEnabled ? colors.enabled : colors.disabled,
                !isEnabled && colors.hover
              )}
              title={config.description}
            >
              <div className="flex items-center space-x-2">
                <div className={cn(
                  "flex items-center justify-center",
                  isEnabled ? "text-current" : "text-gray-400"
                )}>
                  {config.icon}
                </div>
                <div className="text-left">
                  <div className="font-medium">{config.label}</div>
                  <div className="text-xs opacity-75">{config.description}</div>
                </div>
              </div>
              
              {/* Toggle indicator */}
              <div className={cn(
                "w-2 h-2 rounded-full flex-shrink-0",
                isEnabled ? "bg-current" : "bg-gray-300"
              )} />
            </button>
          );
        })}
      </div>

      {/* Help text */}
      <p className="text-xs text-gray-500 leading-relaxed">
        Select data sources that the AI can search when answering questions.
      </p>
    </div>
  );
} 