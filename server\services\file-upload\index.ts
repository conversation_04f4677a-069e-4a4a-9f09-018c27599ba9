// File Upload Services - Core functionality only
export * from './config.service';
export * from './processing.service';
export * from './management.service';

// Create a simple facade that only uses core services
import { FileUploadConfigService } from './config.service';
import { FileUploadProcessingService } from './processing.service';
import { FileUploadManagementService } from './management.service';

class SimpleFileUploadService {
  private configService: FileUploadConfigService;
  private processingService: FileUploadProcessingService;
  private managementService: FileUploadManagementService;

  constructor() {
    this.configService = new FileUploadConfigService();
    this.processingService = new FileUploadProcessingService();
    this.managementService = new FileUploadManagementService();
  }

  async initialize(): Promise<void> {
    await this.configService.initialize();
    await this.processingService.initialize();
    await this.managementService.initialize();
  }

  async cleanup(): Promise<void> {
    // Cleanup all sub-services if they have cleanup methods
    await Promise.all([
      this.configService.cleanup?.(),
      this.processingService.cleanup?.(),
      this.managementService.cleanup?.(),
    ].filter(Boolean));
  }

  getUploadMiddleware() {
    return this.configService.getUploadMiddleware();
  }

  async processUploadedFile(file: Express.Multer.File, userId?: string) {
    return this.processingService.processUploadedFile(file, userId);
  }

  async processMultipleFiles(files: Express.Multer.File[], userId?: string) {
    return this.processingService.processMultipleFiles(files, userId);
  }

  getSupportedFileTypes(): string[] {
    return this.configService.getSupportedFileTypes();
  }

  validateFile(file: Express.Multer.File) {
    return this.configService.validateFile(file);
  }

  async deleteUploadedFile(fileId: number) {
    return this.managementService.deleteUploadedFile(fileId);
  }

  async getUploadedFileInfo(fileId: number) {
    return this.processingService.getUploadedFileInfo(fileId);
  }
}

export const fileUploadService = new SimpleFileUploadService();
export { SimpleFileUploadService }; 