#!/usr/bin/env node

/**
 * Development Startup Script for MeetSync Microservices
 * Orchestrates the startup of all services in the correct order
 */

import { spawn } from 'child_process';
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const path = require('path');

const COLORS = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset', prefix = '') {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${COLORS[color]}[${timestamp}]${prefix ? ` [${prefix}]` : ''} ${message}${COLORS.reset}`);
}

class ServiceManager {
  constructor() {
    this.services = new Map();
    this.isShuttingDown = false;
  }

  async startService(name, command, cwd, color = 'reset') {
    return new Promise((resolve, reject) => {
      log(`Starting ${name}...`, color, name);
      
      const [cmd, ...args] = command.split(' ');
      const child = spawn(cmd, args, {
        cwd,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      this.services.set(name, { process: child, color });

      let hasStarted = false;
      let startupTimeout;

      // Set startup timeout
      startupTimeout = setTimeout(() => {
        if (!hasStarted) {
          log(`${name} startup timeout`, 'red', name);
          reject(new Error(`${name} failed to start within timeout`));
        }
      }, 30000); // 30 second timeout

      child.stdout.on('data', (data) => {
        const output = data.toString();
        
        // Check for startup success indicators
        if (
          output.includes('started successfully') ||
          output.includes('running on port') ||
          output.includes('Health check:') ||
          (name === 'RAG Service' && output.includes('RAG Service running'))
        ) {
          if (!hasStarted) {
            hasStarted = true;
            clearTimeout(startupTimeout);
            log(`${name} started successfully`, 'green', name);
            resolve();
          }
        }

        // Log service output with color coding
        output.split('\n').forEach(line => {
          if (line.trim()) {
            log(line.trim(), color, name);
          }
        });
      });

      child.stderr.on('data', (data) => {
        const output = data.toString();
        output.split('\n').forEach(line => {
          if (line.trim()) {
            log(`ERROR: ${line.trim()}`, 'red', name);
          }
        });
      });

      child.on('error', (error) => {
        log(`${name} process error: ${error.message}`, 'red', name);
        if (!hasStarted) {
          clearTimeout(startupTimeout);
          reject(error);
        }
      });

      child.on('exit', (code, signal) => {
        if (!this.isShuttingDown) {
          log(`${name} exited with code ${code} (signal: ${signal})`, 'red', name);
        }
        this.services.delete(name);
      });
    });
  }

  async startAll() {
    try {
      log('🚀 Starting MeetSync Development Environment', 'bold');
      log('=' .repeat(60), 'blue');

      // Start microservices first
      log('\n📦 Starting Microservices...', 'blue');
      
      // Start services in parallel
      await Promise.all([
        this.startService(
          'Chat Service',
          'npm run dev',
          path.join(process.cwd(), 'microservices', 'chat-service'),
          'cyan'
        ),
        this.startService(
          'RAG Service',
          'npm run dev',
          path.join(process.cwd(), 'microservices', 'rag-service'),
          'magenta'
        )
      ]);

      log('\n✅ All microservices started successfully!', 'green');
      log('\n🌐 Starting Main Application...', 'blue');

      // Start main application
      await this.startService(
        'Main App',
        'concurrently "npm run dev:server" "npm run dev:client"',
        process.cwd(),
        'yellow'
      );

      log('\n🎉 All services are running!', 'green');
      log('=' .repeat(60), 'green');
      log('📊 Service Status:', 'green');
      log('  • Chat Service: http://localhost:3001/health', 'green');
      log('  • RAG Service: http://localhost:3002/health', 'green');
      log('  • Main App: http://localhost:3000', 'green');
      log('  • Frontend: http://localhost:5173', 'green');
      log('=' .repeat(60), 'green');

    } catch (error) {
      log(`❌ Failed to start services: ${error.message}`, 'red');
      await this.shutdown();
      process.exit(1);
    }
  }

  async shutdown() {
    if (this.isShuttingDown) return;
    
    this.isShuttingDown = true;
    log('\n🛑 Shutting down all services...', 'yellow');

    const shutdownPromises = Array.from(this.services.entries()).map(([name, service]) => {
      return new Promise((resolve) => {
        log(`Stopping ${name}...`, 'yellow', name);
        
        service.process.kill('SIGTERM');
        
        setTimeout(() => {
          if (!service.process.killed) {
            log(`Force killing ${name}...`, 'red', name);
            service.process.kill('SIGKILL');
          }
          resolve();
        }, 5000);
      });
    });

    await Promise.all(shutdownPromises);
    log('✅ All services stopped', 'green');
  }
}

// Handle graceful shutdown
const serviceManager = new ServiceManager();

process.on('SIGINT', async () => {
  console.log('\n'); // New line after ^C
  await serviceManager.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await serviceManager.shutdown();
  process.exit(0);
});

// Start all services
serviceManager.startAll().catch(async (error) => {
  log(`❌ Startup failed: ${error.message}`, 'red');
  await serviceManager.shutdown();
  process.exit(1);
});
