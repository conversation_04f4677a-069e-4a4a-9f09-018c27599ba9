import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { IntegrationIcon, getIconsByCategory, type IntegrationType } from './integration-icons';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from './tabs';
import { Button } from './button';
import { Input } from './input';
import { Search, Copy, CheckCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface IconShowcaseProps {
  className?: string;
}

// Latest 2025 icon categories with enhanced descriptions
const iconCategories = {
  ai: {
    title: 'AI & Machine Learning',
    description: 'Latest AI services and ML platforms trending in 2025',
    color: 'from-purple-500 to-indigo-600',
    icons: ['openai', 'anthropic', 'claude', 'midjourney', 'stability', 'chatgpt', 'gpt', 'ai-assistant', 'machine-learning']
  },
  google: {
    title: 'Google Workspace',
    description: 'Complete Google ecosystem integration',
    color: 'from-blue-500 to-blue-600',
    icons: ['google-drive', 'gmail', 'google-calendar', 'google', 'google-workspace']
  },
  microsoft: {
    title: 'Microsoft 365',
    description: 'Modern Microsoft productivity suite',
    color: 'from-blue-600 to-cyan-500',
    icons: ['microsoft', 'teams', 'microsoft-teams', 'outlook', 'onedrive', 'office365', 'sharepoint']
  },
  communication: {
    title: 'Communication',
    description: 'Modern messaging and video platforms',
    color: 'from-green-500 to-emerald-600',
    icons: ['slack', 'zoom', 'discord', 'whatsapp', 'telegram', 'signal']
  },
  storage: {
    title: 'Storage & Data',
    description: 'Cloud storage and data management',
    color: 'from-gray-500 to-slate-600',
    icons: ['dropbox', 'file', 'cloud', 'database']
  },
  project: {
    title: 'Project Management',
    description: '2025 trending productivity platforms',
    color: 'from-orange-500 to-red-500',
    icons: ['notion', 'trello', 'asana', 'jira', 'confluence', 'project']
  },
  development: {
    title: 'Development',
    description: 'Code hosting and development tools',
    color: 'from-gray-700 to-gray-900',
    icons: ['github', 'gitlab', 'bitbucket', 'code']
  },
  design: {
    title: 'Design Tools',
    description: 'Modern design and prototyping platforms',
    color: 'from-pink-500 to-rose-500',
    icons: ['figma', 'sketch', 'adobe-xd', 'adobexd', 'invision', 'canva', 'design']
  },
  social: {
    title: 'Social Media',
    description: '2025 social platforms and networks',
    color: 'from-purple-600 to-pink-600',
    icons: ['linkedin', 'twitter', 'facebook', 'instagram', 'tiktok', 'youtube', 'twitch', 'social']
  },
  ecommerce: {
    title: 'E-commerce',
    description: 'Online store and website builders',
    color: 'from-emerald-500 to-teal-600',
    icons: ['shopify', 'wordpress', 'wix', 'squarespace', 'ecommerce']
  }
} as const;

type CategoryKey = keyof typeof iconCategories;

export function IconShowcase({ className }: IconShowcaseProps) {
  const [selectedCategory, setSelectedCategory] = useState<CategoryKey>('ai');
  const [searchTerm, setSearchTerm] = useState('');
  const [copiedIcon, setCopiedIcon] = useState<string | null>(null);
  const [iconVariant, setIconVariant] = useState<'default' | 'outline' | 'ghost'>('default');

  const handleCopyIcon = async (iconType: string) => {
    const iconCode = `<IntegrationIcon type="${iconType}" />`;
    await navigator.clipboard.writeText(iconCode);
    setCopiedIcon(iconType);
    setTimeout(() => setCopiedIcon(null), 2000);
  };

  const filteredIcons = iconCategories[selectedCategory].icons.filter(icon =>
    icon.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 rounded-full">
          <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white">
            2025 Latest
          </Badge>
          <span className="text-sm font-medium">Icon Libraries</span>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
          Modern Icon Collection
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Explore our comprehensive collection of the latest icons from trending platforms and services in 2025.
          Updated with AI-first integrations and modern design systems.
        </p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search icons..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={iconVariant === 'default' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setIconVariant('default')}
          >
            Default
          </Button>
          <Button
            variant={iconVariant === 'outline' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setIconVariant('outline')}
          >
            Outline
          </Button>
          <Button
            variant={iconVariant === 'ghost' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setIconVariant('ghost')}
          >
            Ghost
          </Button>
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as CategoryKey)}>
        <TabsList className="grid grid-cols-5 lg:grid-cols-10 gap-1 h-auto p-1">
          {Object.entries(iconCategories).map(([key, category]) => (
            <TabsTrigger
              key={key}
              value={key}
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:text-white text-xs px-2 py-2"
              style={{
                background: selectedCategory === key 
                  ? `linear-gradient(135deg, ${category.color.split(' ')[1]}, ${category.color.split(' ')[3]})` 
                  : undefined
              }}
            >
              {category.title.split(' ')[0]}
            </TabsTrigger>
          ))}
        </TabsList>

        {Object.entries(iconCategories).map(([key, category]) => (
          <TabsContent key={key} value={key}>
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${category.color}`} />
                  <div>
                    <CardTitle className="text-xl">{category.title}</CardTitle>
                    <CardDescription>{category.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-4">
                  <AnimatePresence>
                    {filteredIcons.map((iconType, index) => (
                      <motion.div
                        key={iconType}
                        initial={{ opacity: 0, scale: 0.8, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.8, y: -20 }}
                        transition={{ delay: index * 0.05, duration: 0.2 }}
                        className="group relative"
                      >
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-16 w-full flex flex-col items-center gap-2 p-2 hover:bg-gray-50 border border-transparent hover:border-gray-200 transition-all duration-200"
                          onClick={() => handleCopyIcon(iconType)}
                        >
                          <div className="relative">
                            <IntegrationIcon 
                              type={iconType} 
                              size={24} 
                              variant={iconVariant}
                              className="group-hover:scale-110 transition-transform duration-200"
                            />
                            {copiedIcon === iconType && (
                              <div className="absolute -top-1 -right-1">
                                <CheckCircle className="h-3 w-3 text-green-500 bg-white rounded-full" />
                              </div>
                            )}
                          </div>
                          <span className="text-xs text-gray-600 truncate w-full text-center">
                            {iconType.replace(/-/g, ' ')}
                          </span>
                        </Button>
                        
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                          Click to copy code
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
                
                {filteredIcons.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No icons found matching "{searchTerm}"
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Usage Examples</CardTitle>
          <CardDescription>
            How to use these modern icons in your 2025 React applications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Basic Usage</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded-md overflow-x-auto">
{`<IntegrationIcon type="openai" />
<IntegrationIcon type="slack" size={32} />
<IntegrationIcon type="notion" variant="ghost" />`}
              </pre>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">With Custom Styling</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded-md overflow-x-auto">
{`<IntegrationIcon 
  type="figma" 
  size={28}
  className="hover:rotate-12"
  variant="outline"
/>`}
              </pre>
            </div>
          </div>
          
          <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
            <h4 className="font-medium mb-2 text-blue-900">2025 Features</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• AI-first integrations with OpenAI, Anthropic, Midjourney</li>
              <li>• Enhanced brand colors following 2025 guidelines</li>
              <li>• Multiple variants: default, outline, ghost</li>
              <li>• TypeScript support with auto-completion</li>
              <li>• Category-based icon organization</li>
              <li>• Hover animations and micro-interactions</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 