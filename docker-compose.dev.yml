# =============================================================================
# MeetSync Development Docker Compose Override
# =============================================================================

services:
  # =============================================================================
  # Development Application Service
  # =============================================================================
  meetsync-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: meetsync-app-dev
    environment:
      # Development Settings
      NODE_ENV: development
      PORT: 8080
      HOST: 0.0.0.0
      SERVER_URL: http://localhost:8080
      
      # Development Database Settings
      DATABASE_URL: postgresql://${POSTGRES_USER:-meetsync}:${POSTGRES_PASSWORD:-meetsync123}@postgres:5432/${POSTGRES_DB:-meetsync_dev}
      
      # Development Security Settings (use weaker settings for dev)
      SESSION_SECRET: ${SESSION_SECRET:-dev-session-secret-change-in-production}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-dev-encryption-key-change-in-production}
      
      # External API Settings (same as production)
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      MICROSOFT_CLIENT_ID: ${MICROSOFT_CLIENT_ID}
      MICROSOFT_CLIENT_SECRET: ${MICROSOFT_CLIENT_SECRET}
      MICROSOFT_TENANT_ID: ${MICROSOFT_TENANT_ID}
      MICROSOFT_REDIRECT_URI: ${MICROSOFT_REDIRECT_URI:-http://localhost:8080/api/microsoft/callback}
      NOTION_API_KEY: ${NOTION_API_KEY}
      NOTION_DATABASE_ID: ${NOTION_DATABASE_ID}
      NOTION_INTEGRATION_SECRET: ${NOTION_INTEGRATION_SECRET}
      NOTION_PAGE_URL: ${NOTION_PAGE_URL}
      
      # Development RAG Settings
      CHUNK_SIZE: ${CHUNK_SIZE:-512}
      CHUNK_OVERLAP: ${CHUNK_OVERLAP:-20}
      TOP_K: ${TOP_K:-50}
      SYSTEM_PROMPT: ${SYSTEM_PROMPT:-You are GPT Unify - GPT AI Assistant, an intelligent AI assistant with access to data sources from multiple platforms including Google Drive, meeting transcripts, uploaded files, and other integrated platforms.}
      ENABLE_FUNCTION_TOOLS: ${ENABLE_FUNCTION_TOOLS:-true}
      ENABLE_FILE_CREATION: ${ENABLE_FILE_CREATION:-true}
      ENABLE_GOOGLE_DRIVE_ACTIONS: ${ENABLE_GOOGLE_DRIVE_ACTIONS:-true}
      
      # Development Debugging
      DEBUG: ${DEBUG:-*}
      LOG_LEVEL: ${LOG_LEVEL:-debug}
    volumes:
      # Source code volumes for hot reload
      - ./client:/app/client:delegated
      - ./server:/app/server:delegated
      - ./shared:/app/shared:delegated
      - ./package.json:/app/package.json:ro
      - ./package-lock.json:/app/package-lock.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - ./vite.config.ts:/app/vite.config.ts:ro
      - ./tailwind.config.ts:/app/tailwind.config.ts:ro
      - ./postcss.config.js:/app/postcss.config.js:ro
      - ./components.json:/app/components.json:ro
      - ./drizzle.config.ts:/app/drizzle.config.ts:ro
      
      # Persistent volumes
      - uploads_dev:/app/uploads
      - logs_dev:/app/logs
      
      # Node modules cache (performance optimization)
      - node_modules_cache:/app/node_modules
    ports:
      - "${PORT:-8080}:8080"
      - "${VITE_PORT:-5173}:5173"  # Vite dev server
      - "9229:9229"  # Node.js debugging port
    command: ["npm", "run", "dev"]
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - meetsync-network
    # Remove healthcheck in development for faster startup
    healthcheck:
      disable: true

  # =============================================================================
  # Development PostgreSQL Database
  # =============================================================================
  postgres:
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-meetsync_dev}
      POSTGRES_USER: ${POSTGRES_USER:-meetsync}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-meetsync123}
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./docker/postgres/init-dev.sql:/docker-entrypoint-initdb.d/01-init-dev.sql:ro
      - ./migrations:/migrations:ro  # Mount migrations for easy access
    ports:
      - "${POSTGRES_PORT:-5433}:5432"  # Different port to avoid conflicts

  # =============================================================================
  # Development Redis
  # =============================================================================
  redis:
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123dev}
    volumes:
      - redis_data_dev:/data
    ports:
      - "${REDIS_PORT:-6380}:6379"  # Different port to avoid conflicts

  # =============================================================================
  # Adminer for Database Management (Development Only)
  # =============================================================================
  adminer:
    image: adminer:latest
    container_name: meetsync-adminer
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT:-8081}:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: nette
    networks:
      - meetsync-network
    depends_on:
      - postgres

  # =============================================================================
  # Redis Commander for Redis Management (Development Only)
  # =============================================================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: meetsync-redis-commander
    restart: unless-stopped
    ports:
      - "${REDIS_COMMANDER_PORT:-8082}:8081"
    environment:
      REDIS_HOSTS: "local:redis:6379:0:${REDIS_PASSWORD:-redis123dev}"
      HTTP_USER: ${REDIS_COMMANDER_USER:-admin}
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD:-admin123}
    networks:
      - meetsync-network
    depends_on:
      - redis

# =============================================================================
# Development Volumes
# =============================================================================
volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  uploads_dev:
    driver: local
  logs_dev:
    driver: local
  node_modules_cache:
    driver: local 