const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testBackend() {
  console.log('🧪 Testing Backend APIs...\n');

  try {
    // Test health check
    console.log('1. Testing health check...');
    const health = await makeRequest('/api/diagnostic/health');
    console.log(`   Status: ${health.status}`);
    console.log(`   Response:`, health.data);
    console.log('');

    // Test getting integrations
    console.log('2. Testing get integrations...');
    const integrations = await makeRequest('/api/integrations');
    console.log(`   Status: ${integrations.status}`);
    console.log(`   Response:`, integrations.data);
    console.log('');

    // Test creating an integration
    console.log('3. Testing integration creation...');
    const newIntegration = await makeRequest('/api/integrations', 'POST', {
      type: 'google-drive',
      name: 'Test Integration from Script',
      config: {},
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null
    });
    console.log(`   Status: ${newIntegration.status}`);
    console.log(`   Response:`, newIntegration.data);
    console.log('');

    // Test getting integrations again
    console.log('4. Testing get integrations after creation...');
    const integrationsAfter = await makeRequest('/api/integrations');
    console.log(`   Status: ${integrationsAfter.status}`);
    console.log(`   Response:`, integrationsAfter.data);
    console.log('');

    console.log('✅ Backend test completed successfully!');

  } catch (error) {
    console.error('❌ Backend test failed:', error);
  }
}

// Run the test
testBackend(); 