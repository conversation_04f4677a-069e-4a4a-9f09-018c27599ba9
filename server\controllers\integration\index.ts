import { Request, Response } from 'express';
import { integrationCrudController } from './shared/integration-crud.controller.js';
import { googleOAuthController } from './google/google-oauth.controller.js';
import { googleIntegrationController } from './google/google-integration.controller.js';
import { teamsOAuthController } from './microsoft/teams-oauth.controller.js';
import { teamsIntegrationController } from './microsoft/teams-integration.controller.js';

/**
 * Main Integration Controller Facade
 * Combines all modular controllers while maintaining the same API as the original monolithic controller
 * 
 * This facade ensures zero breaking changes to the existing routes and API contracts
 */
class IntegrationController {
  constructor() {
    // Bind all methods to preserve 'this' context for the facade pattern
    this.getIntegrations = this.getIntegrations.bind(this);
    this.getIntegration = this.getIntegration.bind(this);
    this.createIntegration = this.createIntegration.bind(this);
    this.updateIntegration = this.updateIntegration.bind(this);
    this.deleteIntegration = this.deleteIntegration.bind(this);
    this.getAuthUrl = this.getAuthUrl.bind(this);
    this.handleOAuthCallback = this.handleOAuthCallback.bind(this);
    this.getGoogleDriveFolders = this.getGoogleDriveFolders.bind(this);
    this.getDriveStructure = this.getDriveStructure.bind(this);
    this.debugDriveFolders = this.debugDriveFolders.bind(this);
    this.getTeamsAuthUrl = this.getTeamsAuthUrl.bind(this);
    this.handleTeamsOAuthCallback = this.handleTeamsOAuthCallback.bind(this);
    this.getTeamsSources = this.getTeamsSources.bind(this);
    this.getTeamsFolders = this.getTeamsFolders.bind(this);
    this.getTeamsChannels = this.getTeamsChannels.bind(this);
    this.testTeamsConnection = this.testTeamsConnection.bind(this);
    this.testConnection = this.testConnection.bind(this);
    this.updateSchedule = this.updateSchedule.bind(this);
    this.getSchedules = this.getSchedules.bind(this);
  }

  // ===== INTEGRATION CRUD OPERATIONS =====
  // Delegate to integration-crud.controller.ts

  /**
   * Get all integrations
   */
  async getIntegrations(req: Request, res: Response) {
    return integrationCrudController.getIntegrations(req, res);
  }

  /**
   * Get a single integration by ID
   */
  async getIntegration(req: Request, res: Response) {
    return integrationCrudController.getIntegration(req, res);
  }

  /**
   * Create a new integration
   */
  async createIntegration(req: Request, res: Response) {
    return integrationCrudController.createIntegration(req, res);
  }

  /**
   * Update an integration
   */
  async updateIntegration(req: Request, res: Response) {
    return integrationCrudController.updateIntegration(req, res);
  }

  /**
   * Delete an integration
   */
  async deleteIntegration(req: Request, res: Response) {
    return integrationCrudController.deleteIntegration(req, res);
  }

  /**
   * Update the sync schedule for an integration
   */
  async updateSchedule(req: Request, res: Response) {
    return integrationCrudController.updateSchedule(req, res);
  }

  /**
   * Get all schedules
   */
  async getSchedules(req: Request, res: Response) {
    return integrationCrudController.getSchedules(req, res);
  }

  // ===== GOOGLE OAUTH OPERATIONS =====
  // Delegate to google/google-oauth.controller.ts

  /**
   * Generate an authorization URL for OAuth flow
   * Routes to appropriate OAuth controller based on integration type
   */
  async getAuthUrl(req: Request, res: Response) {
    // This method needs to determine the integration type and route accordingly
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      // Get integration to determine type
      const { storage } = await import('../../storage/index.js');
      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Route to appropriate OAuth controller based on integration type
      const isGoogleIntegration = integration.type.includes('google') ||
        ['gmail', 'google_calendar', 'google-calendar'].includes(integration.type);

      if (isGoogleIntegration) {
        return googleOAuthController.getAuthUrl(req, res);
      } else if (integration.type === 'microsoft_teams' || integration.type === 'microsoft-teams') {
        return teamsOAuthController.getTeamsAuthUrl(req, res);
      } else if (integration.type === 'notion') {
        // Handle Notion direct API key integration
        try {
          if (!process.env.NOTION_INTEGRATION_SECRET) {
            return res.status(400).json({
              message: 'Notion integration secret is not configured. Please add NOTION_INTEGRATION_SECRET to environment variables.'
            });
          }

          // Create a mock redirect URL for Notion that leads to token setup page
          const authUrl = `/integrations/${id}/setup?step=token&type=notion`;
          const oauthState = Math.random().toString(36).substring(2);

          // Store the state temporarily in the integration config
          const redirectUri = req.query.redirectUri as string;
          if (redirectUri) {
            const updatedConfig = integration.config || {};
            await storage.updateIntegration(id, {
              config: {
                ...updatedConfig,
                oauthState,
                redirectUri,
              },
            });
          }

          return res.json({ authUrl });
        } catch (error: any) {
          console.error("Error setting up Notion integration:", error);
          return res.status(500).json({
            message: 'Failed to set up Notion integration'
          });
        }
      } else {
        return res.status(400).json({
          message: `OAuth not implemented for integration type: ${integration.type}`
        });
      }
    } catch (error: any) {
      console.error(`Error generating auth URL for integration ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Failed to generate authorization URL' });
    }
  }

  /**
   * Handle OAuth callback and store tokens
   * Routes to appropriate OAuth controller based on integration type
   */
  async handleOAuthCallback(req: Request, res: Response) {
    // This method needs to determine the integration type and route accordingly
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      // Get integration to determine type
      const { storage } = await import('../../storage/index.js');
      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Route to appropriate OAuth controller based on integration type
      const isGoogleIntegration = integration.type.includes('google') ||
        ['gmail', 'google_calendar', 'google-calendar'].includes(integration.type);

      if (isGoogleIntegration) {
        return googleOAuthController.handleOAuthCallback(req, res);
      } else if (integration.type === 'microsoft_teams' || integration.type === 'microsoft-teams') {
        return teamsOAuthController.handleTeamsOAuthCallback(req, res);
      } else if (integration.type === 'notion') {
        // Handle Notion callback (placeholder for future implementation)
        return res.redirect(`/integrations/${id}/setup?step=2&success=true`);
      } else {
        return res.status(400).json({
          message: `OAuth not implemented for integration type: ${integration.type}`
        });
      }
    } catch (error: any) {
      console.error(`Error handling OAuth callback for integration ${req.params.id}:`, error);
      const integrationId = parseInt(req.params.id, 10);
      if (!isNaN(integrationId)) {
        return res.redirect(`/integrations/setup/${integrationId}?success=false&error=${encodeURIComponent(error.message)}`);
      } else {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }
    }
  }

  // ===== GOOGLE DRIVE OPERATIONS =====
  // Delegate to google/google-integration.controller.ts

  /**
   * List Google Drive folders for integration setup
   */
  async getGoogleDriveFolders(req: Request, res: Response) {
    return googleIntegrationController.getGoogleDriveFolders(req, res);
  }

  /**
   * Get complete Google Drive structure
   */
  async getDriveStructure(req: Request, res: Response) {
    return googleIntegrationController.getDriveStructure(req, res);
  }

  /**
   * Debug endpoint for Google Drive folders
   */
  async debugDriveFolders(req: Request, res: Response) {
    return googleIntegrationController.debugDriveFolders(req, res);
  }

  // ===== MICROSOFT TEAMS OAUTH OPERATIONS =====
  // Delegate to microsoft/teams-oauth.controller.ts

  /**
   * Generate Microsoft Teams OAuth authorization URL
   */
  async getTeamsAuthUrl(req: Request, res: Response) {
    return teamsOAuthController.getTeamsAuthUrl(req, res);
  }

  /**
   * Handle Microsoft Teams OAuth callback
   */
  async handleTeamsOAuthCallback(req: Request, res: Response) {
    return teamsOAuthController.handleTeamsOAuthCallback(req, res);
  }

  // ===== MICROSOFT TEAMS OPERATIONS =====
  // Delegate to microsoft/teams-integration.controller.ts

  /**
   * Get available Teams sources
   */
  async getTeamsSources(req: Request, res: Response) {
    return teamsIntegrationController.getTeamsSources(req, res);
  }

  /**
   * Get Teams channels for a specific team
   */
  async getTeamsChannels(req: Request, res: Response) {
    return teamsIntegrationController.getTeamsChannels(req, res);
  }

  /**
   * Get Teams folders for file selection
   */
  async getTeamsFolders(req: Request, res: Response) {
    return teamsIntegrationController.getTeamsFolders(req, res);
  }

  /**
   * Test Microsoft Teams connection
   */
  async testTeamsConnection(req: Request, res: Response) {
    return teamsIntegrationController.testTeamsConnection(req, res);
  }

  // ===== CONNECTION TESTING =====
  // Route to appropriate controller based on integration type

  /**
   * Test the integration connection and update integration status
   * Routes to appropriate controller based on integration type
   */
  async testConnection(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      // Get integration to determine type
      const { storage } = await import('../../storage/index.js');
      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      // Route to appropriate controller based on integration type
      if (integration.type === 'google_drive' || integration.type === 'google-drive') {
        return googleIntegrationController.testConnection(req, res);
      } else if (integration.type === 'microsoft_teams' || integration.type === 'microsoft-teams') {
        return teamsIntegrationController.testTeamsConnection(req, res);
      } else if (integration.type === 'notion') {
        // Handle Notion connection testing (placeholder for future implementation)
        return res.json({ 
          success: true, 
          message: 'Notion connection testing not yet implemented',
          details: {} 
        });
      } else {
        return res.json({
          success: false,
          message: `Testing not implemented for integration type: ${integration.type}`,
          details: {}
        });
      }
    } catch (error: any) {
      console.error(`Error testing connection for integration ${req.params.id}:`, error);
      return res.status(500).json({
        success: false,
        message: 'Failed to test connection',
        details: { error: error.message },
      });
    }
  }
}

export const integrationController = new IntegrationController(); 