import { EmailStorage } from '../server/storage/features/email.storage.js';
import { initializeDatabase, closeDatabase } from '../server/core/config/database.config.js';
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory of the current file
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from the project root
config({ path: path.join(__dirname, '..', '.env') });

const dummyEmails = [
  {
    externalId: "email1",
    platform: "gmail",
    subject: "Project Update: Q1 Progress Report",
    content: `Dear Team,

I hope this email finds you well. I wanted to provide you with an update on our Q1 progress.

Key Milestones Achieved:
1. Completed the initial phase of the project
2. Successfully integrated the new API
3. Deployed the beta version to test users

Next Steps:
- Begin phase 2 development
- Schedule user feedback sessions
- Prepare for the next sprint

Please let me know if you have any questions.

Best regards,
<PERSON>,
    sender: "<EMAIL>",
    recipients: ["<EMAIL>"],
    cc: ["<EMAIL>"],
    threadId: "thread1",
    metadata: {
      messageId: "msg1",
      inReplyTo: null,
      references: []
    },
    extractedMetadata: {
      sentiment: "positive",
      topics: ["project update", "progress report", "milestones"]
    },
    userId: "user1",
    organizationId: "org1",
    isRead: false,
    isStarred: true,
    labels: ["Important", "Project", "Q1"],
    receivedAt: new Date("2024-03-15T10:00:00Z")
  },
  {
    externalId: "email2",
    platform: "outlook",
    subject: "Meeting Notes: Product Strategy Discussion",
    content: `Hi Team,

Here are the key points from today's product strategy meeting:

1. Market Analysis
- Current market trends show increasing demand
- Competitor analysis reveals opportunities in feature X
- User feedback indicates need for better integration

2. Action Items
- Research potential partnerships
- Develop prototype for feature X
- Schedule follow-up meeting next week

Please review and let me know if you need any clarification.

Regards,
Michael Thompson`,
    sender: "<EMAIL>",
    recipients: ["<EMAIL>"],
    cc: ["<EMAIL>"],
    threadId: "thread2",
    metadata: {
      messageId: "msg2",
      inReplyTo: null,
      references: []
    },
    extractedMetadata: {
      sentiment: "neutral",
      topics: ["meeting notes", "product strategy", "action items"]
    },
    userId: "user1",
    organizationId: "org1",
    isRead: true,
    isStarred: false,
    labels: ["Meeting", "Product", "Strategy"],
    receivedAt: new Date("2024-03-16T14:30:00Z")
  },
  {
    externalId: "email3",
    platform: "gmail",
    subject: "Weekly Team Sync - March 18",
    content: `Hello Team,

I'm writing to confirm our weekly sync meeting scheduled for March 18th at 2:00 PM EST.

Agenda:
1. Project status updates
2. Blockers and challenges
3. Resource allocation
4. Next week's priorities

Please come prepared with your updates. If you can't attend, please send your status report in advance.

Best,
Alex Rodriguez`,
    sender: "<EMAIL>",
    recipients: ["<EMAIL>"],
    cc: [],
    threadId: "thread3",
    metadata: {
      messageId: "msg3",
      inReplyTo: null,
      references: []
    },
    extractedMetadata: {
      sentiment: "neutral",
      topics: ["meeting", "team sync", "agenda"]
    },
    userId: "user1",
    organizationId: "org1",
    isRead: false,
    isStarred: false,
    labels: ["Meeting", "Weekly", "Sync"],
    receivedAt: new Date("2024-03-17T09:15:00Z")
  }
];

async function seedDummyEmails() {
  console.log('🌱 Starting email seeding process...');
  
  try {
    // Check if DATABASE_URL is available
    if (!process.env.DATABASE_URL) {
      console.log('⚠️  DATABASE_URL not found, checking for individual database components...');
      
      // Check if we have individual database components
      const hasIndividualParams = process.env.PGHOST && process.env.PGUSER && process.env.PGDATABASE;
      if (!hasIndividualParams) {
        console.log('⚠️  No database configuration found, using memory storage');
      }
    }
    
    // Initialize database connection
    console.log('🔌 Initializing database connection...');
    await initializeDatabase();
    
    // Create email storage instance
    const emailStorage = new EmailStorage();
    console.log(`📊 Storage info: ${JSON.stringify(emailStorage.getStorageInfo())}`);
    
    console.log(`📧 Seeding ${dummyEmails.length} dummy emails...`);
    
    for (const [index, email] of dummyEmails.entries()) {
      console.log(`📝 Creating email ${index + 1}/${dummyEmails.length}: "${email.subject}"`);
      try {
        await emailStorage.createEmail(email);
        console.log(`  ✅ Successfully created email: ${email.subject}`);
      } catch (emailError) {
        console.error(`  ❌ Error creating email "${email.subject}":`, emailError);
        throw emailError;
      }
    }
    
    console.log('✅ Successfully seeded all dummy emails');
    
    // Test retrieval
    console.log('🔍 Testing email retrieval...');
    const allEmails = await emailStorage.getEmails();
    console.log(`📧 Found ${allEmails.length} emails in storage`);
    
  } catch (error) {
    console.error('❌ Error seeding dummy emails:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Stack trace:', error.stack);
    }
    process.exit(1);
  } finally {
    // Close database connection
    console.log('🔌 Closing database connection...');
    await closeDatabase();
  }
}

// Run the seed function
seedDummyEmails(); 