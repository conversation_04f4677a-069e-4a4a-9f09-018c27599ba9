import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Settings,
  Zap,
  Clock
} from 'lucide-react';
import { MCPServer, MCPStatusResponse } from './types';
import { MCPServerCard } from './MCPServerCard';
import { MCPToolsList } from './MCPToolsList';
import { mcpApi } from '../../lib/mcp-api';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface MCPStatusPanelProps {
  className?: string;
  showToolsList?: boolean;
  compact?: boolean;
}

export function MCPStatusPanel({ 
  className = "", 
  showToolsList = true,
  compact = false 
}: MCPStatusPanelProps) {
  const [mcpStatus, setMcpStatus] = useState<MCPStatusResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Fetch MCP status from API
  const fetchMCPStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await mcpApi.getStatus();
      setMcpStatus(data);
      setLastRefresh(new Date());
    } catch (err) {
      console.error('Error fetching MCP status:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch MCP status');
    } finally {
      setLoading(false);
    }
  };

  // Handle server toggle with optimistic updates
  const handleServerToggle = async (serverId: string, enabled: boolean) => {
    // Optimistic update - immediately update UI
    if (mcpStatus) {
      const updatedStatus = {
        ...mcpStatus,
        servers: mcpStatus.servers.map(server =>
          server.id === serverId
            ? {
                ...server,
                config: { ...server.config, enabled },
                status: enabled ? server.status : { ...server.status, connected: false, healthy: false }
              }
            : server
        )
      };
      setMcpStatus(updatedStatus);
    }

    try {
      const success = await mcpApi.toggleServer(serverId, enabled);
      if (success) {
        // Refresh to get actual server state
        await fetchMCPStatus();
      } else {
        // Revert optimistic update on failure
        await fetchMCPStatus();
      }
    } catch (err) {
      console.error('Error toggling server:', err);
      // Revert optimistic update on error
      await fetchMCPStatus();
    }
  };

  // Handle server reconnect
  const handleServerReconnect = async (serverId: string) => {
    try {
      const success = await mcpApi.reconnectServer(serverId);
      if (success) {
        await fetchMCPStatus(); // Refresh status
      }
    } catch (err) {
      console.error('Error reconnecting server:', err);
    }
  };

  // Handle server configuration
  const handleServerConfigure = (serverId: string) => {
    // This would open a configuration modal or navigate to settings
    console.log('Configure server:', serverId);
  };

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchMCPStatus();
    const interval = setInterval(fetchMCPStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading && !mcpStatus) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2 text-gray-500">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>Loading MCP status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error && !mcpStatus) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <AlertTriangle className="w-8 h-8 text-red-500 mx-auto" />
            <div className="text-sm text-red-600">{error}</div>
            <Button variant="outline" size="sm" onClick={fetchMCPStatus}>
              <RefreshCw className="w-3 h-3 mr-1" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const connectedCount = mcpStatus?.servers?.filter(s => s.status.connected).length || 0;
  const totalServers = mcpStatus?.servers?.length || 0;

  if (compact) {
    return (
      <div className={cn("flex items-center space-x-4 p-3 bg-white border border-gray-200 rounded-lg shadow-sm", className)}>
        <div className="flex items-center space-x-2">
          <Activity className="w-4 h-4 text-blue-500" />
          <span className="text-sm font-medium">MCP Status</span>
        </div>
        
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-1">
            <div className={cn(
              "w-2 h-2 rounded-full",
              connectedCount === totalServers ? "bg-green-500" : 
              connectedCount > 0 ? "bg-yellow-500" : "bg-red-500"
            )}></div>
            <span>{connectedCount}/{totalServers} Connected</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <Zap className="w-3 h-3 text-purple-500" />
            <span>{mcpStatus?.totalTools || 0} Tools</span>
          </div>
        </div>
        
        <Button variant="ghost" size="sm" onClick={fetchMCPStatus}>
          <RefreshCw className={cn("w-3 h-3", loading && "animate-spin")} />
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Status Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-blue-500" />
              <span>MCP Server Status</span>
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              <div className="text-xs text-gray-500">
                Updated {formatDistanceToNow(lastRefresh, { addSuffix: true })}
              </div>
              <Button variant="ghost" size="sm" onClick={fetchMCPStatus} disabled={loading}>
                <RefreshCw className={cn("w-4 h-4", loading && "animate-spin")} />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{connectedCount}/{totalServers}</div>
              <div className="text-sm text-blue-700">Servers Connected</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{mcpStatus?.totalTools || 0}</div>
              <div className="text-sm text-purple-700">Tools Available</div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {mcpStatus?.servers?.filter(s => s.status.healthy).length || 0}
              </div>
              <div className="text-sm text-green-700">Healthy Servers</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Server Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {mcpStatus?.servers?.map(server => (
          <MCPServerCard
            key={server.id}
            server={server}
            onToggle={handleServerToggle}
            onReconnect={handleServerReconnect}
            onConfigure={handleServerConfigure}
          />
        ))}
      </div>

      {/* Tools List */}
      {showToolsList && mcpStatus?.servers && (
        <MCPToolsList servers={mcpStatus.servers} />
      )}
    </div>
  );
}
