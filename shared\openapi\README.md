# MeetSync Web App OpenAPI Specification

This directory contains the modularized OpenAPI specification for the MeetSync Web Application API.

## Structure

The specification is organized into the following components:

### Main Specification
- `openapi.yaml` - Main OpenAPI specification file that references all components

### Components
- `components/schemas.yaml` - Data models and schema definitions
- `components/parameters.yaml` - Reusable path and query parameters
- `components/responses.yaml` - Common response structures

### Path Definitions
- `paths/system.yaml` - System health and information endpoints
- `paths/integrations.yaml` - Integration management and OAuth operations
- `paths/sync.yaml` - Synchronization and scheduling operations
- `paths/chat.yaml` - Chat sessions and messaging
- `paths/files.yaml` - File management and upload operations
- `paths/debug.yaml` - Diagnostic and debugging endpoints
- `paths/rag.yaml` - RAG (Retrieval Augmented Generation) search
- `paths/upload.yaml` - Upload status endpoints
- `paths/test.yaml` - Testing endpoints
- `paths/legacy.yaml` - Legacy compatibility endpoints

## API Coverage

The specification covers all discovered routes from the server implementation:

### System & Health (6 endpoints)
- `/api/info` - API information
- `/api/health` - Health status
- `/api/health/live` - Liveness probe
- `/api/health/ready` - Readiness probe
- `/api/readiness` - Readiness alias
- `/api/liveness` - Liveness alias

### Integration Management (13 endpoints)
- CRUD operations for integrations
- OAuth flows for Google Drive and Microsoft Teams
- Folder and structure retrieval
- Connection testing

### Sync Operations (5 endpoints)
- Sync logs and items
- Schedule management
- Manual sync triggering
- Re-vectorization

### Chat System (6 endpoints)
- Chat session management
- Message handling
- Source management
- Search functionality

### File Management (10 endpoints)
- File CRUD operations
- Upload handling
- Search and filtering
- Embedding processing

### Debug & Diagnostic (15 endpoints)
- Health diagnostics
- Integration testing
- Database state inspection
- Credential management

### Additional Services
- RAG search and status
- Upload status
- Test endpoints
- Legacy compatibility

## Usage

### Viewing the Specification
The main specification file `openapi.yaml` can be opened in:
- Swagger UI
- Redoc
- Postman
- Insomnia
- Any OpenAPI 3.0 compatible tool

### Code Generation
This specification can be used to generate:
- Client SDKs in various languages
- Server stubs
- API documentation
- Mock servers

### Validation
All request/response schemas are defined and can be used for:
- Request validation
- Response validation
- Contract testing

## Key Features

- **Modular Organization**: Components are separated for maintainability
- **Complete Coverage**: All server routes are documented
- **Type Safety**: Comprehensive schema definitions
- **Security**: API key authentication defined
- **Standards Compliant**: OpenAPI 3.0.3 specification
- **Well Documented**: Clear descriptions and examples

## Maintenance

When adding new endpoints to the server:
1. Add the path definition to the appropriate `paths/*.yaml` file
2. Update the main `openapi.yaml` file to reference the new path
3. Add any new schemas to `components/schemas.yaml`
4. Add new parameters to `components/parameters.yaml` if needed
5. Add new response types to `components/responses.yaml` if needed

## Notes

- Route URIs are kept as-is from the current server implementation
- All major data models are based on the existing database schemas
- Authentication is defined but not yet enforced on all endpoints
- Some endpoints are marked as deprecated for legacy compatibility 