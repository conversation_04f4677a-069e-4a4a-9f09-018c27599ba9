#!/usr/bin/env ts-node

import { storage } from '../server/storage/index.js';
import { initializeDatabase, closeDatabase } from '../server/core/config/database.config.js';
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory of the current file
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from the project root
config({ path: path.join(__dirname, '..', '.env') });

async function simpleGmailTest() {
  console.log('📧 Simple Gmail Integration Test\n');

  try {
    // Initialize database
    await initializeDatabase();
    console.log('✅ Database connected');

    // Check emails in database
    const allEmails = await storage.getEmails();
    console.log(`📊 Total emails in database: ${allEmails.length}`);

    // Filter by platform
    const gmailEmails = allEmails.filter(email => email.platform === 'gmail');
    const dummyEmails = allEmails.filter(email => email.sender?.includes('@example.com'));
    
    console.log(`📬 Gmail emails: ${gmailEmails.length}`);
    console.log(`🎭 Dummy emails: ${dummyEmails.length}`);

    if (gmailEmails.length > 0) {
      console.log('\n✅ Gmail Integration Status: WORKING');
      console.log('📋 Sample Gmail emails:');
      
      gmailEmails.slice(0, 3).forEach((email, index) => {
        console.log(`  ${index + 1}. "${email.subject}" from ${email.sender}`);
        console.log(`     Platform: ${email.platform}, Read: ${email.isRead}, Starred: ${email.isStarred}`);
        console.log(`     Content preview: ${email.content.substring(0, 80)}...`);
        console.log(`     Received: ${email.receivedAt}`);
        console.log('');
      });

      // Test basic email search functionality
      console.log('🔍 Basic Email Search Test:');
      
      // Search for emails containing specific keywords
      const projectEmails = allEmails.filter(email => 
        email.content.toLowerCase().includes('project') || 
        email.subject.toLowerCase().includes('project')
      );
      console.log(`  - Emails mentioning "project": ${projectEmails.length}`);

      const meetingEmails = allEmails.filter(email => 
        email.content.toLowerCase().includes('meeting') || 
        email.subject.toLowerCase().includes('meeting')
      );
      console.log(`  - Emails mentioning "meeting": ${meetingEmails.length}`);

      const recentEmails = allEmails.filter(email => {
        const emailDate = new Date(email.receivedAt || email.createdAt);
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return emailDate > sevenDaysAgo;
      });
      console.log(`  - Recent emails (last 7 days): ${recentEmails.length}`);

    } else {
      console.log('\n⚠️  Gmail Integration Status: NO EMAILS');
      console.log('To set up Gmail integration:');
      console.log('1. Go to your web interface');
      console.log('2. Create/connect a Google integration');
      console.log('3. Sync emails via the Gmail endpoints');
    }

    if (dummyEmails.length > 0) {
      console.log('\n🎭 Dummy emails are still in the database');
      console.log('You can remove them once Gmail emails are working');
    }

    // Check integration status
    try {
      const integrations = await storage.getIntegrations();
      const googleIntegrations = integrations.filter(i => 
        i.type.includes('google') || i.type.includes('drive')
      );
      
      console.log('\n🔗 Integration Status:');
      console.log(`  - Total integrations: ${integrations.length}`);
      console.log(`  - Google integrations: ${googleIntegrations.length}`);
      
      googleIntegrations.forEach(integration => {
        console.log(`    * ${integration.name} (${integration.type}) - ${integration.status}`);
      });

    } catch (error) {
      console.log('⚠️  Could not check integration status:', error);
    }

    console.log('\n📝 Summary:');
    console.log(`  - Database connection: ✅ Working`);
    console.log(`  - Total emails: ${allEmails.length}`);
    console.log(`  - Gmail emails: ${gmailEmails.length}`);
    console.log(`  - Gmail integration: ${gmailEmails.length > 0 ? '✅ Working' : '❌ Not set up'}`);
    
    if (gmailEmails.length > 0) {
      console.log('\n🎉 Gmail integration is working! You can now:');
      console.log('  - Use emails as a data source in chat');
      console.log('  - Search your email content semantically');
      console.log('  - Ask questions about your emails');
      console.log('\nTry asking in chat: "What are the main topics in my emails?"');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Close database connection
    await closeDatabase();
  }
}

// Run the test function
simpleGmailTest(); 