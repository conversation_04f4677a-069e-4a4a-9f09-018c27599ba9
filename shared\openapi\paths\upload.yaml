UploadStatus:
  get:
    tags:
      - Files
    summary: Get upload status
    description: Get the status of the upload service
    operationId: getUploadStatus
    responses:
      '200':
        description: Upload status retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                status:
                  type: object
                  properties:
                    enabled:
                      type: boolean
                    maxFileSize:
                      type: integer
                      format: int64
                    allowedTypes:
                      type: array
                      items:
                        type: string
                    uploadPath:
                      type: string
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 