version: '3.8'

networks:
  meetsync-network:
    driver: bridge

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: meetsync_db
      POSTGRES_USER: meetsync_user
      POSTGRES_PASSWORD: meetsync_password
    ports:
      - "5432:5432"
    networks:
      - meetsync-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - meetsync-network
    restart: unless-stopped

  # Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
      - "5000:5000"
      - "9229:9229"  # Node.js debugging
    environment:
      NODE_ENV: development
      DATABASE_URL: **********************************************************/meetsync_db
      REDIS_URL: redis://redis:6379
      PORT: 5000
      FRONTEND_PORT: 3000
    networks:
      - meetsync-network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    # Simplified volume mounts
    volumes:
      - .:/app:delegated
      - /app/node_modules 