/**
 * Extract the page ID from a Notion page URL
 * @param pageUrl Notion page URL
 * @returns Page ID
 */
export function extractPageIdFromUrl(pageUrl: string): string {
  const pattern = /([a-zA-Z0-9]{32})/;
  const match = pageUrl.match(pattern);
  
  if (!match) {
    throw new Error('Invalid Notion page URL format');
  }
  
  return match[1];
}

/**
 * Format a date as YYYY-MM-DD
 * @param date Date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * Parse a cron expression to determine if it's valid
 * @param cronExpression Cron expression to validate
 * @returns True if valid, false otherwise
 */
export function isValidCronExpression(cronExpression: string): boolean {
  // Basic cron validation - simplified to avoid require() issues
  const cronPattern = /^(\*|([0-5]?\d)) (\*|([01]?\d|2[0-3])) (\*|([012]?\d|3[01])) (\*|([0-9]|1[012])) (\*|([0-6]))$/;
  return cronPattern.test(cronExpression.trim());
}

/**
 * Generate a human-readable description of a cron schedule
 * @param cronExpression Cron expression
 * @returns Human-readable description
 */
export function describeCronSchedule(cronExpression: string): string {
  // Simplified cron description to avoid require() issues
  if (!isValidCronExpression(cronExpression)) {
    return 'Invalid cron expression';
  }
  
  const parts = cronExpression.trim().split(' ');
  if (parts.length !== 5) {
    return 'Invalid cron expression';
  }
  
  const [minute, hour, day, month, weekday] = parts;
  
  // Basic descriptions for common patterns
  if (cronExpression === '0 0 * * *') return 'Daily at midnight';
  if (cronExpression === '0 12 * * *') return 'Daily at noon';
  if (cronExpression === '0 0 * * 0') return 'Weekly on Sunday at midnight';
  if (cronExpression === '0 * * * *') return 'Every hour';
  if (cronExpression === '*/5 * * * *') return 'Every 5 minutes';
  if (cronExpression === '*/15 * * * *') return 'Every 15 minutes';
  if (cronExpression === '*/30 * * * *') return 'Every 30 minutes';
  
  return `Cron schedule: ${cronExpression}`;
}

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param html HTML content to sanitize
 * @returns Sanitized HTML
 */
export function sanitizeHtml(html: string): string {
  return html
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * Truncate text to a specified length with ellipsis
 * @param text Text to truncate
 * @param maxLength Maximum length
 * @returns Truncated text
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + '...';
}

/**
 * Convert a duration in minutes to a human-readable format
 * @param minutes Duration in minutes
 * @returns Human-readable duration
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  }
  
  return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
}