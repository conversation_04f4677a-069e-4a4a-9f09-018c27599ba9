{"timestamp": "2025-06-06T19:30:17.469Z", "allReferencesUpdated": false, "results": {"updatedReferences": [{"file": "server/routes.ts", "matches": ["from \"./controllers/integration/index.js\""]}, {"file": "server/routes.ts", "matches": ["import { integrationController } from \"./controllers/integration/index.js\""]}, {"file": "docs/project-backend.md", "matches": ["from \"./controllers/integration/index.js\"", "from \"./controllers/integration/index.js\""]}, {"file": "docs/project-backend.md", "matches": ["import { integrationController } from \"./controllers/integration/index.js\"", "import { integrationController } from \"./controllers/integration/index.js\""]}, {"file": "docs/project-overview.md", "matches": ["from \"./controllers/integration/index.js\""]}, {"file": "docs/project-overview.md", "matches": ["import { integrationController } from \"./controllers/integration/index.js\""]}, {"file": "tests/validate-modularization.js", "matches": ["import('../server/controllers/integration/index.js'"]}], "remainingOldReferences": [], "verifiedImports": ["integrationController export verified", "All 8 methods verified"], "errors": [{"file": "import test", "error": "Could not import from .js or .ts: Cannot find module 'D:\\GPT Integrators\\MeetSync\\server\\controllers\\integration\\index.js' imported from D:\\GPT Integrators\\MeetSync\\validate-references.js"}]}}