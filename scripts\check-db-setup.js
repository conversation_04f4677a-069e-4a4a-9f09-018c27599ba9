#!/usr/bin/env node

/**
 * Database Setup Verification Script
 * Verifies that the database is properly configured and all required tables exist
 */

import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkDatabase() {
  console.log('🔍 Checking database setup...\n');

  try {
    // Test connection
    const client = await pool.connect();
    console.log('✅ Database connection successful');

    // Check required tables
    const requiredTables = [
      'integrations',
      'oauth_tokens',
      'chat_sessions',
      'chat_messages',
      'files',
      'file_chunks',
      'sync_operations'
    ];

    console.log('\n📋 Checking required tables:');
    for (const table of requiredTables) {
      try {
        const result = await client.query(
          `SELECT to_regclass('public.${table}') as exists`
        );
        if (result.rows[0].exists) {
          console.log(`  ✅ ${table}`);
        } else {
          console.log(`  ❌ ${table} - MISSING`);
        }
      } catch (error) {
        console.log(`  ❌ ${table} - ERROR: ${error.message}`);
      }
    }

    // Check oauth_tokens unique constraint
    console.log('\n🔒 Checking constraints:');
    try {
      const constraintResult = await client.query(`
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'oauth_tokens'::regclass 
        AND conname = 'oauth_tokens_user_platform_unique'
      `);
      
      if (constraintResult.rows.length > 0) {
        console.log('  ✅ oauth_tokens unique constraint exists');
      } else {
        console.log('  ⚠️  oauth_tokens unique constraint missing - run migration');
      }
    } catch (error) {
      console.log(`  ❌ Error checking constraints: ${error.message}`);
    }

    // Check sample data
    console.log('\n📊 Data summary:');
    try {
      const integrationCount = await client.query('SELECT COUNT(*) FROM integrations');
      const sessionCount = await client.query('SELECT COUNT(*) FROM chat_sessions');
      const messageCount = await client.query('SELECT COUNT(*) FROM chat_messages');
      
      console.log(`  📱 Integrations: ${integrationCount.rows[0].count}`);
      console.log(`  💬 Chat Sessions: ${sessionCount.rows[0].count}`);
      console.log(`  📝 Chat Messages: ${messageCount.rows[0].count}`);
    } catch (error) {
      console.log(`  ❌ Error getting data summary: ${error.message}`);
    }

    client.release();
    console.log('\n✅ Database check complete!');

  } catch (error) {
    console.error('\n❌ Database connection failed:', error.message);
    console.log('\n🔧 Troubleshooting steps:');
    console.log('1. Ensure PostgreSQL is running');
    console.log('2. Check your DATABASE_URL in .env');
    console.log('3. Verify database exists: CREATE DATABASE meetsync_db;');
    console.log('4. Run migrations: npm run migrate');
    process.exit(1);
  } finally {
    await pool.end();
  }
}

checkDatabase(); 