import { Request, Response, NextFunction } from 'express';
import { isDevelopment } from '../config/environment.config';

/**
 * Logging middleware and utilities
 */

export interface LogContext {
  requestId: string;
  method: string;
  path: string;
  userAgent?: string;
  ip?: string;
  timestamp: string;
  duration?: number;
  statusCode?: number;
  error?: any;
}

/**
 * Generate unique request ID
 */
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Get client IP address
 */
const getClientIP = (req: Request): string => {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unknown'
  );
};

/**
 * Format log message
 */
const formatLogMessage = (level: string, message: string, context?: Partial<LogContext>): string => {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    level: level.toUpperCase(),
    message,
    ...context,
  };

  if (isDevelopment()) {
    // Pretty format for development
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${
      context ? ` | ${JSON.stringify(context, null, 2)}` : ''
    }`;
  } else {
    // JSON format for production
    return JSON.stringify(logData);
  }
};

/**
 * Logger utility
 */
export const logger = {
  info: (message: string, context?: Partial<LogContext>) => {
    console.log(formatLogMessage('info', message, context));
  },

  warn: (message: string, context?: Partial<LogContext>) => {
    console.warn(formatLogMessage('warn', message, context));
  },

  error: (message: string, context?: Partial<LogContext>) => {
    console.error(formatLogMessage('error', message, context));
  },

  debug: (message: string, context?: Partial<LogContext>) => {
    if (isDevelopment()) {
      console.debug(formatLogMessage('debug', message, context));
    }
  },
};

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const requestId = generateRequestId();
  
  // Add request ID to request object for use in other middleware
  (req as any).requestId = requestId;

  const logContext: LogContext = {
    requestId,
    method: req.method,
    path: req.path,
    userAgent: req.headers['user-agent'],
    ip: getClientIP(req),
    timestamp: new Date().toISOString(),
  };

  // Log incoming request
  logger.info('Incoming request', logContext);

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function (body: any) {
    const duration = Date.now() - startTime;
    const responseContext: LogContext = {
      ...logContext,
      duration,
      statusCode: res.statusCode,
    };

    // Log response
    if (res.statusCode >= 400) {
      logger.error('Request failed', {
        ...responseContext,
        error: body.error || body,
      });
    } else {
      logger.info('Request completed', responseContext);
    }

    return originalJson.call(this, body);
  };

  next();
};

/**
 * Performance monitoring middleware
 */
export const performanceLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = process.hrtime.bigint();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds

    const context = {
      requestId: (req as any).requestId,
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration: Math.round(duration * 100) / 100, // Round to 2 decimal places
      timestamp: new Date().toISOString(),
    };

    // Log slow requests
    if (duration > 1000) { // Slower than 1 second
      logger.warn('Slow request detected', context);
    } else if (isDevelopment()) {
      logger.debug('Request performance', context);
    }
  });

  next();
};

/**
 * API endpoint logging middleware
 */
export const apiLogger = (endpoint: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const context = {
      requestId: (req as any).requestId,
      endpoint,
      method: req.method,
      timestamp: new Date().toISOString(),
    };

    logger.debug(`API endpoint: ${endpoint}`, context);
    next();
  };
};

/**
 * Database query logging
 */
export const logDatabaseQuery = (query: string, params?: any[], duration?: number): void => {
  const context = {
    query: query.replace(/\s+/g, ' ').trim(),
    params: params?.length ? params : undefined,
    duration: duration ? Math.round(duration * 100) / 100 : undefined,
    timestamp: new Date().toISOString(),
  };

  if (duration && duration > 100) { // Slow query (>100ms)
    logger.warn('Slow database query', context);
  } else if (isDevelopment()) {
    logger.debug('Database query', context);
  }
};

/**
 * External API call logging
 */
export const logExternalAPI = (
  service: string,
  endpoint: string,
  method: string,
  duration?: number,
  statusCode?: number,
  error?: any
): void => {
  const context = {
    service,
    endpoint,
    method,
    duration: duration ? Math.round(duration * 100) / 100 : undefined,
    statusCode,
    error: error?.message || error,
    timestamp: new Date().toISOString(),
  };

  if (error) {
    logger.error(`External API error: ${service}`, context);
  } else if (statusCode && statusCode >= 400) {
    logger.warn(`External API failed: ${service}`, context);
  } else {
    logger.info(`External API call: ${service}`, context);
  }
};

/**
 * Security event logging
 */
export const logSecurityEvent = (
  event: string,
  req: Request,
  details?: any
): void => {
  const context = {
    event,
    requestId: (req as any).requestId,
    ip: getClientIP(req),
    userAgent: req.headers['user-agent'],
    path: req.path,
    method: req.method,
    details,
    timestamp: new Date().toISOString(),
  };

  logger.warn(`Security event: ${event}`, context);
};
