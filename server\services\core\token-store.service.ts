import { getDatabase } from '../../core/config/database.config.js';
import { oauthTokens } from '../../../shared/schemas/oauth.schema.js';
import { cryptoService } from './crypto-service.js';
import { eq, and } from 'drizzle-orm';

export interface TokenData {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  scope?: string;
  expiry_date: number;
}

/**
 * Service for managing OAuth tokens in the backend
 * Handles token storage, encryption, and management
 */
export class TokenStoreService {
  private static instance: TokenStoreService;
  private db: any;

  private constructor() {
    // Initialize database connection lazily - it will be set when first used
    this.db = null;
  }

  public static getInstance(): TokenStoreService {
    if (!TokenStoreService.instance) {
      TokenStoreService.instance = new TokenStoreService();
    }
    return TokenStoreService.instance;
  }

  /**
   * Get database instance, initializing if needed
   */
  private async getDb() {
    if (!this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        console.error('Failed to get database instance in TokenStoreService:', error);
        throw new Error('Database not available for token storage');
      }
    }
    return this.db;
  }

  /**
   * Store OAuth tokens for a user
   * @param userId - The user's ID
   * @param platform - The platform (e.g., 'google', 'microsoft')
   * @param tokens - The OAuth tokens to store
   */
  async storeTokens(userId: string, platform: string, tokens: TokenData): Promise<void> {
    try {
      const db = await this.getDb();
      
      // Encrypt the tokens before storing
      const encryptedAccessToken = await cryptoService.encrypt(tokens.access_token);
      const encryptedRefreshToken = tokens.refresh_token 
        ? await cryptoService.encrypt(tokens.refresh_token)
        : null;

      // Store in database
      await db.insert(oauthTokens).values({
        userId,
        platform,
        accessToken: encryptedAccessToken,
        refreshToken: encryptedRefreshToken,
        tokenType: tokens.token_type,
        scope: tokens.scope,
        expiresAt: new Date(tokens.expiry_date),
      }).onConflictDoUpdate({
        target: [oauthTokens.userId, oauthTokens.platform],
        set: {
          accessToken: encryptedAccessToken,
          refreshToken: encryptedRefreshToken,
          tokenType: tokens.token_type,
          scope: tokens.scope,
          expiresAt: new Date(tokens.expiry_date),
        }
      });
    } catch (error) {
      console.error('Error storing tokens:', error);
      throw new Error('Failed to store tokens securely');
    }
  }

  /**
   * Retrieve OAuth tokens for a user
   * @param userId - The user's ID
   * @param platform - The platform (e.g., 'google', 'microsoft')
   * @returns The decrypted tokens or null if not found
   */
  async getTokens(userId: string, platform: string): Promise<TokenData | null> {
    try {
      const db = await this.getDb();
      
      const result = await db.select()
        .from(oauthTokens)
        .where(
          and(
            eq(oauthTokens.userId, userId),
            eq(oauthTokens.platform, platform)
          )
        )
        .limit(1);

      if (!result.length) {
        return null;
      }

      const token = result[0];

      // Decrypt the tokens
      const accessToken = await cryptoService.decrypt(token.accessToken);
      const refreshToken = token.refreshToken 
        ? await cryptoService.decrypt(token.refreshToken)
        : undefined;

      return {
        access_token: accessToken,
        refresh_token: refreshToken,
        token_type: token.tokenType,
        scope: token.scope || undefined,
        expiry_date: token.expiresAt.getTime(),
      };
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return null;
    }
  }

  /**
   * Delete stored tokens for a user
   * @param userId - The user's ID
   * @param platform - The platform (e.g., 'google', 'microsoft')
   */
  async deleteTokens(userId: string, platform: string): Promise<void> {
    try {
      const db = await this.getDb();
      
      await db.delete(oauthTokens)
        .where(
          and(
            eq(oauthTokens.userId, userId),
            eq(oauthTokens.platform, platform)
          )
        );
    } catch (error) {
      console.error('Error deleting tokens:', error);
      throw new Error('Failed to delete tokens');
    }
  }

  /**
   * Check if tokens are expired
   * @param tokens - The tokens to check
   * @returns boolean indicating if tokens are expired
   */
  isTokenExpired(tokens: TokenData): boolean {
    return Date.now() >= tokens.expiry_date;
  }
}

// Export singleton instance for backend use
export const tokenStore = TokenStoreService.getInstance(); 