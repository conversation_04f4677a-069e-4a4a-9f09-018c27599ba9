# Database Reset Guide

This guide explains how to reset all "connected state" and "fetched data" in MeetSync while preserving integration records. This is useful for testing integration setup flows without having to recreate integration entries.

## What Gets Reset

When you run the reset, the following data is cleared:

### ✅ **Data That Gets Cleared:**
- **Integration credentials** - All encrypted OAuth tokens
- **Connection status** - All integrations reset to "disconnected"
- **Sync status** - Reset to "idle" 
- **Sync timestamps** - `last_sync_at` and `next_sync_at` cleared
- **All sync logs** - Complete sync history
- **All sync items** - Individual sync records
- **All files** - File references and content 
- **All file chunks** - RAG embeddings (auto-deleted via cascade)
- **All chat sessions** - AI conversation history
- **All chat messages** - AI chat data
- **Uploads folder** - All uploaded files are removed from the file system

### ✅ **Data That Gets Preserved:**
- **Integration records** - Names, types, and configurations
- **User accounts** - Login credentials
- **Project definitions** - Project metadata

## How to Reset

### SQL Script Method

The reset uses a Node.js script that loads your `.env` file and runs the SQL reset script:

```bash
npm run db:reset
```

**What it does:**
1. Loads `DATABASE_URL` from your `.env` file
2. Prompts for confirmation
3. Runs the SQL reset script using `psql` (or Docker if `psql` not available)
4. Clears all files from the uploads folder
5. Shows a summary of what was reset

**Requirements:**
- `.env` file with `DATABASE_URL` set
- `psql` client installed OR Docker available

## Usage Examples

### Testing Fresh Integration Setup

```bash
# 1. Reset all connected state
npm run db:reset

# 2. Start your backend
npm run dev

# 3. Go to /integrations - all should show "Disconnected"
# 4. Test connecting integrations from scratch
```

### Clearing Test Data

```bash
# Reset and restart
npm run db:reset
npm run dev

# Check pages - should be empty:
# - /logs (no sync history)
# - /files (no files)
```

## Verification Steps

After running reset, verify it worked:

### **Check Integration Status**
- Navigate to `/integrations` 
- All integrations should show "Disconnected" status
- Integration cards should still be visible with names/types

### **Check Data Pages**
- Navigate to `/logs` - should show "No sync logs found"
- Navigate to `/files` - should show empty state
- Chat functionality should have no history
- Check `/uploads` folder - should be empty (all uploaded files removed)

## Troubleshooting

### DATABASE_URL Not Found
Make sure your `.env` file exists and contains:
```
DATABASE_URL="postgresql://user:pass@host:port/dbname"
```

### PostgreSQL Client Missing
If you don't have `psql` installed:

**Option 1: Install PostgreSQL client**
```bash
# Ubuntu/Debian
sudo apt-get install postgresql-client

# Mac
brew install postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

**Option 2: Docker**
The script will automatically use Docker if `psql` is not available.

## Safety Features

- **Confirmation Required**: Script prompts before proceeding
- **Transaction Safety**: All operations run in database transaction
- **Error Handling**: Clear error messages and troubleshooting
- **Preservation**: Integration records are never deleted

## Files

The reset functionality uses these files:

```
server/
├── scripts/
│   ├── reset-connected-state.sql    # SQL reset script
│   └── run-reset.js                 # Node.js runner (loads .env)
```

You can also run the SQL script manually:
```bash
psql "$DATABASE_URL" -f server/scripts/reset-connected-state.sql
``` 