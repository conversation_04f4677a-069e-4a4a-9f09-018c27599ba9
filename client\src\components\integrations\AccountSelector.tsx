import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  UserIcon, 
  PlusIcon, 
  CalendarIcon, 
  MailIcon, 
  HardDriveIcon,
  Loader2Icon,
  ExternalLinkIcon,
  CheckIcon
} from 'lucide-react';

interface ConnectedAccount {
  provider: string;
  email: string;
  displayName: string;
  connectedAt: string;
  lastUsed: string | null;
  services: Array<{
    name: string;
    type: string;
    status: string;
    integrationId: number;
    lastSyncAt: string | null;
    itemCount?: number;
  }>;
  scopes: string[];
  isActive: boolean;
}

interface AccountSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  integrationType: string;
  onAccountSelected: (account: ConnectedAccount | 'new') => void;
}

const getProviderIcon = (provider: string) => {
  switch (provider) {
    case 'google':
      return 'https://developers.google.com/identity/images/g-logo.png';
    case 'microsoft':
      return 'https://upload.wikimedia.org/wikipedia/commons/4/44/Microsoft_logo.svg';
    default:
      return 'https://via.placeholder.com/32x32?text=?';
  }
};

const getIntegrationIcon = (type: string) => {
  switch (type) {
    case 'gmail':
      return <MailIcon className="h-5 w-5" />;
    case 'google_calendar':
    case 'google-calendar':
      return <CalendarIcon className="h-5 w-5" />;
    case 'google_drive':
    case 'google-drive':
      return <HardDriveIcon className="h-5 w-5" />;
    default:
      return <UserIcon className="h-5 w-5" />;
  }
};

const getIntegrationTitle = (type: string) => {
  switch (type) {
    case 'gmail':
      return 'Gmail Integration';
    case 'google_calendar':
    case 'google-calendar':
      return 'Google Calendar Integration';
    case 'google_drive':
    case 'google-drive':
      return 'Google Drive Integration';
    default:
      return `${type} Integration`;
  }
};

const getProviderForIntegrationType = (type: string) => {
  if (type.includes('google') || ['gmail', 'google_calendar', 'google-calendar'].includes(type)) {
    return 'google';
  }
  if (type.includes('microsoft')) {
    return 'microsoft';
  }
  return 'unknown';
};

export default function AccountSelector({ 
  isOpen, 
  onClose, 
  integrationType, 
  onAccountSelected 
}: AccountSelectorProps) {
  const [selectedAccount, setSelectedAccount] = useState<ConnectedAccount | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const requiredProvider = getProviderForIntegrationType(integrationType);

  // Fetch connected accounts
  const { data: accountsData, isLoading } = useQuery({
    queryKey: ['/api/accounts'],
    queryFn: async () => {
      const response = await fetch('/api/accounts');
      if (!response.ok) throw new Error('Failed to fetch accounts');
      return response.json();
    },
    enabled: isOpen,
  });

  // Filter accounts by provider
  const accounts: ConnectedAccount[] = (accountsData?.accounts || [])
    .filter((account: ConnectedAccount) => account.provider === requiredProvider);

  const handleAccountSelect = (account: ConnectedAccount) => {
    setSelectedAccount(account);
  };

  const handleContinue = () => {
    if (selectedAccount) {
      onAccountSelected(selectedAccount);
    }
  };

  const handleAddNewAccount = () => {
    onAccountSelected('new');
  };

  const hasExistingIntegration = (account: ConnectedAccount) => {
    return account.services.some(service => service.type === integrationType);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getIntegrationIcon(integrationType)}
            {getIntegrationTitle(integrationType)}
          </DialogTitle>
          <DialogDescription>
            Choose which {requiredProvider === 'google' ? 'Google' : 'Microsoft'} account 
            you want to use for this integration.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2Icon className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading accounts...</span>
            </div>
          ) : accounts.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center">
                  <UserIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold mb-2">
                    No {requiredProvider === 'google' ? 'Google' : 'Microsoft'} accounts connected
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    You need to connect a {requiredProvider === 'google' ? 'Google' : 'Microsoft'} account first
                  </p>
                  <Button onClick={handleAddNewAccount} className="flex items-center gap-2">
                    <img 
                      src={getProviderIcon(requiredProvider)} 
                      alt={requiredProvider} 
                      className="h-4 w-4" 
                    />
                    Connect {requiredProvider === 'google' ? 'Google' : 'Microsoft'} Account
                    <ExternalLinkIcon className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="space-y-3">
                <h3 className="font-semibold">Select an account:</h3>
                {accounts.map((account) => {
                  const alreadyConnected = hasExistingIntegration(account);
                  return (
                    <Card 
                      key={`${account.provider}-${account.email}`}
                      className={`cursor-pointer transition-all ${
                        selectedAccount?.email === account.email
                          ? 'ring-2 ring-blue-500 bg-blue-50'
                          : 'hover:shadow-md'
                      } ${alreadyConnected ? 'opacity-60' : ''}`}
                      onClick={() => !alreadyConnected && handleAccountSelect(account)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <img 
                              src={getProviderIcon(account.provider)} 
                              alt={account.provider} 
                              className="h-8 w-8" 
                            />
                            <div>
                              <div className="font-medium">
                                {account.displayName || account.email}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {account.email}
                              </div>
                              {alreadyConnected && (
                                <Badge variant="secondary" className="mt-1">
                                  Already connected to {getIntegrationTitle(integrationType)}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            {selectedAccount?.email === account.email && (
                              <CheckIcon className="h-5 w-5 text-blue-500" />
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Add new account option */}
              <Card 
                className="cursor-pointer border-dashed hover:shadow-md transition-all"
                onClick={handleAddNewAccount}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <PlusIcon className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Connect new account</div>
                      <div className="text-sm text-muted-foreground">
                        Add another {requiredProvider === 'google' ? 'Google' : 'Microsoft'} account
                      </div>
                    </div>
                    <ExternalLinkIcon className="h-4 w-4 ml-auto text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {accounts.length > 0 && selectedAccount && !hasExistingIntegration(selectedAccount) && (
            <Button onClick={handleContinue}>
              Continue with {selectedAccount.displayName || selectedAccount.email}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 