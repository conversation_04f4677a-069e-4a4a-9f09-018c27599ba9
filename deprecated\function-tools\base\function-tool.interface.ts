/**
 * Core interface for OpenAI function calling tools
 * Provides standardized structure for all function tools in the system
 */
export interface FunctionTool {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
  handler: (params: any) => Promise<string> | string;
}

/**
 * Tool category enumeration for organizing tools
 */
export enum ToolCategory {
  FILE_MANAGEMENT = 'file_management',
  SEARCH = 'search',
  INFO = 'info',
  PLATFORM_SPECIFIC = 'platform_specific',
  CONFIRMATION = 'confirmation',
  DYNAMIC = 'dynamic',
  HITL = 'hitl',
  BULK_OPERATIONS = 'bulk_operations'
}

/**
 * Tool registration metadata
 */
export interface ToolRegistration {
  tool: FunctionTool;
  category: ToolCategory;
  isDynamic?: boolean;
  platform?: string;
  priority?: number;
}

/**
 * Tool execution result
 */
export interface ToolExecutionResult {
  success: boolean;
  result?: string;
  error?: string;
  executionTime?: number;
}

/**
 * Tool execution context
 */
export interface ToolExecutionContext {
  userId?: string;
  integrationId?: number;
  requestId?: string;
  metadata?: Record<string, any>;
}

/**
 * Base interface for tool categories
 * Each tool category should implement this interface
 */
export interface IToolCategory {
  /**
   * Get the category name
   */
  getCategory(): ToolCategory;

  /**
   * Get all tools in this category
   */
  getTools(): FunctionTool[];

  /**
   * Check if a tool belongs to this category
   */
  hasToolCalled(toolName: string): boolean;

  /**
   * Execute a tool from this category
   */
  executeTool(toolName: string, parameters: any, context?: ToolExecutionContext): Promise<string>;

  /**
   * Initialize the category (register tools, setup, etc.)
   */
  initialize(): void;
}

/**
 * Abstract base class for tool categories
 * Provides common functionality for all tool categories
 */
export abstract class BaseToolCategory implements IToolCategory {
  protected tools: Map<string, FunctionTool> = new Map();
  protected category: ToolCategory;

  constructor(category: ToolCategory) {
    this.category = category;
  }

  abstract initialize(): void;

  getCategory(): ToolCategory {
    return this.category;
  }

  getTools(): FunctionTool[] {
    return Array.from(this.tools.values());
  }

  hasToolCalled(toolName: string): boolean {
    return this.tools.has(toolName);
  }

  async executeTool(toolName: string, parameters: any, context?: ToolExecutionContext): Promise<string> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new Error(`Tool '${toolName}' not found in category '${this.category}'`);
    }

    try {
      this.log('info', `Executing ${toolName} in category ${this.category}`, parameters);
      const result = await tool.handler(parameters);
      this.log('info', `${toolName} executed successfully`);
      return result;
    } catch (error: any) {
      this.log('error', `Error executing ${toolName}:`, error);
      throw new Error(`Failed to execute ${toolName}: ${error.message}`);
    }
  }

  /**
   * Register a tool in this category
   */
  protected registerTool(tool: FunctionTool): void {
    this.tools.set(tool.name, tool);
    this.log('info', `Registered tool '${tool.name}' in category '${this.category}'`);
  }

  /**
   * Helper method to create tool parameters
   */
  protected createParameters(properties: Record<string, any>, required: string[] = []): any {
    return {
      type: "object",
      properties,
      required
    };
  }

  /**
   * Helper method for logging with consistent format
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const prefix = `[FunctionTools:${this.category.toUpperCase()}]`;
    
    switch (level) {
      case 'info':
        console.log(`${prefix} ${message}`, data || '');
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, data || '');
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data || '');
        break;
    }
  }

  /**
   * Helper method to check for affirmative confirmation
   */
  protected isAffirmativeConfirmation(confirmation: string): boolean {
    const affirmativeWords = ['yes', 'confirm', 'proceed', 'create', 'go ahead', 'ok', 'okay', 'do it'];
    return affirmativeWords.some(word => 
      confirmation.toLowerCase().includes(word)
    );
  }

  /**
   * Helper method to get MIME type based on file extension
   */
  protected getMimeType(fileType: string): string {
    const mimeTypes: Record<string, string> = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'json': 'application/json',
      'csv': 'text/csv',
      'html': 'text/html',
      'xml': 'application/xml',
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };

    return mimeTypes[fileType.toLowerCase()] || 'text/plain';
  }
}

/**
 * Tool validation utilities
 */
export class ToolValidator {
  /**
   * Validate that a tool conforms to the FunctionTool interface
   */
  static validateTool(tool: any): tool is FunctionTool {
    return (
      typeof tool === 'object' &&
      typeof tool.name === 'string' &&
      typeof tool.description === 'string' &&
      typeof tool.parameters === 'object' &&
      typeof tool.parameters.type === 'string' &&
      typeof tool.parameters.properties === 'object' &&
      Array.isArray(tool.parameters.required) &&
      typeof tool.handler === 'function'
    );
  }

  /**
   * Validate tool parameters against the tool's parameter schema
   */
  static validateParameters(tool: FunctionTool, parameters: any): boolean {
    // Basic validation - check required parameters are present
    if (!tool.parameters.required) return true;
    
    for (const required of tool.parameters.required) {
      if (!(required in parameters)) {
        return false;
      }
    }
    
    return true;
  }
} 