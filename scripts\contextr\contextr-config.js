// MeetSync Contextr Configuration
// This file contains predefined configurations for exploring different parts of the project

export const configs = {
  // Full project overview - good for getting started
  overview: {
    name: "Project Overview",
    config: {
      includeDirs: ['client/src', 'server', 'shared'],
      includeFiles: [
        'package.json', 
        'README.md', 
        'tsconfig.json', 
        'vite.config.ts', 
        'tailwind.config.ts',
        'drizzle.config.ts'
      ],
      exclude: [
        'node_modules/**', 
        '.git/**', 
        '**/*.log', 
        '**/*.png', 
        '**/*.jpg', 
        '**/*.ico',
        '**/*.test.ts',
        '**/*.test.tsx',
        '**/*.spec.ts'
      ],
      listOnlyPatterns: ['**/*.png', '**/*.jpg', '**/*.ico', '**/*.svg']
    }
  },

  // Frontend React application
  frontend: {
    name: "Frontend (React)",
    config: {
      includeDirs: ['client/src'],
      includeFiles: [
        'client/src/main.tsx',
        'client/src/App.tsx',
        'client/src/index.css',
        'vite.config.ts',
        'tailwind.config.ts'
      ],
      exclude: [
        '**/*.test.ts',
        '**/*.test.tsx',
        '**/*.spec.ts'
      ]
    }
  },

  // Backend Express application
  backend: {
    name: "Backend (Express)",
    config: {
      includeDirs: ['server'],
      includeFiles: [
        'server/index.ts',
        'server/db.ts',
        'server/routes.ts',
        'drizzle.config.ts'
      ],
      exclude: [
        '**/*.test.ts',
        '**/*.test.js',
        '**/*.spec.ts'
      ]
    }
  },

  // UI Components only
  components: {
    name: "UI Components",
    config: {
      includeDirs: ['client/src/components'],
      exclude: [
        '**/*.test.ts',
        '**/*.test.tsx',
        '**/*.spec.ts'
      ]
    }
  },

  // Pages and routing
  pages: {
    name: "Pages & Routing",
    config: {
      includeDirs: ['client/src/pages'],
      includeFiles: [
        'client/src/App.tsx',
        'client/src/main.tsx'
      ]
    }
  },

  // Server controllers and services
  serverLogic: {
    name: "Server Logic",
    config: {
      includeDirs: ['server/controllers', 'server/services'],
      includeFiles: [
        'server/routes.ts',
        'server/utils.ts'
      ]
    }
  },

  // Database and data layer
  database: {
    name: "Database Layer",
    config: {
      includeFiles: [
        'server/db.ts',
        'server/storage.ts',
        'drizzle.config.ts',
        'server/seed-data.ts'
      ],
      includeDirs: ['shared']
    }
  },

  // Configuration files only
  config: {
    name: "Configuration Files",
    config: {
      includeFiles: [
        'package.json',
        'tsconfig.json',
        'vite.config.ts',
        'tailwind.config.ts',
        'drizzle.config.ts',
        'postcss.config.js',
        '.env',
        'env.template'
      ]
    }
  },

  // Integration-specific code
  integrations: {
    name: "Integrations",
    config: {
      includeDirs: ['client/src/components/integrations', 'client/src/pages/integrations'],
      includeFiles: [
        'server/setup-notion.ts'
      ],
      searchPatterns: ['notion', 'integration', 'teams', 'calendar']
    }
  }
};

// Helper function to get a specific configuration
export function getConfig(name) {
  return configs[name];
}

// Helper function to list all available configurations
export function listConfigs() {
  return Object.keys(configs).map(key => ({
    key,
    name: configs[key].name,
    description: `Explore ${configs[key].name.toLowerCase()}`
  }));
} 