import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import {
  initializeCore,
  environmentConfig,
  errorH<PERSON>ler,
  notFound<PERSON>andler,
  requestLogger,
  performanceLogger,
  logger,
  createSessionMiddleware,
  initializeSessionManager,
  cleanupSessionManager,
} from "./core";
import { MCPManager } from './services/mcp/mcp-manager.service';
import { mcpServers } from "./core/config/mcp.config";

const app = express(); 
export const mcpManager = new MCPManager();

/**
 * Initialize MCP servers
 */
export async function initializeMCPServers(): Promise<void> {
  try {
    await mcpManager.initializeServers(mcpServers);
  } catch (error) {
    throw new Error(`Failed to initialize MCP servers: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Cleanup MCP servers
 */
export async function cleanupMCPServers(): Promise<void> {
  try {
    await mcpManager.disconnect();
  } catch (error) {
    console.error('Error cleaning up MCP servers:', error);
  }
} 

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Use new core middleware
app.use(requestLogger);
app.use(performanceLogger);

// Legacy logging middleware for backward compatibility
app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  try {
    // Initialize core infrastructure
    logger.info('🚀 Starting GPT Unify Web App...');
    const coreInit = await initializeCore();

    if (!coreInit.success) {
      logger.error('❌ Failed to initialize core infrastructure:', { error: coreInit.error });
      process.exit(1);
    }

    // Initialize MCP servers
    try {
      await initializeMCPServers();
      logger.info('✅ MCP servers initialized');
    } catch (error) {
      logger.warn('⚠️ MCP servers initialization failed:', { error: (error as Error).message });
    }

    // Initialize session management
    try {
      await initializeSessionManager();
      app.use(createSessionMiddleware());
      logger.info('✅ Session management initialized');
    } catch (error) {
      logger.warn('⚠️ Session management initialization failed:', { error: (error as Error).message });
    }

    // Initialize services
    try {
      const { ragService } = await import("./services/rag");
      if (ragService && typeof ragService.initialize === 'function') {
        await ragService.initialize();
        logger.info('✅ RAG service initialized');
      }
    } catch (error) {
      logger.warn('⚠️ RAG service initialization failed:', { error: (error as Error).message });
    }

    try {
      const { fileUploadService } = await import("./services/file-upload");
      if (fileUploadService && typeof fileUploadService.initialize === 'function') {
        await fileUploadService.initialize();
        logger.info('✅ File upload service initialized');
      }
    } catch (error) {
      logger.warn('⚠️ File upload service initialization failed:', { error: (error as Error).message });
    }

    try {
      const { googleService } = await import("./services/platform-integrations/google");
      if (googleService && typeof googleService.initialize === 'function') {
        await googleService.initialize();
        logger.info('✅ Google service initialized');
      }
    } catch (error) {
      logger.warn('⚠️ Google service initialization failed:', { error: (error as Error).message });
    }

    try {
      const { microsoftService } = await import("./services/platform-integrations/microsoft");
      if (microsoftService && typeof microsoftService.initialize === 'function') {
        await microsoftService.initialize();
        logger.info('✅ Microsoft service initialized');
      }
    } catch (error) {
      logger.warn('⚠️ Microsoft service initialization failed:', { error: (error as Error).message });
    }

    try {
      const { unifiedContentExtractor } = await import("./services/ai/unified-content-extractor.service");
      if (unifiedContentExtractor && typeof unifiedContentExtractor.initialize === 'function') {
        await unifiedContentExtractor.initialize();
        logger.info('✅ Unified Content Extractor service initialized');
      }
    } catch (error) {
      logger.warn('⚠️ Unified Content Extractor initialization failed:', { error: (error as Error).message });
    }

    // Register routes
    const server = await registerRoutes(app, mcpManager);

    // Seed test data in development mode
    if (environmentConfig.nodeEnv === "development") {
      try {
        const { seedTestData } = await import("./seed-data");
        await seedTestData();
        logger.info('✅ Test data seeded successfully');
      } catch (error) {
        logger.error('Failed to seed test data:', { error: (error as Error).message });
      }
    }

    // Setup Vite in development or serve static files in production
    // IMPORTANT: This must come BEFORE error handlers to serve the frontend
    if (environmentConfig.nodeEnv === "development") {
      await setupVite(app, server);
      logger.info('✅ Vite development server configured');
    } else {
      serveStatic(app);
      logger.info('✅ Static file serving configured');
    }

    // Use new error handling middleware
    // IMPORTANT: These must come AFTER Vite/static setup
    app.use(errorHandler);
    app.use(notFoundHandler);

    // Start the server
    const port = environmentConfig.port;
    const host = environmentConfig.host;

    server.listen(port, host, () => {
      logger.info(`🎉 Server running on http://${host}:${port}`);
      logger.info(`📊 Environment: ${environmentConfig.nodeEnv}`);
      logger.info(`🔧 Core infrastructure initialized successfully`);
    });

    // Improved graceful shutdown handling
    const activeConnections = new Set<any>();
    
    // Track active connections
    server.on('connection', (connection) => {
      activeConnections.add(connection);
      connection.on('close', () => {
        activeConnections.delete(connection);
      });
    });

    let isShuttingDown = false;

    const gracefulShutdown = async (signal: string) => {
      if (isShuttingDown) {
        logger.warn('⚠️ Shutdown already in progress...');
        return;
      }
      
      isShuttingDown = true;
      logger.info(`📴 Received ${signal}, shutting down gracefully...`);

      // Set a timeout to force exit if graceful shutdown takes too long
      const forceExitTimeout = setTimeout(() => {
        logger.error('💀 Force killing server due to timeout');
        process.exit(1);
      }, 10000); // 10 seconds timeout

      try {
        // Stop accepting new connections
        server.close(async () => {
          try {
            logger.info('🔌 Server stopped accepting new connections');

            // Close database connections
            try {
              const { closeDatabase } = await import('./core/config/database.config');
              await closeDatabase();
              logger.info('🗄️ Database connections closed');
            } catch (dbError) {
              logger.warn('⚠️ Error closing database:', { error: (dbError as Error).message });
            }

            // Cleanup session manager
            try {
              await cleanupSessionManager();
              logger.info('🗄️ Session manager cleaned up');
            } catch (sessionError) {
              logger.warn('⚠️ Error cleaning up session manager:', { error: (sessionError as Error).message });
            }



            // Cleanup services
            try {
              const services = await Promise.allSettled([
                import('./services/rag').then(({ ragService }) => ragService.cleanup?.()),
                import('./services/platform-integrations/google').then(({ googleService }) => googleService.cleanup?.()),
                import('./services/platform-integrations/microsoft').then(({ microsoftService }) => microsoftService.cleanup?.()),
                import('./services/file-upload').then(({ fileUploadService }) => fileUploadService.cleanup?.()).catch(() => {}),
                cleanupMCPServers()
              ]);
              logger.info('🧹 Services cleanup completed');
            } catch (serviceError) {
              logger.warn('⚠️ Error during service cleanup:', { error: (serviceError as Error).message });
            }

            clearTimeout(forceExitTimeout);
            logger.info('✅ Server closed successfully');
            process.exit(0);
          } catch (error) {
            logger.error('❌ Error during graceful shutdown:', { error: (error as Error).message });
            clearTimeout(forceExitTimeout);
            process.exit(1);
          }
        });

        // Force close active connections after a brief delay
        setTimeout(() => {
          if (activeConnections.size > 0) {
            logger.info(`🔌 Closing ${activeConnections.size} active connections`);
            activeConnections.forEach((connection) => {
              connection.destroy();
            });
          }
        }, 1000); // 1 second delay

      } catch (error) {
        logger.error('❌ Error initiating graceful shutdown:', { error: (error as Error).message });
        clearTimeout(forceExitTimeout);
        process.exit(1);
      }
    };

    // Handle different termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // Windows-specific: handle when console window is closed
    if (process.platform === "win32") {
      import('readline').then(({ createInterface }) => {
        const rl = createInterface({
          input: process.stdin,
          output: process.stdout
        });

        rl.on('SIGINT', () => {
          process.emit('SIGINT');
        });
      });
    }

    // Handle uncaught exceptions and unhandled rejections
    process.on('uncaughtException', (error) => {
      logger.error('💥 Uncaught Exception:', { error: error.message });
      console.error('Stack trace:', error.stack);
      gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('💥 Unhandled Rejection');
      console.error('Reason:', reason);
      console.error('Promise:', promise);
      gracefulShutdown('UNHANDLED_REJECTION');
    });

  } catch (error) {
    logger.error('❌ Failed to start server:', { error: (error as Error).message });
    process.exit(1);
  }
})();
