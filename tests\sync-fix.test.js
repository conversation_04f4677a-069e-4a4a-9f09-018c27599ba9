// Quick test to verify sync functionality is fixed
console.log('🔧 Testing Sync Functionality Fix...\n');

// Wait for server to start
setTimeout(async () => {
  try {
    console.log('1️⃣ Testing server health...');
    const healthResponse = await fetch('http://localhost:3000/api/diagnostic/health');
    if (healthResponse.ok) {
      console.log('✅ Server is running');
    } else {
      console.log('❌ Server not responding');
      return;
    }

    console.log('\n2️⃣ Testing new sync diagnostic endpoint...');
    const syncTestResponse = await fetch('http://localhost:3000/api/diagnostic/test-sync/1', {
      method: 'POST'
    });

    if (syncTestResponse.ok) {
      const syncData = await syncTestResponse.json();
      console.log('✅ Sync test endpoint working:', syncData.success ? 'SUCCESS' : 'FAILED');
      if (syncData.results) {
        console.log('   - Google credentials:', syncData.results.googleCredentials);
        console.log('   - Source folder access:', syncData.results.sourceFolderAccess);
        console.log('   - Notion setup:', syncData.results.notionSetup);
      }
    } else if (syncTestResponse.status === 404) {
      console.log('❌ Sync test endpoint not found - server may not have restarted properly');
    } else {
      console.log('❌ Sync test failed with status:', syncTestResponse.status);
    }

    console.log('\n3️⃣ Testing if NotionService has the new method...');
    // This will be tested when we try a real sync

    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Go to http://localhost:5000/debug');
    console.log('2. Test "Test Sync ID 1" - should work now');
    console.log('3. Re-authenticate your Google integration');
    console.log('4. Try running a real sync');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}, 3000);