// DEPRECATED: This file is being modularized for better maintainability
// New imports should use the modular structure from ./rag/
// This file is kept for backward compatibility during the migration

import OpenAI from "openai";
import { storage } from "../../storage/index.js";
import { embeddingService } from "../embedding-service.js";
import { functionToolsService } from "../function-tools.js";
import type { InsertChatMessage, InsertChatSession } from "../../../shared/index.js";

// Import the new modular RAG service facade
import { RAGServiceFacade } from "../rag/rag.facade";

/**
 * RAG Service for Retrieval-Augmented Generation
 * @deprecated Use RAGServiceFacade from ./rag/ instead
 */
class RAGService {
  private openai!: OpenAI;
  private initialized: boolean = false;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn(
        "OPENAI_API_KEY not found in environment. RAG features will be disabled.",
      );
      return;
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.initialized = true;
    console.log("RAG service initialized successfully");
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Create a new chat session
   */
  async createChatSession(
    title?: string,
    enabledSources?: string[],
    userId?: string
  ): Promise<any> {
    try {
      const sessionData: InsertChatSession = {
        title: title || "New Chat",
        enabledSources: enabledSources || [],
        userId: userId || "anonymous",
      };

      const session = await storage.createChatSession(sessionData);
      console.log(`Created chat session ${session.id}`);
      return session;
    } catch (error: any) {
      console.error("Error creating chat session:", error);
      throw error;
    }
  }

  /**
   * Send a message and get AI response with RAG context and agentic actions
   */
  async sendMessage(
    sessionId: number,
    content: string,
    enabledSources: string[] = []
  ): Promise<any> {
    if (!this.initialized) {
      throw new Error("RAG service not initialized");
    }

    try {
      console.log(`Processing message for session ${sessionId}`);
      console.log(`Enabled sources: ${enabledSources.join(", ")}`);

      // Store user message
      const userMessage: InsertChatMessage = {
        sessionId,
        role: "user",
        content,
        sourcesUsed: enabledSources,
        tokenCount: this.estimateTokenCount(content),
      };

      const savedUserMessage = await storage.createChatMessage(userMessage);

      // Get relevant context from enabled sources with configurable similarity
      const topK = this.getTopK();
      const rawChunks = await embeddingService.searchSimilarChunks(
        content,
        enabledSources,
        topK // Configurable similarity top K inspired by LlamaIndex
      );

      // Skip permission filtering for MVP (all files belong to same user)
      const relevantChunks = rawChunks;

      console.log(`Using TOP_K=${topK} for retrieval (configurable via environment)`);
      console.log(`Permission filtering: DISABLED for MVP - using all ${relevantChunks.length} chunks`);

      console.log(`Found ${relevantChunks.length} relevant chunks`);

      // Build context for ChatGPT
      const context = this.buildContextFromChunks(relevantChunks);

      // Get chat history for context
      const chatHistory = await storage.getChatMessages(sessionId, 10); // Last 10 messages

      // Generate AI response with agentic actions support
      const aiResponse = await this.generateAIResponseWithActions(content, context, chatHistory, enabledSources, relevantChunks);

      // Store AI message
      const aiMessage: InsertChatMessage = {
        sessionId,
        role: "assistant",
        content: aiResponse,
        sourcesUsed: enabledSources,
        relevantChunks: relevantChunks.map(chunk => ({
          id: chunk.id,
          fileId: chunk.fileId,
          fileName: chunk.fileName,
          similarity: chunk.similarity,
        })),
        tokenCount: this.estimateTokenCount(aiResponse),
        model: "gpt-4.1-nano-2025-04-14", // Fixed model name to match working version
      };

      const savedAiMessage = await storage.createChatMessage(aiMessage);

      return {
        userMessage: savedUserMessage,
        aiMessage: savedAiMessage,
        relevantChunks: relevantChunks.length,
        sourcesUsed: enabledSources,
      };

    } catch (error: any) {
      console.error("Error processing message:", error);
      throw error;
    }
  }

  /**
   * Generate AI response using ChatGPT with context and agentic actions
   * Inspired by LlamaIndex tutorial Part 3 - supports function calling
   */
  private async generateAIResponseWithActions(
    userMessage: string,
    context: string,
    chatHistory: any[],
    enabledSources: string[],
    relevantChunks: any[]
  ): Promise<string> {
    try {
      console.log(`[RAG] Generating AI response for: "${userMessage}"`);
      console.log(`[RAG] Context length: ${context.length} characters`);
      console.log(`[RAG] Chat history length: ${chatHistory.length} messages`);

      // Build conversation history with configurable system prompt
      const baseSystemPrompt = process.env.SYSTEM_PROMPT || "You are GPT Unify, an intelligent AI assistant with access to data sources from multiple platforms including Google Drive, meeting transcripts, uploaded files, and other integrated platforms.";

const messages: any[] = [
        {
          role: "system",
          content: `${baseSystemPrompt}

IMPORTANT IDENTITY INFORMATION:
- Your name is GPT Unify - an AI Assistant with both READ and WRITE capabilities
- You have access to data from multiple integrated platforms through the MeetSync platform
- You can search through meeting transcripts, documents, and files from connected sources
- You are part of the MeetSync ecosystem that syncs and analyzes meeting data
- You can access Google Drive files, meeting transcripts, and other connected data sources
- You can CREATE, SEARCH, and MANAGE files using your function tools

AGENTIC CAPABILITIES:
- You can create new files when users request it
- You can search for existing files by name, content, or description
- You can get detailed information about specific files
- You always follow the Human-in-the-Loop (HITL) pattern: Draft → Confirm → Execute
- For any write action, you MUST first create a draft and get user confirmation
- You have access to function tools for file operations

CURRENT SESSION CONTEXT:
- You have access to ${enabledSources.length} data source(s): ${enabledSources.join(", ")}
- Found ${relevantChunks.length} relevant document chunks for this query
- Available sources include Google Drive files, Microsoft Teams documents, uploaded files, and comprehensive document collections

${context ? `RELEVANT DOCUMENT CONTENT:

${context}

Use this information to provide accurate, helpful responses. Always reference which documents or sources you're using when providing information.` : `No specific document content found for this query. You can still help by explaining what data sources are available and how to access them.`}

RESPONSE GUIDELINES:
- NEVER say "give me a moment" or "let me search" - you already have the search results
- Provide direct, immediate answers based on available content
- When referencing information, mention the source file name and location when possible
- If asked about file locations, provide specific folder paths and file names from the metadata
- If you find relevant content, answer immediately with specific details
- If no relevant content is found, clearly explain what sources you searched and suggest alternatives
- Be conversational and helpful, not robotic
- Always remember you have real access to the user's files and documents
- When users describe files they're looking for, help identify them by name and location
- For file search queries, provide file names, locations, and brief descriptions of content
- If users ask "what files do you have access to", list the available sources and explain the types of files
- Always maintain context about what data sources are enabled and available`,
        },
      ];

      // Add recent chat history (excluding system messages)
      const recentHistory = chatHistory
        .filter(msg => msg.role !== "system")
        .slice(-8) // Last 8 messages for context
        .map(msg => ({
          role: msg.role,
          content: msg.content,
        }));

      messages.push(...recentHistory);

      // Add current user message
      messages.push({
        role: "user",
        content: userMessage,
      });

      console.log(`[RAG] Calling OpenAI with model: gpt-4.1-nano-2025-04-14`);
      console.log(`[RAG] Message count: ${messages.length}`);
      console.log(`[RAG] Context preview: ${context.substring(0, 200)}...`);

      // Get available function tools
      const tools = functionToolsService.getToolsForOpenAI();
      console.log(`[RAG] Available function tools: ${functionToolsService.getToolNames().join(', ')}`);

      const response = await this.openai.chat.completions.create({
        model: "gpt-4.1-nano-2025-04-14", // Fixed model name to match working version
        messages,
        max_tokens: 10000, // Increased from 1500 to 10000 for much longer, detailed responses
        temperature: 0.7,
        tools: tools.length > 0 ? tools : undefined, // Add function tools if available
        tool_choice: "auto", // Let the model decide when to use tools
      });

      console.log(`[RAG] OpenAI response received`);

      const message = response.choices[0].message;

      // Handle function calls if present
      if (message.tool_calls && message.tool_calls.length > 0) {
        console.log(`[RAG] Function calls detected: ${message.tool_calls.length}`);

        let functionResults = "";

        for (const toolCall of message.tool_calls) {
          if (toolCall.type === "function") {
            const functionName = toolCall.function.name;
            const functionArgs = JSON.parse(toolCall.function.arguments);

            console.log(`[RAG] Executing function: ${functionName}`);
            console.log(`[RAG] Function arguments:`, functionArgs);

            try {
              const result = await functionToolsService.executeTool(functionName, functionArgs);
              functionResults += `\n\n**Function Result (${functionName}):**\n${result}`;
            } catch (error: any) {
              console.error(`[RAG] Function execution error:`, error);
              functionResults += `\n\n**Function Error (${functionName}):**\n${error.message}`;
            }
          }
        }

        // If we have function results, combine with the assistant's message
        const assistantMessage = message.content || "";
        return assistantMessage + functionResults;
      }

      console.log(`[RAG] Response length: ${message.content?.length || 0} characters`);
      console.log(`[RAG] Response preview: ${message.content?.substring(0, 100)}...`);

      return message.content || "I apologize, but I couldn't generate a response.";

    } catch (error: any) {
      console.error("[RAG] Error generating AI response:", error);
      console.error("[RAG] Error details:", {
        message: error.message,
        status: error.status,
        type: error.type,
        code: error.code
      });
      throw new Error(`Failed to generate AI response: ${error.message}`);
    }
  }

  /**
   * Build context string from relevant chunks
   */
  private buildContextFromChunks(chunks: any[]): string {
    if (chunks.length === 0) {
      return "No relevant documents found.";
    }

    let context = "Relevant information from your documents:\n\n";

    chunks.forEach((chunk, index) => {
      context += `Document: ${chunk.fileName}\n`;
      context += `Platform: ${chunk.platform}\n`;
      if (chunk.similarity) {
        context += `Relevance: ${Math.round(chunk.similarity * 100)}%\n`;
      }
      context += `Content: ${chunk.content}\n`;
      if (index < chunks.length - 1) {
        context += "\n---\n\n";
      }
    });

    return context;
  }

  /**
   * Get available sources (integrations) for a user
   */
  async getAvailableSources(_userId?: string): Promise<any[]> {
    try {
      const integrations = await storage.getIntegrations();

      // Filter to connected integrations only
      const connectedIntegrations = integrations.filter(
        integration => integration.status === "connected" || integration.status === "configured"
      );

      const sources = connectedIntegrations.map(integration => ({
        id: integration.id.toString(),
        name: integration.name,
        type: integration.type,
        platform: integration.type,
        status: integration.status,
      }));

      // Add uploaded files as a virtual source if there are any uploaded files
      try {
        const uploadedFiles = await storage.getFiles('uploaded_files', undefined, 1, 0);
        if (uploadedFiles && uploadedFiles.length > 0) {
          sources.push({
            id: 'uploaded-files',
            name: 'Uploaded Files',
            type: 'uploaded-files',
            platform: 'uploaded_files',
            status: 'connected',
          });
        }
      } catch (error) {
        console.log('No uploaded files found or error checking:', error);
      }

      return sources;

    } catch (error: any) {
      console.error("Error getting available sources:", error);
      return [];
    }
  }

  /**
   * Get chat sessions for a user
   */
  async getChatSessions(userId?: string, limit: number = 20): Promise<any[]> {
    try {
      return await storage.getChatSessions(userId, limit);
    } catch (error: any) {
      console.error("Error getting chat sessions:", error);
      return [];
    }
  }

  /**
   * Get messages for a chat session
   */
  async getChatMessages(sessionId: number, limit: number = 50): Promise<any[]> {
    try {
      return await storage.getChatMessages(sessionId, limit);
    } catch (error: any) {
      console.error("Error getting chat messages:", error);
      return [];
    }
  }

  /**
   * Estimate token count for a text (rough approximation)
   */
  private estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Get configurable TOP_K for similarity search (inspired by LlamaIndex)
   */
  private getTopK(): number {
    return parseInt(process.env.TOP_K || "50");
  }

  /**
   * Delete a chat session and all its messages
   */
  async deleteChatSession(sessionId: number): Promise<boolean> {
    try {
      return await storage.deleteChatSession(sessionId);
    } catch (error: any) {
      console.error("Error deleting chat session:", error);
      return false;
    }
  }

  /**
   * Update chat session (e.g., change enabled sources)
   */
  async updateChatSession(
    sessionId: number,
    updates: { title?: string; enabledSources?: string[] }
  ): Promise<any> {
    try {
      return await storage.updateChatSession(sessionId, updates);
    } catch (error: any) {
      console.error("Error updating chat session:", error);
      throw error;
    }
  }

  /**
   * Search for files by description or content
   */
  async searchFilesByDescription(
    description: string,
    enabledSources: string[] = [],
    limit: number = 25 // Increased default from 10 to 25 for better file discovery
  ): Promise<any[]> {
    try {
      console.log(`Searching files by description: "${description}"`);

      // First, try vector search to find relevant content
      const relevantChunks = await embeddingService.searchSimilarChunks(
        description,
        enabledSources,
        limit * 4 // Increased multiplier from 2 to 4 for better file discovery with more chunks
      );

      // Group chunks by file and get file metadata
      const fileMap = new Map();

      for (const chunk of relevantChunks) {
        if (!fileMap.has(chunk.fileId)) {
          try {
            const file = await storage.getFile(chunk.fileId);
            if (file) {
              fileMap.set(chunk.fileId, {
                ...file,
                relevantChunks: [chunk],
                maxSimilarity: chunk.similarity || 0
              });
            }
          } catch (error) {
            console.error(`Error getting file ${chunk.fileId}:`, error);
          }
        } else {
          const existingFile = fileMap.get(chunk.fileId);
          existingFile.relevantChunks.push(chunk);
          existingFile.maxSimilarity = Math.max(existingFile.maxSimilarity, chunk.similarity || 0);
        }
      }

      // Convert to array and sort by relevance
      const results = Array.from(fileMap.values())
        .sort((a, b) => b.maxSimilarity - a.maxSimilarity)
        .slice(0, limit);

      console.log(`Found ${results.length} files matching description`);
      return results;

    } catch (error: any) {
      console.error("Error searching files by description:", error);
      return [];
    }
  }

  // =============================================================================
  // BATCH PROCESSING METHODS (OpenAI Batch API)
  // =============================================================================

  /**
   * Process multiple files using batch API (50% cheaper, no rate limits)
   */
  async processBatchEmbeddings(fileIds: number[]): Promise<string> {
    try {
      console.log(`Starting batch processing for ${fileIds.length} files`);

      // Get file contents
      const files = [];
      for (const fileId of fileIds) {
        const file = await storage.getFile(fileId);
        if (file && file.fileContent) {
          files.push({
            fileId,
            content: file.fileContent,
          });
        }
      }

      if (files.length === 0) {
        throw new Error("No files with content found for batch processing");
      }

      // Submit batch job
      // const batchId = await batchEmbeddingService.processFilesForEmbeddingsBatch(files);
      const batchId = 'deprecated-batch-id';

      console.log(`Batch job submitted: ${batchId}`);
      console.log(`Check status with: checkBatchStatus("${batchId}")`);

      return batchId;

    } catch (error: any) {
      console.error("Error in batch processing:", error);
      throw error;
    }
  }

  /**
   * Check status of batch processing job
   */
  async checkBatchStatus(batchId: string): Promise<any> {
    try {
      // return await batchEmbeddingService.checkBatchStatus(batchId);
      return { status: 'deprecated', message: 'This service is deprecated' };
    } catch (error: any) {
      console.error("Error checking batch status:", error);
      throw error;
    }
  }

  /**
   * Smart processing: Choose between real-time and batch based on context
   */
  async processFilesSmart(fileIds: number[], options: {
    urgent?: boolean;
    userWaiting?: boolean;
    bulkOperation?: boolean;
  } = {}): Promise<{ method: string; result: any }> {
    const { urgent = false, userWaiting = false, bulkOperation = false } = options;

    // Decision logic
    const shouldUseBatch = this.shouldUseBatchProcessing(fileIds.length, { urgent, userWaiting, bulkOperation });

    if (shouldUseBatch) {
      console.log(`Using BATCH processing for ${fileIds.length} files (background)`);
      const batchId = await this.processBatchEmbeddings(fileIds);
      return {
        method: 'batch',
        result: {
          batchId,
          message: `Files submitted for background processing. Check status with batch ID: ${batchId}`,
          estimatedTime: '10 minutes to 6 hours',
          cost: '50% cheaper than real-time'
        }
      };
    } else {
      console.log(`Using REAL-TIME processing for ${fileIds.length} files (immediate)`);
      // Process files one by one with the queue system
      const results = [];
      for (const fileId of fileIds) {
        try {
          const file = await storage.getFile(fileId);
          if (file && file.fileContent) {
            await embeddingService.processFileForEmbeddings(fileId, file.fileContent);
            results.push({ fileId, status: 'completed' });
          }
        } catch (error: any) {
          results.push({ fileId, status: 'failed', error: error.message });
        }
      }
      return {
        method: 'real-time',
        result: {
          processed: results,
          message: 'Files processed immediately',
          estimatedTime: 'completed',
          cost: 'standard rate'
        }
      };
    }
  }

  /**
   * Decision logic for processing method
   */
  private shouldUseBatchProcessing(fileCount: number, options: {
    urgent?: boolean;
    userWaiting?: boolean;
    bulkOperation?: boolean;
  }): boolean {
    const { urgent, userWaiting, bulkOperation } = options;

    // Force real-time if user is waiting
    if (userWaiting || urgent) {
      return false;
    }

    // Force batch for bulk operations
    if (bulkOperation) {
      return true;
    }

    // Use batch for large numbers of files
    const batchThreshold = parseInt(process.env.BATCH_PROCESSING_THRESHOLD || "5");
    if (fileCount >= batchThreshold) {
      return true;
    }

    // Check if batch processing is enabled
    const batchEnabled = process.env.ENABLE_BATCH_PROCESSING === 'true';
    if (!batchEnabled) {
      return false;
    }

    // Default to real-time for small numbers
    return false;
  }

  /**
   * Auto-detect and process files that need embeddings using smart logic
   */
  async processPendingFilesSmart(options: {
    urgent?: boolean;
    userWaiting?: boolean;
  } = {}): Promise<{ method: string; result: any } | null> {
    try {
      // Get all files without embeddings
      const allFiles = await storage.getFiles();
      const filesNeedingEmbeddings = [];

      for (const file of allFiles) {
        const hasEmbeddings = await embeddingService.hasEmbeddings(file.id);
        if (!hasEmbeddings && file.fileContent) {
          filesNeedingEmbeddings.push(file.id);
        }
      }

      if (filesNeedingEmbeddings.length === 0) {
        console.log("No files need embedding processing");
        return null;
      }

      console.log(`Found ${filesNeedingEmbeddings.length} files needing embeddings`);

      // Use smart processing
      return await this.processFilesSmart(filesNeedingEmbeddings, {
        ...options,
        bulkOperation: true // This is a bulk operation
      });

    } catch (error: any) {
      console.error("Error processing pending files:", error);
      throw error;
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  async processPendingFilesBatch(): Promise<string | null> {
    const result = await this.processPendingFilesSmart({ userWaiting: false });
    return result?.method === 'batch' ? result.result.batchId : null;
  }
}

// DEPRECATED: Use the new modular RAG service instead
// This is kept for backward compatibility during migration

// Use the new modular RAG service facade
export const ragService = new RAGServiceFacade();

// Legacy RAG service class (kept for reference during migration)
export const legacyRagService = new RAGService();
