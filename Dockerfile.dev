# =============================================================================
# MeetSync Development Dockerfile
# =============================================================================

FROM node:20-alpine AS development

# Install system dependencies for native modules and development tools
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    git \
    curl \
    bash

# Create non-root user for development
RUN addgroup -g 1001 -S nodejs && \
    adduser -S meetsync -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Create necessary directories and set ownership
RUN mkdir -p /app/node_modules /app/logs /app/uploads /app/temp && \
    chown -R meetsync:nodejs /app

# Switch to non-root user
USER meetsync

# Copy package files and install dependencies
COPY --chown=meetsync:nodejs package*.json ./
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    npm config set fetch-retries 3 && \
    npm ci --verbose

# Copy source code
COPY --chown=meetsync:nodejs . .

# Expose ports
EXPOSE 8080 5173 9229

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# Start development server with hot reload
CMD ["npm", "run", "dev"] 