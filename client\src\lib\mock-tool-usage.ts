import { ToolUsage } from '../components/chat/ToolUsageBadge';

// Mock tool usage generator for demonstration
export class MockToolUsageGenerator {
  private static toolTemplates = [
    {
      toolName: 'search_files',
      serverName: 'Google Drive',
      results: [
        'Found 5 relevant documents matching your query',
        'Located 3 PDF files and 2 Word documents',
        'Discovered 8 files in the specified folder',
        'Search completed - 12 files found across 3 folders'
      ],
      errors: [
        'Search quota exceeded - please try again later',
        'Invalid search parameters provided',
        'Network timeout while searching files'
      ]
    },
    {
      toolName: 'get_file_content',
      serverName: 'Google Drive',
      results: [
        'Successfully extracted content from document.pdf (2,340 words)',
        'Retrieved full text from presentation.pptx (15 slides)',
        'Loaded document content - ready for analysis',
        'File content extracted successfully (1,850 characters)'
      ],
      errors: [
        'File is password protected and cannot be accessed',
        'Document format not supported for content extraction',
        'File too large to process (exceeds 10MB limit)'
      ]
    },
    {
      toolName: 'list_files',
      serverName: 'Google Drive',
      results: [
        'Listed 23 files in the current directory',
        'Found 15 documents and 8 images',
        'Directory contains 31 items (files and folders)',
        'Retrieved file list successfully'
      ],
      errors: [
        'Access denied to the specified folder',
        'Folder not found or has been deleted',
        'Permission error - cannot list folder contents'
      ]
    },
    {
      toolName: 'get_messages',
      serverName: 'Microsoft Teams',
      results: [
        'Retrieved 45 messages from the last 7 days',
        'Found 12 relevant conversations in the channel',
        'Loaded message history successfully',
        'Extracted 28 messages containing your keywords'
      ],
      errors: [
        'OAuth token expired - please re-authenticate',
        'Channel not found or access denied',
        'Rate limit exceeded for Teams API'
      ]
    },
    {
      toolName: 'search_content',
      serverName: 'Microsoft Teams',
      results: [
        'Found 8 relevant messages across 3 channels',
        'Located 15 files shared in team conversations',
        'Search completed across all accessible teams',
        'Discovered 6 meeting transcripts with your keywords'
      ],
      errors: [
        'Search service temporarily unavailable',
        'Invalid search query format',
        'Insufficient permissions to search all channels'
      ]
    },
    {
      toolName: 'upload_file',
      serverName: 'File Upload',
      results: [
        'File uploaded successfully and processed',
        'Document analyzed and indexed for search',
        'Upload completed - file ready for queries',
        'File processed and added to knowledge base'
      ],
      errors: [
        'File size exceeds maximum limit (50MB)',
        'Unsupported file format',
        'Upload failed due to network error'
      ]
    },
    {
      toolName: 'get_file_content',
      serverName: 'File Upload',
      results: [
        'Successfully extracted text from uploaded PDF',
        'Document content loaded and ready for analysis',
        'File processed - 3,240 words extracted',
        'Content extraction completed successfully'
      ],
      errors: [
        'File corrupted or unreadable',
        'Content extraction failed - unsupported format',
        'Processing timeout - file too complex'
      ]
    }
  ];

  static generateToolUsage(messageId: string, isAIMessage: boolean = true): ToolUsage[] {
    // Only generate tool usage for AI messages
    if (!isAIMessage) return [];

    // Randomly decide if this message used tools (70% chance)
    if (Math.random() > 0.7) return [];

    // Generate 1-3 tool usages
    const numTools = Math.floor(Math.random() * 3) + 1;
    const usages: ToolUsage[] = [];

    for (let i = 0; i < numTools; i++) {
      const template = this.toolTemplates[Math.floor(Math.random() * this.toolTemplates.length)];
      const success = Math.random() > 0.15; // 85% success rate
      const duration = Math.floor(Math.random() * 2000) + 200; // 200-2200ms

      const usage: ToolUsage = {
        toolName: template.toolName,
        serverName: template.serverName,
        timestamp: new Date(Date.now() - Math.floor(Math.random() * 10000)), // Random recent time
        duration,
        success,
        result: success ? template.results[Math.floor(Math.random() * template.results.length)] : undefined,
        error: !success ? template.errors[Math.floor(Math.random() * template.errors.length)] : undefined
      };

      usages.push(usage);
    }

    return usages;
  }

  static generateActiveTools(): string[] {
    const possibleTools = [
      'search_files',
      'get_file_content',
      'list_files',
      'get_messages',
      'search_content',
      'upload_file'
    ];

    // Randomly select 1-2 active tools
    const numActive = Math.floor(Math.random() * 2) + 1;
    const activeTools: string[] = [];

    for (let i = 0; i < numActive; i++) {
      const tool = possibleTools[Math.floor(Math.random() * possibleTools.length)];
      if (!activeTools.includes(tool)) {
        activeTools.push(tool);
      }
    }

    return activeTools;
  }
}
