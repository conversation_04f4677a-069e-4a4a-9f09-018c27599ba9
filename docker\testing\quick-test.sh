#!/bin/bash

echo "🚀 MeetSync Docker Quick Test"
echo "============================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
PASSED=0
FAILED=0

# Test function
test_check() {
    local name="$1"
    local command="$2"
    
    echo -n "Testing $name... "
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}"
        ((FAILED++))
    fi
}

echo "Running quick Docker validation tests..."
echo ""

# Basic connectivity tests
test_check "Application Health" "curl -s http://localhost:8080/health | jq -e '.status == \"healthy\"'"
test_check "Metrics Endpoint" "curl -s http://localhost:8080/metrics | grep -q 'http_requests_total'"
test_check "System Info" "curl -s http://localhost:8080/api/system/info | jq -e '.service'"
test_check "Redis Sessions" "curl -s http://localhost:8080/api/system/info | jq -e '.session.type == \"redis\"'"
test_check "Database Health" "curl -s http://localhost:8080/api/health | jq -e '.checks.database.status == \"healthy\"'"

# Container tests
test_check "Container Health" "docker ps --format '{{.Status}}' | grep -q healthy"
test_check "App Container Running" "docker ps | grep -q meetsync-app-dev"
test_check "Database Container" "docker ps | grep -q meetsync-postgres-dev"
test_check "Redis Container" "docker ps | grep -q meetsync-redis-dev"

# Security tests
test_check "Non-root Execution" "docker exec meetsync-app-dev whoami | grep -q meetsync"
test_check "File Permissions" "docker exec meetsync-app-dev stat -c '%U' /app | grep -q meetsync"

echo ""
echo "Quick Test Results:"
echo "=================="
echo -e "${GREEN}✅ Passed: $PASSED${NC}"
echo -e "${RED}❌ Failed: $FAILED${NC}"

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All quick tests passed! Your Docker setup looks good.${NC}"
    echo -e "${BLUE}💡 Run './docker/testing/run-all-tests.sh' for comprehensive testing.${NC}"
else
    echo -e "${YELLOW}⚠️  Some tests failed. Run './docker/testing/run-all-tests.sh' for detailed analysis.${NC}"
fi

echo ""
echo "Service URLs:"
echo "- Application: http://localhost:8080"
echo "- Health Check: http://localhost:8080/health"
echo "- Metrics: http://localhost:8080/metrics"
echo "- System Info: http://localhost:8080/api/system/info" 