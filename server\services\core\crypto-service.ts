import crypto from 'crypto';
import dotenv from "dotenv";
dotenv.config();

/**
 * Service for encrypting and decrypting sensitive data
 */
class CryptoService {
  private algorithm = 'aes-256-cbc';
  private key: Buffer;
  private ivLength = 16;

  constructor() {
    // Get encryption key from environment
    const encryptionKey = process.env.ENCRYPTION_KEY;
    
    if (!encryptionKey) {
      console.warn('ENCRYPTION_KEY not found in environment variables. Using a default key for development. DO NOT USE IN PRODUCTION!');
      // Default key for development - 32 bytes (256 bits)
      this.key = crypto.scryptSync('default-development-key-do-not-use-in-production', 'salt', 32);
    } else {
      // Create a fixed-length key using the provided encryption key
      this.key = crypto.scryptSync(encryptionKey, 'salt', 32);
    }
  }

  /**
   * Encrypt a string value
   * @param text String to encrypt
   * @returns Encrypted string in format: iv:encryptedData (base64)
   */
  async encrypt(text: string): Promise<string> {
    try {
      console.log('[CRYPTO] Encrypting data, text length:', text.length);
      
      // Generate a random initialization vector
      const iv = crypto.randomBytes(this.ivLength);
      
      // Create cipher
      const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);
      
      // Encrypt the text
      let encrypted = cipher.update(text, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      // Return IV and encrypted data as a single string (IV:encrypted)
      const result = `${iv.toString('base64')}:${encrypted}`;
      console.log('[CRYPTO] Encryption successful, result length:', result.length);
      
      return result;
    } catch (error) {
      console.error('[CRYPTO] Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt an encrypted string
   * @param encryptedText String in format: iv:encryptedData (base64)
   * @returns Decrypted string
   */
  async decrypt(encryptedText: string): Promise<string> {
    try {
      console.log('[CRYPTO] Attempting to decrypt data, input length:', encryptedText.length);
      console.log('[CRYPTO] Encrypted text preview:', encryptedText.substring(0, 50) + '...');
      
      // Split the IV and encrypted data
      const [ivBase64, encryptedData] = encryptedText.split(':');
      
      if (!ivBase64 || !encryptedData) {
        console.error('[CRYPTO] Invalid format - missing IV or encrypted data');
        console.error('[CRYPTO] Split result:', { ivBase64, encryptedData });
        throw new Error('Invalid encrypted text format - expected "iv:encryptedData"');
      }
      
      console.log('[CRYPTO] IV length:', ivBase64.length, 'Encrypted data length:', encryptedData.length);
      
      // Convert the IV from base64 to Buffer
      let iv: Buffer;
      try {
        iv = Buffer.from(ivBase64, 'base64');
        if (iv.length !== this.ivLength) {
          throw new Error(`Invalid IV length: expected ${this.ivLength}, got ${iv.length}`);
        }
      } catch (error) {
        console.error('[CRYPTO] Failed to decode IV from base64:', error);
        throw new Error('Invalid IV format');
      }
      
      // Create decipher
      const decipher = crypto.createDecipheriv(this.algorithm, this.key, iv);
      
      // Decrypt the data
      let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      console.log('[CRYPTO] Decryption successful, result length:', decrypted.length);
      
      return decrypted;
    } catch (error: any) {
      console.error('[CRYPTO] Decryption error:', error);
      console.error('[CRYPTO] Input that failed:', encryptedText.substring(0, 100));
      throw new Error(`Failed to decrypt data: ${error.message}`);
    }
  }

  /**
   * Validate that a string can be decrypted without actually decrypting it
   * @param encryptedText Encrypted text to validate
   * @returns Boolean indicating if the format is valid
   */
  validateEncryptedFormat(encryptedText: string): boolean {
    try {
      const [ivBase64, encryptedData] = encryptedText.split(':');
      
      if (!ivBase64 || !encryptedData) {
        return false;
      }
      
      // Try to decode the IV
      const iv = Buffer.from(ivBase64, 'base64');
      if (iv.length !== this.ivLength) {
        return false;
      }
      
      // Try to decode the encrypted data (just check if it's valid base64)
      Buffer.from(encryptedData, 'base64');
      
      return true;
    } catch (error: any) {
      return false;
    }
  }
}

export const cryptoService = new CryptoService();