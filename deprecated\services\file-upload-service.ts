// DEPRECATED: This file is being modularized for better maintainability
// New imports should use the modular structure from ./file-upload/
// This file is kept for backward compatibility during the migration

import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { storage } from '../../storage/index.js';
import { embeddingService } from '../embedding-service.js';
import mammoth from 'mammoth';

// Import the new modular File Upload service facade
import { FileUploadServiceFacade } from "../file-upload/file-upload.facade";

// Configure multer for file uploads
const uploadDir = path.join(process.cwd(), 'uploads');

// Ensure upload directory exists
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const storage_config = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  }
});

// File filter to accept business file types
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = [
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'application/rtf',
    
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    
    // Audio/Video
    'audio/mpeg',
    'audio/wav',
    'audio/mp4',
    'video/mp4',
    'video/mpeg',
    'video/quicktime',
    
    // Archives
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    
    // Other
    'application/json',
    'application/xml',
    'text/xml'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} not supported. Please upload business documents, images, audio, or video files.`));
  }
};

export const upload = multer({
  storage: storage_config,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Max 10 files at once
  }
});

/**
 * Service for handling file uploads and processing
 * @deprecated Use FileUploadServiceFacade from ./file-upload/ instead
 */
export class FileUploadService {
  
  /**
   * Process uploaded file and create database record
   */
  async processUploadedFile(file: Express.Multer.File, userId?: string): Promise<any> {
    try {
      console.log(`Processing uploaded file: ${file.originalname}`);
      
      // Determine file type based on extension and mime type
      const fileType = this.determineFileType(file);
      
      // Create file record in database
      const fileRecord = await storage.createFile({
        externalId: `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        fileName: file.originalname,
        fileType,
        mimeType: file.mimetype,
        fileSize: file.size,
        platform: 'uploaded_files',
        fileUrl: file.path,
        downloadUrl: file.path,
        userId: userId || 'anonymous',
        status: 'active',
        extractedMetadata: {
          uploadedAt: new Date().toISOString(),
          originalPath: file.path,
          uploadedBy: userId || 'anonymous'
        }
      });

      console.log(`Created file record with ID: ${fileRecord.id}`);

      // Extract text content and process for embeddings
      await this.processFileForEmbeddings(fileRecord.id, file.path, fileType);

      return fileRecord;
    } catch (error) {
      console.error('Error processing uploaded file:', error);
      throw error;
    }
  }

  /**
   * Determine file type from extension and mime type
   */
  private determineFileType(file: Express.Multer.File): string {
    const ext = path.extname(file.originalname).toLowerCase();
    const mimeType = file.mimetype;

    // Document types
    if (mimeType.includes('pdf') || ext === '.pdf') return 'pdf';
    if (mimeType.includes('word') || ['.doc', '.docx'].includes(ext)) return 'doc';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet') || ['.xls', '.xlsx'].includes(ext)) return 'spreadsheet';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation') || ['.ppt', '.pptx'].includes(ext)) return 'presentation';
    if (mimeType.includes('text') || ['.txt', '.csv', '.rtf'].includes(ext)) return 'text';
    
    // Media types
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('video/')) return 'video';
    
    // Archive types
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) return 'archive';
    
    // Default
    return 'document';
  }

  /**
   * Extract text content from file and process for embeddings
   */
  private async processFileForEmbeddings(fileId: number, filePath: string, fileType: string): Promise<void> {
    try {
      console.log(`Extracting text from ${fileType} file: ${filePath}`);
      
      let textContent = '';
      
      // Extract text based on file type
      switch (fileType) {
        case 'text':
          textContent = await this.extractTextFromTextFile(filePath);
          break;
        case 'pdf':
          textContent = await this.extractTextFromPDF(filePath);
          break;
        case 'doc':
          textContent = await this.extractTextFromDoc(filePath);
          break;
        case 'spreadsheet':
          textContent = await this.extractTextFromSpreadsheet(filePath);
          break;
        case 'presentation':
          textContent = await this.extractTextFromPresentation(filePath);
          break;
        default:
          console.log(`Text extraction not implemented for file type: ${fileType}`);
          textContent = `File: ${path.basename(filePath)} (${fileType}) - Content not extracted but file is searchable by name and metadata.`;
          break;
      }

      if (textContent.trim().length > 0) {
        console.log(`Extracted ${textContent.length} characters of text`);
        
        // Process for embeddings
        await embeddingService.processFileForEmbeddings(fileId, textContent);
        console.log(`Successfully processed embeddings for file ${fileId}`);
      } else {
        console.log(`No text content extracted from file ${fileId}`);
      }
    } catch (error) {
      console.error(`Error processing file ${fileId} for embeddings:`, error);
      // Don't throw - we still want the file record to exist even if embedding fails
    }
  }

  /**
   * Extract text from plain text files
   */
  private async extractTextFromTextFile(filePath: string): Promise<string> {
    try {
      return fs.readFileSync(filePath, 'utf-8');
    } catch (error) {
      console.error('Error reading text file:', error);
      return '';
    }
  }

  /**
   * Extract text from PDF files using pdfjs-dist
   */
  private async extractTextFromPDF(filePath: string): Promise<string> {
    try {
      // Dynamic import to avoid module loading issues
      const pdfjsLib = await import('pdfjs-dist');

      const data = new Uint8Array(fs.readFileSync(filePath));
      const pdf = await pdfjsLib.getDocument({ data }).promise;

      let fullText = '';

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');

        fullText += `Page ${pageNum}:\n${pageText}\n\n`;
      }

      return fullText.trim();
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      // Fallback to metadata if extraction fails
      const fileName = path.basename(filePath);
      const stats = fs.statSync(filePath);
      return `PDF Document: ${fileName} (${Math.round(stats.size / 1024)}KB) - Text extraction failed, but file is searchable by name and metadata.`;
    }
  }

  /**
   * Extract text from Word documents using mammoth
   */
  private async extractTextFromDoc(filePath: string): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value;
    } catch (error) {
      console.error('Error extracting text from Word document:', error);
      return `Word document: ${path.basename(filePath)} (text extraction failed)`;
    }
  }

  /**
   * Extract text from spreadsheet files (placeholder)
   */
  private async extractTextFromSpreadsheet(filePath: string): Promise<string> {
    // TODO: Implement Excel/spreadsheet text extraction
    console.log('Spreadsheet text extraction not yet implemented');
    return `Spreadsheet file: ${path.basename(filePath)} - Contains data tables and formulas`;
  }

  /**
   * Extract text from presentation files (placeholder)
   */
  private async extractTextFromPresentation(filePath: string): Promise<string> {
    // TODO: Implement PowerPoint/presentation text extraction
    console.log('Presentation text extraction not yet implemented');
    return `Presentation file: ${path.basename(filePath)} - Contains slides and content`;
  }

  /**
   * Delete uploaded file and its database record
   */
  async deleteUploadedFile(fileId: number): Promise<boolean> {
    try {
      const file = await storage.getFile(fileId);
      if (!file || file.platform !== 'uploaded_files') {
        return false;
      }

      // Delete file chunks first
      await storage.deleteFileChunks(fileId);

      // Delete physical file
      if (file.fileUrl && fs.existsSync(file.fileUrl)) {
        fs.unlinkSync(file.fileUrl);
      }

      // Delete database record
      return await storage.deleteFile(fileId);
    } catch (error) {
      console.error('Error deleting uploaded file:', error);
      return false;
    }
  }
}

// DEPRECATED: Use the new modular File Upload service instead
// This is kept for backward compatibility during migration

// Use the new modular File Upload service facade
export const fileUploadService = new FileUploadServiceFacade();

// Legacy File Upload service class (kept for reference during migration)
export const legacyFileUploadService = new FileUploadService();
