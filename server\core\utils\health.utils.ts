import { checkAllHealth, getAllConfigInfo } from '../config';

/**
 * Health check utilities
 */

export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy';
      error?: string;
    };
    openai: {
      status: 'healthy' | 'unhealthy';
      error?: string;
    };
    environment: {
      status: 'healthy' | 'unhealthy';
      errors?: string[];
    };
  };
  system: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
  };
  config: any;
}

/**
 * Get system memory usage
 */
const getMemoryUsage = () => {
  const usage = process.memoryUsage();
  const total = usage.heapTotal;
  const used = usage.heapUsed;
  
  return {
    used: Math.round(used / 1024 / 1024), // MB
    total: Math.round(total / 1024 / 1024), // MB
    percentage: Math.round((used / total) * 100),
  };
};

/**
 * Get CPU usage (simplified)
 */
const getCpuUsage = () => {
  const usage = process.cpuUsage();
  const total = usage.user + usage.system;
  
  return {
    usage: Math.round(total / 1000), // Convert to milliseconds
  };
};

/**
 * Get application version
 */
const getAppVersion = (): string => {
  // Hardcode version to avoid require() issues
  return '1.0.0';
};

/**
 * Calculate uptime in seconds
 */
const getUptime = (): number => {
  return Math.floor(process.uptime());
};

/**
 * Perform comprehensive health check
 */
export const performHealthCheck = async (): Promise<HealthCheckResult> => {
  const startTime = Date.now();

  // Get system info first (these should never fail)
  const memory = getMemoryUsage();
  const cpu = getCpuUsage();
  const uptime = getUptime();
  const version = getAppVersion();

  try {
    // Check all services with individual error handling
    const healthResults = await checkAllHealth();
    const config = await getAllConfigInfo();

    console.log('Health results from checkAllHealth:', healthResults);

    // Determine overall status more reliably
    let status: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';

    // Check each service individually for more accurate status
    const isDatabaseHealthy = healthResults.services.database.healthy;
    const isOpenAIHealthy = healthResults.services.openai.healthy;
    const isEnvironmentHealthy = healthResults.services.environment.valid;

    // Count working services for better status determination
    const workingServices = [isDatabaseHealthy, isOpenAIHealthy, isEnvironmentHealthy].filter(Boolean).length;
    const totalServices = 3;

    if (workingServices === totalServices) {
      status = 'healthy';
    } else if (workingServices > 0) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    // Only mark as degraded for very high memory usage (98%+)
    // 90-95% is normal for Node.js applications
    if (memory.percentage > 98) {
      status = status === 'healthy' ? 'degraded' : status;
    }

    const result: HealthCheckResult = {
      status,
      timestamp: new Date().toISOString(),
      uptime,
      version,
      services: {
        database: {
          status: healthResults.services.database.healthy ? 'healthy' : 'unhealthy',
          error: healthResults.services.database.error,
        },
        openai: {
          status: healthResults.services.openai.healthy ? 'healthy' : 'unhealthy',
          error: healthResults.services.openai.error,
        },
        environment: {
          status: healthResults.services.environment.valid ? 'healthy' : 'unhealthy',
          errors: healthResults.services.environment.errors,
        },
      },
      system: {
        memory,
        cpu,
      },
      config,
    };

    const duration = Date.now() - startTime;
    console.log(`Health check completed in ${duration}ms - Status: ${status}`);

    return result;

  } catch (error) {
    console.error('Health check failed with exception:', error);

    // Try to get individual service health with fallback error handling
    let databaseHealth: { healthy: boolean; error?: string } = { healthy: false, error: 'Health check failed' };
    let openaiHealth: { healthy: boolean; error?: string } = { healthy: false, error: 'Health check failed' };
    let environmentHealth: { valid: boolean; errors: string[] } = { valid: false, errors: ['Health check failed'] };

    try {
      const { checkDatabaseHealth } = await import('../config/database.config');
      const dbHealth = await checkDatabaseHealth();
      databaseHealth = { healthy: dbHealth.healthy, error: dbHealth.error };
    } catch (dbError) {
      console.error('Database health check failed:', dbError);
      databaseHealth = {
        healthy: false,
        error: dbError instanceof Error ? dbError.message : 'Database health check failed'
      };
    }

    try {
      const { checkOpenAIHealth } = await import('../config/openai.config');
      const aiHealth = await checkOpenAIHealth();
      openaiHealth = { healthy: aiHealth.healthy, error: aiHealth.error };
    } catch (aiError) {
      console.error('OpenAI health check failed:', aiError);
      openaiHealth = {
        healthy: false,
        error: aiError instanceof Error ? aiError.message : 'OpenAI health check failed'
      };
    }

    try {
      const { validateEnvironment } = await import('../config/environment.config');
      environmentHealth = validateEnvironment();
    } catch (envError) {
      console.error('Environment validation failed:', envError);
      environmentHealth = {
        valid: false,
        errors: [envError instanceof Error ? envError.message : 'Environment validation failed']
      };
    }

    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime,
      version,
      services: {
        database: {
          status: databaseHealth.healthy ? 'healthy' : 'unhealthy',
          error: databaseHealth.error
        },
        openai: {
          status: openaiHealth.healthy ? 'healthy' : 'unhealthy',
          error: openaiHealth.error
        },
        environment: {
          status: environmentHealth.valid ? 'healthy' : 'unhealthy',
          errors: environmentHealth.errors
        },
      },
      system: {
        memory,
        cpu,
      },
      config: {},
    };
  }
};

/**
 * Simple readiness check
 */
export const performReadinessCheck = async (): Promise<{ ready: boolean; error?: string }> => {
  try {
    const health = await checkAllHealth();
    
    // For readiness, we need at least the environment to be valid
    const ready = health.services.environment.valid;
    
    return {
      ready,
      error: ready ? undefined : 'Environment configuration invalid',
    };
    
  } catch (error) {
    return {
      ready: false,
      error: error instanceof Error ? error.message : 'Readiness check failed',
    };
  }
};

/**
 * Simple liveness check
 */
export const performLivenessCheck = (): { alive: boolean } => {
  // Basic liveness check - if we can execute this function, we're alive
  return { alive: true };
};

/**
 * Get service status summary
 */
export const getServiceStatus = async () => {
  const health = await performHealthCheck();
  
  return {
    status: health.status,
    services: Object.entries(health.services).map(([name, service]) => ({
      name,
      status: service.status,
      error: (service as any).error || (service as any).errors?.join(', '),
    })),
    uptime: health.uptime,
    version: health.version,
  };
};
