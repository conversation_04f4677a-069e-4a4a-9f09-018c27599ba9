import { storage } from '../../../storage/index.js';
import { 
  BaseToolCategory, 
  ToolCategory, 
  FunctionTool,
  ToolExecutionContext 
} from '../base/function-tool.interface';

/**
 * Dynamic tool generation category
 * Creates tools based on available integrations and runtime capabilities
 */
export class DynamicToolGeneratorCategory extends BaseToolCategory {
  constructor() {
    super(ToolCategory.DYNAMIC);
  }

  initialize(): void {
    this.log('info', 'Initializing dynamic tool generator...');
    
    // Create platform-specific tools based on available integrations
    this.createPlatformSpecificTools();
    
    this.log('info', `Initialized ${this.tools.size} dynamic tools`);
  }

  /**
   * Create platform-specific tools based on available integrations
   */
  private createPlatformSpecificTools(): void {
    // For MVP, we'll create tools for available platforms
    const availablePlatforms = ['google_drive', 'uploaded_files'];

    availablePlatforms.forEach(platform => {
      this.registerTool(this.createPlatformSearchTool(platform));
    });
  }

  /**
   * Create a platform-specific search tool
   */
  private createPlatformSearchTool(platform: string): FunctionTool {
    const platformDisplayName = platform.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    const toolName = `search${platform.charAt(0).toUpperCase() + platform.slice(1).replace('_', '')}Files`;
    
    return {
      name: toolName,
      description: `Search for files specifically in ${platformDisplayName}`,
      parameters: this.createParameters({
        query: {
          type: "string",
          description: `Search query for ${platformDisplayName} files`,
        },
        fileType: {
          type: "string",
          description: "Optional: specific file type to search for",
        },
        limit: {
          type: "number",
          description: "Maximum number of results (default: 10)",
        },
      }, ["query"]),
      handler: (params) => this.handlePlatformSearch(params, platform, platformDisplayName),
    };
  }

  /**
   * Handle platform-specific search
   */
  private async handlePlatformSearch(params: any, platform: string, platformDisplayName: string): Promise<string> {
    const { query, fileType, limit = 10 } = params;

    this.log('info', `Searching ${platformDisplayName} files with query: ${query}`, { fileType, limit });

    try {
      const files = await storage.searchFiles(query, platform, fileType);
      const limitedFiles = files.slice(0, limit);

      if (limitedFiles.length === 0) {
        return `🔍 **No files found** in ${platformDisplayName} matching "${query}".`;
      }

      let result = `🔍 **Found ${limitedFiles.length} file(s)** in ${platformDisplayName} matching "${query}":\n\n`;

      limitedFiles.forEach((file, index) => {
        result += `**${index + 1}. ${file.fileName}**\n`;
        result += `   - Type: ${file.fileType}\n`;
        result += `   - Size: ${file.fileSize || 'Unknown'} bytes\n`;
        result += `   - Modified: ${file.lastModified || file.updatedAt}\n\n`;
      });

      this.log('info', `Platform search completed: found ${limitedFiles.length} files in ${platformDisplayName}`);
      return result;
    } catch (error: any) {
      this.log('error', `Search error in ${platformDisplayName} for query ${query}:`, error);
      return `❌ **Search Error in ${platformDisplayName}:** ${error.message}`;
    }
  }

  /**
   * Generate a new tool at runtime
   */
  generateRuntimeTool(toolConfig: {
    name: string;
    description: string;
    parameters: any;
    handler: (params: any) => Promise<string> | string;
  }): FunctionTool {
    this.log('info', `Generating runtime tool: ${toolConfig.name}`);
    
    const tool: FunctionTool = {
      name: toolConfig.name,
      description: toolConfig.description,
      parameters: toolConfig.parameters,
      handler: toolConfig.handler
    };

    this.registerTool(tool);
    return tool;
  }

  /**
   * Generate platform search tools for a new platform
   */
  generatePlatformTools(platform: string): FunctionTool[] {
    this.log('info', `Generating tools for platform: ${platform}`);
    
    const tools: FunctionTool[] = [];
    
    // Search tool
    const searchTool = this.createPlatformSearchTool(platform);
    this.registerTool(searchTool);
    tools.push(searchTool);
    
    // Add more platform-specific tools as needed
    
    return tools;
  }

  /**
   * Remove dynamically generated tools
   */
  removeDynamicTool(toolName: string): boolean {
    const removed = this.tools.delete(toolName);
    if (removed) {
      this.log('info', `Removed dynamic tool: ${toolName}`);
    }
    return removed;
  }

  /**
   * Get all dynamic tools
   */
  getDynamicTools(): FunctionTool[] {
    return this.getTools();
  }
} 