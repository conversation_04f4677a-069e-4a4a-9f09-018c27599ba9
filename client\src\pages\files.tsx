import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getFiles, searchFiles, deleteFile, getIntegrations } from "@/lib/api";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { formatDate } from "@/lib/utils/date";
import { getIntegrationName } from "@/lib/utils/integrations";
import { IntegrationIcon } from "@/components/ui/integration-icons";
import { 
  SearchIcon, 
  ExternalLinkIcon, 
  TrashIcon, 
  FileIcon, 
  FilterIcon,
  RefreshCwIcon,
  EyeIcon,
  UsersIcon,
  FolderIcon
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import React from "react";

interface FileItem {
  id: number;
  externalId: string;
  fileName: string;
  platform: string;
  sourceUrl: string;
  owner: string;
  fileType: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  userId?: string;
  metadata?: any;
  extractedMetadata?: any;
  lastModified?: string;
}

export default function FilesPage() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  // State for filtering and search
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlatform, setSelectedPlatform] = useState("All Platforms");
  const [selectedFileType, setSelectedFileType] = useState("All Types");
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedFolderName, setSelectedFolderName] = useState<string | null>(null);
  const [platformFilter, setPlatformFilter] = useState<string>("all");
  const [fileTypeFilter, setFileTypeFilter] = useState<string>("all");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  
  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchQuery]);
  
  // Files query - get all files when no search query
  const { data: filesData, isLoading: isLoadingFiles, error: filesError, refetch } = useQuery({
    queryKey: ['/api/files', { 
      platform: platformFilter === "all" ? undefined : platformFilter,
      folderId: selectedFolderId 
    }],
    queryFn: () => getFiles({ 
      platform: platformFilter === "all" ? undefined : platformFilter,
      folderId: selectedFolderId || undefined,
      limit: 1000 // Increased limit for better filtering
    }),
    enabled: !debouncedSearchQuery, // Only fetch when not searching
    refetchInterval: 30000, // Refresh every 30 seconds
  });
  
  // Search query - triggered when there's a search term
  const { data: searchData, isLoading: isSearching, error: searchError } = useQuery({
    queryKey: ['/api/files/search', { 
      query: debouncedSearchQuery, 
      platform: platformFilter === "all" ? undefined : platformFilter,
      fileType: fileTypeFilter === "all" ? undefined : fileTypeFilter,
      folderId: selectedFolderId 
    }],
    queryFn: () => searchFiles({ 
      query: debouncedSearchQuery,
      platform: platformFilter === "all" ? undefined : platformFilter,
      fileType: fileTypeFilter === "all" ? undefined : fileTypeFilter,
      folderId: selectedFolderId || undefined
    }),
    enabled: !!debouncedSearchQuery && debouncedSearchQuery.length > 0,
  });
  
  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: deleteFile,
    onSuccess: () => {
      toast({
        title: "File deleted",
        description: "File reference has been removed successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/files'] });
      if (debouncedSearchQuery) {
        queryClient.invalidateQueries({ queryKey: ['/api/files/search'] });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete file",
        description: error.message || "An error occurred while deleting the file.",
        variant: "destructive",
      });
    },
  });
  
  // Get the appropriate data source based on search state
  const allFiles: FileItem[] = debouncedSearchQuery 
    ? (searchData?.files || []) 
    : (filesData?.files || []);
  
  // Apply remaining client-side filters for file type when not searching (since search API handles it)
  const filteredFiles = React.useMemo(() => {
    let files = allFiles;
    
    // Apply search filter only if we're not using the search API
    if (searchQuery.trim() && !debouncedSearchQuery) {
      const query = searchQuery.toLowerCase();
      files = files.filter(file => 
        file.fileName.toLowerCase().includes(query) ||
        (file.extractedMetadata as any)?.owners?.[0]?.displayName?.toLowerCase().includes(query) ||
        file.platform.toLowerCase().includes(query)
      );
    }
    
    // Apply file type filter only when not searching (since search API handles it)
    if (fileTypeFilter !== "all" && !debouncedSearchQuery) {
      files = files.filter(file => file.fileType === fileTypeFilter);
    }
    
    return files;
  }, [allFiles, searchQuery, debouncedSearchQuery, fileTypeFilter]);
  
  const totalCount = filteredFiles.length;
  const isLoading = debouncedSearchQuery ? isSearching : isLoadingFiles;
  const error = debouncedSearchQuery ? searchError : filesError;
  
  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
  };
  
  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
    setDebouncedSearchQuery("");
  };
  
  // Get owner name from nested structure
  const getOwnerName = (file: FileItem) => {
    // Try extractedMetadata.owners first
    if (file.extractedMetadata?.owners && file.extractedMetadata.owners.length > 0) {
      return file.extractedMetadata.owners[0].displayName || file.extractedMetadata.owners[0].emailAddress;
    }
    // Fall back to userId or owner
    return file.owner || file.userId || 'Unknown';
  };
  
  // Get folder name from extracted metadata
  const getFolderName = (file: FileItem): string => {
    const metadata = file.extractedMetadata as any;
    const sourceFolderId = metadata?._sourceFolderId;
    
    if (!sourceFolderId) return "—";
    
    // Find the folder name from our selected folders list
    const folder = selectedFolders.find(f => f.id === sourceFolderId);
    return folder ? folder.name : sourceFolderId.substring(0, 8) + "...";
  };
  
  // Get attendees from extracted metadata
  const getAttendees = (file: FileItem): string[] => {
    if (file.extractedMetadata?.aiExtracted?.attendees) {
      return file.extractedMetadata.aiExtracted.attendees;
    }
    return [];
  };
  
  // Format attendees for display
  const formatAttendees = (attendees: string[]) => {
    if (attendees.length === 0) return "—";
    if (attendees.length <= 2) return attendees.join(", ");
    
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="cursor-help">
              {attendees.slice(0, 2).join(", ")} +{attendees.length - 2} more
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <div className="max-w-sm">
              <p className="font-medium mb-1">All Attendees:</p>
              <ul className="text-sm space-y-1">
                {attendees.map((attendee, index) => (
                  <li key={index}>• {attendee}</li>
                ))}
              </ul>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };
  
  // Get file type icon - using basic FileIcon for all types
  const getFileTypeIcon = (fileType: string) => {
    return <FileIcon className="h-4 w-4 text-muted-foreground" />;
  };
  
  // Get unique platforms and file types for filters
  const uniquePlatforms = Array.from(new Set(allFiles.map(f => f.platform)));
  const uniqueFileTypes = Array.from(new Set(allFiles.map(f => f.fileType)));

  // Get integrations to show selected folders
  const { data: integrationsData } = useQuery({
    queryKey: ["integrations"],
    queryFn: getIntegrations,
  });

  // Extract all selected folders from Google integrations
  const selectedFolders = React.useMemo(() => {
    if (!integrationsData?.integrations) return [];
    
    const folders: Array<{id: string, name: string, integrationName: string}> = [];
    
    integrationsData.integrations.forEach((integration: any) => {
      if ((integration.type === 'google-drive' || integration.type === 'google_drive') && integration.sourceConfig) {
        const config = integration.sourceConfig;
        
        // Handle both multi-folder and single folder configurations
        if (config.driveIds && Array.isArray(config.driveIds)) {
          config.driveIds.forEach((id: string, index: number) => {
            const name = config.driveNames?.[index] || `Folder ${index + 1}`;
            folders.push({
              id,
              name,
              integrationName: integration.name
            });
          });
        } else if (config.driveId) {
          folders.push({
            id: config.driveId,
            name: config.driveName || 'Unknown Folder',
            integrationName: integration.name
          });
        }
      }
    });
    
    return folders;
  }, [integrationsData]);

  return (
    <AppLayout title="Files">
      <div className="space-y-6">
        {/* Header with search and filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Files Repository</span>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setSearchQuery("");
                  setPlatformFilter("all");
                  setFileTypeFilter("all");
                  refetch();
                }}
                disabled={isLoading}
              >
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </CardTitle>
            <CardDescription>
              {selectedFolderName ? `Viewing files from: ${selectedFolderName}` : "Manage and browse your synchronized files"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Selected Folders Navigation */}
            {selectedFolders.length > 0 && (
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <FolderIcon className="h-4 w-4" />
                  <span className="text-sm font-medium">Browse by Folder:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={selectedFolderId === null ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setSelectedFolderId(null);
                      setSelectedFolderName(null);
                    }}
                  >
                    All Folders
                  </Button>
                  {selectedFolders.map((folder) => (
                    <Button
                      key={folder.id}
                      variant={selectedFolderId === folder.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        setSelectedFolderId(folder.id);
                        setSelectedFolderName(folder.name);
                      }}
                      className="max-w-[200px] truncate"
                    >
                      <FolderIcon className="h-3 w-3 mr-1" />
                      {folder.name}
                    </Button>
                  ))}
                </div>
                {selectedFolderId && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    From integration: {selectedFolders.find(f => f.id === selectedFolderId)?.integrationName}
                  </div>
                )}
              </div>
            )}
              
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search input */}
              <div className="flex-1 relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search files by name or owner..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-9"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    onClick={clearSearch}
                  >
                    ×
                  </Button>
                )}
              </div>
              
              {/* Platform filter */}
              <Select value={platformFilter} onValueChange={setPlatformFilter}>
                <SelectTrigger className="w-[180px]">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Platforms</SelectItem>
                  {uniquePlatforms.map(platform => (
                    <SelectItem key={platform} value={platform}>
                      {getIntegrationName(platform)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* File type filter */}
              <Select value={fileTypeFilter} onValueChange={setFileTypeFilter}>
                <SelectTrigger className="w-[180px]">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="File Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {uniqueFileTypes.map(fileType => (
                    <SelectItem key={fileType} value={fileType}>
                      {fileType.charAt(0).toUpperCase() + fileType.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {debouncedSearchQuery && (
              <div className="mt-2 text-sm text-muted-foreground">
                {isSearching ? "Searching..." : `Found ${totalCount} file(s) matching "${debouncedSearchQuery}"`}
                <Button variant="link" className="h-auto p-0 ml-2" onClick={clearSearch}>
                  Clear search
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Files table */}
        <Card>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCwIcon className="h-6 w-6 animate-spin mr-2" />
                <span>Loading files...</span>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-8 text-red-600">
                <span>Error loading files: {error.message}</span>
              </div>
            ) : filteredFiles.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                <FileIcon className="h-12 w-12 mb-4 text-muted-foreground/50" />
                <h3 className="text-lg font-medium mb-2">No files found</h3>
                <p className="text-sm">
                  {debouncedSearchQuery || platformFilter !== "all" || fileTypeFilter !== "all"
                    ? "Try adjusting your search criteria or filters" 
                    : "No files have been synced yet. Connect an integration and run a sync to see files here."
                  }
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[300px]">File</TableHead>
                    <TableHead>Platform</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Folder</TableHead>
                    <TableHead>Owner</TableHead>
                    <TableHead className="min-w-[200px]">Attendees</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Modified</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFiles.map((file) => {
                    const attendees = getAttendees(file);
                    return (
                      <TableRow 
                        key={file.id} 
                        className={cn(
                          "hover:bg-muted/50",
                          file.status === "deleted" && "opacity-50 bg-muted/30"
                        )}
                      >
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            {getFileTypeIcon(file.fileType)}
                            <div className="min-w-0 flex-1">
                              {file.sourceUrl ? (
                                <a
                                  href={file.sourceUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-sm font-medium text-primary hover:text-primary/80 hover:underline truncate block"
                                  title={`Open ${file.fileName} in ${getIntegrationName(file.platform)}`}
                                >
                                  {file.fileName}
                                  <ExternalLinkIcon className="inline h-3 w-3 ml-1" />
                                </a>
                              ) : (
                                <p className="text-sm font-medium text-foreground truncate">
                                  {file.fileName}
                                </p>
                              )}
                              <p className="text-sm text-muted-foreground truncate">
                                ID: {file.externalId}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 rounded flex-shrink-0 bg-white border border-gray-100 flex items-center justify-center">
                              <IntegrationIcon type={file.platform} size={20} />
                            </div>
                            <span className="text-sm text-muted-foreground">
                              {getIntegrationName(file.platform)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground capitalize">
                            {file.fileType}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {getFolderName(file)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {getOwnerName(file)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {attendees.length > 0 && (
                              <UsersIcon className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="text-sm text-muted-foreground">
                              {formatAttendees(attendees)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={file.status === "active" ? "default" : "secondary"}
                            className={file.status === "deleted" ? "bg-red-100 text-red-800" : ""}
                          >
                            {file.status || "active"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(file.createdAt)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {file.lastModified ? formatDate(file.lastModified) : formatDate(file.updatedAt)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                •••
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <EyeIcon className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              {file.status !== "deleted" && (
                                <DropdownMenuItem 
                                  onClick={() => deleteMutation.mutate(file.id)}
                                  className="text-red-600"
                                  disabled={deleteMutation.isPending}
                                >
                                  <TrashIcon className="h-4 w-4 mr-2" />
                                  Delete Reference
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
} 