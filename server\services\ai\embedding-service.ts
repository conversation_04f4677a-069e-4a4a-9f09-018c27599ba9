import OpenAI from "openai";
import { storage } from "../../storage/index.js";
import type { InsertFileChunk } from "../../../shared/index.js";

class EmbeddingService {
  private openai!: OpenAI;
  private initialized: boolean = false;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn(
        "OPENAI_API_KEY not found in environment. Embedding features will be disabled.",
      );
      return;
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.initialized = true;
    console.log("Embedding service initialized successfully");
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Generate embedding for a single text using OpenAI API
   */
  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.initialized) {
      throw new Error("Embedding service not initialized");
    }

    try {
      console.log(`Generating embedding for text (${text.length} chars): "${text.substring(0, 100)}..."`);

      const response = await this.openai.embeddings.create({
        model: "text-embedding-3-small",
        input: text,
      });

      const embedding = response.data[0].embedding;
      console.log(`Successfully generated embedding with ${embedding.length} dimensions`);

      return embedding;
    } catch (error: any) {
      console.error("Error generating embedding:", error);
      throw new Error(`Failed to generate embedding: ${error.message}`);
    }
  }

  /**
   * Split text into chunks for embedding
   */
  splitTextIntoChunks(text: string, chunkSize: number = 500, overlap: number = 50): string[] {
    const chunks: string[] = [];
    const words = text.split(/\s+/);

    for (let i = 0; i < words.length; i += chunkSize - overlap) {
      const chunk = words.slice(i, i + chunkSize).join(' ');
      if (chunk.trim().length > 0) {
        chunks.push(chunk.trim());
      }
    }

    return chunks;
  }

  /**
   * Process a file and generate embeddings for its chunks
   */
  async processFileForEmbeddings(fileId: number, content: string): Promise<void> {
    if (!this.initialized) {
      console.warn("Embedding service not initialized, skipping embedding generation");
      return;
    }

    try {
      console.log(`Processing file ${fileId} for embeddings...`);

      // Split content into chunks
      const chunks = this.splitTextIntoChunks(content);
      console.log(`Split content into ${chunks.length} chunks`);

      // Generate embeddings for each chunk
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];

        try {
          console.log(`Generating embedding for chunk ${i + 1}/${chunks.length}`);
          const embedding = await this.generateEmbedding(chunk);

          // Store chunk with embedding
          const chunkData: InsertFileChunk = {
            fileId,
            chunkIndex: i,
            content: chunk,
            embedding,
            tokenCount: this.estimateTokenCount(chunk),
            metadata: {
              chunkSize: chunk.length,
              wordCount: chunk.split(/\s+/).length,
            },
          };

          await storage.createFileChunk(chunkData);

          // Add smart delay to avoid rate limiting - longer for big files
          const delay = chunks.length > 20 ? 1000 : 100;
          await new Promise(resolve => setTimeout(resolve, delay));

        } catch (chunkError: any) {
          console.error(`Error processing chunk ${i}:`, chunkError);
          // Continue with other chunks even if one fails
        }
      }

      console.log(`Successfully processed ${chunks.length} chunks for file ${fileId}`);

    } catch (error: any) {
      console.error(`Error processing file ${fileId} for embeddings:`, error);
      throw error;
    }
  }

  /**
   * Search for similar chunks using vector similarity
   */
  async searchSimilarChunks(
    query: string,
    enabledSources: string[] = [],
    limit: number = 10
  ): Promise<any[]> {
    if (!this.initialized) {
      console.warn("Embedding service not initialized, returning empty results");
      return [];
    }

    try {
      console.log(`Generating embedding for query: "${query}"`);

      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);

      console.log(`Generated embedding with length: ${queryEmbedding ? queryEmbedding.length : 'undefined'}`);

      if (!queryEmbedding || !Array.isArray(queryEmbedding) || queryEmbedding.length === 0) {
        console.error("Failed to generate valid embedding for query");
        return [];
      }

      // Search for similar chunks in the database
      const similarChunks = await storage.searchSimilarChunks(
        queryEmbedding,
        enabledSources,
        limit
      );

      return similarChunks;

    } catch (error: any) {
      console.error("Error searching similar chunks:", error);
      return [];
    }
  }

  /**
   * Estimate token count for a text (rough approximation)
   */
  private estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Check if a file already has embeddings
   */
  async hasEmbeddings(fileId: number): Promise<boolean> {
    try {
      const chunks = await storage.getFileChunks(fileId);
      return chunks.length > 0;
    } catch (error) {
      console.error(`Error checking embeddings for file ${fileId}:`, error);
      return false;
    }
  }

  /**
   * Process an email chunk for embeddings
   */
  async processEmailChunkForEmbeddings(chunkId: number, content: string): Promise<void> {
    if (!this.initialized) {
      console.warn("Embedding service not initialized, skipping email chunk embedding generation");
      return;
    }

    try {
      console.log(`Processing email chunk ${chunkId} for embeddings...`);

      // Generate embedding for the chunk content
      const embedding = await this.generateEmbedding(content);

      // Update the email chunk with the embedding
      await storage.updateEmailChunkEmbedding(chunkId, embedding);

      console.log(`Successfully processed email chunk ${chunkId} for embeddings`);

    } catch (error: any) {
      console.error(`Error processing email chunk ${chunkId} for embeddings:`, error);
      throw error;
    }
  }

  /**
   * Regenerate embeddings for a file (useful when content changes)
   */
  async regenerateEmbeddings(fileId: number, content: string): Promise<void> {
    try {
      // Delete existing chunks
      await storage.deleteFileChunks(fileId);

      // Generate new embeddings
      await this.processFileForEmbeddings(fileId, content);

      console.log(`Regenerated embeddings for file ${fileId}`);
    } catch (error: any) {
      console.error(`Error regenerating embeddings for file ${fileId}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const embeddingService = new EmbeddingService();
