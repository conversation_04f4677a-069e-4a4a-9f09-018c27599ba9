import { Client } from '@microsoft/microsoft-graph-client';
import { BaseService } from "../../base/service.interface";
import type { MicrosoftCredentials } from './oauth.service';

export interface TeamsSource {
  id: string;
  name: string;
  type: 'teams_channel' | 'sharepoint_site' | 'onedrive_folder';
  parentId?: string;
  parentName?: string;
}

export interface TeamsFileMetadata {
  _sourceType: 'teams_channel' | 'sharepoint_site' | 'onedrive_personal';
  _sourceFolderId: string;
  _teamId?: string;
  _channelId?: string;
  _siteId?: string;
  meetingId?: string;
  meetingType?: 'channel' | 'adhoc' | 'scheduled';
  organizer?: {
    displayName: string;
    email: string;
    id: string;
  };
  webUrl: string;
  driveId: string;
  parentReference?: {
    driveId: string;
    id: string;
    path: string;
  };
  shared?: boolean;
  permissions?: Array<{
    grantedTo?: {
      user?: { displayName: string; email: string; };
      application?: { displayName: string; };
    };
    roles: string[];
  }>;
}

/**
 * Microsoft Teams Service - handles Teams operations and file management
 */
export class MicrosoftTeamsService extends BaseService {
  constructor() {
    super('MicrosoftTeams', '1.0.0', 'Microsoft Teams operations and file management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Microsoft Teams service initialized');
  }

  protected getDependencies(): string[] {
    return ['MicrosoftOAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      apiVersion: 'v1.0',
      supportedOperations: ['teams', 'channels', 'files', 'chat'],
    };
  }

  /**
   * Get user's joined teams
   */
  async getUserTeams(client: Client): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');

    try {
      const response = await client.api('/me/joinedTeams').get();
      return response.value || [];
    } catch (error: any) {
      this.log('error', 'Error getting user teams', error);
      return [];
    }
  }

  /**
   * Get Teams channels for a specific team
   */
  async getTeamsChannels(client: Client, teamId: string): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(teamId, 'team ID');

    try {
      const response = await client.api(`/teams/${teamId}/channels`).get();
      return response.value || [];
    } catch (error: any) {
      this.log('error', `Error getting channels for team ${teamId}`, error);
      return [];
    }
  }

  /**
   * Sync files from a Teams channel with comprehensive file retrieval
   */
  async syncTeamsChannel(
    client: Client,
    teamId: string,
    channelId: string
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(teamId, 'team ID');
    this.validateString(channelId, 'channel ID');

    try {
      this.log('info', `Starting comprehensive sync for Teams channel ${channelId}`);
      
      // Get the channel's files folder
      const folderResponse = await client.api(`/teams/${teamId}/channels/${channelId}/filesFolder`).get();
      const driveId = folderResponse.parentReference.driveId;
      const folderId = folderResponse.id;

      this.log('info', `Channel files folder - driveId: ${driveId}, folderId: ${folderId}`);

      // Get ALL files recursively from the channel folder
      const allFiles = await this.getAllFilesRecursively(
        client,
        driveId,
        folderId,
        teamId,
        channelId
      );

      this.log('info', `Found ${allFiles.length} total files in channel ${channelId}`);
      return allFiles;

    } catch (error: any) {
      this.handleError(error, `syncing Teams channel ${teamId}/${channelId}`);
    }
  }

  /**
   * Get chat history for a Teams channel or meeting
   */
  async getChatHistory(
    client: Client,
    teamId?: string,
    channelId?: string,
    chatId?: string
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');

    try {
      let messages: any[] = [];

      if (teamId && channelId) {
        // Get channel messages
        const response = await client.api(`/teams/${teamId}/channels/${channelId}/messages`)
          .top(50)
          .select('id,body,from,createdDateTime,attachments,mentions')
          .get();
        messages = response.value || [];
      } else if (chatId) {
        // Get chat messages
        const response = await client.api(`/chats/${chatId}/messages`)
          .top(50)
          .select('id,body,from,createdDateTime,attachments,mentions')
          .get();
        messages = response.value || [];
      }

      return messages.map(msg => ({
        id: msg.id,
        content: msg.body?.content,
        contentType: msg.body?.contentType,
        sender: msg.from?.user?.displayName,
        senderEmail: msg.from?.user?.userPrincipalName,
        timestamp: msg.createdDateTime,
        attachments: msg.attachments || [],
        mentions: msg.mentions || []
      }));

    } catch (error: any) {
      this.log('error', 'Error getting chat history', error);
      return [];
    }
  }

  /**
   * Get all files recursively from a folder, including subfolders
   */
  private async getAllFilesRecursively(
    client: Client,
    driveId: string,
    folderId: string,
    teamId?: string,
    channelId?: string,
    siteId?: string,
    folderPath: string = ''
  ): Promise<any[]> {
    const allFiles: any[] = [];
    
    try {
      this.log('info', `Scanning folder: ${folderId} at path: ${folderPath || 'root'}`);
      
      // Get items in current folder with expanded properties
      const response = await client.api(`/drives/${driveId}/items/${folderId}/children`)
        .select('id,name,file,folder,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference')
        .expand('thumbnails')
        .get();
      
      const items = response.value || [];
      this.log('info', `Found ${items.length} items in folder ${folderId}`);
      
      for (const item of items) {
        if (item.file) {
          // This is a file - process it
          this.log('info', `Processing file: ${item.name} (${item.id})`);
          
          // Check if this is a meeting-related file
          const isMeetingFile = this.determineFileType(item) === 'transcript' || 
                               item.name.toLowerCase().includes('meeting') ||
                               item.name.toLowerCase().includes('recording');
          
          const file = {
            externalId: item.id,
            fileName: item.name,
            fileType: this.determineFileType(item),
            platform: 'microsoft_teams',
            sourceUrl: item.webUrl,
            userId: item.createdBy?.user?.id || item.lastModifiedBy?.user?.id || 'unknown',
            lastModified: item.lastModifiedDateTime ? new Date(item.lastModifiedDateTime) : new Date(),
            extractedMetadata: {
              _sourceType: teamId ? 'teams_channel' : siteId ? 'sharepoint_site' : 'onedrive_personal',
              _sourceFolderId: teamId ? channelId : folderId,
              ...(teamId && { _teamId: teamId }),
              ...(channelId && { _channelId: channelId }),
              ...(siteId && { _siteId: siteId }),
              webUrl: item.webUrl,
              driveId: driveId,
              parentReference: item.parentReference,
              folderPath: folderPath,
              folderName: folderPath ? folderPath.split('/').pop() || 'Unknown Folder' : 'Root',
              ownerName: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
              ownerEmail: item.createdBy?.user?.userPrincipalName || item.lastModifiedBy?.user?.userPrincipalName || null,
              fileExtension: this.getFileExtension(item.name),
              mimeType: item.file?.mimeType || 'unknown',
              shared: item.shared !== undefined,
              lastModifiedDateTime: item.lastModifiedDateTime,
              createdDateTime: item.createdDateTime,
              size: item.size || 0,
              createdBy: item.createdBy?.user?.displayName,
              lastModifiedBy: item.lastModifiedBy?.user?.displayName,
              owner: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
              hasContent: true,
              isDownloadable: true,
              isMeetingFile,
            } as TeamsFileMetadata
          };
          
          allFiles.push(file);
        } else if (item.folder) {
          // This is a folder - recurse into it
          const subFolderPath = folderPath ? `${folderPath}/${item.name}` : item.name;
          this.log('info', `Recursing into subfolder: ${item.name} (${item.id})`);
          
          try {
            const subFiles = await this.getAllFilesRecursively(
              client,
              driveId,
              item.id,
              teamId,
              channelId,
              siteId,
              subFolderPath
            );
            allFiles.push(...subFiles);
          } catch (subFolderError) {
            this.log('error', `Error scanning subfolder ${item.name}`, subFolderError);
            // Continue with other folders
          }
        }
      }
      
      return allFiles;
    } catch (error: any) {
      this.handleError(error, `getting files from folder ${folderId}`);
    }
  }

  /**
   * Determine file type based on file properties
   */
  private determineFileType(item: any): string {
    const name = item.name?.toLowerCase() || '';
    const mimeType = item.file?.mimeType?.toLowerCase() || '';
    const extension = this.getFileExtension(name);

    // Video files (including meeting recordings)
    if (mimeType.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'webm', 'mkv', 'flv'].includes(extension)) {
      if (name.includes('recording') || name.includes('meeting recording') || name.includes('teams meeting')) {
        return 'recording';
      }
      return 'video';
    }

    // Audio files
    if (mimeType.startsWith('audio/') || ['mp3', 'wav', 'm4a', 'wma', 'aac', 'flac', 'ogg'].includes(extension)) {
      return 'audio';
    }

    // Meeting transcripts
    if ((name.includes('transcript') || name.includes('meeting notes') || name.includes('notes by gemini')) &&
        (mimeType.includes('text') || ['txt', 'vtt', 'srt', 'docx', 'doc'].includes(extension))) {
      return 'transcript';
    }

    // Documents
    if (mimeType.includes('document') || mimeType.includes('text') || 
        ['docx', 'doc', 'pdf', 'txt', 'rtf', 'odt'].includes(extension)) {
      return 'document';
    }

    // Presentations
    if (mimeType.includes('presentation') || 
        ['pptx', 'ppt', 'odp', 'key'].includes(extension)) {
      return 'presentation';
    }

    // Spreadsheets
    if (mimeType.includes('sheet') || 
        ['xlsx', 'xls', 'csv', 'ods', 'numbers'].includes(extension)) {
      return 'spreadsheet';
    }

    // Images
    if (mimeType.startsWith('image/') || 
        ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      return 'image';
    }

    return extension || 'unknown';
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
  }

  /**
   * Get Teams folder structure for selection
   */
  async getTeamsStructure(client: Client): Promise<{
    teams: Array<{
      id: string;
      name: string;
      channels: Array<{
        id: string;
        name: string;
        itemCount?: number;
      }>;
    }>;
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');

    try {
      const teams = await this.getUserTeams(client);
      const teamsStructure = [];

      for (const team of teams) {
        try {
          const channels = await this.getTeamsChannels(client, team.id);
          const channelsWithCount = [];

          for (const channel of channels) {
            let itemCount = 0;
            try {
              const folderResponse = await client.api(`/teams/${team.id}/channels/${channel.id}/filesFolder`).get();
              const driveId = folderResponse.parentReference.driveId;
              const folderId = folderResponse.id;
              const filesResponse = await client.api(`/drives/${driveId}/items/${folderId}/children`).get();
              itemCount = filesResponse.value?.length || 0;
            } catch (error) {
              // Continue if we can't get file count
            }

            channelsWithCount.push({
              id: channel.id,
              name: channel.displayName,
              itemCount,
            });
          }

          teamsStructure.push({
            id: team.id,
            name: team.displayName,
            channels: channelsWithCount,
          });
        } catch (error) {
          this.log('error', `Error processing team ${team.displayName}`, error);
        }
      }

      return { teams: teamsStructure };
    } catch (error: any) {
      this.handleError(error, 'getting Teams structure');
    }
  }
}
