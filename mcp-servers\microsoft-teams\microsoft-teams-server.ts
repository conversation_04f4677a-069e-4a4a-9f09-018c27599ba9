import { BaseMCPServer } from '../base-mcp.server';
import { listTeams<PERSON>and<PERSON>, getMessagesHandler, searchContentHandler } from './microsoft-teams-tools';

export class MicrosoftTeamsMCPServer extends BaseMCPServer {
  constructor() {
    super('microsoft-teams', 'SDK-enhanced Microsoft Teams operations and content management', '2.0.0');
  }

  protected async initializeServer(): Promise<void> {
    // Any Microsoft Teams-specific initialization can go here
  }

  protected async registerTools(): Promise<void> {
    this.addTool(
      {
        name: 'list_teams',
        description: "List user's Microsoft Teams with optional filtering",
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for authentication' },
            maxResults: { type: 'number', description: 'Maximum number of results (default: 50)' }
          },
          required: ['userId']
        },
      },
      listTeamsHandler,
      'teams-management'
    );

    this.addTool(
      {
        name: 'get_messages',
        description: 'Get messages from Microsoft Teams channels',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for authentication' },
            teamId: { type: 'string', description: 'Team ID' },
            channelId: { type: 'string', description: 'Channel ID' },
            maxResults: { type: 'number', description: 'Maximum number of messages (default: 20)' }
          },
          required: ['userId', 'teamId', 'channelId']
        },
      },
      getMessagesHandler,
      'content-access'
    );

    this.addTool(
      {
        name: 'search_content',
        description: 'Search across Microsoft Teams content',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for authentication' },
            query: { type: 'string', description: 'Search query' },
            entityTypes: { type: 'array', description: 'Types to search (messages, files, etc.)' },
            maxResults: { type: 'number', description: 'Maximum number of results (default: 20)' }
          },
          required: ['userId', 'query']
        },
      },
      searchContentHandler,
      'search'
    );
  }

  protected async registerResources(): Promise<void> {
    // No resources for Microsoft Teams MCP (for now)
  }

  protected async registerPrompts(): Promise<void> {
    // No prompts for Microsoft Teams MCP (for now)
  }
} 