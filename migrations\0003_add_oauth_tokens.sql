-- Create oauth_tokens table for storing encrypted OAuth tokens
CREATE TABLE "oauth_tokens" (
    "id" serial PRIMARY KEY NOT NULL,
    "user_id" varchar(255) NOT NULL,
    "platform" varchar(50) NOT NULL,
    "access_token" text NOT NULL,
    "refresh_token" text,
    "token_type" varchar(50) NOT NULL,
    "scope" text,
    "expires_at" timestamp NOT NULL,
    "created_at" timestamp DEFAULT now() NOT NULL,
    "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX "oauth_tokens_user_id_idx" ON "oauth_tokens" ("user_id");
CREATE INDEX "oauth_tokens_platform_idx" ON "oauth_tokens" ("platform");
CREATE UNIQUE INDEX "oauth_tokens_user_platform_idx" ON "oauth_tokens" ("user_id", "platform");

-- Add trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
R<PERSON>URNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_oauth_tokens_updated_at
    BEFORE UPDATE ON oauth_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 