import { storage } from "./storage/index.js";

async function seedTestData() {
  try {
    console.log('🌱 Seeding test data...');

    // Clear all existing data for fresh start (development only) - DISABLED to preserve data
    // if (storage.clearAllData) {
    //   await storage.clearAllData();
    // }

    // Check for existing integrations to avoid duplicates
    const existingIntegrations = await storage.getIntegrations();
    if (existingIntegrations.length > 0) {
      console.log(`📊 Found ${existingIntegrations.length} existing integrations, skipping seed data creation`);
      return;
    }

    // Create Google Drive integration (first)
    const googleDriveIntegration = await storage.createIntegration({
      type: 'google-drive',
      name: 'Google Drive Integration',
      status: 'connected',
      credentials: null,
      config: {
        description: 'Sync all files from Google Drive for AI-powered search'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    // Create Gmail integration (second)
    const gmailIntegration = await storage.createIntegration({
      type: 'gmail',
      name: 'Gmail Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        description: 'Sync emails from Gmail for AI-powered search and analysis'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    // Create Google Calendar integration (third)
    const googleCalendarIntegration = await storage.createIntegration({
      type: 'google_calendar',
      name: 'Google Calendar Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        description: 'Sync calendar events and meetings for AI-powered scheduling insights'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    // Create Microsoft Teams integration (fourth)
    const teamsIntegration = await storage.createIntegration({
      type: 'microsoft_teams',
      name: 'Microsoft Teams Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        description: 'Sync meeting transcripts from Microsoft Teams'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    // Create Slack integration (fifth)
    const slackIntegration = await storage.createIntegration({
      type: 'slack',
      name: 'Slack Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        description: 'Sync conversation logs from Slack channels'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    console.log('✅ Successfully seeded test data:');
    console.log(`   - Google Drive Integration (ID: ${googleDriveIntegration.id})`);
    console.log(`   - Gmail Integration (ID: ${gmailIntegration.id})`);
    console.log(`   - Google Calendar Integration (ID: ${googleCalendarIntegration.id})`);
    console.log(`   - Microsoft Teams Integration (ID: ${teamsIntegration.id})`);
    console.log(`   - Slack Integration (ID: ${slackIntegration.id})`);

    // Verify the data was created
    const allIntegrations = await storage.getIntegrations();
    console.log(`📊 Total integrations in storage: ${allIntegrations.length}`);

  } catch (error) {
    console.error('❌ Error seeding test data:', error);
    throw error;
  }
}

// Export for use in other modules
export { seedTestData };

// Run directly if this file is executed
if (import.meta.url === `file://${process.argv[1]}`) {
  seedTestData().catch(console.error);
} 