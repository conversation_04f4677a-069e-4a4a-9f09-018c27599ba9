import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { environmentConfig } from '../config/environment.config';
import { createError } from './error.middleware';
import { logSecurityEvent } from './logging.middleware';

/**
 * Authentication middleware
 */

export interface AuthenticatedUser {
  id: string;
  username: string;
  email?: string;
  roles?: string[];
}

export interface AuthenticatedRequest extends Request {
  user?: AuthenticatedUser;
  token?: string;
}

/**
 * JWT token utilities
 */
export const tokenUtils = {
  /**
   * Generate JWT token
   */
  generateToken: (payload: any, expiresIn: string | number = '24h'): string => {
    if (!environmentConfig.sessionSecret) {
      throw new Error('Session secret not configured');
    }

    const options: jwt.SignOptions = {
      issuer: 'gpt-unify-app',
      audience: 'gpt-unify-users',
    };

    if (typeof expiresIn === 'string') {
      (options as any).expiresIn = expiresIn;
    } else if (typeof expiresIn === 'number') {
      (options as any).expiresIn = expiresIn;
    }

    return jwt.sign(payload, environmentConfig.sessionSecret, options);
  },

  /**
   * Verify JWT token
   */
  verifyToken: (token: string): any => {
    if (!environmentConfig.sessionSecret) {
      throw new Error('Session secret not configured');
    }

    return jwt.verify(token, environmentConfig.sessionSecret, {
      issuer: 'gpt-unify-app',
      audience: 'gpt-unify-users',
    });
  },

  /**
   * Decode token without verification (for debugging)
   */
  decodeToken: (token: string): any => {
    return jwt.decode(token);
  },
};

/**
 * Extract token from request
 */
const extractToken = (req: Request): string | null => {
  // Check Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Check query parameter
  if (req.query.token && typeof req.query.token === 'string') {
    return req.query.token;
  }

  // Check cookies
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }

  return null;
};

/**
 * Authentication middleware - requires valid JWT token
 */
export const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const token = extractToken(req);

    if (!token) {
      logSecurityEvent('AUTH_TOKEN_MISSING', req);
      throw createError.unauthorized('Authentication token required');
    }

    // Verify token
    const decoded = tokenUtils.verifyToken(token);
    
    // Add user info to request
    req.user = {
      id: decoded.sub || decoded.id,
      username: decoded.username,
      email: decoded.email,
      roles: decoded.roles || [],
    };
    req.token = token;

    next();

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logSecurityEvent('AUTH_TOKEN_INVALID', req, { error: error.message });
      
      if (error instanceof jwt.TokenExpiredError) {
        throw createError.unauthorized('Token has expired');
      } else {
        throw createError.unauthorized('Invalid authentication token');
      }
    }
    
    throw error;
  }
};

/**
 * Optional authentication middleware - adds user info if token is present
 */
export const optionalAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const token = extractToken(req);

    if (token) {
      try {
        const decoded = tokenUtils.verifyToken(token);
        req.user = {
          id: decoded.sub || decoded.id,
          username: decoded.username,
          email: decoded.email,
          roles: decoded.roles || [],
        };
        req.token = token;
      } catch (error) {
        // Log but don't fail for optional auth
        logSecurityEvent('AUTH_OPTIONAL_TOKEN_INVALID', req, { error: (error as Error).message });
      }
    }

    next();

  } catch (error) {
    next(error);
  }
};

/**
 * Role-based authorization middleware
 */
export const requireRole = (roles: string | string[]) => {
  const requiredRoles = Array.isArray(roles) ? roles : [roles];

  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createError.unauthorized('Authentication required');
    }

    const userRoles = req.user.roles || [];
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      logSecurityEvent('AUTH_INSUFFICIENT_PERMISSIONS', req, {
        requiredRoles,
        userRoles,
      });
      throw createError.forbidden('Insufficient permissions');
    }

    next();
  };
};

/**
 * API key authentication middleware
 */
export const requireApiKey = (req: Request, res: Response, next: NextFunction): void => {
  const apiKey = req.headers['x-api-key'] as string;

  if (!apiKey) {
    logSecurityEvent('API_KEY_MISSING', req);
    throw createError.unauthorized('API key required');
  }

  // In a real application, you would validate the API key against a database
  // For now, we'll just check if it's present
  if (!apiKey.startsWith('mk_')) {
    logSecurityEvent('API_KEY_INVALID', req, { apiKey: apiKey.substring(0, 8) + '...' });
    throw createError.unauthorized('Invalid API key');
  }

  next();
};

/**
 * Rate limiting by user
 */
export const rateLimitByUser = (maxRequests = 100, windowMs = 60000) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    const userId = req.user?.id || req.ip;
    const now = Date.now();

    // Clean up expired entries
    for (const [key, value] of Array.from(userRequests.entries())) {
      if (now > value.resetTime) {
        userRequests.delete(key);
      }
    }

    // Get or create user entry
    const userIdKey = userId || 'anonymous';
    let userEntry = userRequests.get(userIdKey);
    if (!userEntry || now > userEntry.resetTime) {
      userEntry = { count: 0, resetTime: now + windowMs };
      userRequests.set(userIdKey, userEntry);
    }

    // Check rate limit
    if (userEntry.count >= maxRequests) {
      logSecurityEvent('RATE_LIMIT_EXCEEDED', req, {
        userId: userIdKey,
        count: userEntry.count,
        maxRequests,
      });
      
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': Math.ceil(userEntry.resetTime / 1000).toString(),
      });
      
      throw createError.serviceUnavailable('Rate limit exceeded');
    }

    // Increment counter
    userEntry.count++;

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': maxRequests.toString(),
      'X-RateLimit-Remaining': (maxRequests - userEntry.count).toString(),
      'X-RateLimit-Reset': Math.ceil(userEntry.resetTime / 1000).toString(),
    });

    next();
  };
};
