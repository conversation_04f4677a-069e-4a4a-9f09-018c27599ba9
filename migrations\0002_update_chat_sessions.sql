-- Update chat_sessions table to use varchar ID
ALTER TABLE "chat_sessions" 
  ALTER COLUMN "id" TYPE varchar(255) USING id::varchar(255);

-- Update chat_messages table to use varchar session_id
ALTER TABLE "chat_messages" 
  ALTER COLUMN "session_id" TYPE varchar(255) USING session_id::varchar(255);

-- Drop the existing foreign key constraint
ALTER TABLE "chat_messages" 
  DROP CONSTRAINT IF EXISTS "chat_messages_session_id_chat_sessions_id_fk";

-- Add the new foreign key constraint
ALTER TABLE "chat_messages" 
  ADD CONSTRAINT "chat_messages_session_id_chat_sessions_id_fk" 
  FOREIGN KEY ("session_id") 
  REFERENCES "chat_sessions"("id") 
  ON DELETE CASCADE; 