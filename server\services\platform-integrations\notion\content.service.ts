import { Client } from "@notionhq/client";
import { BaseService } from "../../base/service.interface";

/**
 * Notion Content Service - handles content creation, chunking, and management
 */
export class NotionContentService extends BaseService {
  constructor() {
    super('NotionContent', '1.0.0', 'Notion content creation, chunking, and management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Notion Content service initialized');
  }

  protected getDependencies(): string[] {
    return ['NotionAuth', 'NotionPage'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      maxChunkLength: 1950,
      maxBlocksPerPage: 90,
      supportedContentTypes: ['transcript', 'notes', 'summary'],
    };
  }

  /**
   * Create a transcript page with comprehensive content management
   */
  async createTranscriptPage(
    client: Client,
    databaseId: string,
    data: any
  ): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');
    this.validateRequired(data, 'transcript data');

    try {
      this.log('info', `Creating transcript page: ${data.title || 'Untitled Meeting'}`);

      // Build page properties
      const properties = this.buildPageProperties(data);

      // Validate properties for Notion API compliance
      this.validatePageProperties(properties);

      // Create the page
      const response = await client.pages.create({
        parent: { database_id: databaseId },
        properties,
      });

      this.log('info', `Created transcript page: ${response.id}`);

      // Add content blocks if available
      if (data.content || data.summary || data.notes) {
        await this.addTranscriptContent(client, response.id, data);
      }

      return response.id;
    } catch (error: any) {
      this.handleError(error, 'creating transcript page');
    }
  }

  /**
   * Build page properties from transcript data
   */
  private buildPageProperties(data: any): any {
    const properties: any = {
      Name: {
        title: [
          {
            type: "text",
            text: { content: data.title || "Untitled Meeting" },
          },
        ],
      },
    };

    // Add date property
    if (data.date) {
      properties.Date = {
        date: {
          start: data.date instanceof Date
            ? data.date.toISOString().split("T")[0]
            : data.date,
        },
      };
    }

    // Add duration property
    if (data.duration) {
      const durationNumber = typeof data.duration === 'number' 
        ? data.duration 
        : parseInt(data.duration);
      
      if (!isNaN(durationNumber) && durationNumber > 0) {
        properties.Duration = {
          rich_text: [
            {
              type: "text",
              text: { content: this.formatDuration(durationNumber) },
            },
          ],
        };
      }
    }

    // Add source URL
    if (data.sourceUrl) {
      properties.SourceURL = { url: data.sourceUrl };
    }

    // Add attendees
    if (data.participants && Array.isArray(data.participants)) {
      properties.Attendees = {
        rich_text: [
          {
            type: "text",
            text: { content: data.participants.join(", ") },
          },
        ],
      };
    }

    // Add topics
    if (data.topics) {
      if (Array.isArray(data.topics)) {
        properties.Topics = {
          multi_select: data.topics.map((name: string) => ({
            name: this.sanitizeMultiSelectValue(name),
          })),
        };
      } else if (typeof data.topics === "string") {
        const topicsArray = data.topics
          .split(",")
          .map((t: string) => t.trim())
          .filter(Boolean)
          .map((name: string) => ({
            name: this.sanitizeMultiSelectValue(name),
          }));
        properties.Topics = { multi_select: topicsArray };
      }
    }

    // Add AI summary
    if (data.summary) {
      properties["AI summary"] = {
        rich_text: [
          {
            type: "text",
            text: { content: data.summary.substring(0, 2000) },
          },
        ],
      };
    }

    // Add time
    if (data.time) {
      properties.Time = {
        rich_text: [
          {
            type: "text",
            text: { content: data.time },
          },
        ],
      };
    } else if (!data.time && data.date instanceof Date) {
      properties.Time = {
        rich_text: [
          {
            type: "text",
            text: { content: data.date.toLocaleTimeString() },
          },
        ],
      };
    }

    return properties;
  }

  /**
   * Add transcript content with intelligent sub-page management
   */
  private async addTranscriptContent(
    client: Client,
    pageId: string,
    data: any
  ): Promise<void> {
    try {
      const blocks: any[] = [];
      
      // Add metadata header
      blocks.push({
        object: "block",
        type: "callout",
        callout: {
          rich_text: [{ 
            type: "text", 
            text: { 
              content: `📋 This meeting document contains ${data.notes ? 'Gemini Notes' : ''}${data.notes && data.content ? ' and ' : ''}${data.content ? 'Full Transcript' : ''}. ${this.calculateContentStats(data)}` 
            } 
          }],
          icon: { emoji: "📋" },
          color: "blue_background"
        }
      });

      blocks.push({ object: "block", type: "divider", divider: {} });

      // Handle Gemini Notes
      if (data.notes) {
        await this.addNotesContent(client, pageId, data.notes, blocks);
      }

      // Handle Transcript
      if (data.content) {
        await this.addTranscriptContentWithSubPages(client, pageId, data.content, blocks);
      }

      // Add initial blocks to the main page
      if (blocks.length > 0) {
        await this.addBlocksInBatches(client, pageId, blocks);
      }

      this.log('info', `Added content structure to page ${pageId}`);
    } catch (error: any) {
      this.log('error', `Error adding content to page ${pageId}`, error);
    }
  }

  /**
   * Add notes content with sub-page management
   */
  private async addNotesContent(
    client: Client,
    parentPageId: string,
    notesText: string,
    mainBlocks: any[]
  ): Promise<void> {
    // Add Notes section header
    mainBlocks.push({
      object: "block",
      type: "heading_2",
      heading_2: {
        rich_text: [{ type: "text", text: { content: "🤖 Gemini Meeting Notes" } }],
      },
    });

    const notesChunks = this.chunkText(notesText, 1950);
    const maxBlocksPerPage = 90;

    if (notesChunks.length <= maxBlocksPerPage) {
      // All notes fit on main page
      this.log('info', `Adding ${notesChunks.length} notes chunks directly to main page`);
      
      for (const chunk of notesChunks) {
        mainBlocks.push({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [{ type: "text", text: { content: chunk } }],
          },
        });
      }
    } else {
      // Create sub-pages for notes
      await this.createSubPagesForContent(
        client,
        parentPageId,
        notesChunks,
        'Gemini Notes',
        mainBlocks,
        maxBlocksPerPage
      );
    }

    mainBlocks.push({ object: "block", type: "divider", divider: {} });
  }

  /**
   * Add transcript content with sub-page management
   */
  private async addTranscriptContentWithSubPages(
    client: Client,
    parentPageId: string,
    transcriptText: string,
    mainBlocks: any[]
  ): Promise<void> {
    // Add Transcript section header
    mainBlocks.push({
      object: "block",
      type: "heading_2",
      heading_2: {
        rich_text: [{ type: "text", text: { content: "📝 Full Meeting Transcript" } }],
      },
    });

    const transcriptChunks = this.chunkText(transcriptText, 1950);
    const maxBlocksPerPage = 90;

    if (transcriptChunks.length <= maxBlocksPerPage) {
      // All transcript fits on main page
      this.log('info', `Adding ${transcriptChunks.length} transcript chunks directly to main page`);
      
      for (const chunk of transcriptChunks) {
        mainBlocks.push({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [{ type: "text", text: { content: chunk } }],
          },
        });
      }
    } else {
      // Create sub-pages for transcript
      await this.createSubPagesForContent(
        client,
        parentPageId,
        transcriptChunks,
        'Transcript',
        mainBlocks,
        maxBlocksPerPage
      );
    }
  }

  /**
   * Create sub-pages for large content
   */
  private async createSubPagesForContent(
    client: Client,
    parentPageId: string,
    chunks: string[],
    contentType: string,
    mainBlocks: any[],
    maxBlocksPerPage: number
  ): Promise<void> {
    const pagesNeeded = Math.ceil(chunks.length / maxBlocksPerPage);
    this.log('info', `${contentType} content too long: splitting into ${pagesNeeded} sub-pages`);

    for (let pageNum = 1; pageNum <= pagesNeeded; pageNum++) {
      const startIdx = (pageNum - 1) * maxBlocksPerPage;
      const endIdx = Math.min(pageNum * maxBlocksPerPage, chunks.length);
      const pageChunks = chunks.slice(startIdx, endIdx);

      // Create sub-page
      const subPageTitle = pagesNeeded > 1 
        ? `${contentType} ${pageNum}/${pagesNeeded}` 
        : contentType;

      const subPageBlocks = [
        {
          object: "block",
          type: "callout",
          callout: {
            rich_text: [{ 
              type: "text", 
              text: { 
                content: `Part ${pageNum} of ${pagesNeeded} • ${pageChunks.length} sections • ${pageChunks.reduce((total, chunk) => total + chunk.length, 0)} characters` 
              } 
            }],
            icon: { emoji: contentType === 'Transcript' ? "📝" : "🤖" },
            color: "gray_background"
          }
        },
        { object: "block", type: "divider", divider: {} },
        ...pageChunks.map(chunk => ({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [{ type: "text", text: { content: chunk } }],
          },
        }))
      ];

      const subPage = await client.pages.create({
        parent: { page_id: parentPageId },
        properties: {
          title: [{ type: "text", text: { content: subPageTitle } }],
        },
        children: subPageBlocks as any,
      });

      // Add link to sub-page on main page
      mainBlocks.push({
        object: "block",
        type: "paragraph",
        paragraph: {
          rich_text: [
            { type: "text", text: { content: "📄 " } },
            {
              type: "text",
              text: { content: subPageTitle },
              annotations: { bold: true },
              href: `https://notion.so/${subPage.id.replace(/-/g, "")}`,
            },
            { 
              type: "text", 
              text: { 
                content: ` (${pageChunks.length} sections, ${pageChunks.reduce((total, chunk) => total + chunk.length, 0)} characters)` 
              } 
            },
          ],
        },
      });

      this.log('info', `Created ${contentType} sub-page ${pageNum}/${pagesNeeded}: ${subPage.id}`);
    }
  }

  /**
   * Add blocks to page in batches
   */
  private async addBlocksInBatches(
    client: Client,
    pageId: string,
    blocks: any[]
  ): Promise<void> {
    const maxBlocksPerRequest = 100;
    
    for (let i = 0; i < blocks.length; i += maxBlocksPerRequest) {
      const batch = blocks.slice(i, i + maxBlocksPerRequest);
      await client.blocks.children.append({
        block_id: pageId,
        children: batch,
      });
    }
  }

  /**
   * Chunk text into manageable pieces
   */
  chunkText(text: string, maxChunkLength: number): string[] {
    if (!text) return [];
    
    const safeMaxLength = maxChunkLength - 50; // Safety buffer
    if (text.length <= safeMaxLength) return [text];
    
    const chunks: string[] = [];
    let currentPosition = 0;
    
    while (currentPosition < text.length) {
      let endPosition = Math.min(currentPosition + safeMaxLength, text.length);
      
      // Try to break at natural boundaries
      if (endPosition < text.length) {
        const paragraphBreak = text.lastIndexOf("\n\n", endPosition);
        if (paragraphBreak > currentPosition && paragraphBreak > endPosition - 300) {
          endPosition = paragraphBreak + 2;
        } else {
          const lineBreak = text.lastIndexOf("\n", endPosition);
          if (lineBreak > currentPosition && lineBreak > endPosition - 200) {
            endPosition = lineBreak + 1;
          } else {
            const sentenceBreak = text.lastIndexOf(". ", endPosition);
            if (sentenceBreak > currentPosition && sentenceBreak > endPosition - 150) {
              endPosition = sentenceBreak + 2;
            } else {
              const wordBreak = text.lastIndexOf(" ", endPosition);
              if (wordBreak > currentPosition && wordBreak > endPosition - 50) {
                endPosition = wordBreak + 1;
              }
            }
          }
        }
      }
      
      if (endPosition - currentPosition > maxChunkLength) {
        endPosition = currentPosition + safeMaxLength;
      }
      
      const chunk = text.substring(currentPosition, endPosition).trim();
      if (chunk.length > 0) {
        chunks.push(chunk);
      }
      
      currentPosition = endPosition;
    }
    
    return chunks;
  }

  /**
   * Calculate content statistics
   */
  private calculateContentStats(data: any): string {
    const stats = [];
    
    if (data.notes) {
      const notesLength = data.notes.length;
      const notesPages = Math.ceil(notesLength / (90 * 1950));
      stats.push(`Notes: ${Math.round(notesLength / 1000)}k chars${notesPages > 1 ? ` (${notesPages} pages)` : ''}`);
    }
    
    if (data.content) {
      const transcriptLength = data.content.length;
      const transcriptPages = Math.ceil(transcriptLength / (90 * 1950));
      stats.push(`Transcript: ${Math.round(transcriptLength / 1000)}k chars${transcriptPages > 1 ? ` (${transcriptPages} pages)` : ''}`);
    }
    
    return stats.length > 0 ? `[${stats.join(', ')}]` : '';
  }

  /**
   * Format duration from minutes to human-readable format
   */
  private formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} mins`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return `${hours} ${hours === 1 ? 'hr' : 'hrs'}`;
    }
    
    return `${hours} ${hours === 1 ? 'hr' : 'hrs'} ${remainingMinutes} mins`;
  }

  /**
   * Sanitize multi-select values
   */
  private sanitizeMultiSelectValue(value: string): string {
    return value.replace(/,/g, " &");
  }

  /**
   * Validate page properties for Notion API compliance
   */
  private validatePageProperties(properties: any): void {
    Object.entries(properties).forEach(([key, prop]: [string, any]) => {
      if (prop.select && prop.select.name && prop.select.name.includes(",")) {
        throw new Error(`Select field "${key}" value contains a comma: ${prop.select.name}`);
      }
      if (prop.multi_select && Array.isArray(prop.multi_select)) {
        prop.multi_select.forEach((option: any) => {
          if (option.name && option.name.includes(",")) {
            throw new Error(`Multi-select field "${key}" value contains a comma: ${option.name}`);
          }
        });
      }
    });
  }
}
