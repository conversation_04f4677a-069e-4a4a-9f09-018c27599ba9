import { ReactNode } from "react";
import Sidebar from "./Sidebar";
import Header from "./Header";

interface AppLayoutProps {
  children: ReactNode;
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function AppLayout({
  children,
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: AppLayoutProps) {
  return (
    <div className="bg-background h-screen flex overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden h-screen">
        <Header 
          title={title} 
          onAddIntegration={onAddIntegration}
          tabs={tabs}
          currentTab={currentTab}
          onTabChange={onTabChange}
        />
        
        <main className="flex-1 overflow-y-auto bg-gradient-to-br from-background via-background to-muted/20 scrollbar-hide">
          <div className="container mx-auto p-6 max-w-7xl">
          {children}
          </div>
        </main>
      </div>
    </div>
  );
}
