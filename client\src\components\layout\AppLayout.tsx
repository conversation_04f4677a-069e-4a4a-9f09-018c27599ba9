import { ReactNode } from "react";
import Sidebar from "./Sidebar";
import Header from "./Header";

interface AppLayoutProps {
  children: ReactNode;
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function AppLayout({
  children,
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: AppLayoutProps) {
  return (
    <div className="bg-background h-screen flex overflow-hidden scrollbar-hide">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden scrollbar-hide">
        <Header 
          title={title} 
          onAddIntegration={onAddIntegration}
          tabs={tabs}
          currentTab={currentTab}
          onTabChange={onTabChange}
        />
        
        <main className="flex-1 overflow-y-auto bg-background p-6 scrollbar-hide">
          {children}
        </main>
      </div>
    </div>
  );
}
