import { Request, Response } from "express";
import { storage } from "../storage/index.js";
import { googleService } from "../services/google";
import { microsoftService } from "../services/microsoft";
import { cryptoService } from "../services/crypto-service.js";
import { openaiService } from "../services/openai-service";
import { simpleEmbeddingService as embeddingService } from "../services/simple-embedding-service.js";
import { syncNowSchema } from "@shared/schema";

/**
 * Controller for synchronization operations
 */
class SyncController {
  /**
   * Start a synchronization job for an integration
   * @param integrationId Integration ID to sync
   * @returns The created sync log
   */
  async startSync(integrationId: number): Promise<any> {
    try {
      // Get the integration
      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        throw new Error(`Integration ${integrationId} not found`);
      }

      // Check if integration is connected or configured
      if (
        (integration.status !== "connected" &&
          integration.status !== "configured") ||
        !integration.credentials
      ) {
        throw new Error(
          `Integration ${integrationId} is not properly configured`,
        );
      }

      // Update integration status to syncing
      await storage.updateIntegrationStatus(integrationId, "syncing");

      // Create a sync log
      const syncLog = await storage.createSyncLog({
        status: "running",
        integrationId,
        startTime: new Date(),
        itemsProcessed: 0,
        itemsSuccess: 0,
        itemsFailed: 0,
        details: {},
        error: null,
      });

      // Run the sync process in the background
      this.runSync(integration, syncLog.id).catch((error) => {
        console.error(`Error in sync job ${syncLog.id}:`, error);
      });

      return syncLog;
    } catch (error: any) {
      console.error(
        `Error starting sync for integration ${integrationId}:`,
        error,
      );

      // Update integration status back to connected
      await storage.updateIntegrationStatus(integrationId, "connected");

      throw error;
    }
  }

  /**
   * Run the synchronization process
   * @param integration Integration to sync
   * @param syncLogId ID of the sync log
   */
  private async runSync(integration: any, syncLogId: number): Promise<void> {
    try {
      console.log(`Starting sync for integration ${integration.id} (${integration.type})`);

      if (integration.type === "google-drive" || integration.type === "google_drive") {
        // Use the NEW reference-based sync for Google Drive
        await this.runReferenceBasedGoogleSync(integration, syncLogId);
      } else if (integration.type === "microsoft-teams" || integration.type === "microsoft_teams") {
        // Use the NEW reference-based sync for Microsoft Teams
        await this.runReferenceBasedMicrosoftSync(integration, syncLogId);
      } else {
        // For other integration types, we'll add them later
        console.log(`Integration type ${integration.type} not yet supported in reference-based sync`);
        throw new Error(`Integration type ${integration.type} not yet supported`);
      }

    } catch (error: any) {
      console.error(`Error in sync for integration ${integration.id}:`, error);
      throw error;
    }
  }

  /**
   * Reference-based Google Drive sync for storing file metadata references
   */
  private async runReferenceBasedGoogleSync(integration: any, syncLogId: number): Promise<void> {
    let itemsProcessed = 0;
    let itemsSuccess = 0;
    let itemsFailed = 0;
    let itemsSkipped = 0;

    try {
      console.log(`Starting incremental reference-based Google Drive sync for integration ${integration.id}`);

      // Get the source config - support both single folder (legacy) and multiple folders (new)
      const sourceConfig = integration.sourceConfig || {};
      let sourceFolderIds: string[] = [];

      if (sourceConfig.driveIds && Array.isArray(sourceConfig.driveIds)) {
        // New multi-folder configuration
        sourceFolderIds = sourceConfig.driveIds;
      } else if (sourceConfig.driveId) {
        // Legacy single folder configuration
        sourceFolderIds = [sourceConfig.driveId];
      } else {
        // Default to root if no configuration
        sourceFolderIds = ['root'];
      }

      console.log(`Scanning Google Drive folders: ${sourceFolderIds.join(', ')}`);

      // Get Google auth client
      const auth = await googleService.getAuthorizedClient(integration.credentials);

      // Get ALL files from all selected Google Drive folders
      let allFiles: any[] = [];
      for (const folderId of sourceFolderIds) {
        console.log(`Scanning folder: ${folderId}`);
        const folderFiles = await googleService.listAllFiles(auth, folderId, true);
        console.log(`Found ${folderFiles.length} files in folder ${folderId}`);

        // Add folder source info to each file for tracking
        const filesWithSource = folderFiles.map(file => ({
          ...file,
          _sourceFolderId: folderId
        }));

        allFiles = allFiles.concat(filesWithSource);
      }

      console.log(`Found ${allFiles.length} total files in Google Drive`);

      // Get existing files for this platform
      const existingFiles = await storage.getFilesForIntegration(integration.id, 'google_drive');
      console.log(`Found ${existingFiles.length} existing files in database`);

      // Create a map of existing files by externalId for quick lookup
      const existingFileMap = new Map<string, any>();
      for (const file of existingFiles) {
        existingFileMap.set(file.externalId, file);
      }

      // Track which external IDs we've seen in this sync
      const foundExternalIds = new Set<string>();

        // Update sync log with total files count
        await storage.updateSyncLog(syncLogId, {
          details: {
          totalFiles: allFiles.length,
          existingFiles: existingFiles.length,
          scanMode: 'incremental_reference_based',
          sourceFolderIds: sourceFolderIds,
          },
        });

      // Process each file for metadata extraction and reference storage
      for (const file of allFiles) {
          itemsProcessed++;
        foundExternalIds.add(file.id || '');

        // Minimal delay between files (sequential processing is very safe)
        if (itemsProcessed > 1) {
          console.log('Waiting 200ms before processing next file...');
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        try {
          // Check if we already have this file in our database
          const existingFile = existingFileMap.get(file.id || '');

          // Extract comprehensive metadata
          const fileMetadata = googleService.extractFileMetadata(file);

          if (!fileMetadata) {
            console.warn(`Could not extract metadata for file: ${file.name} (${file.id})`);
            itemsFailed++;
            continue;
          }

          // Check if file has changed (only if it exists)
          let needsProcessing = true;
          let needsAIProcessing = false;

          if (existingFile) {
            // Compare modification times
            const existingModified = existingFile.lastModified ? new Date(existingFile.lastModified) : new Date(0);
            const sourceModified = fileMetadata.lastModified ? new Date(fileMetadata.lastModified) : new Date();

            if (sourceModified <= existingModified && existingFile.status === 'active') {
              // File hasn't changed, skip processing
              console.log(`Skipping unchanged file: ${fileMetadata.fileName}`);
              itemsSkipped++;
              needsProcessing = false;
            } else {
              console.log(`File changed, will reprocess: ${fileMetadata.fileName}`);
              needsAIProcessing = true;
            }
          } else {
            // New file, needs full processing
            console.log(`New file detected: ${fileMetadata.fileName}`);
            needsAIProcessing = true;
          }

          // Skip AI processing if file hasn't changed
          if (!needsProcessing) {
            // Just update status to active if it was deleted before
            if (existingFile && existingFile.status !== 'active') {
              await storage.updateFileStatus(existingFile.id, 'active');
              console.log(`Reactivated file: ${fileMetadata.fileName}`);
            }
            continue;
          }

          // Enhanced metadata for meeting transcripts using AI (only if needed)
          if (needsAIProcessing && fileMetadata.fileType === 'transcript' && integration.isLlmEnabled && openaiService.isInitialized()) {
            try {
              console.log(`Extracting AI metadata for transcript: ${file.name}`);

              // For transcripts, we still extract some content for AI analysis
              // but we don't store the full content, just enhanced metadata
              const { transcriptText } = await googleService.extractDocContent(auth, file.id || "");

              if (transcriptText) {
                const aiMetadata = await openaiService.extractMetadata(
                  transcriptText.substring(0, 6000), // Limit content for AI analysis
                  file.name || "Unknown File",
                );

                // Merge AI metadata into our file metadata
                fileMetadata.extractedMetadata = {
                  ...fileMetadata.extractedMetadata,
                  aiExtracted: {
                    attendees: aiMetadata.attendees,
                    topics: aiMetadata.topics,
                    meetingTitle: aiMetadata.title,
                    date: aiMetadata.date,
                    time: aiMetadata.time,
                    summary: aiMetadata.summary,
                  }
                };
              }
            } catch (aiError) {
              console.error(`Error extracting AI metadata for ${file.name}:`, aiError);
              // Continue without AI metadata
            }
          }

          let savedFile: any;

          if (existingFile) {
            // Update existing file with latest metadata
            savedFile = await storage.updateFile(existingFile.id, {
              fileName: fileMetadata.fileName,
              lastModified: fileMetadata.lastModified,
              extractedMetadata: fileMetadata.extractedMetadata,
              isShared: fileMetadata.isShared,
              sharedWith: fileMetadata.sharedWith,
              status: 'active', // Ensure it's marked as active
              updatedAt: new Date(),
            });

            console.log(`Updated file reference: ${fileMetadata.fileName}`);
          } else {
            // Create new file reference
            // Handle large file sizes that exceed PostgreSQL integer limits
            const safeFileSize = fileMetadata.fileSize && fileMetadata.fileSize > 2147483647
              ? 2147483647 // Max PostgreSQL integer value
              : fileMetadata.fileSize;

            savedFile = await storage.createFile({
              externalId: fileMetadata.externalId,
              fileName: fileMetadata.fileName,
              fileType: fileMetadata.fileType,
              mimeType: fileMetadata.mimeType,
              fileSize: safeFileSize,
              platform: fileMetadata.platform,
              sourceUrl: fileMetadata.sourceUrl,
              downloadUrl: fileMetadata.downloadUrl,
              thumbnailUrl: fileMetadata.thumbnailUrl,
              userId: fileMetadata.userId,
              lastModified: fileMetadata.lastModified,
              isShared: fileMetadata.isShared,
              sharedWith: fileMetadata.sharedWith,
              extractedMetadata: fileMetadata.extractedMetadata,
              status: 'active',
            });

            console.log(`Created new file reference: ${fileMetadata.fileName}`);
          }

          // Generate embeddings for RAG functionality (if enabled and content available)
          if (needsAIProcessing && embeddingService.isInitialized()) {
            try {
              // Check if file already has embeddings
              const hasEmbeddings = await embeddingService.hasEmbeddings(savedFile.id);

              if (!hasEmbeddings) {
                console.log(`Generating embeddings for file: ${fileMetadata.fileName}`);

                // Extract ALL possible content for comprehensive vectorization
                let contentToVectorize = "";

                try {
                  // 1. Always include file metadata (name, description, etc.)
                  const metadataContent = [
                    `File: ${fileMetadata.fileName}`,
                    `Type: ${fileMetadata.fileType}`,
                    `Platform: ${fileMetadata.platform || 'google_drive'}`,
                  ];

                  if (fileMetadata.extractedMetadata) {
                    const metadata = fileMetadata.extractedMetadata;
                    if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
                    if (metadata.title && metadata.title !== fileMetadata.fileName) {
                      metadataContent.push(`Title: ${metadata.title}`);
                    }
                    if (metadata.topics && Array.isArray(metadata.topics)) {
                      metadataContent.push(`Topics: ${metadata.topics.join(', ')}`);
                    }
                    if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
                  }

                  contentToVectorize = metadataContent.join('\n') + '\n\n';

                  // 2. Comprehensive content extraction with specialized document processing
                  try {
                    console.log(`[SYNC] Extracting content for ${fileMetadata.fileName}...`);

                    let contentExtracted = false;

                    // First, try specialized document processing for supported file types
                    const { pdfService } = await import('../services/pdf-service.js');
                    const { wordService } = await import('../services/word-service.js');

                    // Check if this is a PDF file
                    if ((pdfService as any).isPDFFile && (pdfService as any).isPDFFile(fileMetadata.fileName, file.mimeType)) {
                      try {
                        console.log(`[GOOGLE] Processing PDF: ${fileMetadata.fileName}`);
                        const pdfBuffer = await googleService.downloadPDFContent(auth, file.id || "", fileMetadata.fileName);

                        if (pdfBuffer) {
                          const platformMetadata = {
                            platform: 'google_drive' as const,
                            sourceType: 'google_drive_folder',
                            sourceContext: `Google Drive`,
                            folderPath: file.parents && file.parents.length > 0 ? `Folder ID: ${file.parents[0]}` : 'Root',
                            owner: fileMetadata.extractedMetadata?.owner || 'Unknown',
                            lastModified: fileMetadata.lastModified || new Date()
                          };

                          const pdfResult = await pdfService.processPDFFile(
                            pdfBuffer,
                            fileMetadata.fileName,
                            platformMetadata,
                            integration.isLlmEnabled && openaiService.isInitialized()
                          );

                          if (pdfResult.success && pdfResult.vectorizationContent) {
                            console.log(`[GOOGLE] Successfully processed PDF: ${fileMetadata.fileName}`);
                            console.log(`[GOOGLE] PDF content length: ${pdfResult.vectorizationContent.length} characters`);

                            // Use the comprehensive vectorization content from PDF service
                            contentToVectorize = pdfResult.vectorizationContent;
                            contentExtracted = true;

                            // Update file metadata with PDF-extracted metadata
                            if (pdfResult.metadata) {
                              fileMetadata.extractedMetadata = {
                                ...fileMetadata.extractedMetadata,
                                pdfMetadata: pdfResult.metadata,
                                hasExtractedContent: true,
                                contentType: 'pdf'
                              };
                            }
                          } else {
                            console.log(`[GOOGLE] PDF processing failed for ${fileMetadata.fileName}: ${pdfResult.error}`);
                          }
                        } else {
                          console.log(`[GOOGLE] Could not download PDF content for: ${fileMetadata.fileName}`);
                        }
                      } catch (pdfError: any) {
                        console.error(`[GOOGLE] Error processing PDF ${fileMetadata.fileName}:`, pdfError.message);
                        // Continue to fallback methods
                      }
                    }
                    // Check if this is a Word document
                    else if ((wordService as any).isWordFile && (wordService as any).isWordFile(fileMetadata.fileName, file.mimeType)) {
                      try {
                        console.log(`[GOOGLE] Processing Word document: ${fileMetadata.fileName}`);
                        const wordBuffer = await googleService.downloadWordContent(auth, file.id || "", fileMetadata.fileName);

                        if (wordBuffer) {
                          const platformMetadata = {
                            platform: 'google_drive' as const,
                            sourceType: 'google_drive_folder',
                            sourceContext: `Google Drive`,
                            folderPath: file.parents && file.parents.length > 0 ? `Folder ID: ${file.parents[0]}` : 'Root',
                            owner: fileMetadata.extractedMetadata?.owner || 'Unknown',
                            lastModified: fileMetadata.lastModified || new Date()
                          };

                          const wordResult = await wordService.processWordFile(
                            wordBuffer,
                            fileMetadata.fileName,
                            platformMetadata,
                            integration.isLlmEnabled && openaiService.isInitialized()
                          );

                          if (wordResult.success && wordResult.vectorizationContent) {
                            console.log(`[GOOGLE] Successfully processed Word document: ${fileMetadata.fileName}`);
                            console.log(`[GOOGLE] Word content length: ${wordResult.vectorizationContent.length} characters`);

                            // Use the comprehensive vectorization content from Word service
                            contentToVectorize = wordResult.vectorizationContent;
                            contentExtracted = true;

                            // Update file metadata with Word-extracted metadata
                            if (wordResult.metadata) {
                              fileMetadata.extractedMetadata = {
                                ...fileMetadata.extractedMetadata,
                                wordMetadata: wordResult.metadata,
                                hasExtractedContent: true,
                                contentType: 'word'
                              };
                            }
                          } else {
                            console.log(`[GOOGLE] Word processing failed for ${fileMetadata.fileName}: ${wordResult.error}`);
                          }
                        } else {
                          console.log(`[GOOGLE] Could not download Word content for: ${fileMetadata.fileName}`);
                        }
                      } catch (wordError: any) {
                        console.error(`[GOOGLE] Error processing Word document ${fileMetadata.fileName}:`, wordError.message);
                        // Continue to fallback methods
                      }
                    }

                    // If specialized processing didn't work, try universal content extraction
                    if (!contentExtracted) {
                      try {
                        // Try the universal extractFileContent method as fallback
                        const fileContent = await googleService.extractFileContent(auth, file);
                        if (fileContent && fileContent.trim().length > 0) {
                          contentToVectorize += `Content:\n${fileContent}`;
                          contentExtracted = true;
                          console.log(`[GOOGLE] Universal extraction: ${fileContent.length} characters from ${fileMetadata.fileName}`);
                        }
                      } catch (universalError: any) {
                        console.log(`[GOOGLE] Universal extraction failed for ${fileMetadata.fileName}: ${universalError.message}`);

                        // Final fallback: try Google Docs API for document types
                        try {
                          const { transcriptText } = await googleService.extractDocContent(auth, file.id || "");
                          if (transcriptText && transcriptText.trim().length > 0) {
                            contentToVectorize += `Content:\n${transcriptText}`;
                            contentExtracted = true;
                            console.log(`[GOOGLE] Docs API extraction: ${transcriptText.length} characters from ${fileMetadata.fileName}`);
                          }
                        } catch (docsError: any) {
                          console.log(`[GOOGLE] Docs API extraction failed for ${fileMetadata.fileName}: ${docsError.message}`);
                        }
                      }
                    }

                    if (!contentExtracted) {
                      console.log(`[GOOGLE] No content extracted for ${fileMetadata.fileName}, using metadata-only vectorization`);
                    }

                  } catch (contentError: any) {
                    console.error(`[GOOGLE] Content extraction error for ${fileMetadata.fileName}:`, contentError.message);
                    console.log(`[GOOGLE] Falling back to metadata-only vectorization for ${fileMetadata.fileName}`);
                  }

                  // 3. For files we can't extract content from, create meaningful searchable text
                  if (contentToVectorize.trim().length < 100) {
                    // Add file path and folder information for better searchability
                    if (file.parents && file.parents.length > 0) {
                      contentToVectorize += `\nLocation: Folder ID ${file.parents[0]}`;
                    }

                    // Add MIME type information
                    if (file.mimeType) {
                      contentToVectorize += `\nMIME Type: ${file.mimeType}`;

                      // Add human-readable file type descriptions
                      const typeDescriptions: { [key: string]: string } = {
                        'application/vnd.google-apps.document': 'Google Document - text document',
                        'application/vnd.google-apps.spreadsheet': 'Google Spreadsheet - data and calculations',
                        'application/vnd.google-apps.presentation': 'Google Presentation - slides and presentations',
                        'application/vnd.google-apps.folder': 'Google Drive Folder - contains other files',
                        'application/pdf': 'PDF Document - portable document format',
                        'image/jpeg': 'JPEG Image - photo or picture',
                        'image/png': 'PNG Image - photo or picture',
                        'video/mp4': 'MP4 Video - video file',
                        'audio/mpeg': 'MP3 Audio - audio file',
                        'text/plain': 'Text File - plain text document',
                      };

                      const description = typeDescriptions[file.mimeType];
                      if (description) {
                        contentToVectorize += `\nFile Description: ${description}`;
                      }
                    }

                    // Add creation/modification dates for temporal search
                    if (file.createdTime) {
                      contentToVectorize += `\nCreated: ${new Date(file.createdTime).toLocaleDateString()}`;
                    }
                    if (file.modifiedTime) {
                      contentToVectorize += `\nModified: ${new Date(file.modifiedTime).toLocaleDateString()}`;
                    }
                  }

                  // Always generate embeddings if we have any content
                  if (contentToVectorize.trim().length > 10) {
                    console.log(`Vectorizing ${contentToVectorize.length} characters for ${fileMetadata.fileName}`);

                    // Generate embeddings sequentially (await to prevent parallel processing)
                    try {
                      await embeddingService.processFileForEmbeddings(savedFile.id, contentToVectorize);
                      console.log(`✅ Successfully generated embeddings for: ${fileMetadata.fileName}`);
                    } catch (embeddingError) {
                      console.error(`❌ Error generating embeddings for ${fileMetadata.fileName}:`, embeddingError);
                    }
                  } else {
                    console.log(`⚠️ Minimal content for ${fileMetadata.fileName}, but still vectorizing metadata`);
                    // Even with minimal content, vectorize the filename and type
                    const minimalContent = `File: ${fileMetadata.fileName}\nType: ${fileMetadata.fileType}`;
                    try {
                      await embeddingService.processFileForEmbeddings(savedFile.id, minimalContent);
                    } catch (embeddingError) {
                      console.error(`❌ Error generating minimal embeddings for ${fileMetadata.fileName}:`, embeddingError);
                    }
                  }

                } catch (vectorizationError) {
                  console.error(`❌ Error in vectorization process for ${fileMetadata.fileName}:`, vectorizationError);
                  // Still try to create minimal embeddings
                  const fallbackContent = `File: ${fileMetadata.fileName}\nType: ${fileMetadata.fileType}`;
                  try {
                    await embeddingService.processFileForEmbeddings(savedFile.id, fallbackContent);
                  } catch (fallbackError) {
                    console.error(`❌ Complete vectorization failure for ${fileMetadata.fileName}:`, fallbackError);
                  }
                }
              } else {
                console.log(`✅ Embeddings already exist for: ${fileMetadata.fileName}`);
              }
            } catch (embeddingError) {
              console.error(`Error processing embeddings for ${fileMetadata.fileName}:`, embeddingError);
              // Continue with sync even if embedding generation fails
            }
          }

          // Create a sync item for tracking (keeping the existing sync tracking)
          await storage.createSyncItem({
            type: fileMetadata.fileType,
            title: fileMetadata.fileName,
              status: "success",
            integrationId: integration.id,
            syncLogId,
            externalId: fileMetadata.externalId,
            sourceUrl: fileMetadata.sourceUrl,
            destinationUrl: null, // We're not creating Notion pages anymore
              metadata: {
              platform: 'google_drive',
              fileId: savedFile.id,
              referenceSync: true,
              incremental: true,
              wasUpdated: !!existingFile,
              },
            });

            itemsSuccess++;
          } catch (itemError: any) {
            itemsFailed++;
          console.error(`Error processing file ${file.name || "Unknown file"} (${file.id || "Unknown ID"}):`, itemError);

          // Create a failed sync item for tracking
          await storage.createSyncItem({
            type: "unknown",
            title: file.name || "Unknown file",
            status: "failed",
            integrationId: integration.id,
            syncLogId,
            externalId: file.id || "",
            sourceUrl: file.webViewLink || "",
                error: itemError.message || "Unknown error occurred",
            metadata: {
              platform: 'google_drive',
              referenceSync: true,
              incremental: true,
            },
          });
        }

        // Update sync log progress periodically
        if (itemsProcessed % 50 === 0) {
          await storage.updateSyncLog(syncLogId, {
            itemsProcessed,
            itemsSuccess,
            itemsFailed,
          });
        }
      }

      // Mark files as deleted if they're no longer in the source
      let deletedCount = 0;
      const missingExternalIds: string[] = [];

      for (const existingFile of existingFiles) {
        if (!foundExternalIds.has(existingFile.externalId) && existingFile.status === 'active') {
          missingExternalIds.push(existingFile.externalId);
          deletedCount++;
        }
      }

      if (missingExternalIds.length > 0) {
        console.log(`Marking ${missingExternalIds.length} files as deleted for platform: google_drive`);
        await storage.markFilesAsDeleted(missingExternalIds, 'google_drive');
        console.log(`Marked ${deletedCount} files as deleted (no longer in source)`);
      }

      // Final sync log update
      const syncStatus = itemsFailed === 0 ? "success" : itemsSuccess > 0 ? "partial" : "failed";

        await storage.updateSyncLog(syncLogId, {
          status: syncStatus,
          endTime: new Date(),
          itemsProcessed,
          itemsSuccess,
          itemsFailed,
          details: {
          totalFiles: allFiles.length,
          existingFiles: existingFiles.length,
          newFiles: itemsSuccess - (itemsProcessed - allFiles.length),
          updatedFiles: itemsProcessed - allFiles.length,
          skippedFiles: itemsSkipped,
          deletedFiles: deletedCount,
          scanMode: 'incremental_reference_based',
          sourceFolderIds: sourceFolderIds,
            completedAt: new Date().toISOString(),
          },
        });

      // Update integration status
      await storage.updateIntegrationStatus(
        integration.id,
        syncStatus === "failed" ? "error" : "connected"
      );

      console.log(`Incremental sync completed. Processed: ${itemsProcessed}, Success: ${itemsSuccess}, Failed: ${itemsFailed}, Skipped: ${itemsSkipped}, Deleted: ${deletedCount}`);

    } catch (error: any) {
      console.error(`Error in incremental reference-based Google Drive sync:`, error);

      // Update sync log with error
      await storage.updateSyncLog(syncLogId, {
        status: "failed",
        endTime: new Date(),
        itemsProcessed,
        itemsSuccess,
        itemsFailed,
        error: error.message || "Unknown error occurred",
      });

      // Update integration status
      await storage.updateIntegrationStatus(integration.id, "error");

      throw error;
    }
  }

  /**
   * Reference-based Microsoft Teams sync for storing file metadata references
   */
  private async runReferenceBasedMicrosoftSync(integration: any, syncLogId: number): Promise<void> {
    let itemsProcessed = 0;
    let itemsSuccess = 0;
    let itemsFailed = 0;
    let itemsSkipped = 0;

    try {
      console.log(`Starting comprehensive Microsoft Teams sync for integration ${integration.id}`);

      // Get Microsoft credentials (decrypt them)
      const credentialsJson = await cryptoService.decrypt(integration.credentials);
      const credentials = JSON.parse(credentialsJson);

      // Use the comprehensive sync method that aggregates from all sources
      const syncResult = await microsoftService.syncAllSources(credentials);
      
      console.log(`Found ${syncResult.files.length} total files across all Microsoft sources`);

      // Get existing files for this platform
      const existingFiles = await storage.getFilesForIntegration(integration.id, 'microsoft_teams');
      console.log(`Found ${existingFiles.length} existing files in database`);

      // Create a map of existing files by externalId for quick lookup
      const existingFileMap = new Map<string, any>();
      for (const file of existingFiles) {
        existingFileMap.set(file.externalId, file);
      }

      // Track which external IDs we've seen in this sync
      const foundExternalIds = new Set<string>();

      // Update sync log with total files count
      await storage.updateSyncLog(syncLogId, {
        details: {
          totalFiles: syncResult.files.length,
          existingFiles: existingFiles.length,
          scanMode: 'comprehensive_reference_based',
          sources: syncResult.sources,
          summary: syncResult.summary,
        },
      });

      // Process each file for metadata extraction and reference storage
      for (const file of syncResult.files) {
        itemsProcessed++;
        foundExternalIds.add(file.externalId || '');

        // Minimal delay between files (sequential processing is very safe)
        if (itemsProcessed > 1) {
          console.log('Waiting 200ms before processing next Teams file...');
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        try {
          // Check if we already have this file in our database
          const existingFile = existingFileMap.get(file.externalId || '');
          
          // Check if file has changed (only if it exists)
          let needsProcessing = true;
          let needsAIProcessing = false;

          if (existingFile) {
            // Compare modification times
            const existingModified = existingFile.lastModified ? new Date(existingFile.lastModified) : new Date(0);
            const sourceModified = file.lastModified ? new Date(file.lastModified) : new Date();
            
            if (sourceModified <= existingModified && existingFile.status === 'active') {
              // File hasn't changed, skip processing
              console.log(`Skipping unchanged file: ${file.fileName}`);
              itemsSkipped++;
              needsProcessing = false;
            } else {
              console.log(`File changed, will reprocess: ${file.fileName}`);
              needsAIProcessing = true;
            }
          } else {
            // New file, needs full processing
            console.log(`New file detected: ${file.fileName}`);
            needsAIProcessing = true;
          }

          // Skip AI processing if file hasn't changed
          if (!needsProcessing) {
            // Just update status to active if it was deleted before
            if (existingFile && existingFile.status !== 'active') {
              await storage.updateFileStatus(existingFile.id, 'active');
              console.log(`Reactivated file: ${file.fileName}`);
            }
            continue;
          }

          // Enhanced metadata for meeting transcripts using AI (only if needed)
          if (needsAIProcessing && file.fileType === 'transcript' && integration.isLlmEnabled && openaiService.isInitialized()) {
            try {
              console.log(`Extracting enhanced AI metadata for Microsoft Teams file: ${file.fileName}`);
              
              // For Microsoft Teams, we now have enhanced metadata from Graph API
              // Pass this to the OpenAI service for better extraction
              const aiMetadata = await openaiService.extractMetadata(
                '', // We don't have the file content, but metadata is more accurate now
                file.fileName,
                file.extractedMetadata // Pass the enhanced Teams metadata
              );
              
              // Merge AI metadata with existing Teams metadata
              file.extractedMetadata = {
                ...file.extractedMetadata,
                aiExtractedTitle: aiMetadata.title,
                aiExtractedAttendees: aiMetadata.attendees,
                aiExtractedTopics: aiMetadata.topics,
                aiExtractedDate: aiMetadata.date,
                aiExtractedTime: aiMetadata.time,
                aiProcessed: true,
                aiProcessedAt: new Date().toISOString(),
                enhancedWithGraphAPI: !!(file.extractedMetadata as any)?.meetingAttendees || !!(file.extractedMetadata as any)?.calendarAttendees
              };
              
              console.log(`Enhanced AI metadata extracted for ${file.fileName}:`, {
                title: aiMetadata.title,
                attendeeCount: aiMetadata.attendees.length,
                topics: aiMetadata.topics,
                hasGraphAPIData: !!(file.extractedMetadata as any)?.meetingAttendees || !!(file.extractedMetadata as any)?.calendarAttendees
              });
            } catch (aiError) {
              console.error(`Error extracting AI metadata for ${file.fileName}:`, aiError);
              // Continue without AI metadata but mark as attempted
              file.extractedMetadata = {
                ...file.extractedMetadata,
                aiProcessed: false,
                aiError: (aiError as any)?.message || 'Unknown AI processing error',
                aiProcessedAt: new Date().toISOString()
              };
            }
          }

          let savedFile: any;

          if (existingFile) {
            // Update existing file with latest metadata
            savedFile = await storage.updateFile(existingFile.id, {
              fileName: file.fileName,
              lastModified: file.lastModified,
              extractedMetadata: file.extractedMetadata,
              status: 'active', // Ensure it's marked as active
              updatedAt: new Date(),
            });
            
            console.log(`Updated file reference: ${file.fileName}`);
          } else {
            // Create new file reference
            savedFile = await storage.createFile({
              externalId: file.externalId,
              fileName: file.fileName,
              fileType: file.fileType,
              platform: file.platform,
              sourceUrl: file.sourceUrl,
              userId: file.userId,
              lastModified: file.lastModified,
              extractedMetadata: file.extractedMetadata as any,
              status: 'active',
            });
            
            console.log(`Created new file reference: ${file.fileName}`);
          }

          // Generate embeddings for RAG functionality (similar to Google sync)
          if (embeddingService.isInitialized()) {
            try {
              // Check if file already has embeddings
              const hasEmbeddings = await embeddingService.hasEmbeddings(savedFile.id);

              if (!hasEmbeddings) {
                console.log(`Generating embeddings for Teams file: ${file.fileName}`);

                // Build comprehensive content for vectorization
                let contentToVectorize = "";

                try {
                  // 1. Always include file metadata (name, description, etc.)
                  const metadataContent = [
                    `File: ${file.fileName}`,
                    `Type: ${file.fileType}`,
                    `Platform: microsoft_teams`,
                  ];

                  if (file.extractedMetadata) {
                    const metadata = file.extractedMetadata as any;
                    
                    // Add general metadata fields
                    if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
                    if (metadata.title && metadata.title !== file.fileName) {
                      metadataContent.push(`Title: ${metadata.title}`);
                    }
                    if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
                    
                    // Add Teams-specific metadata
                    if (metadata._sourceType) {
                      metadataContent.push(`Source Type: ${metadata._sourceType}`);
                    }
                    if (metadata._sourceContext) {
                      metadataContent.push(`Source: ${metadata._sourceContext}`);
                    }
                    if (metadata._teamName) {
                      metadataContent.push(`Team: ${metadata._teamName}`);
                    }
                    if (metadata._channelName) {
                      metadataContent.push(`Channel: ${metadata._channelName}`);
                    }
                    if (metadata._siteName) {
                      metadataContent.push(`SharePoint Site: ${metadata._siteName}`);
                    }
                    if (metadata._driveName) {
                      metadataContent.push(`Drive: ${metadata._driveName}`);
                    }
                    
                    // Add meeting-specific metadata
                    if (metadata.meetingSubject) {
                      metadataContent.push(`Meeting Subject: ${metadata.meetingSubject}`);
                    }
                    if (metadata.calendarSubject) {
                      metadataContent.push(`Calendar Subject: ${metadata.calendarSubject}`);
                    }
                    if (metadata.meetingAttendees && Array.isArray(metadata.meetingAttendees)) {
                      const attendeeNames = metadata.meetingAttendees
                        .map((a: any) => a.displayName || a.email)
                        .filter(Boolean)
                        .join(', ');
                      if (attendeeNames) {
                        metadataContent.push(`Meeting Attendees: ${attendeeNames}`);
                      }
                    }
                    if (metadata.calendarAttendees && Array.isArray(metadata.calendarAttendees)) {
                      const attendeeNames = metadata.calendarAttendees
                        .map((a: any) => a.emailAddress?.name || a.emailAddress?.address)
                        .filter(Boolean)
                        .join(', ');
                      if (attendeeNames) {
                        metadataContent.push(`Calendar Attendees: ${attendeeNames}`);
                      }
                    }
                    if (metadata.organizer) {
                      metadataContent.push(`Organizer: ${metadata.organizer.displayName || metadata.organizer.email}`);
                    }
                    if (metadata.meetingOrganizer) {
                      metadataContent.push(`Meeting Organizer: ${metadata.meetingOrganizer.displayName || metadata.meetingOrganizer.email}`);
                    }
                    if (metadata.calendarOrganizer) {
                      metadataContent.push(`Calendar Organizer: ${metadata.calendarOrganizer.displayName || metadata.calendarOrganizer.email}`);
                    }
                    
                    // Add AI-extracted metadata if available
                    if (metadata.aiExtractedTitle) {
                      metadataContent.push(`AI Title: ${metadata.aiExtractedTitle}`);
                    }
                    if (metadata.aiExtractedAttendees && Array.isArray(metadata.aiExtractedAttendees)) {
                      metadataContent.push(`AI Attendees: ${metadata.aiExtractedAttendees.join(', ')}`);
                    }
                    if (metadata.aiExtractedTopics && Array.isArray(metadata.aiExtractedTopics)) {
                      metadataContent.push(`AI Topics: ${metadata.aiExtractedTopics.join(', ')}`);
                    }
                    if (metadata.aiExtractedDate) {
                      metadataContent.push(`AI Date: ${metadata.aiExtractedDate}`);
                    }
                    if (metadata.aiExtractedTime) {
                      metadataContent.push(`AI Time: ${metadata.aiExtractedTime}`);
                    }
                  }

                  contentToVectorize = metadataContent.join('\n') + '\n\n';

                  // 2. Try to extract content for PDFs and Word documents
                  const { pdfService } = await import('../services/pdf-service.js');
                  const { wordService } = await import('../services/word-service.js');

                  const isPdf = (pdfService as any).isPDFFile && (pdfService as any).isPDFFile(file.fileName, (file.extractedMetadata as any)?.mimeType);
                  const isWord = (wordService as any).isWordFile && (wordService as any).isWordFile(file.fileName, (file.extractedMetadata as any)?.mimeType);

                  if (file.fileType === 'document' && (isPdf || isWord)) {
                    try {
                      const fileType = isPdf ? 'PDF' : 'Word document';
                      console.log(`[TEAMS] Attempting ${fileType} content extraction for: ${file.fileName}`);

                      // Get drive ID and file ID from metadata
                      const metadata = file.extractedMetadata as any;
                      const driveId = metadata?.driveId;
                      const fileId = file.externalId;

                      if (driveId && fileId) {
                        // Download the file buffer first
                        const client = (microsoftService as any).createGraphClient(credentials);
                        const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
                          .responseType('arraybuffer' as any)
                          .get();

                        if (downloadResponse) {
                          // Convert response to buffer (same logic as microsoft-service.ts)
                          let buffer: Buffer;
                          if (Buffer.isBuffer(downloadResponse)) {
                            buffer = downloadResponse;
                          } else if (downloadResponse instanceof ArrayBuffer) {
                            buffer = Buffer.from(downloadResponse);
                          } else if (downloadResponse instanceof Uint8Array) {
                            buffer = Buffer.from(downloadResponse);
                          } else if (typeof downloadResponse === 'string') {
                            buffer = Buffer.from(downloadResponse, 'binary');
                          } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
                            buffer = Buffer.from(downloadResponse.buffer);
                          } else {
                            throw new Error('Unexpected response type');
                          }

                          // Build platform metadata for document service
                          const platformMetadata = {
                            platform: 'microsoft_teams' as const,
                            sourceType: metadata._sourceType || 'teams_file',
                            sourceContext: metadata._sourceContext || `Microsoft Teams file`,
                            folderPath: metadata.folderPath || metadata._sourceFolderId || 'Unknown',
                            owner: metadata.owner || metadata.ownerName || 'Unknown',
                            lastModified: file.lastModified || new Date()
                          };

                          // Process document using appropriate centralized service
                          let documentResult;
                          if (isPdf) {
                            documentResult = await pdfService.processPDFFile(
                              buffer,
                              file.fileName,
                              platformMetadata,
                              integration.isLlmEnabled && openaiService.isInitialized()
                            );
                          } else if (isWord) {
                            documentResult = await wordService.processWordFile(
                              buffer,
                              file.fileName,
                              platformMetadata,
                              integration.isLlmEnabled && openaiService.isInitialized()
                            );
                          }

                          if (documentResult && documentResult.success && documentResult.vectorizationContent) {
                            console.log(`[TEAMS] Successfully processed ${fileType} using centralized service: ${file.fileName}`);
                            console.log(`[TEAMS] ${fileType} content length: ${documentResult.vectorizationContent.length} characters`);

                            // Use the comprehensive vectorization content from document service
                            contentToVectorize = documentResult.vectorizationContent;

                            // Update file metadata with document-extracted metadata
                            if (documentResult.metadata) {
                              file.extractedMetadata = {
                                ...file.extractedMetadata,
                                [isPdf ? 'pdfMetadata' : 'wordMetadata']: documentResult.metadata,
                                hasExtractedContent: true,
                                contentType: isPdf ? 'pdf' : 'word'
                              };
                            }
                          } else {
                            console.log(`[TEAMS] Centralized ${fileType} processing failed for ${file.fileName}: ${documentResult?.error || 'Unknown error'}`);
                            // Continue with metadata-only vectorization
                          }
                        }
                      } else {
                        console.log(`[TEAMS] Missing driveId or fileId for ${fileType} extraction: ${file.fileName}`);
                      }
                    } catch (documentError: any) {
                      const fileType = isPdf ? 'PDF' : 'Word document';
                      console.error(`[TEAMS] Error extracting ${fileType} content for ${file.fileName}:`, documentError.message);
                      // Continue with metadata-only vectorization
                    }
                  }

                  // 3. Add file type descriptions and context
                  if (file.fileType === 'transcript') {
                    contentToVectorize += `File Description: Microsoft Teams meeting transcript or recording file\n`;
                  } else if (file.fileType === 'document') {
                    contentToVectorize += `File Description: Microsoft Teams shared document\n`;
                  } else {
                    contentToVectorize += `File Description: Microsoft Teams file - ${file.fileType}\n`;
                  }

                  // 4. Add temporal context
                  if (file.lastModified) {
                    contentToVectorize += `Last Modified: ${new Date(file.lastModified).toLocaleDateString()}\n`;
                  }
                  if (file.createdAt) {
                    contentToVectorize += `Created: ${new Date(file.createdAt).toLocaleDateString()}\n`;
                  }

                  // 5. Add meeting time context if available
                  if (file.extractedMetadata) {
                    const metadata = file.extractedMetadata as any;
                    if (metadata.meetingStartTime) {
                      contentToVectorize += `Meeting Start: ${new Date(metadata.meetingStartTime).toLocaleString()}\n`;
                    }
                    if (metadata.calendarStartTime) {
                      contentToVectorize += `Calendar Start: ${new Date(metadata.calendarStartTime).toLocaleString()}\n`;
                    }
                  }

                  // Always generate embeddings if we have any content
                  if (contentToVectorize.trim().length > 10) {
                    console.log(`Vectorizing ${contentToVectorize.length} characters for Teams file: ${file.fileName}`);

                    // Generate embeddings sequentially (await to prevent parallel processing)
                    try {
                      await embeddingService.processFileForEmbeddings(savedFile.id, contentToVectorize);
                      console.log(`✅ Successfully generated embeddings for Teams file: ${file.fileName}`);
                    } catch (embeddingError) {
                      console.error(`❌ Error generating embeddings for Teams file ${file.fileName}:`, embeddingError);
                    }
                  } else {
                    console.log(`⚠️ Minimal content for Teams file ${file.fileName}, using fallback vectorization`);
                    // Even with minimal content, vectorize the filename and type
                    const minimalContent = `File: ${file.fileName}\nType: ${file.fileType}\nPlatform: microsoft_teams`;
                    try {
                      await embeddingService.processFileForEmbeddings(savedFile.id, minimalContent);
                    } catch (embeddingError) {
                      console.error(`❌ Error generating minimal embeddings for Teams file ${file.fileName}:`, embeddingError);
                    }
                  }

                } catch (vectorizationError: any) {
                  console.error(`❌ Error in vectorization process for Teams file ${file.fileName}:`, vectorizationError);
                  // Still try to create minimal embeddings
                  const fallbackContent = `File: ${file.fileName}\nType: ${file.fileType}\nPlatform: microsoft_teams`;
                  try {
                    await embeddingService.processFileForEmbeddings(savedFile.id, fallbackContent);
                  } catch (fallbackError) {
                    console.error(`❌ Complete vectorization failure for Teams file ${file.fileName}:`, fallbackError);
                  }
                }
              } else {
                console.log(`✅ Embeddings already exist for Teams file: ${file.fileName}`);
              }
            } catch (embeddingError) {
              console.error(`Error processing embeddings for Teams file ${file.fileName}:`, embeddingError);
              // Continue with sync even if embedding generation fails
            }
          }

          // Create a sync item for tracking
          await storage.createSyncItem({
            type: file.fileType,
            title: file.fileName,
            status: "success",
            integrationId: integration.id,
            syncLogId,
            externalId: file.externalId,
            sourceUrl: file.sourceUrl,
            destinationUrl: null, // We're not creating Notion pages anymore
            metadata: {
              platform: 'microsoft_teams',
              fileId: savedFile.id,
              referenceSync: true,
              comprehensive: true,
              wasUpdated: !!existingFile,
              sourceType: (file.extractedMetadata as any)?._sourceType,
              sourceContext: (file.extractedMetadata as any)?._sourceContext,
            },
          });

          itemsSuccess++;
        } catch (itemError: any) {
          itemsFailed++;
          console.error(`Error processing file ${file.fileName || "Unknown file"} (${file.externalId || "Unknown ID"}):`, itemError);

          // Create a failed sync item for tracking
          await storage.createSyncItem({
            type: "unknown",
            title: file.fileName || "Unknown file",
            status: "failed",
            integrationId: integration.id,
            syncLogId,
            externalId: file.externalId || "",
            sourceUrl: file.sourceUrl || "",
            error: itemError.message || "Unknown error occurred",
            metadata: {
              platform: 'microsoft_teams',
              referenceSync: true,
              comprehensive: true,
            },
          });
        }

        // Update sync log progress periodically
        if (itemsProcessed % 50 === 0) {
          await storage.updateSyncLog(syncLogId, {
            itemsProcessed,
            itemsSuccess,
            itemsFailed,
          });
        }
      }

      // Mark files as deleted if they're no longer in the source
      let deletedCount = 0;
      const missingExternalIds: string[] = [];
      
      for (const existingFile of existingFiles) {
        if (!foundExternalIds.has(existingFile.externalId) && existingFile.status === 'active') {
          missingExternalIds.push(existingFile.externalId);
          deletedCount++;
        }
      }

      if (missingExternalIds.length > 0) {
        await storage.markFilesAsDeleted(missingExternalIds, 'microsoft_teams');
        console.log(`Marked ${deletedCount} files as deleted (no longer in source)`);
      }

      // Final sync log update
      const syncStatus = itemsFailed === 0 ? "success" : itemsSuccess > 0 ? "partial" : "failed";

      await storage.updateSyncLog(syncLogId, {
        status: syncStatus,
        endTime: new Date(),
        itemsProcessed,
        itemsSuccess,
        itemsFailed,
        details: {
          totalFiles: syncResult.files.length,
          existingFiles: existingFiles.length,
          newFiles: itemsSuccess - (itemsProcessed - syncResult.files.length),
          updatedFiles: itemsProcessed - syncResult.files.length,
          skippedFiles: itemsSkipped,
          deletedFiles: deletedCount,
          scanMode: 'comprehensive_reference_based',
          sources: syncResult.sources,
          summary: syncResult.summary,
          completedAt: new Date().toISOString(),
        },
      });

      // Update integration status
      await storage.updateIntegrationStatus(
        integration.id,
        syncStatus === "failed" ? "error" : "connected"
      );

      console.log(`Comprehensive sync completed. Processed: ${itemsProcessed}, Success: ${itemsSuccess}, Failed: ${itemsFailed}, Skipped: ${itemsSkipped}, Deleted: ${deletedCount}`);

    } catch (error: any) {
      console.error(`Error in comprehensive Microsoft Teams sync:`, error);

      // Update sync log with error
      await storage.updateSyncLog(syncLogId, {
        status: "failed",
        endTime: new Date(),
        itemsProcessed,
        itemsSuccess,
        itemsFailed,
        error: error.message || "Unknown error occurred",
      });

      // Update integration status
      await storage.updateIntegrationStatus(integration.id, "error");

      throw error;
    }
  }

  /**
   * Get sync logs for an integration
   */
  async getSyncLogs(req: Request, res: Response) {
    try {
      console.log('Getting sync logs...');
      const { integrationId, limit } = req.query;

      const parsedIntegrationId = integrationId
        ? parseInt(integrationId as string)
        : undefined;
      const parsedLimit = limit ? parseInt(limit as string) : undefined;

      if (parsedIntegrationId && isNaN(parsedIntegrationId)) {
        return res.status(400).json({ message: "Invalid integration ID" });
      }

      if (parsedLimit && isNaN(parsedLimit)) {
        return res.status(400).json({ message: "Invalid limit" });
      }

      const logs = await storage.getSyncLogs(parsedIntegrationId, parsedLimit);
      console.log(`Found ${logs.length} sync logs`);

      return res.json({ logs });
    } catch (error: any) {
      console.error("Error getting sync logs:", error);
      console.error("Error stack:", error.stack);
      return res.status(500).json({
        message: "Failed to get sync logs",
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get a specific sync log
   */
  async getSyncLog(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid sync log ID" });
      }

      const syncLog = await storage.getSyncLog(id);
      if (!syncLog) {
        return res.status(404).json({ message: "Sync log not found" });
      }

      return res.json({ syncLog });
    } catch (error: any) {
      console.error(`Error getting sync log ${req.params.id}:`, error);
      return res.status(500).json({ message: "Failed to get sync log" });
    }
  }

  /**
   * Trigger a manual sync
   */
  async syncNow(req: Request, res: Response) {
    try {
      // Validate the request body
      const validationResult = syncNowSchema.safeParse(req.body);

      if (!validationResult.success) {
        return res.status(400).json({
          message: "Invalid sync request",
          errors: validationResult.error.errors,
        });
      }

      const { integrationId } = validationResult.data;

      // Start the sync
      const syncLog = await this.startSync(integrationId);

      return res.json({
        message: "Sync started successfully",
        syncLog,
      });
    } catch (error: any) {
      console.error("Error starting sync:", error);
      return res.status(500).json({
        message: "Failed to start sync",
        error: error.message,
      });
    }
  }

  /**
   * Re-vectorize all files (force regenerate embeddings for everything)
   */
  async reVectorizeAll(req: Request, res: Response) {
    try {
      const { integrationId, forceAll } = req.body;

      if (!integrationId) {
        return res.status(400).json({ message: "Integration ID is required" });
      }

      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        return res.status(404).json({ message: "Integration not found" });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: "Integration not connected" });
      }

      console.log(`🔄 Starting re-vectorization for integration ${integrationId} (${integration.type})`);

      // Map integration type to platform name
      let platformName = integration.type;
      if (integration.type === 'google-drive' || integration.type === 'google_drive') {
        platformName = 'google_drive';
      } else if (integration.type === 'microsoft-teams' || integration.type === 'microsoft_teams') {
        platformName = 'microsoft_teams';
      }

      // Get all files for this integration
      const allFiles = await storage.getFilesForIntegration(integrationId, platformName);
      console.log(`Found ${allFiles.length} files to re-vectorize`);

      let processed = 0;
      let success = 0;
      let failed = 0;

      for (const file of allFiles) {
        try {
          processed++;
          console.log(`Re-vectorizing ${processed}/${allFiles.length}: ${file.fileName}`);

          // Delete existing embeddings if forceAll is true
          if (forceAll) {
            await embeddingService.regenerateEmbeddings(file.id, ""); // Will delete and recreate
          }

          // Check if embeddings exist
          const hasEmbeddings = await embeddingService.hasEmbeddings(file.id);

          if (!hasEmbeddings || forceAll) {
            // Use comprehensive vectorization logic with metadata
            let contentToVectorize = "";

            // 1. Always include file metadata
            const metadataContent = [
              `File: ${file.fileName}`,
              `Type: ${file.fileType}`,
              `Platform: ${file.platform}`,
            ];

            if (file.extractedMetadata) {
              const metadata = file.extractedMetadata as any;
              
              // Add common metadata fields
              if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
              if (metadata.title && metadata.title !== file.fileName) {
                metadataContent.push(`Title: ${metadata.title}`);
              }
              if (metadata.topics && Array.isArray(metadata.topics)) {
                metadataContent.push(`Topics: ${metadata.topics.join(', ')}`);
              }
              if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
              
              // Add Teams-specific metadata if available
              if (metadata.meetingAttendees && Array.isArray(metadata.meetingAttendees)) {
                metadataContent.push(`Attendees: ${metadata.meetingAttendees.map((a: any) => a.displayName || a.email).join(', ')}`);
              }
              if (metadata.calendarAttendees && Array.isArray(metadata.calendarAttendees)) {
                metadataContent.push(`Meeting Attendees: ${metadata.calendarAttendees.map((a: any) => a.emailAddress?.name || a.emailAddress?.address).join(', ')}`);
              }
              if (metadata.meetingSubject) {
                metadataContent.push(`Meeting Subject: ${metadata.meetingSubject}`);
              }
              if (metadata.calendarSubject) {
                metadataContent.push(`Calendar Subject: ${metadata.calendarSubject}`);
              }
              if (metadata.organizer) {
                metadataContent.push(`Organizer: ${metadata.organizer.displayName || metadata.organizer.email}`);
              }
              if (metadata._sourceType) {
                metadataContent.push(`Source: ${metadata._sourceType}`);
              }
              if (metadata._teamId && metadata._channelId) {
                metadataContent.push(`Teams Channel: ${metadata._teamId}/${metadata._channelId}`);
              }
            }

            contentToVectorize = metadataContent.join('\n') + '\n\n';

            // 2. Try to extract file content (for Google integrations)
            if (integration.type === "google-drive" || integration.type === "google_drive") {
              try {
                console.log(`[GOOGLE] Extracting content for ${file.fileName}...`);
                const auth = await googleService.getAuthorizedClient(integration.credentials);

                let contentExtracted = false;

                // First, try specialized document processing for supported file types
                const { pdfService } = await import('../services/pdf-service.js');
                const { wordService } = await import('../services/word-service.js');

                // Check if this is a PDF file
                if ((pdfService as any).isPDFFile && (pdfService as any).isPDFFile(file.fileName, file.mimeType)) {
                  try {
                    console.log(`[GOOGLE] Processing PDF: ${file.fileName}`);
                    const pdfBuffer = await googleService.downloadPDFContent(auth, file.externalId || "", file.fileName);

                    if (pdfBuffer) {
                      const platformMetadata = {
                        platform: 'google_drive' as const,
                        sourceType: 'google_drive_folder',
                        sourceContext: `Google Drive`,
                        folderPath: (file.extractedMetadata as any)?.folderPath || 'Root',
                        owner: (file.extractedMetadata as any)?.owner || 'Unknown',
                        lastModified: file.lastModified || new Date()
                      };

                      const pdfResult = await pdfService.processPDFFile(
                        pdfBuffer,
                        file.fileName,
                        platformMetadata,
                        Boolean(integration.isLlmEnabled) && openaiService.isInitialized()
                      );

                      if (pdfResult.success && pdfResult.vectorizationContent) {
                        console.log(`[GOOGLE] Successfully processed PDF: ${file.fileName}`);
                        console.log(`[GOOGLE] PDF content length: ${pdfResult.vectorizationContent.length} characters`);

                        // Use the comprehensive vectorization content from PDF service
                        contentToVectorize = pdfResult.vectorizationContent;
                        contentExtracted = true;

                        // Update file metadata with PDF-extracted metadata
                        if (pdfResult.metadata) {
                          file.extractedMetadata = {
                            ...(file.extractedMetadata || {}),
                            pdfMetadata: pdfResult.metadata,
                            hasExtractedContent: true,
                            contentType: 'pdf'
                          };
                        }
                      } else {
                        console.log(`[GOOGLE] PDF processing failed for ${file.fileName}: ${pdfResult.error}`);
                      }
                    } else {
                      console.log(`[GOOGLE] Could not download PDF content for: ${file.fileName}`);
                    }
                  } catch (pdfError: any) {
                    console.error(`[GOOGLE] Error processing PDF ${file.fileName}:`, pdfError.message);
                    // Continue to fallback methods
                  }
                }
                // Check if this is a Word document
                else if ((wordService as any).isWordFile && (wordService as any).isWordFile(file.fileName, file.mimeType)) {
                  try {
                    console.log(`[GOOGLE] Processing Word document: ${file.fileName}`);
                    const wordBuffer = await googleService.downloadWordContent(auth, file.externalId || "", file.fileName);

                    if (wordBuffer) {
                      const platformMetadata = {
                        platform: 'google_drive' as const,
                        sourceType: 'google_drive_folder',
                        sourceContext: `Google Drive`,
                        folderPath: (file.extractedMetadata as any)?.folderPath || 'Root',
                        owner: (file.extractedMetadata as any)?.owner || 'Unknown',
                        lastModified: file.lastModified || new Date()
                      };

                      const wordResult = await wordService.processWordFile(
                        wordBuffer,
                        file.fileName,
                        platformMetadata,
                        Boolean(integration.isLlmEnabled) && openaiService.isInitialized()
                      );

                      if (wordResult.success && wordResult.vectorizationContent) {
                        console.log(`[GOOGLE] Successfully processed Word document: ${file.fileName}`);
                        console.log(`[GOOGLE] Word content length: ${wordResult.vectorizationContent.length} characters`);

                        // Use the comprehensive vectorization content from Word service
                        contentToVectorize = wordResult.vectorizationContent;
                        contentExtracted = true;

                        // Update file metadata with Word-extracted metadata
                        if (wordResult.metadata) {
                          file.extractedMetadata = {
                            ...(file.extractedMetadata || {}),
                            wordMetadata: wordResult.metadata,
                            hasExtractedContent: true,
                            contentType: 'word'
                          };
                        }
                      } else {
                        console.log(`[GOOGLE] Word processing failed for ${file.fileName}: ${wordResult.error}`);
                      }
                    } else {
                      console.log(`[GOOGLE] Could not download Word content for: ${file.fileName}`);
                    }
                  } catch (wordError: any) {
                    console.error(`[GOOGLE] Error processing Word document ${file.fileName}:`, wordError.message);
                    // Continue to fallback methods
                  }
                }

                // If specialized processing didn't work, try universal content extraction
                if (!contentExtracted) {
                  try {
                    // Create a minimal file object for the extractFileContent method
                    const driveFile = {
                      id: file.externalId,
                      name: file.fileName,
                      mimeType: file.mimeType || 'application/octet-stream',
                      size: file.fileSize?.toString() || null,
                      modifiedTime: file.lastModified?.toISOString()
                    };

                    const fileContent = await googleService.extractFileContent(auth, driveFile);

                    if (fileContent && fileContent.trim().length > 0) {
                      contentToVectorize += `Content:\n${fileContent}`;
                      contentExtracted = true;
                      console.log(`[GOOGLE] Universal extraction: ${fileContent.length} characters from ${file.fileName}`);
                    }
                  } catch (universalError: any) {
                    console.log(`[GOOGLE] Universal extraction failed for ${file.fileName}: ${universalError.message}`);

                    // Final fallback: try Google Docs API for document types
                    try {
                      const { transcriptText } = await googleService.extractDocContent(auth, file.externalId || "");
                      if (transcriptText && transcriptText.trim().length > 0) {
                        contentToVectorize += `Content:\n${transcriptText}`;
                        contentExtracted = true;
                        console.log(`[GOOGLE] Docs API extraction: ${transcriptText.length} characters from ${file.fileName}`);
                      }
                    } catch (docsError: any) {
                      console.log(`[GOOGLE] Docs API extraction failed for ${file.fileName}: ${docsError.message}`);
                    }
                  }
                }

                if (!contentExtracted) {
                  console.log(`[GOOGLE] No content extracted for ${file.fileName}, using metadata-only vectorization`);
                }

              } catch (contentError: any) {
                console.error(`[GOOGLE] Content extraction error for ${file.fileName}:`, contentError.message);
                console.log(`[GOOGLE] Falling back to metadata-only vectorization for ${file.fileName}`);
              }
            } else if (integration.type === "microsoft-teams" || integration.type === "microsoft_teams") {
              // Check if this is a PDF file for Teams
              const { pdfService } = await import('../services/pdf-service.js');
              if ((pdfService as any).isPDFFile && (pdfService as any).isPDFFile(file.fileName, file.mimeType)) {
                try {
                  console.log(`[TEAMS] Re-vectorizing PDF: ${file.fileName}`);
                  
                  const metadata = file.extractedMetadata as any;
                  const driveId = metadata?.driveId;
                  const fileId = file.externalId;
                  
                  if (driveId && fileId) {
                    const credentials = JSON.parse(await cryptoService.decrypt(integration.credentials));
                    const client = (microsoftService as any).createGraphClient(credentials);
                    const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
                      .responseType('arraybuffer' as any)
                      .get();
                    
                    if (downloadResponse) {
                      // Convert response to buffer
                      let buffer: Buffer;
                      if (Buffer.isBuffer(downloadResponse)) {
                        buffer = downloadResponse;
                      } else if (downloadResponse instanceof ArrayBuffer) {
                        buffer = Buffer.from(downloadResponse);
                      } else if (downloadResponse instanceof Uint8Array) {
                        buffer = Buffer.from(downloadResponse);
                      } else if (typeof downloadResponse === 'string') {
                        buffer = Buffer.from(downloadResponse, 'binary');
                      } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
                        buffer = Buffer.from(downloadResponse.buffer);
                      } else {
                        throw new Error('Unexpected response type');
                      }
                      
                      const platformMetadata = {
                        platform: 'microsoft_teams' as const,
                        sourceType: metadata._sourceType || 'teams_file',
                        sourceContext: metadata._sourceContext || `Microsoft Teams file`,
                        folderPath: metadata.folderPath || metadata._sourceFolderId || 'Unknown',
                        owner: metadata.owner || metadata.ownerName || 'Unknown',
                        lastModified: file.lastModified || new Date()
                      };
                      
                      const pdfResult = await pdfService.processPDFFile(
                        buffer,
                        file.fileName,
                        platformMetadata,
                        Boolean(integration.isLlmEnabled) && openaiService.isInitialized()
                      );
                      
                      if (pdfResult.success && pdfResult.vectorizationContent) {
                        console.log(`[TEAMS] Successfully re-processed PDF: ${file.fileName}`);
                        contentToVectorize = pdfResult.vectorizationContent;
                      }
                    }
                  }
                } catch (pdfError: any) {
                  console.log(`[TEAMS] PDF processing failed during re-vectorization: ${pdfError.message}`);
                }
              } else {
                // Check if this is a Word document for Teams
                const { wordService } = await import('../services/word-service.js');
                if ((wordService as any).isWordFile && (wordService as any).isWordFile(file.fileName, file.mimeType)) {
                  try {
                    console.log(`[TEAMS] Re-vectorizing Word document: ${file.fileName}`);

                    const metadata = file.extractedMetadata as any;
                    const driveId = metadata?.driveId;
                    const fileId = file.externalId;

                    if (driveId && fileId) {
                      const credentials = JSON.parse(await cryptoService.decrypt(integration.credentials));
                      const client = (microsoftService as any).createGraphClient(credentials);
                      const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
                        .responseType('arraybuffer' as any)
                        .get();

                      if (downloadResponse) {
                        // Convert response to buffer
                        let buffer: Buffer;
                        if (Buffer.isBuffer(downloadResponse)) {
                          buffer = downloadResponse;
                        } else if (downloadResponse instanceof ArrayBuffer) {
                          buffer = Buffer.from(downloadResponse);
                        } else if (downloadResponse instanceof Uint8Array) {
                          buffer = Buffer.from(downloadResponse);
                        } else if (typeof downloadResponse === 'string') {
                          buffer = Buffer.from(downloadResponse, 'binary');
                        } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
                          buffer = Buffer.from(downloadResponse.buffer);
                        } else {
                          throw new Error('Unexpected response type');
                        }

                        const platformMetadata = {
                          platform: 'microsoft_teams' as const,
                          sourceType: metadata._sourceType || 'teams_file',
                          sourceContext: metadata._sourceContext || `Microsoft Teams file`,
                          folderPath: metadata.folderPath || metadata._sourceFolderId || 'Unknown',
                          owner: metadata.owner || metadata.ownerName || 'Unknown',
                          lastModified: file.lastModified || new Date()
                        };

                        const wordResult = await wordService.processWordFile(
                          buffer,
                          file.fileName,
                          platformMetadata,
                          Boolean(integration.isLlmEnabled) && openaiService.isInitialized()
                        );

                        if (wordResult.success && wordResult.vectorizationContent) {
                          console.log(`[TEAMS] Successfully re-processed Word document: ${file.fileName}`);
                          contentToVectorize = wordResult.vectorizationContent;
                        }
                      }
                    }
                  } catch (wordError: any) {
                    console.log(`[TEAMS] Word processing failed during re-vectorization: ${wordError.message}`);
                  }
                } else {
                  console.log(`[TEAMS] Using metadata-only vectorization for ${file.fileName} (unsupported file type)`);
                }
              }
            }

            // 3. Add file type descriptions and platform-specific metadata
            if (contentToVectorize.trim().length < 200) {
              contentToVectorize += `\nMIME Type: ${file.mimeType || 'unknown'}`;
              
              if (integration.type === "microsoft-teams" || integration.type === "microsoft_teams") {
                contentToVectorize += `\nFile Description: Microsoft Teams file - ${file.fileType}`;
                if (file.extractedMetadata) {
                  const metadata = file.extractedMetadata as any;
                  if (metadata._sourceType === 'teams_channel') {
                    contentToVectorize += `\nLocation: Teams Channel Meeting Recording/Transcript`;
                  } else if (metadata._sourceType === 'onedrive_personal') {
                    contentToVectorize += `\nLocation: OneDrive Personal`;
                  } else if (metadata._sourceType === 'sharepoint_site') {
                    contentToVectorize += `\nLocation: SharePoint Site`;
                  }
                }
              } else {
                contentToVectorize += `\nFile Description: Google Drive file - ${file.fileType}`;
              }
              
              if (file.createdAt) {
                contentToVectorize += `\nCreated: ${new Date(file.createdAt).toLocaleDateString()}`;
              }
            }

            // Generate embeddings
            console.log(`🔀 Vectorizing ${contentToVectorize.length} characters for ${file.fileName}`);
            await embeddingService.processFileForEmbeddings(file.id, contentToVectorize);
            success++;
            console.log(`✅ Re-vectorized: ${file.fileName}`);
          } else {
            console.log(`⏭️ Skipping ${file.fileName} (already has embeddings)`);
            success++;
          }

          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error: any) {
          failed++;
          console.error(`❌ Failed to re-vectorize ${file.fileName}:`, error.message);
        }
      }

      console.log(`🎉 Re-vectorization complete: ${success} success, ${failed} failed out of ${processed} total`);

      return res.json({
        message: "Re-vectorization completed",
        stats: {
          totalFiles: allFiles.length,
          processed,
          success,
          failed,
        },
      });

    } catch (error: any) {
      console.error("Error in re-vectorization:", error);
      return res.status(500).json({
        message: "Failed to re-vectorize files",
        error: error.message,
      });
    }
  }

  /**
   * Get sync items for a specific sync log
   */
  async getSyncItems(req: Request, res: Response) {
    try {
      const { syncLogId, status } = req.query;

      const parsedSyncLogId = syncLogId
        ? parseInt(syncLogId as string)
        : undefined;

      if (parsedSyncLogId && isNaN(parsedSyncLogId)) {
        return res.status(400).json({ message: "Invalid sync log ID" });
      }

      const items = await storage.getSyncItems(
        parsedSyncLogId,
        (status as string) || undefined,
      );

      return res.json({ items });
    } catch (error: any) {
      console.error("Error getting sync items:", error);
      return res.status(500).json({ message: "Failed to get sync items" });
    }
  }
}

export const syncController = new SyncController();
