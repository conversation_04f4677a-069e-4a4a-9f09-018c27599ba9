
> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Scheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
2:35:35 AM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
2:35:38 AM [express] GET /api/integrations 304 in 79ms :: {"integrations":[{"id":1,"name":"Google Me…
2:35:38 AM [express] GET /api/sync-logs 304 in 321ms :: {"logs":[{"id":18,"integrationId":1,"startTi…
2:35:43 AM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
2:35:43 AM [express] GET /api/sync-logs 304 in 75ms :: {"logs":[{"id":18,"integrationId":1,"startTim…
2:35:44 AM [express] GET /api/integrations 304 in 73ms :: {"integrations":[{"id":1,"name":"Google Me…
2:35:44 AM [express] GET /api/sync-logs 304 in 75ms :: {"logs":[{"id":18,"integrationId":1,"startTim…
2:36:13 AM [express] GET /api/integrations 200 in 349ms :: {"integrations":[{"id":1,"name":"Google M…
2:36:51 AM [express] GET /api/integrations 304 in 318ms :: {"integrations":[{"id":1,"name":"Google M…
OpenAI service initialized successfully
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
2:36:52 AM [express] POST /api/sync-now 200 in 261ms :: {"message":"Sync started successfully","sync…
2:36:52 AM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Found 0 files in processed folder
After filtering: 3 files remaining to process
Moved file 1itgiRTEU4UiXBlkdlyLuKiwaqlJd2ClMZxnJZACU8Xw to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Skipped already processed file Test meeting 1 and moved to processed folder
Extracting content from document: Test-meeting-2 (ID: 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc)
Successfully extracted content (40694 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Technical Developments Meeting
Created Notion page for "Strategic Initiatives and Technical Developments Meeting": 1fbd68e5-c2ad-819e-acd7-c79a18146d2f
Splitting summary of 478 chars into 1 chunks
Created chunk with 1950 characters (max: 2000)
Created chunk with 1943 characters (max: 2000)
Created chunk with 1880 characters (max: 2000)
Created chunk with 1815 characters (max: 2000)
Created chunk with 1826 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1892 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1876 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1866 characters (max: 2000)
Created chunk with 1871 characters (max: 2000)
Created chunk with 1873 characters (max: 2000)
Created chunk with 1835 characters (max: 2000)
Created chunk with 1791 characters (max: 2000)
Created chunk with 1894 characters (max: 2000)
Created chunk with 1763 characters (max: 2000)
Created chunk with 1935 characters (max: 2000)
Created chunk with 1931 characters (max: 2000)
Created chunk with 928 characters (max: 2000)
Created chunk with 75 characters (max: 2000)
Added content blocks to page 1fbd68e5-c2ad-819e-acd7-c79a18146d2f
Moved file 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Successfully processed Test-meeting-2 and added to Notion database
Moved file 1whenWIHnBxkME_IApKLZ4WtiYYvi_Rpd4ph-UuF6YKI to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Skipped already processed file Test meeting 3 and moved to processed folder
Sync completed with status: success
Items processed: 3, Success: 1, Failed: 0