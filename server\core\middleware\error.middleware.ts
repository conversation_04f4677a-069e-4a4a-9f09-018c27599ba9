import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { isDevelopment } from '../config/environment.config';

/**
 * Error handling middleware
 */

export interface AppError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

/**
 * Custom error class
 */
export class CustomError extends Error implements AppError {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(message: string, statusCode = 500, code = 'INTERNAL_ERROR', details?: any) {
    super(message);
    this.name = 'CustomError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    
    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, CustomError);
  }
}

/**
 * Create common error types
 */
export const createError = {
  badRequest: (message: string, details?: any) => 
    new CustomError(message, 400, 'BAD_REQUEST', details),
  
  unauthorized: (message: string = 'Unauthorized') => 
    new CustomError(message, 401, 'UNAUTHORIZED'),
  
  forbidden: (message: string = 'Forbidden') => 
    new CustomError(message, 403, 'FORBIDDEN'),
  
  notFound: (message: string = 'Not found') => 
    new CustomError(message, 404, 'NOT_FOUND'),
  
  conflict: (message: string, details?: any) => 
    new CustomError(message, 409, 'CONFLICT', details),
  
  validation: (message: string, details?: any) => 
    new CustomError(message, 422, 'VALIDATION_ERROR', details),
  
  internal: (message: string = 'Internal server error', details?: any) => 
    new CustomError(message, 500, 'INTERNAL_ERROR', details),
  
  serviceUnavailable: (message: string = 'Service unavailable') => 
    new CustomError(message, 503, 'SERVICE_UNAVAILABLE'),
};

/**
 * Format error response
 */
const formatErrorResponse = (error: AppError, req: Request) => {
  const response: any = {
    success: false,
    error: {
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR',
      statusCode: error.statusCode || 500,
    },
  };

  // Add details in development or for validation errors
  if (isDevelopment() || error.code === 'VALIDATION_ERROR') {
    if (error.details) {
      response.error.details = error.details;
    }
  }

  // Add stack trace in development
  if (isDevelopment()) {
    response.error.stack = error.stack;
    response.error.path = req.path;
    response.error.method = req.method;
  }

  return response;
};

/**
 * Handle Zod validation errors
 */
const handleZodError = (error: ZodError): CustomError => {
  const details = error.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));

  return createError.validation('Validation failed', details);
};

/**
 * Handle database errors
 */
const handleDatabaseError = (error: any): CustomError => {
  // PostgreSQL error codes
  if (error.code) {
    switch (error.code) {
      case '23505': // unique_violation
        return createError.conflict('Resource already exists', {
          constraint: error.constraint,
          detail: error.detail,
        });
      
      case '23503': // foreign_key_violation
        return createError.badRequest('Referenced resource does not exist', {
          constraint: error.constraint,
          detail: error.detail,
        });
      
      case '23502': // not_null_violation
        return createError.badRequest('Required field is missing', {
          column: error.column,
        });
      
      default:
        console.error('Database error:', error);
        return createError.internal('Database operation failed');
    }
  }

  return createError.internal('Database error occurred');
};

/**
 * Main error handling middleware
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString(),
  });

  let appError: AppError;

  // Handle different error types
  if (error instanceof CustomError) {
    appError = error;
  } else if (error instanceof ZodError) {
    appError = handleZodError(error);
  } else if (error.name === 'PostgresError' || error.name === 'DatabaseError') {
    appError = handleDatabaseError(error);
  } else {
    // Generic error
    appError = createError.internal(
      isDevelopment() ? error.message : 'An unexpected error occurred'
    );
  }

  const statusCode = appError.statusCode || 500;
  const response = formatErrorResponse(appError, req);

  res.status(statusCode).json(response);
};

/**
 * 404 handler for unmatched routes
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  const error = createError.notFound(`Route ${req.method} ${req.path} not found`);
  const response = formatErrorResponse(error, req);
  
  res.status(404).json(response);
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
