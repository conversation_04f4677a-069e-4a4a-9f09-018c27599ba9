/**
 * Modular route system for MeetSync Web App
 * 
 * This file exports all route registration functions for easy import
 * and maintains the same structure as the OpenAPI specification.
 */

export { registerSystemRoutes } from "./system";
export { registerIntegrationRoutes } from "./integrations";
export { registerSyncRoutes } from "./sync";
export { registerChatRoutes } from "./chat";
export { registerFileRoutes } from "./files";
export { registerEmailRoutes } from "./emails";
export { registerRagRoutes } from "./rag";
export { registerUploadRoutes } from "./upload";
export { registerMCPRoutes } from "./mcp";
export { registerLegacyRoutes } from "./legacy";
export { registerTestRoutes } from "./test";
export { registerDebugRoutes } from "./diagnostic";
export { registerMonitoringRoutes } from "./monitoring";