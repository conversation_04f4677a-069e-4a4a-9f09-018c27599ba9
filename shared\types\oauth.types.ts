// COMMENTED OUT: This file was causing duplicate export conflicts
// The same types are properly exported from '../schemas/oauth.schema.ts'
// Keeping this file for reference but not actively exporting to avoid conflicts

// import { oauthTokens } from '../schemas/oauth.schema';

// // Type for OAuth token data
// export type OAuthToken = typeof oauthTokens.$inferSelect;
// export type NewOAuthToken = typeof oauthTokens.$inferInsert; 

// NOTE: To use OAuth types, import from:
// - 'shared/schemas/oauth.schema' (direct)
// - 'shared/schemas' (via index)
// - 'shared/schema' (legacy compatibility) 