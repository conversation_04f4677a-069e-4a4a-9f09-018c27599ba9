import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Zap, 
  CheckCircle, 
  XCircle, 
  Clock, 
  ChevronDown, 
  ChevronRight,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { MCPToolUsage } from '../mcp/types';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface ToolUsageIndicatorProps {
  toolUsages: MCPToolUsage[];
  isActive?: boolean;
  className?: string;
}

export function ToolUsageIndicator({ 
  toolUsages, 
  isActive = false, 
  className = "" 
}: ToolUsageIndicatorProps) {
  const [expanded, setExpanded] = useState(false);
  const [animatingTools, setAnimatingTools] = useState<Set<string>>(new Set());

  // Animate new tool usages
  useEffect(() => {
    const newTools = new Set<string>();
    toolUsages.forEach(usage => {
      if (Date.now() - usage.timestamp.getTime() < 2000) { // Within last 2 seconds
        newTools.add(usage.toolName);
      }
    });
    
    if (newTools.size > 0) {
      setAnimatingTools(newTools);
      const timer = setTimeout(() => {
        setAnimatingTools(new Set());
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [toolUsages]);

  if (toolUsages.length === 0 && !isActive) {
    return null;
  }

  const getToolIcon = (toolName: string) => {
    if (toolName.includes('search')) return '🔍';
    if (toolName.includes('list')) return '📋';
    if (toolName.includes('get') || toolName.includes('read')) return '📖';
    if (toolName.includes('create') || toolName.includes('upload')) return '✨';
    if (toolName.includes('delete')) return '🗑️';
    return '⚡';
  };

  const getServerIcon = (serverName: string) => {
    if (serverName.toLowerCase().includes('google')) return '📁';
    if (serverName.toLowerCase().includes('teams') || serverName.toLowerCase().includes('microsoft')) return '👥';
    if (serverName.toLowerCase().includes('upload') || serverName.toLowerCase().includes('file')) return '📤';
    return '🔧';
  };

  const successfulTools = toolUsages.filter(usage => usage.success);
  const failedTools = toolUsages.filter(usage => !usage.success);
  const activeTools = toolUsages.filter(usage => 
    Date.now() - usage.timestamp.getTime() < 5000 // Active within last 5 seconds
  );

  return (
    <div className={cn("bg-white border border-gray-200 rounded-lg shadow-sm", className)}>
      {/* Header */}
      <div className="px-3 py-2 border-b border-gray-100">
        <button
          onClick={() => setExpanded(!expanded)}
          className="w-full flex items-center justify-between hover:bg-gray-50 rounded px-2 py-1 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              {isActive ? (
                <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              ) : (
                <Zap className="w-4 h-4 text-blue-500" />
              )}
              <span className="text-sm font-medium">
                {isActive ? 'AI Tools Active' : 'Tools Used'}
              </span>
            </div>
            
            <div className="flex items-center space-x-1">
              {successfulTools.length > 0 && (
                <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs">
                  {successfulTools.length} ✓
                </Badge>
              )}
              {failedTools.length > 0 && (
                <Badge variant="secondary" className="bg-red-100 text-red-700 text-xs">
                  {failedTools.length} ✗
                </Badge>
              )}
            </div>
          </div>
          
          {expanded ? (
            <ChevronDown className="w-4 h-4 text-gray-400" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-400" />
          )}
        </button>
      </div>

      {/* Tool Usage List */}
      {expanded && (
        <div className="p-3 space-y-2 max-h-64 overflow-y-auto">
          {toolUsages.length === 0 ? (
            <div className="text-center py-4 text-gray-500 text-sm">
              {isActive ? 'AI is preparing to use tools...' : 'No tools used yet'}
            </div>
          ) : (
            toolUsages.map((usage, index) => (
              <div
                key={`${usage.toolName}-${usage.timestamp.getTime()}-${index}`}
                className={cn(
                  "flex items-start space-x-3 p-2 rounded-lg transition-all duration-500",
                  usage.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200",
                  animatingTools.has(usage.toolName) && "animate-pulse scale-105"
                )}
              >
                {/* Tool Icon */}
                <div className="flex-shrink-0 mt-0.5">
                  <div className="text-sm">{getToolIcon(usage.toolName)}</div>
                </div>
                
                {/* Tool Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium truncate">{usage.toolName}</span>
                    <div className="flex items-center space-x-1">
                      <div className="text-xs">{getServerIcon(usage.serverName)}</div>
                      <span className="text-xs text-gray-500 truncate">{usage.serverName}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-1">
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      <span>{formatDistanceToNow(usage.timestamp, { addSuffix: true })}</span>
                      {usage.duration && (
                        <>
                          <span>•</span>
                          <span>{usage.duration}ms</span>
                        </>
                      )}
                    </div>
                    
                    <div className="flex-shrink-0">
                      {usage.success ? (
                        <CheckCircle className="w-3 h-3 text-green-500" />
                      ) : (
                        <XCircle className="w-3 h-3 text-red-500" />
                      )}
                    </div>
                  </div>
                  
                  {/* Error Message */}
                  {!usage.success && usage.error && (
                    <div className="mt-1 text-xs text-red-600 bg-red-100 rounded px-2 py-1">
                      {usage.error}
                    </div>
                  )}
                  
                  {/* Result Preview */}
                  {usage.success && usage.result && (
                    <div className="mt-1 text-xs text-gray-600 bg-gray-100 rounded px-2 py-1 max-h-16 overflow-hidden">
                      {typeof usage.result === 'string' 
                        ? usage.result.substring(0, 100) + (usage.result.length > 100 ? '...' : '')
                        : JSON.stringify(usage.result).substring(0, 100) + '...'
                      }
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      )}
      
      {/* Quick Stats (when collapsed) */}
      {!expanded && toolUsages.length > 0 && (
        <div className="px-3 py-2 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-3">
            <span>{toolUsages.length} tool{toolUsages.length !== 1 ? 's' : ''} used</span>
            {activeTools.length > 0 && (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span>{activeTools.length} active</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {successfulTools.length > 0 && (
              <div className="flex items-center space-x-1 text-green-600">
                <CheckCircle className="w-3 h-3" />
                <span>{successfulTools.length}</span>
              </div>
            )}
            {failedTools.length > 0 && (
              <div className="flex items-center space-x-1 text-red-600">
                <XCircle className="w-3 h-3" />
                <span>{failedTools.length}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
