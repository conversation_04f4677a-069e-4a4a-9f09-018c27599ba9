#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration for different exploration modes
const configs = {
  overview: {
    name: "Project Overview",
    include: [
      'package.json',
      'README.md',
      'tsconfig.json',
      'vite.config.ts',
      'tailwind.config.ts',
      'drizzle.config.ts',
      'client/src/App.tsx',
      'client/src/main.tsx',
      'server/index.ts',
      'server/routes.ts',
      'server/db.ts'
    ],
    directories: ['client/src/pages', 'client/src/components', 'server'],
    exclude: ['node_modules', '.git', '**/*.log', '**/*.png', '**/*.jpg', '**/*.ico']
  },
  frontend: {
    name: "Frontend (React)",
    include: [
      'client/src/App.tsx',
      'client/src/main.tsx',
      'client/src/index.css',
      'vite.config.ts',
      'tailwind.config.ts'
    ],
    directories: ['client/src'],
    exclude: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts']
  },
  backend: {
    name: "Backend (Express)",
    include: [
      'server/index.ts',
      'server/routes.ts',
      'server/db.ts',
      'drizzle.config.ts'
    ],
    directories: ['server'],
    exclude: ['**/*.test.ts', '**/*.test.js', '**/*.spec.ts']
  }
};

function shouldExclude(filePath, excludePatterns) {
  return excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
      return regex.test(filePath);
    }
    return filePath.includes(pattern);
  });
}

function exploreDirectory(dirPath, config, results = []) {
  if (!fs.existsSync(dirPath)) return results;
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const relativePath = path.relative('.', fullPath);
    
    if (shouldExclude(relativePath, config.exclude)) continue;
    
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      exploreDirectory(fullPath, config, results);
    } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx') || item.endsWith('.json'))) {
      results.push({
        path: relativePath,
        size: stat.size,
        type: 'file'
      });
    }
  }
  
  return results;
}

function readFileContent(filePath) {
  try {
    if (!fs.existsSync(filePath)) return null;
    return fs.readFileSync(filePath, 'utf-8');
  } catch (error) {
    return `Error reading file: ${error.message}`;
  }
}

function generateContext(configName) {
  const config = configs[configName];
  if (!config) {
    console.error(`Unknown configuration: ${configName}`);
    console.log('Available configurations:', Object.keys(configs).join(', '));
    return;
  }

  console.log(`🔍 Exploring ${config.name}...\n`);

  let output = `# ${config.name}\n\n`;
  output += `Generated on: ${new Date().toISOString()}\n\n`;

  // Add specific included files
  if (config.include && config.include.length > 0) {
    output += `## Key Files\n\n`;
    
    for (const filePath of config.include) {
      if (fs.existsSync(filePath)) {
        const content = readFileContent(filePath);
        const stat = fs.statSync(filePath);
        
        output += `### ${filePath}\n`;
        output += `Size: ${(stat.size / 1024).toFixed(2)} KB\n\n`;
        output += `\`\`\`${path.extname(filePath).slice(1)}\n`;
        output += content || 'Unable to read file content';
        output += `\n\`\`\`\n\n`;
      }
    }
  }

  // Explore directories
  if (config.directories && config.directories.length > 0) {
    output += `## Directory Structure\n\n`;
    
    const allFiles = [];
    for (const dirPath of config.directories) {
      if (fs.existsSync(dirPath)) {
        exploreDirectory(dirPath, config, allFiles);
      }
    }

    // Group by directory
    const filesByDir = {};
    allFiles.forEach(file => {
      const dir = path.dirname(file.path);
      if (!filesByDir[dir]) filesByDir[dir] = [];
      filesByDir[dir].push(file);
    });

    for (const [dir, files] of Object.entries(filesByDir)) {
      output += `### ${dir}/\n`;
      files.forEach(file => {
        output += `- ${path.basename(file.path)} (${(file.size / 1024).toFixed(2)} KB)\n`;
      });
      output += '\n';
    }

    // Add content of smaller files
    output += `## File Contents\n\n`;
    const smallFiles = allFiles.filter(f => f.size < 10240); // Less than 10KB
    
    for (const file of smallFiles.slice(0, 10)) { // Limit to first 10 files
      const content = readFileContent(file.path);
      if (content && content.length > 0) {
        output += `### ${file.path}\n\n`;
        output += `\`\`\`${path.extname(file.path).slice(1)}\n`;
        output += content.length > 2000 ? content.slice(0, 2000) + '\n... (truncated)' : content;
        output += `\n\`\`\`\n\n`;
      }
    }
  }

  // Save output
  const outputFile = `project-${configName}.md`;
  fs.writeFileSync(outputFile, output);
  console.log(`✅ Context saved to: ${outputFile}`);
  console.log(`📊 Total size: ${(output.length / 1024).toFixed(2)} KB`);
}

// Command line interface
const args = process.argv.slice(2);
const configName = args[0] || 'overview';

if (args.includes('--help') || args.includes('-h')) {
  console.log('🚀 MeetSync Project Explorer\n');
  console.log('Usage: node project-explorer.js [config]\n');
  console.log('Available configurations:');
  Object.entries(configs).forEach(([key, config]) => {
    console.log(`  ${key.padEnd(12)} - ${config.name}`);
  });
  console.log('\nExamples:');
  console.log('  node project-explorer.js overview');
  console.log('  node project-explorer.js frontend');
  console.log('  node project-explorer.js backend');
} else {
  generateContext(configName);
} 