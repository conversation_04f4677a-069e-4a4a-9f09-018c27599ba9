// DEPRECATED: This file is being modularized for better maintainability
// New imports should use the modular structure from ./notion/
// This file is kept for backward compatibility during the migration

import { Client } from "@notionhq/client";
import { extractPageIdFromUrl } from "../../utils";

// Import the new modular Notion service facade
import { NotionServiceFacade } from "../notion/notion.facade";

/**
 * Service for interacting with Notion API
 * @deprecated Use NotionServiceFacade from ./notion/ instead
 */
class NotionService {
  private client: Client | null = null;
  private isInitialized = false;

  async initialize(apiKey: string): Promise<void> {
    try {
      this.client = new Client({ auth: apiKey });
      const response = await this.client.users.me({});
      if (response && response.id) {
        this.isInitialized = true;
        console.log("Notion service initialized successfully");
      } else {
        throw new Error("Failed to validate Notion API connection");
      }
    } catch (error) {
      console.error("Error initializing Notion service:", error);
      this.isInitialized = false;
      throw error;
    }
  }

  async initializeFromEncrypted(encryptedApiKey: string): Promise<void> {
    try {
      const { cryptoService } = await import("../crypto-service");
      const apiKey = await cryptoService.decrypt(encryptedApiKey);
      await this.initialize(apiKey);
    } catch (error) {
      console.error(
        "Error initializing Notion service from encrypted key:",
        error,
      );
      this.isInitialized = false;
      throw error;
    }
  }

  isReady(): boolean {
    return this.isInitialized && this.client !== null;
  }

  async getPage(pageId: string): Promise<any> {
    if (!this.isReady()) throw new Error("Notion service not initialized");
    try {
      return await this.client!.pages.retrieve({ page_id: pageId });
    } catch (error) {
      console.error(`Error retrieving Notion page ${pageId}:`, error);
      throw error;
    }
  }

  private sanitizeSelectValue(value: string): string {
    return value.replace(/,/g, " &");
  }
  private sanitizeMultiSelectValue(value: string): string {
    return value.replace(/,/g, " &");
  }

  /**
   * Strictly checks that no select/multi-select property contains a comma.
   * Throws an error if a comma is found, to avoid Notion API errors.
   */
  private strictPropertyGuard(properties: any) {
    Object.entries(properties).forEach(([key, prop]: [string, any]) => {
      if (prop.select && prop.select.name && prop.select.name.includes(",")) {
        throw new Error(
          `Select field "${key}" value contains a comma: ${prop.select.name}`,
        );
      }
      if (prop.multi_select && Array.isArray(prop.multi_select)) {
        prop.multi_select.forEach((option: any) => {
          if (option.name && option.name.includes(",")) {
            throw new Error(
              `Multi-select field "${key}" value contains a comma: ${option.name}`,
            );
          }
        });
      }
    });
  }

  /**
   * Format duration from minutes to human-readable format (X hrs Y mins)
   * @param minutes Duration in minutes
   * @returns Formatted duration string
   */
  private formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} mins`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return `${hours} ${hours === 1 ? 'hr' : 'hrs'}`;
    }
    
    return `${hours} ${hours === 1 ? 'hr' : 'hrs'} ${remainingMinutes} mins`;
  }

  /**
   * Create a new page in the Meeting Transcripts database
   * @param databaseId Database ID
   * @param data Transcript data
   * @returns ID of the created page
   */
  async createTranscriptPage(databaseId: string, data: any): Promise<string> {
    if (!this.isReady()) throw new Error("Notion service not initialized");
    try {
      // Match exactly your Notion table: Name, AI summary, Attendees, Date, Duration, Source, SourceURL, Time, Topics
      console.log(
        `Creating page in Notion database ${databaseId} with title: ${data.title}`,
      );

      const properties: any = {
        Name: {
          title: [
            {
              type: "text",
              text: { content: data.title || "Untitled Meeting" },
            },
          ],
        },
      };

      // Only add properties that exist in the database schema
      if (data.date) {
        properties.Date = {
          date: {
            start:
              data.date instanceof Date
                ? data.date.toISOString().split("T")[0]
                : data.date,
          },
        };
      }
      
      if (data.duration) {
        // Ensure duration is a valid number before formatting
        const durationNumber = typeof data.duration === 'number' ? data.duration : parseInt(data.duration);
        
        if (!isNaN(durationNumber) && durationNumber > 0) {
          // Send as formatted text instead of number since the column is now text type
          properties.Duration = {
            rich_text: [
              {
                type: "text",
                text: { content: this.formatDuration(durationNumber) },
              },
            ],
          };
          console.log(`Setting duration to "${this.formatDuration(durationNumber)}" (${durationNumber} minutes)`);
        } else {
          console.log(`Invalid duration value: ${data.duration}, skipping duration field`);
        }
      }
      
      if (data.sourceUrl) {
        properties.SourceURL = { url: data.sourceUrl };
      }
      
      if (data.participants && Array.isArray(data.participants)) {
        properties.Attendees = {
          rich_text: [
            {
              type: "text",
              text: { content: data.participants.join(", ") },
            },
          ],
        };
      }
      
      // Handle topics as multi_select (fully sanitized)
      if (data.topics && Array.isArray(data.topics)) {
        properties.Topics = {
          multi_select: data.topics.map((name: string) => ({
            name: this.sanitizeMultiSelectValue(name),
          })),
        };
      } else if (data.topics && typeof data.topics === "string") {
        const topicsArray = data.topics
          .split(",")
          .map((t: string) => t.trim())
          .filter(Boolean)
          .map((name: string) => ({
            name: this.sanitizeMultiSelectValue(name),
          }));
        properties.Topics = { multi_select: topicsArray };
      } else {
        properties.Topics = { multi_select: [] };
      }
      
      if (data.summary) {
        properties["AI summary"] = {
          rich_text: [
            {
              type: "text",
              text: { content: data.summary.substring(0, 2000) },
            },
          ],
        };
      }
      
      if (data.time) {
        properties.Time = {
          rich_text: [
            {
              type: "text",
              text: { content: data.time },
            },
          ],
        };
      } else if (!data.time && data.date instanceof Date) {
        properties.Time = {
          rich_text: [
            {
              type: "text",
              text: { content: data.date.toLocaleTimeString() },
            },
          ],
        };
      }

      // Log properties for debugging
      console.log(
        "Notion properties about to send:",
        JSON.stringify(properties, null, 2),
      );
      // Guard against any accidental commas
      this.strictPropertyGuard(properties);

      // Create the page
      const response = await this.client!.pages.create({
        parent: { database_id: databaseId },
        properties,
      });

      console.log(`Created Notion page for "${data.title}": ${response.id}`);

      // Add content blocks if available
      if (data.content || data.summary || data.notes) {
        await this.addTranscriptContent(response.id, data);
      }

      return response.id;
    } catch (error) {
      console.error("Error creating transcript page:", error);
      throw error;
    }
  }

  private async addTranscriptContent(pageId: string, data: any): Promise<void> {
    try {
      const blocks: any[] = [];
      
      // Add metadata header
      blocks.push({
        object: "block",
        type: "callout",
        callout: {
          rich_text: [{ 
            type: "text", 
            text: { 
              content: `📋 This meeting document contains ${data.notes ? 'Gemini Notes' : ''}${data.notes && data.content ? ' and ' : ''}${data.content ? 'Full Transcript' : ''}. ${this.calculateContentStats(data)}` 
            } 
          }],
          icon: { emoji: "📋" },
          color: "blue_background"
        }
      });

      blocks.push({ object: "block", type: "divider", divider: {} });

      // Handle Gemini Notes
      if (data.notes) {
        await this.addNotesContent(pageId, data.notes, blocks);
      }

      // Handle Transcript
      if (data.content) {
        await this.addTranscriptContentWithSubPages(pageId, data.content, blocks);
      }

      // Add initial blocks to the main page (in batches of 100)
      if (blocks.length > 0) {
        for (let i = 0; i < blocks.length; i += 100) {
          const batch = blocks.slice(i, i + 100);
          await this.client!.blocks.children.append({
            block_id: pageId,
            children: batch,
          });
        }
      }

      console.log(`Added content structure to page ${pageId}`);
    } catch (error) {
      console.error(`Error adding content to page ${pageId}:`, error);
    }
  }

  /**
   * Calculate content statistics for display
   */
  private calculateContentStats(data: any): string {
    const stats = [];
    
    if (data.notes) {
      const notesLength = data.notes.length;
      const notesPages = Math.ceil(notesLength / (90 * 1950)); // 90 blocks per page, ~1950 chars per block
      stats.push(`Notes: ${Math.round(notesLength / 1000)}k chars${notesPages > 1 ? ` (${notesPages} pages)` : ''}`);
    }
    
    if (data.content) {
      const transcriptLength = data.content.length;
      const transcriptPages = Math.ceil(transcriptLength / (90 * 1950));
      stats.push(`Transcript: ${Math.round(transcriptLength / 1000)}k chars${transcriptPages > 1 ? ` (${transcriptPages} pages)` : ''}`);
    }
    
    return stats.length > 0 ? `[${stats.join(', ')}]` : '';
  }

  /**
   * Add Gemini Notes content (with sub-pages if needed)
   */
  private async addNotesContent(parentPageId: string, notesText: string, mainBlocks: any[]): Promise<void> {
    // Add Notes section header
    mainBlocks.push({
      object: "block",
      type: "heading_2",
      heading_2: {
        rich_text: [{ type: "text", text: { content: "🤖 Gemini Meeting Notes" } }],
      },
    });

    const notesChunks = this.chunkText(notesText, 1950); // Slightly smaller chunks for safety
    const maxBlocksPerPage = 90; // Leave room for headers and structure

    if (notesChunks.length <= maxBlocksPerPage) {
      // All notes fit on main page
      console.log(`Adding ${notesChunks.length} notes chunks directly to main page`);
      
      for (const chunk of notesChunks) {
        mainBlocks.push({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [{ type: "text", text: { content: chunk } }],
          },
        });
      }
    } else {
      // Create sub-pages for notes
      const pagesNeeded = Math.ceil(notesChunks.length / maxBlocksPerPage);
      console.log(`Notes content too long: splitting into ${pagesNeeded} sub-pages`);

      for (let pageNum = 1; pageNum <= pagesNeeded; pageNum++) {
        const startIdx = (pageNum - 1) * maxBlocksPerPage;
        const endIdx = Math.min(pageNum * maxBlocksPerPage, notesChunks.length);
        const pageChunks = notesChunks.slice(startIdx, endIdx);

        // Create sub-page
        const subPageTitle = pagesNeeded > 1 ? `Gemini Notes ${pageNum}/${pagesNeeded}` : "Gemini Notes";
        const subPageBlocks = pageChunks.map(chunk => ({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [{ type: "text", text: { content: chunk } }],
          },
        }));

        const subPage = await this.client!.pages.create({
          parent: { page_id: parentPageId },
          properties: {
            title: [{ type: "text", text: { content: subPageTitle } }],
          },
          children: subPageBlocks as any,
        });

        // Add link to sub-page on main page
        mainBlocks.push({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [
              { type: "text", text: { content: "📄 " } },
              {
                type: "text",
                text: { content: subPageTitle },
                annotations: { bold: true },
                href: `https://notion.so/${subPage.id.replace(/-/g, "")}`,
              },
              { 
                type: "text", 
                text: { 
                  content: ` (${pageChunks.length} sections, ${pageChunks.reduce((total, chunk) => total + chunk.length, 0)} characters)` 
                } 
              },
            ],
          },
        });

        console.log(`Created Gemini Notes sub-page ${pageNum}/${pagesNeeded}: ${subPage.id}`);
      }
    }

    mainBlocks.push({ object: "block", type: "divider", divider: {} });
  }

  /**
   * Add Transcript content with intelligent sub-page creation
   */
  private async addTranscriptContentWithSubPages(parentPageId: string, transcriptText: string, mainBlocks: any[]): Promise<void> {
    // Add Transcript section header
    mainBlocks.push({
      object: "block",
      type: "heading_2",
      heading_2: {
        rich_text: [{ type: "text", text: { content: "📝 Full Meeting Transcript" } }],
      },
    });

    const transcriptChunks = this.chunkText(transcriptText, 1950); // Slightly smaller chunks for safety
    const maxBlocksPerPage = 90; // Leave room for headers and structure

    if (transcriptChunks.length <= maxBlocksPerPage) {
      // All transcript fits on main page
      console.log(`Adding ${transcriptChunks.length} transcript chunks directly to main page`);
      
      for (const chunk of transcriptChunks) {
        mainBlocks.push({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [{ type: "text", text: { content: chunk } }],
          },
        });
      }
    } else {
      // Create sub-pages for transcript
      const pagesNeeded = Math.ceil(transcriptChunks.length / maxBlocksPerPage);
      console.log(`Transcript content too long: splitting into ${pagesNeeded} sub-pages`);

      for (let pageNum = 1; pageNum <= pagesNeeded; pageNum++) {
        const startIdx = (pageNum - 1) * maxBlocksPerPage;
        const endIdx = Math.min(pageNum * maxBlocksPerPage, transcriptChunks.length);
        const pageChunks = transcriptChunks.slice(startIdx, endIdx);

        // Create sub-page
        const subPageTitle = `Transcript ${pageNum}/${pagesNeeded}`;
        const subPageBlocks = [
          {
            object: "block",
            type: "callout",
            callout: {
              rich_text: [{ 
                type: "text", 
                text: { 
                  content: `Part ${pageNum} of ${pagesNeeded} • ${pageChunks.length} sections • ${pageChunks.reduce((total, chunk) => total + chunk.length, 0)} characters` 
                } 
              }],
              icon: { emoji: "📝" },
              color: "gray_background"
            }
          },
          { object: "block", type: "divider", divider: {} },
          ...pageChunks.map(chunk => ({
            object: "block",
            type: "paragraph",
            paragraph: {
              rich_text: [{ type: "text", text: { content: chunk } }],
            },
          }))
        ];

        const subPage = await this.client!.pages.create({
          parent: { page_id: parentPageId },
          properties: {
            title: [{ type: "text", text: { content: subPageTitle } }],
          },
          children: subPageBlocks as any,
        });

        // Add link to sub-page on main page
        mainBlocks.push({
          object: "block",
          type: "paragraph",
          paragraph: {
            rich_text: [
              { type: "text", text: { content: "📄 " } },
              {
                type: "text",
                text: { content: subPageTitle },
                annotations: { bold: true },
                href: `https://notion.so/${subPage.id.replace(/-/g, "")}`,
              },
              { 
                type: "text", 
                text: { 
                  content: ` (${pageChunks.length} sections, ${pageChunks.reduce((total, chunk) => total + chunk.length, 0)} characters)` 
                } 
              },
            ],
          },
        });

        console.log(`Created Transcript sub-page ${pageNum}/${pagesNeeded}: ${subPage.id}`);
      }
    }
  }

  private chunkText(text: string, maxChunkLength: number): string[] {
    if (!text) return [];
    
    // Use a safety buffer to ensure we never exceed Notion's limits
    const safeMaxLength = maxChunkLength - 50; // Safety buffer
    
    if (text.length <= safeMaxLength) return [text];
    
    const chunks: string[] = [];
    let currentPosition = 0;
    
    while (currentPosition < text.length) {
      let endPosition = Math.min(currentPosition + safeMaxLength, text.length);
      
      // Try to break at natural boundaries to maintain readability
      if (endPosition < text.length) {
        // First try: break at double newline (paragraph boundary)
        const paragraphBreak = text.lastIndexOf("\n\n", endPosition);
        if (paragraphBreak > currentPosition && paragraphBreak > endPosition - 300) {
          endPosition = paragraphBreak + 2;
        } else {
          // Second try: break at single newline
          const lineBreak = text.lastIndexOf("\n", endPosition);
          if (lineBreak > currentPosition && lineBreak > endPosition - 200) {
            endPosition = lineBreak + 1;
          } else {
            // Third try: break at sentence boundary
            const sentenceBreak = text.lastIndexOf(". ", endPosition);
            if (sentenceBreak > currentPosition && sentenceBreak > endPosition - 150) {
              endPosition = sentenceBreak + 2;
            } else {
              // Fourth try: break at word boundary
              const wordBreak = text.lastIndexOf(" ", endPosition);
              if (wordBreak > currentPosition && wordBreak > endPosition - 50) {
                endPosition = wordBreak + 1;
              }
              // If no good break point found, just cut at the safe length
            }
          }
        }
      }
      
      // Ensure we don't exceed the absolute maximum
      if (endPosition - currentPosition > maxChunkLength) {
        endPosition = currentPosition + safeMaxLength;
      }
      
      const chunk = text.substring(currentPosition, endPosition).trim();
      
      // Only add non-empty chunks
      if (chunk.length > 0) {
        chunks.push(chunk);
        console.log(`Created chunk ${chunks.length} with ${chunk.length} characters (max: ${maxChunkLength})`);
      }
      
      currentPosition = endPosition;
    }
    
    return chunks;
  }

  /**
   * Find an existing Meeting Transcripts database in a Notion page
   * @param pageId The parent page ID to search in
   * @returns Database ID if found, null otherwise
   */
  async findTranscriptsDatabase(pageId: string): Promise<string | null> {
    if (!this.isReady()) throw new Error("Notion service not initialized");
    
    try {
      console.log(`Searching for Meeting Transcripts database in page: ${pageId}`);
      
      const response = await this.client!.blocks.children.list({
        block_id: pageId,
      });
      
      // Look for child database blocks
      const databaseBlocks = response.results.filter(
        (block: any) => block.type === 'child_database'
      );
      
      console.log(`Found ${databaseBlocks.length} database blocks in page`);
      
      // Check each database to find one named "Meeting Transcripts"
      for (const block of databaseBlocks) {
        try {
          const databaseInfo = await this.client!.databases.retrieve({
            database_id: block.id,
          });
          
          // Extract database title
          let title = '';
          if ((databaseInfo as any).title && (databaseInfo as any).title.length > 0) {
            title = (databaseInfo as any).title
              .map((titlePart: any) => titlePart.plain_text)
              .join('');
          }
          
          console.log(`Found database: "${title}" (ID: ${block.id})`);
          
          if (title.toLowerCase().includes('meeting') || 
              title.toLowerCase().includes('transcript')) {
            console.log(`Found existing Meeting Transcripts database: ${block.id}`);
            return block.id;
          }
        } catch (dbError) {
          console.error(`Error checking database ${block.id}:`, dbError);
          continue;
        }
      }
      
      console.log('No Meeting Transcripts database found');
      return null;
    } catch (error) {
      console.error('Error finding Meeting Transcripts database:', error);
      return null;
    }
  }

  /**
   * Create a Meeting Transcripts database in a Notion page
   * @param pageId The parent page ID to create the database in
   * @returns Database ID of the created database
   */
  async createTranscriptsDatabase(pageId: string): Promise<string> {
    if (!this.isReady()) throw new Error("Notion service not initialized");
    
    try {
      console.log(`Creating Meeting Transcripts database in page: ${pageId}`);
      
      // Create the database with the same schema as setup-notion.ts
      const response = await this.client!.databases.create({
        parent: {
          type: 'page_id',
          page_id: pageId,
        },
        title: [
          {
            type: 'text',
            text: {
              content: 'Meeting Transcripts',
            },
          },
        ],
        properties: {
          Name: {
            title: {},
          },
          Date: {
            date: {},
          },
          Source: {
            select: {
              options: [
                { name: 'Google Meet', color: 'blue' },
                { name: 'Google Chat', color: 'green' },
                { name: 'Microsoft Teams', color: 'purple' },
                { name: 'Zoom', color: 'orange' },
                { name: 'Slack', color: 'pink' },
                { name: 'Other', color: 'gray' },
              ],
            },
          },
          Attendees: {
            rich_text: {},
          },
          Topics: {
            multi_select: {
              options: [],
            },
          },
          Duration: {
            rich_text: {},
          },
          SourceURL: {
            url: {},
          },
          Time: {
            rich_text: {},
          },
          "AI summary": {
            rich_text: {},
          },
        },
      });
      
      console.log(`Created Meeting Transcripts database: ${response.id}`);
      return response.id;
    } catch (error) {
      console.error('Error creating Meeting Transcripts database:', error);
      throw error;
    }
  }

  /**
   * Find or create a Meeting Transcripts database in a Notion page
   * @param pageId The parent page ID to search/create in
   * @returns Database ID
   */
  async findOrCreateTranscriptsDatabase(pageId: string): Promise<string> {
    // First try to find existing database
    const existingDatabaseId = await this.findTranscriptsDatabase(pageId);
    if (existingDatabaseId) {
      return existingDatabaseId;
    }
    
    // If not found, create a new one
    return await this.createTranscriptsDatabase(pageId);
  }
}

// DEPRECATED: Use the new modular Notion service instead
// This is kept for backward compatibility during migration

// Use the new modular Notion service facade
export const notionService = new NotionServiceFacade();

// Legacy Notion service class (kept for reference during migration)
export const legacyNotionService = new NotionService();
