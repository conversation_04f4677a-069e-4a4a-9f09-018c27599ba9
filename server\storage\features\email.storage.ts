import { emails, emailChunks, type Email, type InsertEmail, type EmailChunk, type InsertEmailChunk } from "../../../shared/index.js";
import { eq, and, like, desc, sql } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config.js";
import { RAGStorage } from "./rag.storage.js";

/**
 * Email storage class following the same pattern as FileStorage
 */
export class EmailStorage {
  private db: any;
  private memoryEmails: Map<number, Email>;
  private emailIdCounter: number;
  private useDatabase: boolean;
  private ragStorage: RAGStorage;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memoryEmails = new Map();
    this.emailIdCounter = 1;
    this.ragStorage = new RAGStorage(useDatabase);
  }

  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        console.warn('Database not available, falling back to memory storage');
        this.useDatabase = false;
      }
    }
  }

  async getEmails(platform?: string, userId?: string, page = 1, pageSize = 20): Promise<Email[]> {
    this.ensureDatabase();
    const offset = (page - 1) * pageSize;

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(emails);
      
      const conditions = [];
      if (platform) conditions.push(eq(emails.platform, platform));
      if (userId) conditions.push(eq(emails.userId, userId));

      if (conditions.length > 0) {
        baseQuery = baseQuery.where(and(...conditions)) as any;
      }

      baseQuery = baseQuery.orderBy(desc(emails.receivedAt)) as any;
      baseQuery = baseQuery.limit(pageSize) as any;
      baseQuery = baseQuery.offset(offset) as any;

      return await baseQuery;
    } else {
      let allEmails = Array.from(this.memoryEmails.values());
      
      if (platform) {
        allEmails = allEmails.filter(email => email.platform === platform);
      }
      if (userId) {
        allEmails = allEmails.filter(email => email.userId === userId);
      }
      
      allEmails.sort((a, b) =>
        new Date(b.receivedAt || b.createdAt).getTime() - new Date(a.receivedAt || a.createdAt).getTime()
      );

      return allEmails.slice(offset, offset + pageSize);
    }
  }

  async getEmail(id: number): Promise<Email | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(emails).where(eq(emails.id, id));
      return result[0];
    } else {
      return this.memoryEmails.get(id);
    }
  }

  async getEmailByExternalId(externalId: string, platform: string): Promise<Email | undefined> {
    this.validateString(externalId, 'externalId');
    this.validateString(platform, 'platform');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(emails)
        .where(and(eq(emails.externalId, externalId), eq(emails.platform, platform)));
      return result[0];
    } else {
      return Array.from(this.memoryEmails.values()).find(
        (email) => email.externalId === externalId && email.platform === platform
      );
    }
  }

  async getEmailCount(platform?: string, userId?: string): Promise<number> {
    this.ensureDatabase();

    if (this.useDatabase) {
      let countQuery = this.db.select({ count: sql`COUNT(*)` }).from(emails);

      const conditions = [];
      if (platform) conditions.push(eq(emails.platform, platform));
      if (userId) conditions.push(eq(emails.userId, userId));
      
      // Only count active emails
      conditions.push(eq(emails.status, 'active'));

      if (conditions.length > 0) {
        countQuery = countQuery.where(and(...conditions)) as any;
      }

      const result = await countQuery;
      return Number(result[0]?.count) || 0;
    } else {
      return Array.from(this.memoryEmails.values()).filter((email) => {
        const platformMatch = !platform || email.platform === platform;
        const userIdMatch = !userId || email.userId === userId;
        const statusMatch = email.status === 'active';
        return platformMatch && userIdMatch && statusMatch;
      }).length;
    }
  }

  async createEmail(email: InsertEmail): Promise<Email> {
    this.validateString(email.externalId, 'externalId');
    this.validateString(email.subject, 'subject');
    this.validateString(email.content, 'content');
    this.validateString(email.sender, 'sender');
    this.validateString(email.platform, 'platform');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(emails).values(email).returning();
      const createdEmail = result[0];
      
      // Create chunks for vectorization
      await this.createEmailChunks(createdEmail);
      
      return createdEmail;
    } else {
      const id = this.emailIdCounter++;
      const newEmail: Email = {
        ...email,
        id,
        status: email.status || "active",
        recipients: email.recipients || [],
        cc: email.cc || [],
        bcc: email.bcc || [],
        metadata: email.metadata || {},
        extractedMetadata: email.extractedMetadata || {},
        labels: email.labels || [],
        isRead: email.isRead || false,
        isStarred: email.isStarred || false,
        isArchived: email.isArchived || false,
        threadId: email.threadId || null,
        userId: email.userId || null,
        organizationId: email.organizationId || null,
        syncItemId: email.syncItemId || null,
        createdAt: new Date(),
        updatedAt: new Date(),
        receivedAt: email.receivedAt || new Date(),
      } as Email;
      this.memoryEmails.set(id, newEmail);

      // Create chunks for vectorization
      await this.createEmailChunks(newEmail);

      return newEmail;
    }
  }

  async updateEmail(id: number, data: Partial<Email>): Promise<Email | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.update(emails)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(emails.id, id))
        .returning();
      return result[0];
    } else {
      const email = this.memoryEmails.get(id);
      if (!email) return undefined;

      const updated: Email = {
        ...email,
        ...data,
        updatedAt: new Date(),
      };

      this.memoryEmails.set(id, updated);
      return updated;
    }
  }

  async searchEmails(query: string, platform?: string, userId?: string): Promise<Email[]> {
    this.validateString(query, 'query');
    this.ensureDatabase();

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(emails);

      const conditions = [];
      if (platform) conditions.push(eq(emails.platform, platform));
      if (userId) conditions.push(eq(emails.userId, userId));

      // Search in subject and content
      if (query && query !== '*') {
        conditions.push(sql`(${emails.subject} ILIKE ${'%' + query + '%'} OR ${emails.content} ILIKE ${'%' + query + '%'})`);
      }

      if (conditions.length > 0) {
        baseQuery = baseQuery.where(and(...conditions)) as any;
      }

      return await (baseQuery.orderBy(desc(emails.receivedAt)) as any);
    } else {
      return Array.from(this.memoryEmails.values()).filter((email) => {
        let searchMatch = true;
        
        if (query !== '*') {
          const subjectMatch = email.subject.toLowerCase().includes(query.toLowerCase());
          const contentMatch = email.content.toLowerCase().includes(query.toLowerCase());
          const senderMatch = email.sender.toLowerCase().includes(query.toLowerCase());
          searchMatch = subjectMatch || contentMatch || senderMatch;
        }

        const platformMatch = !platform || email.platform === platform;
        const userIdMatch = !userId || email.userId === userId;

        return searchMatch && platformMatch && userIdMatch;
      });
    }
  }

  async markEmailsAsDeleted(externalIds: string[], platform: string): Promise<void> {
    this.validateString(platform, 'platform');

    if (externalIds.length === 0) return;

    this.ensureDatabase();

    if (this.useDatabase) {
      for (const externalId of externalIds) {
        await this.db.update(emails)
          .set({ status: 'deleted', updatedAt: new Date() })
          .where(and(
            eq(emails.externalId, externalId),
            eq(emails.platform, platform)
          ));
      }
    } else {
      const allEmails = Array.from(this.memoryEmails.values());
      for (const email of allEmails) {
        if (email.platform === platform && externalIds.includes(email.externalId)) {
          const updated: Email = {
            ...email,
            status: "deleted",
            updatedAt: new Date(),
          };
          this.memoryEmails.set(email.id, updated);
        }
      }
    }
  }

  // Email Chunks Operations
  private async createEmailChunks(email: Email): Promise<void> {
    // Split email content into chunks
    const chunks = this.splitIntoChunks(email.content);
      
    // Create chunks and process them for embeddings
    for (let i = 0; i < chunks.length; i++) {
      const chunk: InsertEmailChunk = {
        emailId: email.id,
        chunkIndex: i,
        content: chunks[i],
        metadata: {
          subject: email.subject,
          sender: email.sender,
          platform: email.platform,
          recipient: Array.isArray(email.recipients) ? email.recipients[0] || '' : '',
          isRead: email.isRead,
          labels: Array.isArray(email.labels) ? email.labels : [],
        },
      };
      const createdChunk = await this.createEmailChunk(chunk);
      
      // Process for embeddings if we have the embedding service available
      try {
        const { embeddingService } = await import('../../services/ai/embedding-service.js');
        if (embeddingService?.isInitialized()) {
          await embeddingService.processEmailChunkForEmbeddings(createdChunk.id, createdChunk.content);
        }
      } catch (error) {
        console.warn('Could not process email chunk for embeddings:', error);
      }
    }
  }

  private async createEmailChunk(chunk: InsertEmailChunk): Promise<EmailChunk> {
    this.validateId(chunk.emailId);
    this.validateString(chunk.content, 'content');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(emailChunks).values(chunk).returning();
      return result[0];
    } else {
      const id = this.emailIdCounter++;
      const newChunk: EmailChunk = {
        ...chunk,
        id,
        metadata: chunk.metadata || {},
        embedding: chunk.embedding || null,
        tokenCount: chunk.tokenCount || null,
        createdAt: new Date(),
      };
      return newChunk;
    }
  }

  private splitIntoChunks(content: string, maxChunkSize: number = 1000): string[] {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxChunkSize) {
        if (currentChunk.length > 0) {
          chunks.push(currentChunk.trim());
          currentChunk = '';
        }
        // If a single sentence is longer than maxChunkSize, split it into words
        if (sentence.length > maxChunkSize) {
          const words = sentence.split(/\s+/);
          let currentWordChunk = '';
          for (const word of words) {
            if (currentWordChunk.length + word.length + 1 > maxChunkSize) {
              chunks.push(currentWordChunk.trim());
              currentWordChunk = word;
            } else {
              currentWordChunk += (currentWordChunk ? ' ' : '') + word;
            }
          }
          if (currentWordChunk) {
            chunks.push(currentWordChunk.trim());
          }
        } else {
          chunks.push(sentence.trim());
        }
      } else {
        currentChunk += (currentChunk ? ' ' : '') + sentence;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  // Utility methods
  private validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid email ID provided');
    }
  }

  private validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  async updateEmailChunkEmbedding(chunkId: number, embedding: number[]): Promise<void> {
    this.validateId(chunkId);
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.update(emailChunks)
        .set({ 
          embedding: embedding,
          tokenCount: this.estimateTokenCount(embedding.length)
        })
        .where(eq(emailChunks.id, chunkId));
    }
    // Note: For memory storage, email chunks with embeddings aren't fully supported yet
  }

  private estimateTokenCount(length: number): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(length / 4);
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      emailCount: this.useDatabase ? 'unknown' : this.memoryEmails.size,
    };
  }
} 