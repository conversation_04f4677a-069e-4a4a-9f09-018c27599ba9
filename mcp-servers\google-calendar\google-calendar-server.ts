import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { 
  CallToolRequestSchema, 
  ListToolsRequestSchema,
  Tool 
} from '@modelcontextprotocol/sdk/types.js';
import { GoogleCalendarTools } from './google-calendar-tools.js';
import { mcpTokenService } from '../utils/mcp-token.service.js';
import dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

export class MCPCalendarServer {
  private server: Server;
  private calendarTools: GoogleCalendarTools;

  constructor() {
    this.server = new Server({
      name: 'google-calendar-mcp',
      version: '1.0.0',
    }, {
      capabilities: {
        tools: {},
      },
    });

    // Initialize Google Calendar tools with environment variables
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    const redirectUri = process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/auth/google/callback';

    if (!clientId || !clientSecret) {
      throw new Error('Google OAuth credentials not found');
    }

    this.calendarTools = new GoogleCalendarTools(clientId, clientSecret, redirectUri);
    this.setupHandlers();
  }

  private setupHandlers(): void {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'list_events',
            description: 'List events from Google Calendar',
            inputSchema: {
              type: 'object',
              properties: {
                userId: { type: 'string', description: 'User ID for authentication' },
                maxResults: { type: 'number', description: 'Maximum number of events to return' },
                timeMin: { type: 'string', description: 'Lower bound for events (ISO 8601)' },
                timeMax: { type: 'string', description: 'Upper bound for events (ISO 8601)' },
              },
              required: ['userId'],
            },
          },
          {
            name: 'create_event',
            description: 'Create a new event in Google Calendar',
            inputSchema: {
              type: 'object',
              properties: {
                userId: { type: 'string', description: 'User ID for authentication' },
                summary: { type: 'string', description: 'Event title' },
                description: { type: 'string', description: 'Event description' },
                start: {
                  type: 'object',
                  properties: {
                    dateTime: { type: 'string', description: 'Start date/time (ISO 8601)' },
                    timeZone: { type: 'string', description: 'Time zone' },
                  },
                  required: ['dateTime'],
                },
                end: {
                  type: 'object',
                  properties: {
                    dateTime: { type: 'string', description: 'End date/time (ISO 8601)' },
                    timeZone: { type: 'string', description: 'Time zone' },
                  },
                  required: ['dateTime'],
                },
                attendees: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      email: { type: 'string', description: 'Attendee email' },
                    },
                    required: ['email'],
                  },
                },
              },
              required: ['userId', 'summary', 'start', 'end'],
            },
          },
          {
            name: 'update_event',
            description: 'Update an existing event in Google Calendar',
            inputSchema: {
              type: 'object',
              properties: {
                userId: { type: 'string', description: 'User ID for authentication' },
                eventId: { type: 'string', description: 'Event ID to update' },
                updates: {
                  type: 'object',
                  properties: {
                    summary: { type: 'string', description: 'Event title' },
                    description: { type: 'string', description: 'Event description' },
                    start: {
                      type: 'object',
                      properties: {
                        dateTime: { type: 'string', description: 'Start date/time (ISO 8601)' },
                        timeZone: { type: 'string', description: 'Time zone' },
                      },
                    },
                    end: {
                      type: 'object',
                      properties: {
                        dateTime: { type: 'string', description: 'End date/time (ISO 8601)' },
                        timeZone: { type: 'string', description: 'Time zone' },
                      },
                    },
                  },
                },
              },
              required: ['userId', 'eventId', 'updates'],
            },
          },
          {
            name: 'delete_event',
            description: 'Delete an event from Google Calendar',
            inputSchema: {
              type: 'object',
              properties: {
                userId: { type: 'string', description: 'User ID for authentication' },
                eventId: { type: 'string', description: 'Event ID to delete' },
              },
              required: ['userId', 'eventId'],
            },
          },
          {
            name: 'get_current_datetime',
            description: 'Get the current date and time in ISO 8601 format',
            inputSchema: {
              type: 'object',
              properties: {
                timeZone: { type: 'string', description: 'Optional timezone (e.g., "America/New_York"). Defaults to UTC.' },
                format: { type: 'string', description: 'Optional format: "iso" (default), "readable", or "timestamp"' },
              },
              required: [],
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      // Handle get_current_datetime tool (no auth required)
      if (name === 'get_current_datetime') {
        return this.calendarTools.getCurrentDateTime(args as any);
      }
      
      if (!args || typeof args !== 'object' || !('userId' in args) || typeof args.userId !== 'string') {
        return {
          content: [{
            type: 'text',
            text: 'Error: userId is required for all calendar operations'
          }],
          isError: true
        };
      }

      if (args.userId) {
        try {
          const tokens = await mcpTokenService.getTokens(args.userId, 'google');
          if (!tokens) {
            return {
              content: [{
                type: 'text',
                text: 'No valid tokens found. Please reconnect your Google account.'
              }],
              isError: true
            };
          }

          // Set credentials for this request
          this.calendarTools.setCredentials(tokens);

          try {
            switch (name) {
              case 'list_events':
                return await this.calendarTools.listEvents(args as any);
              case 'create_event':
                return await this.calendarTools.createEvent(args as any);
              case 'update_event':
                return await this.calendarTools.updateEvent(args as any);
              case 'delete_event':
                return await this.calendarTools.deleteEvent(args as any);
              default:
                return {
                  content: [{
                    type: 'text',
                    text: `Unknown tool: ${name}`
                  }],
                  isError: true
                };
            }
          } catch (error) {
            console.error(`Error calling tool ${name}:`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
              content: [{
                type: 'text',
                text: `Error calling tool ${name}: ${errorMessage}`
              }],
              isError: true
            };
          }
        } catch (error) {
          console.error('Error retrieving tokens:', error);
          return {
            content: [{
              type: 'text',
              text: 'Failed to retrieve authentication tokens'
            }],
            isError: true
          };
        }
      }

      // Return a default response if no userId is provided
      return {
        content: [{
          type: 'text',
          text: 'Error: userId is required for all calendar operations'
        }],
        isError: true
      };
    });
  }

  async start(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log('Google Calendar MCP Server started');
  }
}