import { Express } from 'express';
import {
  healthCheckMiddleware,
  metricsEndpointMiddleware
} from '../core/middleware/monitoring.middleware';
import { getSessionStatus } from '../core/middleware/session.middleware';

/**
 * Monitoring and observability routes
 */
export function registerMonitoringRoutes(app: Express) {
  // Health check endpoint
  app.get('/api/health', healthCheckMiddleware);
  app.get('/health', healthCheckMiddleware);
  
  // Prometheus metrics endpoint
  app.get('/api/metrics', metricsEndpointMiddleware);
  app.get('/metrics', metricsEndpointMiddleware);
  
  // System information endpoint
  app.get('/api/system/info', async (req, res) => {
    const systemInfo = {
      service: 'meetsync',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
      memory: process.memoryUsage(),
      pid: process.pid,
      session: getSessionStatus(),
    };
    
    res.json(systemInfo);
  });
  
  // Readiness probe (for Kubernetes)
  app.get('/api/ready', (req, res) => {
    // Add custom readiness checks here
    res.status(200).json({ 
      status: 'ready', 
      timestamp: new Date().toISOString() 
    });
  });
  
  // Liveness probe (for Kubernetes)
  app.get('/api/live', (req, res) => {
    res.status(200).json({ 
      status: 'alive', 
      timestamp: new Date().toISOString() 
    });
  });
} 