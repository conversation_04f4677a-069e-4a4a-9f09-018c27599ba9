
> rest-express@1.0.0 dev
Scheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
9:46:02 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
9:46:06 PM [express] GET /api/sync-logs 304 in 104ms :: {"logs":[]}
9:46:06 PM [express] GET /api/integrations 200 in 375ms :: {"integrations":[{"id":1,"name":"Google M…
9:46:07 PM [express] GET /api/integrations 200 in 782ms :: {"integrations":[{"id":1,"name":"Google M…
9:46:07 PM [express] GET /api/sync-logs 304 in 786ms :: {"logs":[]}
9:46:59 PM [express] GET /api/integrations 200 in 331ms :: {"integrations":[{"id":1,"name":"Google M…
9:47:24 PM [express] GET /api/integrations 304 in 318ms :: {"integrations":[{"id":1,"name":"Google M…
9:47:24 PM [express] GET /api/integrations 304 in 417ms :: {"integrations":[{"id":1,"name":"Google M…
9:49:20 PM [express] GET /api/integrations 304 in 2800ms :: {"integrations":[{"id":1,"name":"Google …
9:49:24 PM [express] GET /api/integrations 304 in 76ms :: {"integrations":[{"id":1,"name":"Google Me…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_1rzzt5gp99x
9:49:25 PM [express] GET /api/integrations/1/auth-url 200 in 164ms :: {"authUrl":"https://accounts.g…
9:49:30 PM [express] GET /api/integrations/oauth/callback 302 in 3ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
9:49:30 PM [express] GET /api/integrations/1/oauth/callback 302 in 240ms
9:49:31 PM [express] GET /api/integrations 200 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 9 folders
9:49:33 PM [express] GET /api/integrations/1/folders 304 in 618ms :: {"folders":[{"parents":["1U_QJy…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2',
    driveName: 'MeetSync'
  }
}
Testing connection to Google Drive folder: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2
Updating integration with verified folder config: { driveId: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2', driveName: 'MeetSync' }
9:49:36 PM [express] POST /api/integrations/1/test-connection 200 in 472ms :: {"success":true,"messa…
Cancelled schedule for integration 1
9:49:37 PM [express] PUT /api/integrations/1 200 in 223ms :: {"message":"Integration updated success…
9:49:38 PM [express] GET /api/integrations 200 in 73ms :: {"integrations":[{"id":1,"name":"Google Me…
9:49:39 PM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
9:49:56 PM [express] GET /api/integrations 304 in 2342ms :: {"integrations":[{"id":1,"name":"Google …
9:49:59 PM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_cur8yy577fg
9:50:00 PM [express] GET /api/integrations/1/auth-url 200 in 155ms :: {"authUrl":"https://accounts.g…
9:50:05 PM [express] GET /api/integrations/oauth/callback 302 in 1ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
9:50:05 PM [express] GET /api/integrations/1/oauth/callback 302 in 246ms
9:50:05 PM [express] GET /api/integrations 200 in 76ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 9 folders
9:50:07 PM [express] GET /api/integrations/1/folders 304 in 527ms :: {"folders":[{"parents":["1U_QJy…
9:50:10 PM [express] PUT /api/integrations/1 200 in 247ms :: {"message":"Integration updated success…
9:50:10 PM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
9:50:11 PM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
