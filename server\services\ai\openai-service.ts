import OpenAI from "openai";

class OpenAIService {
  private openai!: OpenAI;
  private initialized: boolean = false;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn(
        "OPENAI_API_KEY not found in environment. AI features will be disabled.",
      );
      return;
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.initialized = true;
    console.log("OpenAI service initialized successfully");
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Execute OpenAI API call with retry logic and exponential backoff
   */
  private async executeWithRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[OpenAI] Attempt ${attempt}/${maxRetries}`);
        return await apiCall();
      } catch (error: any) {
        if (error.status === 429 && attempt < maxRetries) {
          // Rate limit error - wait before retrying
          const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
          console.log(`[OpenAI] Rate limit hit, waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // If it's the last attempt or not a rate limit error, throw the error
        throw error;
      }
    }
    
    throw new Error('Max retries exceeded');
  }

  /**
   * Extract meeting name from transcript content
   */
  async extractMeetingName(content: string): Promise<string> {
    if (!this.initialized) {
      return "Untitled Meeting";
    }

    try {
      // Take first 3000 characters for context (to stay within token limits)
      const contextText = content.substring(0, 3000);

      const response = await this.openai.chat.completions.create({
        model: "gpt-4.1-nano", // DO NOT CHANGE THIS MODEL UNLESS EXPLICITLY REQUESTED BY THE USER
        messages: [
          {
            role: "system",
            content:
              "Extract a descriptive, professional meeting title from the provided transcript. Return only the title, nothing else. Do not include date or time in the title.",
          },
          {
            role: "user",
            content: contextText,
          },
        ],
        max_tokens: 30,
        temperature: 0.2,
      });

      const title =
        response.choices[0].message.content?.trim() || "Untitled Meeting";
      return title;
    } catch (error) {
      console.error("Error extracting meeting name:", error);
      return "Untitled Meeting";
    }
  }

  /**
   * Extract attendees from transcript content
   */
  async extractAttendees(content: string): Promise<string[]> {
    if (!this.initialized) {
      return ["Unknown"];
    }

    try {
      // Take first 4000 characters for context (to stay within token limits)
      const contextText = content.substring(0, 4000);

      const response = await this.openai.chat.completions.create({
        model: "gpt-4.1-nano", // DO NOT CHANGE THIS MODEL UNLESS EXPLICITLY REQUESTED BY THE USER
        messages: [
          {
            role: "system",
            content:
              "Extract the list of meeting attendees from the provided transcript. The list of attendees may be present in the summary at the beginning of the transcript, or may be mentioned throughout the transcript. Do not include AI assistants like Gemini or other note-taking bots. Return the result as a JSON array of names.",
          },
          {
            role: "user",
            content: contextText,
          },
        ],
        response_format: { type: "json_object" },
        max_tokens: 500 ,
        temperature: 0.2,
      });

      try {
        const result = JSON.parse(response.choices[0].message.content || "{}");
        if (Array.isArray(result.attendees)) {
          return result.attendees
            .map((name: string) => name.trim())
            .filter(Boolean);
        }
        // Fallback in case the response format isn't as expected
        const content = response.choices[0].message.content || "{}";
        const matches = content.match(/\[(.*?)\]/);
        if (matches && matches[1]) {
          return matches[1]
            .split(",")
            .map((name) => name.trim().replace(/"/g, ""))
            .filter(Boolean);
        }
      } catch (parseError) {
        console.error("Error parsing attendees JSON:", parseError);
      }

      return ["Unknown"];
    } catch (error) {
      console.error("Error extracting attendees:", error);
      return ["Unknown"];
    }
  }

  /**
   * Extract topics/keywords from transcript content
   */
  async extractTopics(content: string): Promise<string[]> {
    if (!this.initialized) {
      return ["General Discussion"];
    }

    try {
      // Take first 6000 characters for context (to stay within token limits)
      const contextText = content.substring(0, 2000);

      const response = await this.openai.chat.completions.create({
        model: "gpt-4.1-nano", // DO NOT CHANGE THIS MODEL UNLESS EXPLICITLY REQUESTED BY THE USER
        messages: [
          {
            role: "system",
            content:
              "Based on the conversation, which of the following departments are relevant to this meeting? Choose all that apply: [Dev/Engineering, HR, Sales, Marketing, Finance, Legal, Customer Support, Operations, IT, Executive]. Return the result as a JSON object with an array of topics.",
          },
          {
            role: "user",
            content: contextText,
          },
        ],
        response_format: { type: "json_object" },
        max_tokens: 200,
        temperature: 0.3,
      });

      try {
        const result = JSON.parse(response.choices[0].message.content || "{}");
        if (Array.isArray(result.topics)) {
          return result.topics
            .map((topic: string) => topic.trim())
            .filter(Boolean);
        }
      } catch (parseError) {
        console.error("Error parsing topics JSON:", parseError);
      }

      return ["General Discussion"];
    } catch (error) {
      console.error("Error extracting topics:", error);
      return ["General Discussion"];
    }
  }

  /**
   * Extract all metadata from transcript content in a single function
   */
  async extractMetadata(
    content: string,
    filename: string,
    existingMetadata?: any
  ): Promise<{
    title: string;
    attendees: string[];
    topics: string[];
    summary: string;
    date: string | null;
    time: string | null;
  }> {
    // Check if we have enhanced Microsoft Teams metadata
    if (existingMetadata?.meetingAttendees || existingMetadata?.calendarAttendees) {
      console.log('[OPENAI] Using enhanced Microsoft Teams metadata for attendees');
      
      const teamsAttendees = existingMetadata.meetingAttendees || existingMetadata.calendarAttendees || [];
      const attendeeNames = teamsAttendees.map((attendee: any) => 
        attendee.identity?.user?.displayName || 
        attendee.identity?.user?.userPrincipalName ||
        attendee.emailAddress?.name ||
        'Unknown Attendee'
      ).filter(Boolean);

      // Use meeting subject if available
      const title = existingMetadata.meetingSubject || 
                   existingMetadata.calendarSubject || 
                   await this.executeWithRetry(() => this.extractMeetingName(content));

      // Extract topics from content (still use AI for this)
      const topics = await this.executeWithRetry(() => this.extractTopics(content));

      // Use meeting times if available
      let date: string | null = null;
      let time: string | null = null;

      if (existingMetadata.meetingStartTime || existingMetadata.calendarStartTime) {
        const startTime = existingMetadata.meetingStartTime || existingMetadata.calendarStartTime;
        try {
          const startDate = new Date(startTime);
          date = startDate.toISOString().split('T')[0]; // ISO format
          time = startDate.toLocaleTimeString('en-US', { 
            hour: 'numeric', 
            minute: '2-digit', 
            hour12: true 
          });
        } catch (error) {
          console.error('Error parsing meeting start time:', error);
        }
      }

      // Fall back to filename extraction if no meeting times
      if (!date || !time) {
        const filenameMetadata = this.extractDateTimeFromFilename(filename);
        date = date || filenameMetadata.date;
        time = time || filenameMetadata.time;
      }

      return {
        title,
        attendees: attendeeNames,
        topics,
        summary: "", // Leave empty for Notion to auto-generate
        date,
        time,
      };
    }

    // Fall back to original extraction logic for non-Teams files
    return this.extractMetadataOriginal(content, filename);
  }

  /**
   * Original metadata extraction logic (renamed)
   */
  private async extractMetadataOriginal(
    content: string,
    filename: string,
  ): Promise<{
    title: string;
    attendees: string[];
    topics: string[];
    summary: string;
    date: string | null;
    time: string | null;
  }> {
    // Extract date/time from filename using regex
    const { date, time } = this.extractDateTimeFromFilename(filename);

    // Run extraction tasks in parallel for efficiency with retry logic
    const [title, attendees, topics] = await Promise.all([
      this.executeWithRetry(() => this.extractMeetingName(content)),
      this.executeWithRetry(() => this.extractAttendees(content)),
      this.executeWithRetry(() => this.extractTopics(content)),
    ]);

    return {
      title,
      attendees,
      topics,
      summary: "", // Leave empty for Notion to auto-generate
      date,
      time,
    };
  }

  /**
   * Extract date and time from filename
   */
  private extractDateTimeFromFilename(filename: string): { date: string | null; time: string | null } {
    let date: string | null = null;
    let time: string | null = null;

    try {
      const match = filename.match(
        /(\d{4})\/(\d{2})\/(\d{2}) (\d{2}):(\d{2}) (\w+)/,
      );
      if (match) {
        const [_, year, month, day, hour, minute, tz] = match;

        // Create date object
        const dateObj = new Date(`${year}-${month}-${day}`);
        date = dateObj.toISOString().split("T")[0]; // ISO format for Notion

        // Format time (12-hour with AM/PM)
        const hour12 = parseInt(hour) % 12 || 12;
        const ampm = parseInt(hour) < 12 ? "AM" : "PM";
        time = `${hour12}:${minute} ${ampm} ${tz}`;
      }
    } catch (error) {
      console.error("Error parsing date/time from filename:", error);
    }

    return { date, time };
  }
}

// Export singleton instance
export const openaiService = new OpenAIService();
