import { 
  FunctionTool, 
  Too<PERSON><PERSON>xe<PERSON>ionR<PERSON>ult, 
  ToolExecutionContext,
  ToolValidator 
} from './function-tool.interface';
import { ToolRegistry } from './tool-registry';

/**
 * Tool execution engine with comprehensive error handling and monitoring
 */
export class ToolExecutor {
  private static instance: ToolExecutor;
  private registry: ToolRegistry;
  private executionHistory: Map<string, ToolExecutionResult[]> = new Map();

  private constructor() {
    this.registry = ToolRegistry.getInstance();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ToolExecutor {
    if (!ToolExecutor.instance) {
      ToolExecutor.instance = new ToolExecutor();
    }
    return ToolExecutor.instance;
  }

  /**
   * Execute a tool with comprehensive error handling and monitoring
   */
  async executeTool(
    toolName: string, 
    parameters: any, 
    context?: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    const requestId = context?.requestId || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.log('info', `Starting execution of tool '${toolName}'`, { 
      requestId, 
      parameters: this.sanitizeParameters(parameters),
      context 
    });

    try {
      // Get tool from registry
      const tool = this.registry.getTool(toolName);
      if (!tool) {
        const error = `Tool '${toolName}' not found`;
        this.log('error', error, { requestId });
        
        const result: ToolExecutionResult = {
          success: false,
          error,
          executionTime: Date.now() - startTime
        };
        
        this.recordExecution(toolName, result);
        return result;
      }

      // Validate parameters
      if (!ToolValidator.validateParameters(tool, parameters)) {
        const error = `Invalid parameters for tool '${toolName}'`;
        this.log('error', error, { requestId, parameters: this.sanitizeParameters(parameters) });
        
        const result: ToolExecutionResult = {
          success: false,
          error,
          executionTime: Date.now() - startTime
        };
        
        this.recordExecution(toolName, result);
        return result;
      }

      // Execute the tool
      this.log('info', `Executing tool '${toolName}'`, { requestId });
      const toolResult = await this.executeWithTimeout(tool, parameters, 30000); // 30 second timeout
      
      const executionTime = Date.now() - startTime;
      this.log('info', `Tool '${toolName}' executed successfully`, { 
        requestId, 
        executionTime,
        resultLength: typeof toolResult === 'string' ? toolResult.length : 0
      });

      const result: ToolExecutionResult = {
        success: true,
        result: toolResult,
        executionTime
      };

      this.recordExecution(toolName, result);
      return result;

    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error.message || 'Unknown error occurred';
      
      this.log('error', `Tool '${toolName}' execution failed`, { 
        requestId, 
        error: errorMessage, 
        executionTime 
      });

      const result: ToolExecutionResult = {
        success: false,
        error: errorMessage,
        executionTime
      };

      this.recordExecution(toolName, result);
      return result;
    }
  }

  /**
   * Execute tool with timeout protection
   */
  private async executeWithTimeout(
    tool: FunctionTool, 
    parameters: any, 
    timeoutMs: number
  ): Promise<string> {
    return new Promise(async (resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Tool execution timeout after ${timeoutMs}ms`));
      }, timeoutMs);

      try {
        const result = await tool.handler(parameters);
        clearTimeout(timeout);
        resolve(result);
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Batch execute multiple tools
   */
  async executeTools(
    executions: Array<{
      toolName: string;
      parameters: any;
      context?: ToolExecutionContext;
    }>
  ): Promise<ToolExecutionResult[]> {
    this.log('info', `Starting batch execution of ${executions.length} tools`);

    const results = await Promise.allSettled(
      executions.map(({ toolName, parameters, context }) =>
        this.executeTool(toolName, parameters, context)
      )
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        const execution = executions[index];
        return {
          success: false,
          error: `Batch execution failed for '${execution.toolName}': ${result.reason}`,
          executionTime: 0
        };
      }
    });
  }

  /**
   * Execute tools in sequence (one after another)
   */
  async executeToolsSequentially(
    executions: Array<{
      toolName: string;
      parameters: any;
      context?: ToolExecutionContext;
    }>
  ): Promise<ToolExecutionResult[]> {
    this.log('info', `Starting sequential execution of ${executions.length} tools`);

    const results: ToolExecutionResult[] = [];

    for (const { toolName, parameters, context } of executions) {
      try {
        const result = await this.executeTool(toolName, parameters, context);
        results.push(result);

        // Stop on first failure if configured
        if (!result.success) {
          this.log('warn', `Sequential execution stopped at '${toolName}' due to failure`);
          break;
        }
      } catch (error: any) {
        const failureResult: ToolExecutionResult = {
          success: false,
          error: `Sequential execution failed at '${toolName}': ${error.message}`,
          executionTime: 0
        };
        results.push(failureResult);
        break;
      }
    }

    return results;
  }

  /**
   * Get execution history for a tool
   */
  getExecutionHistory(toolName: string): ToolExecutionResult[] {
    return this.executionHistory.get(toolName) || [];
  }

  /**
   * Get execution statistics for a tool
   */
  getExecutionStats(toolName: string): {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    successRate: number;
  } {
    const history = this.getExecutionHistory(toolName);
    
    if (history.length === 0) {
      return {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageExecutionTime: 0,
        successRate: 0
      };
    }

    const successful = history.filter(r => r.success);
    const failed = history.filter(r => !r.success);
    const totalTime = history.reduce((sum, r) => sum + (r.executionTime || 0), 0);

    return {
      totalExecutions: history.length,
      successfulExecutions: successful.length,
      failedExecutions: failed.length,
      averageExecutionTime: totalTime / history.length,
      successRate: successful.length / history.length
    };
  }

  /**
   * Get overall execution statistics
   */
  getOverallStats(): {
    totalToolsExecuted: number;
    totalExecutions: number;
    overallSuccessRate: number;
    averageExecutionTime: number;
    toolStats: Record<string, {
      totalExecutions: number;
      successfulExecutions: number;
      failedExecutions: number;
      averageExecutionTime: number;
      successRate: number;
    }>;
  } {
    const toolNames = Array.from(this.executionHistory.keys());
    const toolStats: Record<string, ReturnType<typeof this.getExecutionStats>> = {};
    
    let totalExecutions = 0;
    let totalSuccessful = 0;
    let totalTime = 0;

    for (const toolName of toolNames) {
      const stats = this.getExecutionStats(toolName);
      toolStats[toolName] = stats;
      
      totalExecutions += stats.totalExecutions;
      totalSuccessful += stats.successfulExecutions;
      totalTime += stats.averageExecutionTime * stats.totalExecutions;
    }

    return {
      totalToolsExecuted: toolNames.length,
      totalExecutions,
      overallSuccessRate: totalExecutions > 0 ? totalSuccessful / totalExecutions : 0,
      averageExecutionTime: totalExecutions > 0 ? totalTime / totalExecutions : 0,
      toolStats
    };
  }

  /**
   * Clear execution history
   */
  clearHistory(toolName?: string): void {
    if (toolName) {
      this.executionHistory.delete(toolName);
      this.log('info', `Cleared execution history for tool '${toolName}'`);
    } else {
      this.executionHistory.clear();
      this.log('info', 'Cleared all execution history');
    }
  }

  /**
   * Record tool execution result
   */
  private recordExecution(toolName: string, result: ToolExecutionResult): void {
    if (!this.executionHistory.has(toolName)) {
      this.executionHistory.set(toolName, []);
    }

    const history = this.executionHistory.get(toolName)!;
    history.push(result);

    // Keep only last 100 executions per tool to prevent memory issues
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
  }

  /**
   * Sanitize parameters for logging (remove sensitive data)
   */
  private sanitizeParameters(parameters: any): any {
    if (!parameters || typeof parameters !== 'object') {
      return parameters;
    }

    const sanitized = { ...parameters };
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'credentials'];

    for (const key of Object.keys(sanitized)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * Helper method for logging with consistent format
   */
  private log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const prefix = '[ToolExecutor]';
    
    switch (level) {
      case 'info':
        console.log(`${prefix} ${message}`, data || '');
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, data || '');
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data || '');
        break;
    }
  }

  /**
   * Validate tool execution environment
   */
  validateExecutionEnvironment(): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check if registry is initialized
    const registryStats = this.registry.getStats();
    if (registryStats.totalTools === 0) {
      issues.push('No tools registered in the registry');
    }

    // Check for tool name conflicts
    const toolNames = this.registry.getToolNames();
    const uniqueNames = new Set(toolNames);
    if (toolNames.length !== uniqueNames.size) {
      issues.push('Duplicate tool names detected');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
} 