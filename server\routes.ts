import type { Express } from "express";
import { createServer, type Server } from "http";
import { schedulerService } from "./services/core/scheduler-service";
import { fileUploadService } from "./services/file-upload";
import { logger } from "./core";

// Import modular route handlers
import { registerSystemRoutes } from "./routes/system";
import { registerIntegrationRoutes } from "./routes/integrations";
import { registerSyncRoutes } from "./routes/sync";
import { registerChatRoutes } from "./routes/chat";
import { registerFileRoutes } from "./routes/files";
import { registerEmailRoutes } from "./routes/emails";
import { registerRagRoutes } from "./routes/rag";
import { registerUploadRoutes } from "./routes/upload";
import { registerMCPRoutes } from "./routes/mcp";
import { registerLegacyRoutes } from "./routes/legacy";
import { registerTestRoutes } from "./routes/test";
import { registerDebugRoutes } from "./routes/diagnostic";
import { registerMonitoringRoutes } from "./routes/monitoring";
import { registerDebugRoutes as registerNewDebugRoutes } from "./routes/debug";
import { MCPManager } from "./services/mcp/mcp-manager.service";

/**
 * Register all application routes
 * 
 * This function organizes routes into modular components that match
 * the OpenAPI specification structure:
 * 
 * - System routes: Health checks, info, diagnostics
 * - Integration routes: OAuth, CRUD, Google Drive, Microsoft Teams
 * - Sync routes: Schedules, sync logs, manual sync, re-vectorization
 * - Chat routes: Sessions, messages, search, sources
 * - File routes: CRUD, upload, search, processing
 * - Email routes: Email CRUD, search, filtering
 * - RAG routes: Search and status endpoints
 * - Upload routes: Upload service status
 * - Legacy routes: Compatibility endpoints
 * - Test routes: Development testing endpoints
 * - Debug routes: Diagnostic and debugging endpoints
 */
export async function registerRoutes(app: Express, mcpManager: MCPManager): Promise<Server> {
  // Initialize services
  await initializeServices();

  // Register all route modules
  registerSystemRoutes(app);
  registerIntegrationRoutes(app);
  registerSyncRoutes(app);
  registerChatRoutes(app);
  registerFileRoutes(app);
  registerEmailRoutes(app);
  registerRagRoutes(app);
  registerUploadRoutes(app);
  registerMCPRoutes(app, mcpManager);
  registerLegacyRoutes(app);

  // Register monitoring and observability routes
  registerMonitoringRoutes(app);

  // Register diagnostic and test routes
  registerTestRoutes(app);
  registerDebugRoutes(app);
  registerNewDebugRoutes(app);

  const httpServer = createServer(app);
  return httpServer;
}

/**
 * Initialize required services
 */
async function initializeServices() {
  // Initialize the scheduler - Temporarily disabled to fix database connection issue
  // try {
  //   await schedulerService.initialize();
  // } catch (error) {
  //   console.error('Failed to initialize scheduler:', error);
  //   console.log('Continuing without scheduler - manual sync will still work');
  // }

  // Initialize the file upload service
  try {
    await fileUploadService.initialize();
    console.log('✅ File upload service initialized');
  } catch (error) {
    console.error('Failed to initialize file upload service:', error);
    console.log('Continuing without file upload service');
  }
}
