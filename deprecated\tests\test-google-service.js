/**
 * Test the Google service to see what's causing the OAuth issue
 */

import { googleService } from './server/services/google/index.js';

async function testGoogleService() {
  console.log('🔍 TESTING GOOGLE SERVICE');
  console.log('========================\n');

  try {
    console.log('1. Checking Google service info...');
    const info = googleService.getServiceInfo();
    console.log('   Service info:', info);

    console.log('2. Checking health status...');
    const health = await googleService.getHealthStatus();
    console.log('   Health status:', health);

    console.log('3. Attempting to initialize...');
    await googleService.initialize();
    console.log('   ✅ Service initialized successfully');

    console.log('4. Testing getAuthUrl...');
    const result = googleService.getAuthUrl('http://localhost:5000/callback', 1);
    console.log('   ✅ Auth URL generated:', result.url.substring(0, 100) + '...');

  } catch (error) {
    console.error('❌ Error testing Google service:', error.message);
    console.error('   Stack:', error.stack);
  }
}

testGoogleService(); 