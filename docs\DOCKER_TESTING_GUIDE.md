# 🧪 Complete Docker Testing Guide for MeetSync

This guide will help you thoroughly test your Docker implementation to ensure it's 100% production-ready.

## 🚀 Quick Start Testing

### Step 1: Environment Setup

```bash
# 1. Ensure you have a .env file
cp docker/env.template .env

# 2. Add these essential variables to .env:
echo "SESSION_SECRET=$(openssl rand -base64 32)" >> .env
echo "ENCRYPTION_KEY=$(openssl rand -base64 32)" >> .env
echo "REDIS_PASSWORD=$(openssl rand -base64 16)" >> .env
echo "POSTGRES_PASSWORD=secure_test_password" >> .env
echo "OPENAI_API_KEY=sk-test_key" >> .env
echo "NODE_ENV=development" >> .env
echo "METRICS_ENABLED=true" >> .env
echo "APM_ENABLED=true" >> .env
```

### Step 2: Start the Development Environment

```bash
# Start all services
npm run docker:dev

# Wait for startup (usually 30-60 seconds)
# Watch logs to see when ready:
docker-compose -f docker-compose.dev.yml logs -f
```

### Step 3: Quick Validation Tests

**For Windows (PowerShell):**
```powershell
# Run quick tests
.\docker\testing\quick-test.ps1
```

**For Linux/Mac:**
```bash
# Run quick tests
./docker/testing/quick-test.sh
```

### Step 4: Manual Quick Checks

```bash
# 1. Check all containers are healthy
docker ps

# Expected output should show:
# - meetsync-app-dev (healthy)
# - meetsync-postgres-dev (healthy) 
# - meetsync-redis-dev (healthy)

# 2. Test health endpoint
curl http://localhost:8080/health

# Expected: {"status":"healthy","timestamp":"...","checks":{"database":{"status":"healthy"},...}}

# 3. Test session endpoint
curl http://localhost:8080/api/system/info

# Expected: JSON with session.type: "redis"

# 4. Test metrics endpoint
curl http://localhost:8080/metrics

# Expected: Prometheus metrics format with http_requests_total, etc.
```

## 🔧 Comprehensive Testing

### Run Full Test Suite

**For Windows:**
```powershell
# Set execution policy if needed
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Run comprehensive tests (coming soon - use manual tests below for now)
```

**For Linux/Mac:**
```bash
# Run comprehensive test suite
./docker/testing/run-all-tests.sh
```

## 📋 Manual Test Checklist

### ✅ Phase 1: Critical Features

#### Redis Session Integration
```bash
# Test 1: Redis connectivity
docker exec meetsync-redis-dev redis-cli ping
# Expected: PONG

# Test 2: Session persistence
curl -c cookies.txt http://localhost:8080/api/health
docker-compose -f docker-compose.dev.yml restart meetsync-app
sleep 15
curl -b cookies.txt http://localhost:8080/api/system/info
# Expected: Session should persist across restart

# Cleanup
rm cookies.txt
```

#### Health Checks
```bash
# Test 1: Application health
curl http://localhost:8080/health | jq '.status'
# Expected: "healthy"

# Test 2: Database health
curl http://localhost:8080/api/health | jq '.checks.database.status'
# Expected: "healthy"

# Test 3: Container health
docker ps --format "table {{.Names}}\t{{.Status}}" | grep healthy
# Expected: All containers show "healthy"
```

### ✅ Phase 2: Monitoring & Observability

#### Metrics Collection
```bash
# Test 1: Prometheus metrics
curl http://localhost:8080/metrics | head -20
# Expected: Prometheus format metrics

# Test 2: HTTP request metrics
for i in {1..10}; do curl -s http://localhost:8080/api/health > /dev/null; done
curl http://localhost:8080/metrics | grep "http_requests_total"
# Expected: Should show request counts

# Test 3: Response time metrics
curl http://localhost:8080/metrics | grep "http_request_duration_seconds"
# Expected: Should show timing histograms
```

#### Structured Logging
```bash
# Test JSON logging format
docker logs meetsync-app-dev --tail 20 | grep '"level"'
# Expected: JSON formatted logs with level, timestamp, service fields
```

#### System Information
```bash
# Test system info endpoint
curl http://localhost:8080/api/system/info | jq '.'
# Expected: Complete system information including:
# - service: "meetsync"
# - environment: "development"
# - session.type: "redis"
# - memory, uptime, version info
```

### ✅ Phase 3: Security & Performance

#### Security Validation
```bash
# Test 1: Non-root execution
docker exec meetsync-app-dev whoami
# Expected: "meetsync"

docker exec meetsync-app-dev id
# Expected: uid=1001(meetsync) gid=1001(nodejs)

# Test 2: File permissions
docker exec meetsync-app-dev ls -la /app/ | head -5
# Expected: Files owned by meetsync:nodejs

# Test 3: Network isolation
docker exec meetsync-app-dev nslookup postgres
docker exec meetsync-app-dev nslookup redis
# Expected: Both should resolve successfully
```

#### Performance Testing
```bash
# Test 1: Basic load test
for i in {1..50}; do
  curl -s http://localhost:8080/api/health > /dev/null &
  if (( i % 10 == 0 )); then
    wait
    echo "Completed $i requests"
  fi
done
wait

# Test 2: Check resource usage
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Test 3: Response time
time curl -s http://localhost:8080/api/health > /dev/null
# Expected: Should respond quickly (< 1 second)
```

### ✅ Phase 4: Backup & Recovery

#### Backup System
```bash
# Test 1: Backup script exists and is executable
docker exec meetsync-app-dev test -x /app/docker/scripts/backup.sh
echo "Backup script status: $?"
# Expected: 0 (executable)

# Test 2: Create test backup
docker exec meetsync-app-dev mkdir -p /tmp/test_backups
docker exec meetsync-app-dev /bin/sh /app/docker/scripts/backup.sh --dir /tmp/test_backups --retention 1

# Test 3: Verify backup files
docker exec meetsync-app-dev ls -la /tmp/test_backups/
# Expected: Should show backup files with timestamps

# Test 4: Database connectivity for backups
docker exec meetsync-postgres-dev pg_isready -U meetsync -d meetsync
# Expected: "accepting connections"
```

## 🏗️ Multi-Environment Testing

### Development Environment
```bash
# Currently running - test development features
curl http://localhost:8080/api/system/info | jq '.environment'
# Expected: "development"

# Test hot reload (if applicable)
# Test volume mounts
docker exec meetsync-app-dev ls -la /app/client/
docker exec meetsync-app-dev ls -la /app/server/
# Expected: Source code should be visible
```

### Production Environment Testing
```bash
# Stop development environment
docker-compose -f docker-compose.dev.yml down

# Start production environment
NODE_ENV=production npm run docker:prod

# Wait for startup
sleep 45

# Test production optimizations
curl http://localhost:8080/api/system/info | jq '.environment'
# Expected: "production"

# Test production health
curl http://localhost:8080/health | jq '.status'
# Expected: "healthy"

# Cleanup
docker-compose down
```

### Staging Environment Testing
```bash
# Start staging environment
docker-compose -f docker-compose.staging.yml up -d

# Wait for all services
sleep 60

# Test monitoring stack
curl -s http://localhost:9090/api/v1/label/__name__/values > /dev/null
echo "Prometheus accessible: $?"

curl -s http://localhost:3001/api/health > /dev/null
echo "Grafana accessible: $?"

# Test staging app
docker exec meetsync-app-staging env | grep NODE_ENV
# Expected: NODE_ENV=staging

# Cleanup
docker-compose -f docker-compose.staging.yml down
```

## 🚨 Troubleshooting Common Issues

### Services Won't Start
```bash
# Check logs
docker-compose -f docker-compose.dev.yml logs

# Check specific service
docker logs meetsync-app-dev
docker logs meetsync-postgres-dev
docker logs meetsync-redis-dev

# Check ports
netstat -tulpn | grep -E "(8080|5433|6380)"
```

### Health Checks Failing
```bash
# Check if services are actually running
docker ps

# Test database connection manually
docker exec meetsync-postgres-dev pg_isready -U meetsync

# Test Redis connection manually
docker exec meetsync-redis-dev redis-cli ping

# Check application logs
docker logs meetsync-app-dev --tail 50
```

### Performance Issues
```bash
# Check resource usage
docker stats

# Check for errors in logs
docker logs meetsync-app-dev 2>&1 | grep -i error

# Test individual endpoints
curl -w "Total time: %{time_total}s\n" -s http://localhost:8080/health
```

## ✅ Success Criteria

Your Docker setup is **production-ready** when:

- [ ] All containers start and show "healthy" status
- [ ] Health endpoint returns `{"status": "healthy"}`
- [ ] Metrics endpoint provides Prometheus data
- [ ] Session endpoint shows `"type": "redis"`
- [ ] Database health check passes
- [ ] Application runs as non-root user (`meetsync`)
- [ ] Load tests complete without errors
- [ ] Backup system is functional
- [ ] All environments (dev/staging/prod) work
- [ ] Resource usage is reasonable
- [ ] Logs are structured and accessible

## 🎯 Quick Commands Reference

```bash
# Quick health check
curl http://localhost:8080/health | jq '.status'

# Quick session check  
curl http://localhost:8080/api/system/info | jq '.session.type'

# Quick metrics check
curl http://localhost:8080/metrics | grep -c "http_requests_total"

# Quick container status
docker ps --format "table {{.Names}}\t{{.Status}}"

# Quick resource usage
docker stats --no-stream

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Restart services
docker-compose -f docker-compose.dev.yml restart

# Clean restart
docker-compose -f docker-compose.dev.yml down
npm run docker:dev
```

## 📞 Getting Help

If tests fail:

1. **Check the logs first**: `docker logs meetsync-app-dev`
2. **Verify environment**: Ensure `.env` has all required variables
3. **Check ports**: Make sure 8080, 5433, 6380 are available
4. **Resource check**: Ensure Docker has enough memory/CPU
5. **Clean start**: Try `docker-compose down -v` then restart

Your Docker implementation includes enterprise-grade features and should pass all tests for production readiness! 🚀 