import { Router } from 'express';
import { ragService } from '../services/rag';
import { storage } from '../storage/index.js';

const router = Router();

/**
 * Smart file processing endpoint
 * Automatically chooses between real-time and batch processing
 */
router.post('/process-files', async (req, res) => {
  try {
    const { fileIds, urgent = false, userWaiting = false } = req.body;
    
    if (!fileIds || !Array.isArray(fileIds)) {
      return res.status(400).json({ error: 'fileIds array is required' });
    }
    
    console.log(`Processing ${fileIds.length} files with smart logic`);
    
    const result = await ragService.processFilesSmart(fileIds, {
      urgent,
      userWaiting
    });
    
    res.json({
      success: true,
      processing: result
    });
    
  } catch (error: any) {
    console.error('Error in smart processing:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Process pending files (for initial sync)
 */
router.post('/process-pending', async (req, res) => {
  try {
    const { urgent = false } = req.body;
    
    const result = await ragService.processPendingFilesSmart({ 
      userWaiting: false,
      urgent 
    });
    
    if (!result) {
      return res.json({
        success: true,
        message: 'No files need processing'
      });
    }
    
    res.json({
      success: true,
      processing: result
    });
    
  } catch (error: any) {
    console.error('Error processing pending files:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Check batch status
 */
router.get('/batch-status/:batchId', async (req, res) => {
  try {
    const { batchId } = req.params;
    
    const status = await ragService.checkBatchStatus(batchId);
    
    res.json({
      success: true,
      batch: status
    });
    
  } catch (error: any) {
    console.error('Error checking batch status:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Upload and process file (real-time for user uploads)
 */
router.post('/upload-and-process', async (req, res) => {
  try {
    // This would be your file upload logic
    const { fileName, content, urgent = true } = req.body;
    
    // Create file in database
    const file = await storage.createFile({
      externalId: `uploaded_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fileName,
      fileContent: content,
      platform: 'uploaded_files',
      fileType: fileName.split('.').pop() || 'txt',
      fileSize: content.length,
      mimeType: 'text/plain',
      userId: 'user123' // In real app, get from auth
    });
    
    // Process immediately since user is waiting
    const result = await ragService.processFilesSmart([file.id], {
      userWaiting: true, // User uploaded and is waiting
      urgent: true
    });
    
    res.json({
      success: true,
      file: file,
      processing: result
    });
    
  } catch (error: any) {
    console.error('Error in upload and process:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get processing recommendations
 */
router.get('/processing-info', async (req, res) => {
  try {
    const allFiles = await storage.getFiles();
    const pendingFiles = [];
    
    for (const file of allFiles) {
      const hasEmbeddings = await ragService.hasEmbeddings?.(file.id);
      if (!hasEmbeddings && file.fileContent) {
        pendingFiles.push({
          id: file.id,
          name: file.fileName,
          size: file.fileContent.length
        });
      }
    }
    
    const recommendation = pendingFiles.length >= 5 ? 'batch' : 'real-time';
    
    res.json({
      success: true,
      pendingFiles: pendingFiles.length,
      files: pendingFiles,
      recommendation,
      reasons: {
        batch: 'More than 5 files - use background processing (50% cheaper, no rate limits)',
        'real-time': 'Few files - process immediately for instant results'
      }
    });
    
  } catch (error: any) {
    console.error('Error getting processing info:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
