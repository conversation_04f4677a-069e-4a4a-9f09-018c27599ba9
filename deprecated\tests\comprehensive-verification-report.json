{"methodTransfer": {}, "dependencyCheck": {"server/routes.ts": {"success": true, "description": "Routes import modular facade", "issue": null}, "server/controllers/integration/index.ts": {"success": true, "description": "Facade exports controller instance", "issue": null}, "server/controllers/integration/shared/integration-crud.controller.ts": {"success": true, "description": "CRUD controller extends base class", "issue": null}, "server/controllers/integration/google/google-oauth.controller.ts": {"success": true, "description": "Google OAuth controller extends base class", "issue": null}, "server/controllers/integration/microsoft/teams-oauth.controller.ts": {"success": true, "description": "Teams OAuth controller extends base class", "issue": null}}, "apiEndpoints": {"GET /api/integrations": {"expected": 200, "actual": 200, "success": true}, "GET /api/integrations/999": {"expected": 404, "actual": 404, "success": true}, "POST /api/integrations": {"expected": 400, "actual": 400, "success": true}, "GET /api/schedules": {"expected": 200, "actual": 200, "success": true}, "POST /api/schedules": {"expected": 400, "actual": 400, "success": true}}, "codebaseReferences": {"server/routes.ts": {"clean": true, "staleReferences": []}, "tests/validate-modularization.js": {"clean": true, "staleReferences": []}}, "summary": {"totalMethods": 19, "transferredMethods": 0, "missingMethods": [], "extraMethods": [], "allDependenciesFixed": false, "allEndpointsWork": true, "noStaleReferences": true}, "integrationTest": {"success": true}}