#!/bin/bash

echo "🚀 Starting MeetSync Application..."

# Check if PostgreSQL is running
echo "📊 Checking PostgreSQL status..."
if ! brew services list | grep postgresql | grep started > /dev/null; then
    echo "🔄 Starting PostgreSQL..."
    brew services start postgresql
    sleep 2
else
    echo "✅ PostgreSQL is already running"
fi

# Test database connection
echo "🔗 Testing database connection..."
if psql -h localhost -U azha -d meetsync -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    echo "💡 Try running: createdb meetsync"
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js first."
    exit 1
fi

# Check if npm dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing npm dependencies..."
    npm install
fi

# Start the application
echo "🌟 Starting MeetSync on http://localhost:3000..."
npm run dev 