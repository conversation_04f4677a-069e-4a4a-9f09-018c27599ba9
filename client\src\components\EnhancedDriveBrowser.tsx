import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  FolderIcon, 
  HardDriveIcon, 
  ShareIcon, 
  ComputerIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  Loader2Icon,
  TrashIcon,
  RefreshCwIcon
} from 'lucide-react';

interface DriveItem {
  id: string;
  name: string;
  type: 'root' | 'folder' | 'shared_drive' | 'computer' | 'shared_folder';
  parent?: string;
  shared?: boolean;
  owners?: any[];
  capabilities?: any;
  folders?: DriveItem[];
}

interface DriveStructure {
  myDrive: DriveItem;
  sharedDrives: DriveItem[];
  computers: DriveItem[];
  sharedWithMe: DriveItem[];
}

interface EnhancedDriveBrowserProps {
  integrationId: number;
  selectedFolders: { id: string; name: string }[];
  onFolderSelect: (folderId: string, folderName: string) => void;
  onRemoveFolder: (folderId: string) => void;
  onClearAll: () => void;
}

export function EnhancedDriveBrowser({
  integrationId,
  selectedFolders,
  onFolderSelect,
  onRemoveFolder,
  onClearAll
}: EnhancedDriveBrowserProps) {
  const [driveStructure, setDriveStructure] = useState<DriveStructure | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set(['root']));
  const [folderSearch, setFolderSearch] = useState('');

  const fetchDriveStructure = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/integrations/${integrationId}/drive-structure`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to load drive structure');
      }
      
      const data = await response.json();
      setDriveStructure(data.structure);
    } catch (err: any) {
      console.error('Error fetching drive structure:', err);
      setError(err.message || 'Failed to load Google Drive structure');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDriveStructure();
  }, [integrationId]);

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const getItemIcon = (item: DriveItem) => {
    switch (item.type) {
      case 'root':
        return <HardDriveIcon className="h-4 w-4 text-blue-500" />;
      case 'shared_drive':
        return <ShareIcon className="h-4 w-4 text-green-500" />;
      case 'computer':
        return <ComputerIcon className="h-4 w-4 text-purple-500" />;
      case 'shared_folder':
        return <ShareIcon className="h-4 w-4 text-orange-500" />;
      default:
        return <FolderIcon className="h-4 w-4 text-blue-500" />;
    }
  };

  const isItemSelected = (itemId: string) => {
    return selectedFolders.some(f => f.id === itemId);
  };

  const renderDriveItem = (item: DriveItem, level: number = 0) => {
    const isExpanded = expandedItems.has(item.id);
    const isSelected = isItemSelected(item.id);
    const hasChildren = item.folders && item.folders.length > 0;
    
    // Filter children based on search
    const filteredChildren = item.folders?.filter(child =>
      folderSearch === '' || 
      child.name.toLowerCase().includes(folderSearch.toLowerCase())
    ) || [];

    const shouldShow = folderSearch === '' || 
      item.name.toLowerCase().includes(folderSearch.toLowerCase()) ||
      filteredChildren.length > 0;

    if (!shouldShow) return null;

    return (
      <div key={item.id}>
        <div 
          className={`flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md cursor-pointer ${
            isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''
          }`}
          style={{ paddingLeft: `${level * 20 + 8}px` }}
          onClick={() => {
            if (item.type !== 'root') {
              onFolderSelect(item.id, item.name);
            }
            if (hasChildren) {
              toggleExpanded(item.id);
            }
          }}
        >
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 mr-1"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(item.id);
              }}
            >
              {isExpanded ? (
                <ChevronDownIcon className="h-3 w-3" />
              ) : (
                <ChevronRightIcon className="h-3 w-3" />
              )}
            </Button>
          )}
          
          {item.type !== 'root' && (
            <Checkbox
              checked={isSelected}
              onChange={() => {}} // Handled by parent onClick
              className="mr-2"
              onClick={(e) => e.stopPropagation()}
            />
          )}
          
          {getItemIcon(item)}
          
          <span className="flex-1 ml-2 text-sm">
            {item.name}
            {item.shared && <ShareIcon className="inline h-3 w-3 ml-1 text-gray-400" />}
          </span>
          
          {item.type === 'shared_folder' && item.owners && item.owners.length > 0 && (
            <span className="text-xs text-gray-500 ml-2">
              by {item.owners[0].displayName || item.owners[0].emailAddress}
            </span>
          )}
        </div>
        
        {isExpanded && hasChildren && (
          <div>
            {filteredChildren.map(child => renderDriveItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-12 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {error}
          <div className="mt-2">
            <Button variant="outline" size="sm" onClick={fetchDriveStructure}>
              <RefreshCwIcon className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (!driveStructure) {
    return (
      <Alert>
        <AlertTitle>No Drive Structure</AlertTitle>
        <AlertDescription>
          Could not load your Google Drive structure.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div>
        <Label htmlFor="search-folders">Search Folders</Label>
        <Input 
          id="search-folders"
          placeholder="Search folders by name..." 
          className="mt-1"
          value={folderSearch}
          onChange={(e) => setFolderSearch(e.target.value)}
        />
      </div>

      {/* Selected Folders Display */}
      {selectedFolders.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label className="text-sm font-semibold">
              Selected Folders ({selectedFolders.length})
            </Label>
            <Button
              variant="outline"
              size="sm"
              onClick={onClearAll}
              className="text-xs"
            >
              Clear All
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedFolders.map((folder) => (
              <Badge
                key={folder.id}
                variant="secondary"
                className="pr-1 max-w-[200px] truncate"
              >
                <FolderIcon className="mr-1 h-3 w-3" />
                <span className="truncate">{folder.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemoveFolder(folder.id);
                  }}
                >
                  <TrashIcon className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Drive Structure Browser */}
      <div className="border rounded-md h-80 overflow-y-auto">
        <div className="p-2">
          {/* My Drive */}
          {renderDriveItem(driveStructure.myDrive)}
          
          {/* Shared Drives */}
          {driveStructure.sharedDrives.length > 0 && (
            <div className="mt-4">
              <div className="text-sm font-medium text-gray-600 mb-2 px-2">Shared Drives</div>
              {driveStructure.sharedDrives.map(drive => renderDriveItem(drive))}
            </div>
          )}
          
          {/* Computers */}
          {driveStructure.computers.length > 0 && (
            <div className="mt-4">
              <div className="text-sm font-medium text-gray-600 mb-2 px-2">Computers</div>
              {driveStructure.computers.map(computer => renderDriveItem(computer))}
            </div>
          )}
          
          {/* Shared with me */}
          {driveStructure.sharedWithMe.length > 0 && (
            <div className="mt-4">
              <div className="text-sm font-medium text-gray-600 mb-2 px-2">Shared with me</div>
              {driveStructure.sharedWithMe.map(folder => renderDriveItem(folder))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
