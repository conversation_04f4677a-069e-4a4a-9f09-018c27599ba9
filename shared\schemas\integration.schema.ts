import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";

// Integrations table schema
export const integrations = pgTable("integrations", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // "google-drive", "microsoft_teams", "slack"
  status: text("status").notNull().default("disconnected"), // "connected", "disconnected", "error"
  credentials: text("credentials"), // encrypted OAuth tokens or API keys
  config: jsonb("config"), // platform-specific configuration
  lastSyncAt: timestamp("last_sync_at"),
  nextSyncAt: timestamp("next_sync_at"),
  syncSchedule: text("sync_schedule"), // cron expression
  syncStatus: text("sync_status").default("idle"), // "idle", "running", "success", "failed"
  sourceConfig: jsonb("source_config"), // configuration for source (e.g., Google Drive folder ID)
  destinationConfig: jsonb("destination_config"), // configuration for destination (e.g., Notion database ID)
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  isLlmEnabled: boolean("is_llm_enabled").default(true),
  syncFilters: jsonb("sync_filters"), // Any filters to apply to sync process
});

// Sync logs table schema
export const syncLogs = pgTable("sync_logs", {
  id: serial("id").primaryKey(),
  integrationId: integer("integration_id").notNull(),
  startTime: timestamp("start_time").notNull().defaultNow(),
  endTime: timestamp("end_time"),
  status: text("status").notNull(), // "success", "partial", "failed"
  itemsProcessed: integer("items_processed").default(0),
  itemsSuccess: integer("items_success").default(0),
  itemsFailed: integer("items_failed").default(0),
  details: jsonb("details"),
  error: text("error"),
});

// Sync items table schema
export const syncItems = pgTable("sync_items", {
  id: serial("id").primaryKey(),
  integrationId: integer("integration_id").notNull(),
  syncLogId: integer("sync_log_id"),
  externalId: text("external_id").notNull(), // ID in the source system (e.g., Google Doc ID)
  title: text("title").notNull(),
  type: text("type").notNull(), // "transcript", "chat_log"
  sourceUrl: text("source_url"),
  destinationUrl: text("destination_url"),
  metadata: jsonb("metadata"), // extracted metadata: meeting name, date, time, attendees
  status: text("status").notNull(), // "pending", "processing", "success", "failed", "skipped"
  processedAt: timestamp("processed_at"),
  error: text("error"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Insert schemas
export const insertIntegrationSchema = createInsertSchema(integrations).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  lastSyncAt: true,
  nextSyncAt: true,
});

export const insertSyncLogSchema = createInsertSchema(syncLogs).omit({
  id: true,
  endTime: true,
});

export const insertSyncItemSchema = createInsertSchema(syncItems).omit({
  id: true,
  processedAt: true,
});

// Export types
export type Integration = typeof integrations.$inferSelect;
export type InsertIntegration = typeof insertIntegrationSchema._type;

export type SyncLog = typeof syncLogs.$inferSelect;
export type InsertSyncLog = typeof insertSyncLogSchema._type;

export type SyncItem = typeof syncItems.$inferSelect;
export type InsertSyncItem = typeof insertSyncItemSchema._type;
