/**
 * Test Microsoft environment variables
 */

console.log('🔍 CHECKING MICROSOFT ENVIRONMENT VARIABLES');
console.log('==========================================\n');

const requiredVars = [
  'MICROSOFT_CLIENT_ID',
  'MICROSOFT_CLIENT_SECRET', 
  'MICROSOFT_TENANT_ID',
  'MICROSOFT_REDIRECT_URI'
];

let allPresent = true;

requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
    allPresent = false;
  }
});

console.log(`\n${allPresent ? '✅ All Microsoft environment variables are set' : '❌ Some Microsoft environment variables are missing'}`);

// Test Microsoft service initialization
console.log('\n🔧 TESTING MICROSOFT SERVICE INITIALIZATION');
console.log('==========================================\n');

async function testMicrosoftService() {
  try {
    const { microsoftService } = await import('./server/services/microsoft/index.js');
    
    console.log('1. Microsoft service imported successfully');
    
    console.log('2. Checking if service is initialized...');
    console.log('   Is initialized:', microsoftService.isInitialized());
    
    if (!microsoftService.isInitialized()) {
      console.log('3. Attempting to initialize Microsoft service...');
      await microsoftService.initialize();
      console.log('   ✅ Microsoft service initialized successfully');
    }
    
    console.log('4. Getting service health status...');
    const health = await microsoftService.getHealthStatus();
    console.log('   Health status:', health);
    
    console.log('5. Testing getAuthUrl method...');
    try {
      const result = microsoftService.getAuthUrl('http://localhost:5000/callback', 21);
      console.log('   ✅ getAuthUrl result:', result);
    } catch (authError) {
      console.log('   ❌ getAuthUrl error:', authError.message);
    }
    
  } catch (error) {
    console.error('❌ Error testing Microsoft service:', error);
  }
}

testMicrosoftService().catch(console.error); 