

Scheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
11:54:24 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
11:54:27 PM [express] GET /api/sync-logs 200 in 81ms :: {"logs":[{"id":2,"integrationId":1,"startTime…
11:54:28 PM [express] GET /api/integrations 304 in 77ms :: {"integrations":[{"id":1,"name":"Google Me…
11:54:28 PM [express] GET /api/integrations 304 in 304ms :: {"integrations":[{"id":1,"name":"Google M…
11:54:40 PM [express] GET /api/integrations 304 in 305ms :: {"integrations":[{"id":1,"name":"Google M…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_7nv6tvaao2f
11:54:42 PM [express] GET /api/integrations/1/auth-url 200 in 150ms :: {"authUrl":"https://accounts.g…
11:54:51 PM [express] GET /api/integrations/oauth/callback 302 in 3ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
11:54:51 PM [express] GET /api/integrations/1/oauth/callback 302 in 258ms
11:54:52 PM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 9 folders
11:54:54 PM [express] GET /api/integrations/1/folders 304 in 607ms :: {"folders":[{"parents":["1U_QJy…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2',
    driveName: 'MeetSync'
  }
}
Testing connection to Google Drive folder: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2
Updating integration with verified folder config: { driveId: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2', driveName: 'MeetSync' }
11:54:58 PM [express] POST /api/integrations/1/test-connection 200 in 565ms :: {"success":true,"messa…
Cancelled schedule for integration 1
11:54:59 PM [express] PUT /api/integrations/1 200 in 225ms :: {"message":"Integration updated success…
11:54:59 PM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
11:55:00 PM [express] GET /api/integrations 304 in 76ms :: {"integrations":[{"id":1,"name":"Google Me…
OpenAI service initialized successfully
Error in sync job 3: Error: Sync not implemented for integration type: google-meet
    at SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:319:15)
    at SyncController.startSync (/home/<USER>/workspace/server/controllers/sync.ts:47:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async SyncController.syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:23)
11:55:03 PM [express] POST /api/sync-now 200 in 235ms :: {"message":"Sync started successfully","sync…
11:55:03 PM [express] GET /api/integrations 200 in 363ms :: {"integrations":[{"id":1,"name":"Google M…
OpenAI service initialized successfully
Error in sync job 4: Error: Sync not implemented for integration type: google-meet
    at SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:319:15)
    at SyncController.startSync (/home/<USER>/workspace/server/controllers/sync.ts:47:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async SyncController.syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:23)
11:55:09 PM [express] POST /api/sync-now 200 in 243ms :: {"message":"Sync started successfully","sync…
11:55:10 PM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
