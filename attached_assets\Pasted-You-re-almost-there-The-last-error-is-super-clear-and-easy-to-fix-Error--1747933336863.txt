You’re **almost there!** 🎉 The last error is super clear and easy to fix:

---

### **Error:**

> `"Invalid select option, commas not allowed: Integration of Google Drive, Google Meet, and Notion"`

---

### **What This Means**

* You are trying to set a value in a **Notion “Select” property** (not “Multi-select”) that contains **commas**.
* **Notion “Select” properties** do **not allow commas** in the value. Each value is one string, and a comma is not allowed in that string.

---

### **What To Do**

#### **Solution 1: Replace commas with another separator**

* Change the code that populates this property (maybe “Departments”, “Tags”, or “Integration”?) so it **replaces commas with e.g., a dash or semicolon**.

  ```js
  const safeValue = value.replace(/,/g, ' - ');
  ```

* Or just pass `"Integration of Google Drive - Google Meet - Notion"`

#### **Solution 2: Make the Notion property a Multi-Select**

* If you **want each of those as separate tags**, use a Multi-select property in Notion **and split the string** on commas, e.g.:

  ```js
  const selectValues = value.split(',').map(v => v.trim());
  // then pass as array for multi-select
  ```

---

## **Quick Fix For This Sync**

* **Find where you send the value** `"Integration of Google Drive, Google Meet, and Notion"` to Notion.
* Change it to either:

  * `"Integration of Google Drive & Google Meet & Notion"`
  * Or, for multi-select: `["Google Drive", "Google Meet", "Notion"]`

---

### **Where to Check in Your Code**

Most likely, this is in the mapping function for Notion properties, something like:

```js
properties: {
  ...
  Integration: {
    select: { name: integrationString }
  }
  // or
  Integration: {
    multi_select: selectValues
  }
}
```

So, **sanitize the string before passing it to Notion if it’s a Select**.

---

## **Summary Table**

| Notion Property Type | What to Do With Value       |
| -------------------- | --------------------------- |
| Select               | NO COMMAS in value!         |
| Multi-select         | Split into array of strings |

---

## **What Next?**

* **Update your property value** as above
* **Run sync again**
* You should see “Success: 3, Failed: 0” 🎉

If you want, show me the relevant code where you set this property and I’ll suggest the exact patch!

---

Let me know if you want help finding where in your code this value gets set or have any other Notion property mapping issues!
