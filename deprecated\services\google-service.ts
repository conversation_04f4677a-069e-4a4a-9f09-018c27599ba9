// DEPRECATED: This file is being modularized for better maintainability
// New imports should use the modular structure from ./google/
// This file is kept for backward compatibility during the migration

import { google, Auth, drive_v3 } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { cryptoService } from "../crypto-service";

// Import the new modular Google service facade
import { GoogleServiceFacade } from "../google/google.facade";

/**
 * Service for interacting with Google APIs
 * @deprecated Use GoogleServiceFacade from ./google/ instead
 */
class GoogleService {
  private oauth2Client: OAuth2Client | null = null;

  /**
   * Create an OAuth2 client for Google APIs
   */
  createOAuth2Client(): OAuth2Client {
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new Error("Google OAuth credentials not configured");
    }

    this.oauth2Client = new OAuth2Client(
      clientId,
      clientSecret,
      `${process.env.SERVER_URL || "http://localhost:8080"}/api/integrations/oauth/callback`,
    );

    return this.oauth2Client;
  }

  /**
   * Generate an authorization URL for OAuth flow
   * @param redirectUri Original redirect URI after OAuth flow (not used - we use global callback)
   * @param integrationId The ID of the integration being connected (extracted from the request URL)
   * @returns Object containing the auth URL and state token
   */
  getAuthUrl(
    redirectUri: string,
    integrationId?: number,
  ): { url: string; state: string } {
    // Get the integration ID from the redirect URI if not provided
    if (!integrationId && redirectUri) {
      const matches = redirectUri.match(/\/api\/integrations\/(\d+)\/oauth/);
      if (matches && matches[1]) {
        integrationId = parseInt(matches[1], 10);
      }
    }

    console.log(`Generating OAuth URL for integration ID: ${integrationId}`);

    // Create a new OAuth client with the global redirect URI
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new Error("Google OAuth credentials not configured");
    }

    // Always use the global callback endpoint as configured in Google Cloud Console
    const globalRedirectUri = `${process.env.SERVER_URL || "http://localhost:8080"}/api/integrations/oauth/callback`;

    console.log(`Using global redirect URI: ${globalRedirectUri}`);

    // Create a client specifically for this auth request with the global redirect URI
    const oauth2Client = new OAuth2Client(
      clientId,
      clientSecret,
      globalRedirectUri,
    );

    const scopes = [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/drive.file",
      "https://www.googleapis.com/auth/drive.readonly",
      "https://www.googleapis.com/auth/drive.metadata.readonly",
      "https://www.googleapis.com/auth/documents.readonly",
      "profile",
      "email",
    ];

    // Generate a state token that includes the integration ID
    const randomPart = Math.random().toString(36).substring(2, 15);
    const state = `${integrationId || 0}_${randomPart}`;

    console.log(`Generated OAuth state token: ${state}`);

    const authUrl = oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      prompt: "consent",
      state,
      // Add these parameters to help with the connection
      include_granted_scopes: true,
    });

    return {
      url: authUrl,
      state,
    };
  }

  /**
   * Exchange authorization code for tokens
   * @param code Authorization code from OAuth flow
   * @param redirectUri The redirect URI used in the auth request (not used)
   */
  async getTokensFromCode(code: string, redirectUri?: string): Promise<any> {
    // Create a new OAuth client with the global redirect URI
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new Error("Google OAuth credentials not configured");
    }

    // Always use the global callback endpoint as configured in Google Cloud Console
    const globalRedirectUri = `${process.env.SERVER_URL || "http://localhost:8080"}/api/integrations/oauth/callback`;

    console.log(
      `Using global redirect URI for token exchange: ${globalRedirectUri}`,
    );

    // Create a client with the global redirect URI
    const oauth2Client = new OAuth2Client(
      clientId,
      clientSecret,
      globalRedirectUri,
    );

    // Exchange the code for tokens
    try {
      console.log("Exchanging authorization code for tokens...");
      const { tokens } = await oauth2Client.getToken(code);
      oauth2Client.setCredentials(tokens);
      console.log("Successfully obtained tokens from Google");
      return tokens;
    } catch (error: any) {
      console.error("Error exchanging code for tokens:", error);
      throw new Error(
        `Failed to exchange authorization code for tokens: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * Get an authorized OAuth2Client using encrypted credentials
   * @param encryptedCredentials Encrypted credentials from storage
   */
  async getAuthorizedClient(
    encryptedCredentials: string,
  ): Promise<OAuth2Client> {
    try {
      console.log('[GOOGLE] Getting authorized client...');
      console.log('[GOOGLE] Encrypted credentials length:', encryptedCredentials.length);
      console.log('[GOOGLE] Encrypted credentials preview:', encryptedCredentials.substring(0, 50) + '...');

      // Validate the encrypted format first
      if (!cryptoService.validateEncryptedFormat(encryptedCredentials)) {
        console.error('[GOOGLE] Invalid encrypted credentials format');
        throw new Error('Invalid encrypted credentials format - expected "iv:encryptedData"');
      }

      // Decrypt the credentials
      console.log('[GOOGLE] Decrypting credentials...');
      const credentialsJson = await cryptoService.decrypt(encryptedCredentials);
      console.log('[GOOGLE] Decryption successful, JSON length:', credentialsJson.length);
      console.log('[GOOGLE] Decrypted JSON preview:', credentialsJson.substring(0, 100) + '...');

      // Parse the JSON
      let credentials: any;
      try {
        credentials = JSON.parse(credentialsJson);
        console.log('[GOOGLE] JSON parsing successful');
        console.log('[GOOGLE] Credentials keys:', Object.keys(credentials));
      } catch (parseError: any) {
        console.error('[GOOGLE] JSON parsing failed:', parseError.message);
        console.error('[GOOGLE] Raw decrypted text (first 200 chars):', credentialsJson.substring(0, 200));
        throw new Error(`Failed to parse credentials JSON: ${parseError.message}`);
      }

      // Validate required fields
      if (!credentials.access_token) {
        console.error('[GOOGLE] Missing access_token in credentials');
        throw new Error('Invalid credentials: missing access_token');
      }

      // Create a new OAuth2 client
      const oauth2Client = this.createOAuth2Client();

      // Set the credentials
      oauth2Client.setCredentials(credentials);
      console.log('[GOOGLE] OAuth2 client configured successfully');

      // Check if access token is expired and refresh if needed
      if (this.isTokenExpired(credentials)) {
        console.log('[GOOGLE] Token is expired, refreshing...');
        await this.refreshAccessToken(oauth2Client);
        console.log('[GOOGLE] Token refreshed successfully');
      } else {
        console.log('[GOOGLE] Token is still valid');
      }

      return oauth2Client;
    } catch (error: any) {
      console.error("[GOOGLE] Error getting authorized client:", error);
      console.error("[GOOGLE] Error stack:", error.stack);
      throw error;
    }
  }

  /**
   * Check if the access token is expired
   * @param credentials OAuth2 credentials
   */
  private isTokenExpired(credentials: any): boolean {
    if (!credentials.expiry_date) {
      return true;
    }

    // Consider token expired 5 minutes before actual expiry
    return credentials.expiry_date <= Date.now() + 5 * 60 * 1000;
  }

  /**
   * Refresh the access token
   * @param oauth2Client OAuth2 client
   */
  private async refreshAccessToken(oauth2Client: OAuth2Client): Promise<void> {
    try {
      const response = await oauth2Client.refreshAccessToken();
      oauth2Client.setCredentials(response.credentials);
    } catch (error) {
      console.error("Error refreshing access token:", error);
      throw error;
    }
  }

  /**
   * List files in a Google Drive folder
   * @param auth Authorized OAuth2 client
   * @param folderId Google Drive folder ID
   * @param processedFolderId Optional processed folder ID to exclude
   */
  async listFiles(
    auth: OAuth2Client,
    folderId: string,
    processedFolderId?: string,
  ): Promise<drive_v3.Schema$File[]> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Build a comprehensive query for all business file types
      const query = `'${folderId}' in parents and trashed = false and (
        mimeType = 'application/vnd.google-apps.document' or
        mimeType = 'application/vnd.google-apps.spreadsheet' or
        mimeType = 'application/vnd.google-apps.presentation' or
        mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' or
        mimeType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation' or
        mimeType = 'application/pdf' or
        mimeType = 'text/plain' or
        mimeType = 'text/csv' or
        mimeType contains 'image/' or
        mimeType contains 'video/' or
        mimeType contains 'audio/'
      )`;

      console.log(`Google Drive query: ${query}`);

      // If we have a processedFolderId, we'll filter those files out in memory rather than in the query
      // This is more reliable than using complex queries that might not be supported

      const response = await drive.files.list({
        q: query,
        fields: "files(id, name, mimeType, modifiedTime, parents)",
        orderBy: "modifiedTime desc",
      });

      let files = response.data.files || [];

      // If we have a processedFolderId, filter out any files that are already in the processed folder
      if (processedFolderId && files.length > 0) {
        console.log(
          `Filtering out files that are in the processed folder: ${processedFolderId}`,
        );

        // Get list of files in the processed folder
        const processedFilesResponse = await drive.files.list({
          q: `'${processedFolderId}' in parents and trashed = false`,
          fields: "files(id)",
        });

        const processedFileIds = new Set(
          (processedFilesResponse.data.files || []).map((file) => file.id),
        );
        console.log(`Found ${processedFileIds.size} files in processed folder`);

        // Filter out files that are in the processed folder
        files = files.filter((file) => !processedFileIds.has(file.id));
        console.log(
          `After filtering: ${files.length} files remaining to process`,
        );
      }

      return files;
    } catch (error) {
      console.error(`Error listing files in folder ${folderId}:`, error);
      throw error;
    }
  }

  /**
   * Get or create a folder for processed documents
   * @param auth Authorized OAuth2 client
   * @param parentFolderId Parent folder ID
   */
  async getOrCreateProcessedFolder(
    auth: OAuth2Client,
    parentFolderId: string,
  ): Promise<string> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Check if "Processed" folder already exists
      const query = `'${parentFolderId}' in parents and name = 'Processed' and mimeType = 'application/vnd.google-apps.folder' and trashed = false`;
      const response = await drive.files.list({
        q: query,
        fields: "files(id, name)",
      });

      if (response.data.files && response.data.files.length > 0) {
        return response.data.files[0].id || "";
      }

      // Create the folder if it doesn't exist
      const fileMetadata = {
        name: "Processed",
        mimeType: "application/vnd.google-apps.folder",
        parents: [parentFolderId],
      };

      const folder = await drive.files.create({
        requestBody: fileMetadata,
        fields: "id",
      });

      if (!folder.data.id) {
        throw new Error("Failed to create processed folder");
      }

      console.log(`Created "Processed" folder: ${folder.data.id}`);

      return folder.data.id;
    } catch (error) {
      console.error(`Error getting or creating processed folder:`, error);
      throw error;
    }
  }

  /**
   * Move a file to the processed folder
   * @param auth Authorized OAuth2 client
   * @param fileId File ID to move
   * @param sourceFolderId Source folder ID
   * @param processedFolderId Processed folder ID
   */
  async moveFileToProcessedFolder(
    auth: OAuth2Client,
    fileId: string,
    sourceFolderId: string,
    processedFolderId: string,
  ): Promise<void> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Remove the file from the source folder and add to processed folder
      await drive.files.update({
        fileId,
        addParents: processedFolderId,
        removeParents: sourceFolderId,
        fields: "id, parents",
      });

      console.log(
        `Moved file ${fileId} to processed folder ${processedFolderId}`,
      );
    } catch (error) {
      console.error(`Error moving file ${fileId} to processed folder:`, error);
      throw error;
    }
  }

  /**
   * Extract metadata from a Google Meet transcript filename
   * @param filename Filename to extract metadata from
   * @returns Extracted metadata
   */
  extractMetadataFromFilename(filename: string): {
    title: string;
    date: string | null;
    time: string | null;
  } {
    try {
      // Extract date/time from filename (e.g., "Meeting started 2025/05/19 12:20 EDT - Notes by Gemini")
      const match = filename.match(
        /(\d{4})\/(\d{2})\/(\d{2}) (\d{2}):(\d{2}) (\w+)/,
      );

      if (match) {
        const [_, year, month, day, hour, minute, tz] = match;

        // Create date object for ISO format
        const dateObj = new Date(`${year}-${month}-${day}`);
        const isoDate = dateObj.toISOString().split("T")[0]; // ISO format for Notion

        // Format time (12-hour with AM/PM)
        const hour12 = parseInt(hour) % 12 || 12;
        const ampm = parseInt(hour) < 12 ? "AM" : "PM";
        const formattedTime = `${hour12}:${minute} ${ampm} ${tz}`;

        // Try to extract a title from the filename
        // Remove the "Copy of " prefix and date/time parts
        let title = filename.replace(/^Copy of /, "");
        title = title.replace(
          /Meeting started \d{4}\/\d{2}\/\d{2} \d{2}:\d{2} \w+ - /,
          "",
        );
        title = title.replace(/Notes by Gemini$/, "").trim();

        // If we couldn't extract a meaningful title, use a default with the date
        if (!title || title === "") {
          const monthNames = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
          ];
          const monthName = monthNames[parseInt(month) - 1];
          title = `Meeting on ${monthName} ${parseInt(day)}, ${year}`;
        }

        return {
          title,
          date: isoDate,
          time: formattedTime,
        };
      }
    } catch (error) {
      console.error(
        `Error extracting metadata from filename ${filename}:`,
        error,
      );
    }

    return {
      title: filename.replace(/^Copy of /, ""),
      date: null,
      time: null,
    };
  }

  /**
   * Extract content from a Google Doc with tabs (Notes and Transcript)
   * @param auth Authorized OAuth2 client
   * @param docId Google Doc ID
   */
  async extractDocContent(
    auth: OAuth2Client,
    docId: string,
  ): Promise<{
    transcriptText: string;
    notesText: string;
    summaryParagraph: string;
  }> {
    try {
      const docs = google.docs({ version: "v1", auth });

      // First, try to get the document content with tabs included
      // Some documents may not support includeTabsContent parameter
      let response;
      try {
        response = await docs.documents.get({
          documentId: docId,
          includeTabsContent: true,
        });
      } catch (tabError: any) {
        console.log(`Document ${docId} doesn't support tabs, trying without includeTabsContent`);
        // Fallback to basic document retrieval
        response = await docs.documents.get({
          documentId: docId,
        });
      }

      const document = response.data;
      let notesText = "";
      let transcriptText = "";
      let summaryParagraph = "";

      // Check if the document has tabs (modern Google Meet docs)
      if (document.tabs && document.tabs.length > 0) {
        console.log(`Document has ${document.tabs.length} tabs`);

        for (const tab of document.tabs) {
          const tabTitle = tab.tabProperties?.title?.toLowerCase() || "";
          console.log(`Processing tab: "${tabTitle}"`);

          if (tab.documentTab?.body?.content) {
            const tabContent = this.extractTextFromStructuralElements(tab.documentTab.body.content);

            if (tabTitle.includes("notes") || tabTitle.includes("gemini")) {
              notesText = tabContent.trim();
              console.log(`Extracted notes (${notesText.length} chars) from "${tabTitle}" tab`);

              // Extract summary paragraph from notes
              if (!summaryParagraph && notesText) {
                const lines = notesText.split('\n').filter(line => line.trim().length > 0);
                for (const line of lines) {
                  if (line.length > 50 && line.length < 500 && line.endsWith('.') && !line.includes(':')) {
                    summaryParagraph = line.trim();
                    break;
                  }
                }
              }
            } else if (tabTitle.includes("transcript")) {
              transcriptText = tabContent.trim();
              console.log(`Extracted transcript (${transcriptText.length} chars) from "${tabTitle}" tab`);
            }
          }
        }
      } else {
        // Fallback for documents without tabs (older format)
        console.log("Document has no tabs, using fallback extraction");

        if (!document.body || !document.body.content) {
          throw new Error("Document has no content");
        }

        const fullText = this.extractTextFromStructuralElements(document.body.content);

        // Try to split content based on headings
        const sections = this.splitContentIntoSections(fullText);
        notesText = sections.notes;
        transcriptText = sections.transcript || fullText;
        summaryParagraph = sections.summary;
      }

      // Validate we got some content
      if (!notesText && !transcriptText) {
        throw new Error("No content could be extracted from the document");
      }

      return {
        transcriptText: transcriptText || "",
        notesText: notesText || "",
        summaryParagraph: summaryParagraph || "",
      };
    } catch (error) {
      console.error(`Error extracting content from document ${docId}:`, error);
      throw error;
    }
  }

  /**
   * Extract text from structural elements (paragraphs, tables, etc.)
   * @param content Structural elements from Google Docs API
   */
  private extractTextFromStructuralElements(content: any[]): string {
    let text = "";

    for (const element of content) {
      if (element.paragraph) {
        const paragraphText = this.extractParagraphText(element.paragraph);
        text += paragraphText + "\n\n";
      } else if (element.table) {
        // Handle tables (common in meeting transcripts)
        for (const row of element.table.tableRows || []) {
          let rowText = "";

          for (const cell of row.tableCells || []) {
            if (cell.content) {
              for (const cellElement of cell.content) {
                if (cellElement.paragraph) {
                  rowText += this.extractParagraphText(cellElement.paragraph) + " | ";
                }
              }
            }
          }

          if (rowText) {
            text += rowText.trim() + "\n";
          }
        }
        text += "\n";
      }
    }

    return text.trim();
  }

  /**
   * Split content into sections for documents without tabs (fallback)
   * @param content Full document content
   */
  private splitContentIntoSections(content: string): {
    notes: string;
    transcript: string;
    summary: string;
  } {
    const lines = content.split('\n');
    let notes = "";
    let transcript = "";
    let summary = "";
    let currentSection = "unknown";

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lowerLine = line.toLowerCase();

      // Detect section headers
      if (lowerLine.includes('notes') || lowerLine.includes('summary')) {
        currentSection = "notes";
        continue;
      } else if (lowerLine.includes('transcript')) {
        currentSection = "transcript";
        continue;
      }

      // Add content to appropriate section
      if (currentSection === "notes") {
        notes += line + "\n";
      } else if (currentSection === "transcript") {
        transcript += line + "\n";
      } else if (currentSection === "unknown" && line.length > 0) {
        // Default to notes for unidentified content at the start
        notes += line + "\n";
      }

      // Extract potential summary paragraph
      if (!summary && line.length > 50 && line.length < 500 && line.endsWith('.') && !line.includes(':')) {
        summary = line;
      }
    }

    return {
      notes: notes.trim(),
      transcript: transcript.trim(),
      summary: summary.trim(),
    };
  }

  /**
   * Extract content from any supported file type
   * @param auth Authorized OAuth2 client
   * @param file Google Drive file object
   * @returns Extracted text content
   */
  async extractFileContent(auth: OAuth2Client, file: drive_v3.Schema$File): Promise<string> {
    try {
      const mimeType = file.mimeType || '';
      const fileId = file.id || '';

      // Handle Google Docs
      if (mimeType === 'application/vnd.google-apps.document') {
        const result = await this.extractDocContent(auth, fileId);
        return [result.notesText, result.transcriptText, result.summaryParagraph]
          .filter(text => text && text.trim().length > 0)
          .join('\n\n');
      }

      // Handle plain text files
      if (mimeType === 'text/plain' || mimeType === 'text/csv') {
        return await this.extractTxtContent(auth, fileId);
      }

      // Handle Word documents (.docx)
      if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        try {
          const docContent = await this.extractWordDocContent(auth, fileId);
          return docContent;
        } catch (error) {
          console.error(`Error extracting Word document content for ${file.name}:`, error);
          return `Word Document: ${file.name}\nFile type: DOCX\nSize: ${file.size} bytes\nLast modified: ${file.modifiedTime}\nNote: Text extraction failed, but file is searchable by name and metadata.`;
        }
      }

      // Handle Google Sheets - extract actual spreadsheet data
      if (mimeType === 'application/vnd.google-apps.spreadsheet') {
        try {
          const sheetContent = await this.extractSheetsContent(auth, fileId);
          return sheetContent;
        } catch (error) {
          console.error(`Error extracting Google Sheets content for ${file.name}:`, error);
          return `Spreadsheet: ${file.name}\nFile type: Google Sheets\nLast modified: ${file.modifiedTime}\nNote: Data extraction failed, but file is searchable by name and metadata.`;
        }
      }

      // Handle Excel files (.xlsx)
      if (mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        try {
          const excelContent = await this.extractExcelContent(auth, fileId);
          return excelContent;
        } catch (error) {
          console.error(`Error extracting Excel content for ${file.name}:`, error);
          return `Excel Spreadsheet: ${file.name}\nFile type: XLSX\nSize: ${file.size} bytes\nLast modified: ${file.modifiedTime}\nNote: Data extraction failed, but file is searchable by name and metadata.`;
        }
      }

      // Handle Google Slides - extract actual presentation content
      if (mimeType === 'application/vnd.google-apps.presentation') {
        try {
          const slidesContent = await this.extractSlidesContent(auth, fileId);
          return slidesContent;
        } catch (error) {
          console.error(`Error extracting Google Slides content for ${file.name}:`, error);
          return `Presentation: ${file.name}\nFile type: Google Slides\nLast modified: ${file.modifiedTime}\nNote: Content extraction failed, but file is searchable by name and metadata.`;
        }
      }

      // Handle PowerPoint files (.pptx) - temporarily disabled, using universal extraction
      if (mimeType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation') {
        // Skip specialized PowerPoint extraction for now, let universal extraction handle it
        console.log(`[GOOGLE] Skipping specialized PowerPoint extraction for ${file.name}, using universal fallback`);
      }

      // Handle PDFs - download and extract text content
      if (mimeType === 'application/pdf') {
        try {
          const pdfContent = await this.extractPdfContent(auth, fileId);
          return pdfContent;
        } catch (error) {
          console.error(`Error extracting PDF content for ${file.name}:`, error);
          return `PDF Document: ${file.name}\nFile type: PDF\nSize: ${file.size} bytes\nLast modified: ${file.modifiedTime}\nNote: Text extraction failed, but file is searchable by name and metadata.`;
        }
      }

      // Handle images (metadata only)
      if (mimeType.startsWith('image/')) {
        return `Image: ${file.name}\nFile type: ${mimeType}\nSize: ${file.size} bytes\nLast modified: ${file.modifiedTime}`;
      }

      // Handle videos (metadata only)
      if (mimeType.startsWith('video/')) {
        return `Video: ${file.name}\nFile type: ${mimeType}\nSize: ${file.size} bytes\nLast modified: ${file.modifiedTime}`;
      }

      // Handle audio (metadata only)
      if (mimeType.startsWith('audio/')) {
        return `Audio: ${file.name}\nFile type: ${mimeType}\nSize: ${file.size} bytes\nLast modified: ${file.modifiedTime}`;
      }

      // Default: return basic file metadata
      return `File: ${file.name}\nFile type: ${mimeType}\nSize: ${file.size} bytes\nLast modified: ${file.modifiedTime}`;

    } catch (error) {
      console.error(`Error extracting content from file ${file.name}:`, error);
      // Return basic metadata as fallback
      return `File: ${file.name}\nFile type: ${file.mimeType}\nError extracting content: ${error}`;
    }
  }

  /**
   * Extract plain text content from a .txt file (chat transcript)
   * @param auth Authorized OAuth2 client
   * @param fileId File ID (.txt file)
   * @returns Raw plain text as string
   */
  async extractTxtContent(auth: OAuth2Client, fileId: string): Promise<string> {
    const drive = google.drive({ version: 'v3', auth });

    try {
      const response = await drive.files.get({
        fileId: fileId,
        alt: 'media',
      });

      // Return the response data directly
      return response.data as string;
    } catch (error) {
      console.error('Error extracting TXT content:', error);
      throw error;
    }
  }

  /**
   * Extract text content from a PDF file
   * @param auth Authorized OAuth2 client
   * @param fileId File ID (.pdf file)
   * @returns Extracted text content from PDF
   */
  async extractPdfContent(auth: OAuth2Client, fileId: string): Promise<string> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Download the PDF file as binary data
      const res = await drive.files.get(
        {
          fileId,
          alt: "media",
        },
        { responseType: "arraybuffer" },
      );

      // Convert ArrayBuffer to Uint8Array for PDF processing
      const pdfData = new Uint8Array(res.data as ArrayBuffer);

      // Dynamic import to avoid module loading issues
      const pdfjsLib = await import('pdfjs-dist');

      // Load the PDF document
      const pdf = await pdfjsLib.getDocument({ data: pdfData }).promise;

      let fullText = '';

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');

        if (pageText.trim()) {
          fullText += `Page ${pageNum}:\n${pageText.trim()}\n\n`;
        }
      }

      return fullText.trim() || `PDF Document processed but no text content found. This may be a scanned PDF or image-based document.`;

    } catch (error) {
      console.error(`Error extracting content from PDF file ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Extract content from Google Sheets
   */
  async extractSheetsContent(auth: OAuth2Client, fileId: string): Promise<string> {
    try {
      const { google } = await import('googleapis');
      const sheets = google.sheets({ version: 'v4', auth });

      // Get spreadsheet metadata first
      const spreadsheet = await sheets.spreadsheets.get({
        spreadsheetId: fileId,
      });

      const title = spreadsheet.data.properties?.title || 'Untitled Spreadsheet';
      let content = `Spreadsheet: ${title}\nFile type: Google Sheets\n\n`;

      // Get all sheet names
      const sheetNames = spreadsheet.data.sheets?.map(sheet => sheet.properties?.title).filter(Boolean) || [];

      if (sheetNames.length === 0) {
        return content + 'No sheets found in this spreadsheet.';
      }

      // Extract data from each sheet (limit to first 5 sheets to avoid huge responses)
      const sheetsToProcess = sheetNames.slice(0, 5);

      for (const sheetName of sheetsToProcess) {
        try {
          // Get data from the sheet (first 100 rows to avoid huge responses)
          const range = `'${sheetName}'!A1:Z100`;
          const response = await sheets.spreadsheets.values.get({
            spreadsheetId: fileId,
            range: range,
          });

          const values = response.data.values;
          if (values && values.length > 0) {
            content += `\n--- Sheet: ${sheetName} ---\n`;

            // Process rows (limit to first 50 rows for performance)
            const rowsToProcess = values.slice(0, 50);

            for (let i = 0; i < rowsToProcess.length; i++) {
              const row = rowsToProcess[i];
              if (row && row.length > 0) {
                // Filter out empty cells and join with tabs
                const rowData = row.filter(cell => cell && cell.toString().trim().length > 0);
                if (rowData.length > 0) {
                  content += `Row ${i + 1}: ${rowData.join(' | ')}\n`;
                }
              }
            }

            if (values.length > 50) {
              content += `... (${values.length - 50} more rows)\n`;
            }
          } else {
            content += `\n--- Sheet: ${sheetName} ---\n(Empty sheet)\n`;
          }
        } catch (sheetError: any) {
          console.error(`Error extracting data from sheet ${sheetName}:`, sheetError);
          content += `\n--- Sheet: ${sheetName} ---\n(Error reading sheet data)\n`;
        }
      }

      if (sheetNames.length > 5) {
        content += `\n... (${sheetNames.length - 5} more sheets not shown)\n`;
      }

      return content;

    } catch (error) {
      console.error(`Error extracting Google Sheets content for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Extract content from Excel (.xlsx) files
   */
  async extractExcelContent(auth: OAuth2Client, fileId: string): Promise<string> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Download the Excel file as binary data
      const res = await drive.files.get(
        {
          fileId,
          alt: "media",
        },
        { responseType: "arraybuffer" },
      );

      // Convert ArrayBuffer to Buffer
      const excelBuffer = Buffer.from(res.data as ArrayBuffer);

      // Dynamic import to avoid module loading issues
      const XLSX = await import('xlsx');

      // Parse the Excel file
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });

      let content = `Excel Spreadsheet\nFile type: XLSX\n\n`;

      // Get all sheet names
      const sheetNames = workbook.SheetNames;

      if (sheetNames.length === 0) {
        return content + 'No sheets found in this Excel file.';
      }

      // Process each sheet (limit to first 5 sheets)
      const sheetsToProcess = sheetNames.slice(0, 5);

      for (const sheetName of sheetsToProcess) {
        try {
          const worksheet = workbook.Sheets[sheetName];

          if (worksheet) {
            content += `\n--- Sheet: ${sheetName} ---\n`;

            // Convert sheet to JSON to get structured data
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
              header: 1, // Use array of arrays format
              defval: '', // Default value for empty cells
              range: 'A1:Z100' // Limit range to avoid huge files
            });

            if (jsonData.length > 0) {
              // Process rows (limit to first 50 rows)
              const rowsToProcess = jsonData.slice(0, 50);

              for (let i = 0; i < rowsToProcess.length; i++) {
                const row = rowsToProcess[i] as any[];
                if (row && row.length > 0) {
                  // Filter out empty cells and join
                  const rowData = row.filter(cell => cell && cell.toString().trim().length > 0);
                  if (rowData.length > 0) {
                    content += `Row ${i + 1}: ${rowData.join(' | ')}\n`;
                  }
                }
              }

              if (jsonData.length > 50) {
                content += `... (${jsonData.length - 50} more rows)\n`;
              }
            } else {
              content += `(Empty sheet)\n`;
            }
          }
        } catch (sheetError: any) {
          console.error(`Error extracting data from Excel sheet ${sheetName}:`, sheetError);
          content += `\n--- Sheet: ${sheetName} ---\n(Error reading sheet data)\n`;
        }
      }

      if (sheetNames.length > 5) {
        content += `\n... (${sheetNames.length - 5} more sheets not shown)\n`;
      }

      return content;

    } catch (error) {
      console.error(`Error extracting Excel content for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Extract content from Google Slides
   */
  async extractSlidesContent(auth: OAuth2Client, fileId: string): Promise<string> {
    try {
      const { google } = await import('googleapis');
      const slides = google.slides({ version: 'v1', auth });

      // Get presentation metadata and slides
      const presentation = await slides.presentations.get({
        presentationId: fileId,
      });

      const title = presentation.data.title || 'Untitled Presentation';
      let content = `Presentation: ${title}\nFile type: Google Slides\n\n`;

      const slidesList = presentation.data.slides || [];

      if (slidesList.length === 0) {
        return content + 'No slides found in this presentation.';
      }

      // Extract content from each slide (limit to first 50 slides)
      const slidesToProcess = slidesList.slice(0, 50);

      for (let i = 0; i < slidesToProcess.length; i++) {
        const slide = slidesToProcess[i];
        const slideNumber = i + 1;

        content += `\n--- Slide ${slideNumber} ---\n`;

        // Extract text from all page elements
        const pageElements = slide.pageElements || [];
        let slideText = '';

        for (const element of pageElements) {
          if (element.shape && element.shape.text) {
            const textElements = element.shape.text.textElements || [];
            for (const textElement of textElements) {
              if (textElement.textRun && textElement.textRun.content) {
                slideText += textElement.textRun.content;
              }
            }
          }
        }

        if (slideText.trim()) {
          // Clean up the text (remove excessive whitespace)
          const cleanText = slideText.replace(/\s+/g, ' ').trim();
          content += `${cleanText}\n`;
        } else {
          content += '(No text content)\n';
        }
      }

      if (slidesList.length > 50) {
        content += `\n... (${slidesList.length - 50} more slides not shown)\n`;
      }

      return content;

    } catch (error) {
      console.error(`Error extracting Google Slides content for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Extract content from PowerPoint (.pptx) files using node-pptx-parser
   */
  async extractPowerPointContent(auth: OAuth2Client, fileId: string): Promise<string> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Download the PowerPoint file as binary data
      const res = await drive.files.get(
        {
          fileId,
          alt: "media",
        },
        { responseType: "arraybuffer" },
      );

      // Convert ArrayBuffer to Buffer
      const pptBuffer = Buffer.from(res.data as ArrayBuffer);

      // Try node-pptx-parser with proper ES module import
      try {
        const pptxParserModule = await import('node-pptx-parser');
        const parser = pptxParserModule.default || pptxParserModule;

        // Parse the PowerPoint file with preserved formatting
        let presentation;
        if (typeof parser === 'function') {
          presentation = await new (parser as any)(pptBuffer);
        } else if (typeof (parser as any).parse === 'function') {
          presentation = await (parser as any).parse(pptBuffer);
        } else if (typeof (parser as any).parseBuffer === 'function') {
          presentation = await (parser as any).parseBuffer(pptBuffer);
        } else {
          throw new Error('No valid parser function found');
        }

      let content = `PowerPoint Presentation\nFile type: PPTX\n`;

      // Add presentation metadata if available
      if (presentation.title) {
        content += `Title: ${presentation.title}\n`;
      }
      if (presentation.author) {
        content += `Author: ${presentation.author}\n`;
      }
      content += `\n`;

      const slides = presentation.slides || [];

      if (slides.length === 0) {
        return content + 'No slides found in this PowerPoint file.';
      }

      // Process each slide (limit to first 50 slides for performance)
      const slidesToProcess = slides.slice(0, 50);

      for (let i = 0; i < slidesToProcess.length; i++) {
        const slide = slidesToProcess[i];
        const slideNumber = i + 1;

        content += `\n--- Slide ${slideNumber} ---\n`;

        try {
          // Extract text content from the slide
          let slideText = '';

          // node-pptx-parser provides structured text content
          if (slide.text && typeof slide.text === 'string') {
            slideText = slide.text;
          } else if (slide.content && typeof slide.content === 'string') {
            slideText = slide.content;
          } else if (slide.textContent && typeof slide.textContent === 'string') {
            slideText = slide.textContent;
          } else if (Array.isArray(slide.texts)) {
            slideText = slide.texts
              .filter((text: any) => text && typeof text === 'string' && text.trim().length > 0)
              .join('\n');
          } else if (slide.shapes && Array.isArray(slide.shapes)) {
            // Extract text from shapes if available
            const shapeTexts = slide.shapes
              .map((shape: any) => {
                if (shape.text) return shape.text;
                if (shape.content) return shape.content;
                return '';
              })
              .filter((text: any) => text && text.trim().length > 0);
            slideText = shapeTexts.join('\n');
          }

          if (slideText && slideText.trim()) {
            // Clean up the text while preserving line breaks
            const cleanText = slideText
              .replace(/\r\n/g, '\n')  // Normalize line endings
              .replace(/\n{3,}/g, '\n\n')  // Reduce excessive line breaks
              .trim();

            content += `${cleanText}\n`;
          } else {
            content += '(No text content)\n';
          }
        } catch (slideError: any) {
          console.error(`Error extracting slide ${slideNumber}:`, slideError);
          content += `(Error reading slide content)\n`;
        }
      }

      if (slides.length > 50) {
        content += `\n... (${slides.length - 50} more slides not shown)\n`;
      }

      return content;

      } catch (parserError: any) {
        console.error(`node-pptx-parser failed, falling back to basic extraction:`, parserError.message);

        // Fallback to basic PowerPoint metadata extraction
        return `PowerPoint Presentation\nFile type: PPTX\nNote: Advanced content extraction failed, but file is searchable by name and metadata.\nFallback extraction attempted but library compatibility issues encountered.`;
      }

    } catch (error) {
      console.error(`Error extracting PowerPoint content for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Extract text content from a Word document (.docx)
   * @param auth Authorized OAuth2 client
   * @param fileId File ID (.docx file)
   * @returns Extracted text content from Word document
   */
  async extractWordDocContent(auth: OAuth2Client, fileId: string): Promise<string> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Download the Word document as binary data
      const res = await drive.files.get(
        {
          fileId,
          alt: "media",
        },
        { responseType: "arraybuffer" },
      );

      // Convert ArrayBuffer to Buffer for mammoth processing
      const docBuffer = Buffer.from(res.data as ArrayBuffer);

      // Dynamic import to avoid module loading issues
      const mammoth = await import('mammoth');

      // Extract text with better formatting preservation
      const result = await mammoth.extractRawText({
        buffer: docBuffer
      } as any);

      let content = `Word Document\nFile type: DOCX\n\n`;

      if (result.value && result.value.trim()) {
        // Clean up the text while preserving paragraph structure
        const cleanText = result.value
          .replace(/\r\n/g, '\n')  // Normalize line endings
          .replace(/\n{3,}/g, '\n\n')  // Reduce excessive line breaks
          .trim();

        content += cleanText;

        // Add any conversion messages if present
        if (result.messages && result.messages.length > 0) {
          const warnings = result.messages
            .filter(msg => msg.type === 'warning')
            .map(msg => msg.message);

          if (warnings.length > 0) {
            content += `\n\n--- Conversion Notes ---\n${warnings.join('\n')}`;
          }
        }
      } else {
        content += `Word Document processed but no text content found.`;
      }

      return content;

    } catch (error) {
      console.error(`Error extracting content from Word document ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Extract text from a paragraph element
   * @param paragraph Paragraph element
   */
  private extractParagraphText(paragraph: any): string {
    if (!paragraph.elements) {
      return "";
    }

    return paragraph.elements
      .map((element: any) => {
        if (element.textRun && element.textRun.content) {
          return element.textRun.content;
        }
        return "";
      })
      .join("");
  }

  /**
   * Get the complete Google Drive structure including root, shared drives, and computers
   * @param auth Authorized OAuth2 client
   * @returns Structured drive information
   */
  async getDriveStructure(auth: OAuth2Client): Promise<{
    myDrive: drive_v3.Schema$File[];
    sharedDrives: any[];
    computers: drive_v3.Schema$File[];
    sharedWithMe: drive_v3.Schema$File[];
  }> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Get user info
      let userEmail = 'Unknown';
      try {
        const oauth2 = google.oauth2({ version: "v2", auth });
        const userInfo = await oauth2.userinfo.get();
        userEmail = userInfo.data.email || 'Unknown';
        console.log(`Getting Drive structure for: ${userEmail}`);
      } catch (userError) {
        console.warn("Could not get user info:", userError);
      }

      // 1. Get My Drive folders (root level)
      const myDriveQuery = "mimeType='application/vnd.google-apps.folder' and trashed=false and 'root' in parents";
      const myDriveResponse = await drive.files.list({
        q: myDriveQuery,
        fields: "files(id, name, parents, driveId, shared)",
        pageSize: 100,
        orderBy: "name",
        includeItemsFromAllDrives: false,
        supportsAllDrives: false,
      });

      // 2. Get Shared Drives (Team Drives) with their folder contents
      let sharedDrives: any[] = [];
      try {
        const sharedDrivesResponse = await drive.drives.list({
          pageSize: 100,
          fields: "drives(id, name, backgroundImageFile, capabilities)",
        });
        const rawSharedDrives = sharedDrivesResponse.data.drives || [];
        console.log(`Found ${rawSharedDrives.length} shared drives`);

        // For each shared drive, fetch its root-level folders
        for (const sharedDrive of rawSharedDrives) {
          try {
            const sharedDriveFolders = await this.listSharedDriveFolders(auth, sharedDrive.id!);
            sharedDrives.push({
              ...sharedDrive,
              folders: sharedDriveFolders
            });
            console.log(`Loaded ${sharedDriveFolders.length} folders for shared drive: ${sharedDrive.name}`);
          } catch (folderError) {
            console.warn(`Could not load folders for shared drive ${sharedDrive.name}:`, folderError);
            // Still include the shared drive even if we can't load its folders
            sharedDrives.push({
              ...sharedDrive,
              folders: []
            });
          }
        }
      } catch (sharedDriveError) {
        console.warn("Could not access shared drives:", sharedDriveError);
      }

      // 3. Get Computer folders (from Google Drive desktop sync)
      // Try multiple approaches to find computer sync folders
      let computerFolders: drive_v3.Schema$File[] = [];

      // Approach 1: Look for folders with "Computer" in the name
      try {
        const computerQuery1 = "mimeType='application/vnd.google-apps.folder' and trashed=false and name contains 'Computer'";
        const computerResponse1 = await drive.files.list({
          q: computerQuery1,
          fields: "files(id, name, parents, driveId, shared, owners)",
          pageSize: 100,
          orderBy: "name",
          includeItemsFromAllDrives: true,
          supportsAllDrives: true,
        });
        computerFolders.push(...(computerResponse1.data.files || []));
      } catch (error) {
        console.warn("Could not search for 'Computer' folders:", error);
      }

      // Approach 2: Look for folders that might be computer names (common patterns)
      try {
        const computerQuery2 = "mimeType='application/vnd.google-apps.folder' and trashed=false and (name contains 'Desktop' or name contains 'Laptop' or name contains 'PC' or name contains 'MacBook' or name contains 'iMac')";
        const computerResponse2 = await drive.files.list({
          q: computerQuery2,
          fields: "files(id, name, parents, driveId, shared, owners)",
          pageSize: 100,
          orderBy: "name",
          includeItemsFromAllDrives: true,
          supportsAllDrives: true,
        });

        // Add only unique folders (avoid duplicates)
        const newComputers = (computerResponse2.data.files || []).filter(
          newFolder => !computerFolders.some(existing => existing.id === newFolder.id)
        );
        computerFolders.push(...newComputers);
      } catch (error) {
        console.warn("Could not search for computer name patterns:", error);
      }

      // Approach 3: Look for folders that are owned by the user but appear to be synced from desktop
      // These often have specific characteristics like being in root but not created through web interface
      try {
        const rootFoldersQuery = "mimeType='application/vnd.google-apps.folder' and trashed=false and 'root' in parents";
        const rootFoldersResponse = await drive.files.list({
          q: rootFoldersQuery,
          fields: "files(id, name, parents, driveId, shared, owners, createdTime, modifiedTime)",
          pageSize: 100,
          orderBy: "name",
          includeItemsFromAllDrives: false,
          supportsAllDrives: false,
        });

        // Look for folders that might be computer sync folders based on naming patterns
        const potentialComputers = (rootFoldersResponse.data.files || []).filter(folder => {
          const name = folder.name?.toLowerCase() || '';
          // Common computer/device name patterns
          const computerPatterns = [
            /^[a-zA-Z0-9\-_]+(pc|desktop|laptop|computer|macbook|imac|mac)$/i,
            /^(pc|desktop|laptop|computer|macbook|imac|mac)[a-zA-Z0-9\-_]*$/i,
            /^[a-zA-Z0-9\-_]*\s+(pc|desktop|laptop|computer|macbook|imac|mac)$/i,
            /^(windows|macos|ubuntu|linux)[a-zA-Z0-9\-_]*$/i,
          ];

          return computerPatterns.some(pattern => pattern.test(name)) &&
                 !computerFolders.some(existing => existing.id === folder.id);
        });

        computerFolders.push(...potentialComputers);
      } catch (error) {
        console.warn("Could not search root folders for computer patterns:", error);
      }

      console.log(`Found ${computerFolders.length} potential computer folders`);
      if (computerFolders.length > 0) {
        console.log('Computer folders found:', computerFolders.map(f => f.name));
      }

      // 4. Get Shared with me folders
      const sharedWithMeQuery = "mimeType='application/vnd.google-apps.folder' and trashed=false and sharedWithMe=true";
      const sharedWithMeResponse = await drive.files.list({
        q: sharedWithMeQuery,
        fields: "files(id, name, parents, driveId, shared, owners)",
        pageSize: 100,
        orderBy: "name",
        includeItemsFromAllDrives: true,
        supportsAllDrives: true,
      });

      console.log(`Drive structure summary:
        - My Drive folders: ${myDriveResponse.data.files?.length || 0}
        - Shared Drives: ${sharedDrives.length}
        - Computer folders: ${computerFolders.length}
        - Shared with me: ${sharedWithMeResponse.data.files?.length || 0}`);

      return {
        myDrive: myDriveResponse.data.files || [],
        sharedDrives,
        computers: computerFolders,
        sharedWithMe: sharedWithMeResponse.data.files || [],
      };
    } catch (error: any) {
      console.error("Error getting Drive structure:", error);
      throw error;
    }
  }

  /**
   * List folders within a specific shared drive
   * @param auth Authorized OAuth2 client
   * @param driveId Shared drive ID
   * @param parentFolderId Optional parent folder ID within the shared drive (defaults to root)
   * @returns List of folder objects with id, name, and parents
   */
  async listSharedDriveFolders(
    auth: OAuth2Client,
    driveId: string,
    parentFolderId: string | null = null,
  ): Promise<drive_v3.Schema$File[]> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Build query for folders in the shared drive
      let query = "mimeType='application/vnd.google-apps.folder' and trashed=false";
      if (parentFolderId) {
        query = `${query} and '${parentFolderId}' in parents`;
      }

      console.log(`Listing folders in shared drive ${driveId}, parent: ${parentFolderId || 'root'}`);

      // Make request with shared drive parameters
      const requestParams = {
        q: query,
        fields: "files(id, name, parents, driveId, shared)",
        pageSize: 100,
        orderBy: "name",
        // Specify the shared drive
        driveId: driveId,
        corpora: 'drive' as const,
        includeItemsFromAllDrives: true,
        supportsAllDrives: true,
      };

      const response = await drive.files.list(requestParams);

      console.log(`Found ${response.data.files?.length || 0} folders in shared drive ${driveId}`);

      return response.data.files || [];
    } catch (error: any) {
      console.error(`Error listing folders in shared drive ${driveId}:`, error);
      throw error;
    }
  }

  /**
   * List Google Drive folders using the correct API parameters
   * @param auth Authorized OAuth2 client
   * @param parentFolderId Optional parent folder ID to filter results
   * @returns List of folder objects with id, name, and parents
   */
  async listDriveFolders(
    auth: OAuth2Client,
    parentFolderId: string | null = null,
  ): Promise<drive_v3.Schema$File[]> {
    try {
      const drive = google.drive({ version: "v3", auth });

      // Get user info to log which account we're using
      try {
        const oauth2 = google.oauth2({ version: "v2", auth });
        const userInfo = await oauth2.userinfo.get();
        console.log(`Listing folders for Google user: ${userInfo.data.email}`);
      } catch (userError) {
        console.warn("Could not get user info:", userError);
      }

      // Build query based on whether we're looking in a specific parent folder
      let query =
        "mimeType='application/vnd.google-apps.folder' and trashed=false";
      if (parentFolderId) {
        query = `${query} and '${parentFolderId}' in parents`;
      }

      console.log(`Using Drive API query: ${query}`);

      // Make request with proper parameters following Google's documentation
      const requestParams = {
        q: query,
        fields: "files(id, name, parents, driveId), nextPageToken",
        pageSize: 100,
        orderBy: "name",
        // Include shared drives support
        includeItemsFromAllDrives: true,
        supportsAllDrives: true,
      };

      console.log(
        "Drive API request params:",
        JSON.stringify(requestParams, null, 2),
      );

      const response = await drive.files.list(requestParams);

      console.log(`Drive API response status: ${response.status}`);
      console.log(`Found ${response.data.files?.length || 0} folders`);

      return response.data.files || [];
    } catch (error: any) {
      console.error("Error listing Drive folders:", error);
      // Log detailed error information
      if (error?.response) {
        console.error("Error response:", {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers,
        });
      }
      throw error;
    }
  }

  /**
   * Test the connection to Google Drive
   * @param auth Authorized OAuth2 client
   */
  async testConnection(auth: OAuth2Client): Promise<{
    success: boolean;
    username?: string;
    email?: string;
    details?: string;
  }> {
    try {
      // Test API access
      const drive = google.drive({ version: "v3", auth });

      // Call files.list to check if we can access Drive
      await drive.files.list({
        pageSize: 1,
        fields: "files(id, name)",
      });

      // Get user info
      const oauth2 = google.oauth2({ version: "v2", auth });
      const userInfo = await oauth2.userinfo.get();

      return {
        success: true,
        username: userInfo.data.name || undefined,
        email: userInfo.data.email || undefined,
        details: "Successfully connected to Google Drive",
      };
    } catch (error: any) {
      console.error("Error testing Google connection:", error);

      return {
        success: false,
        details: `Connection failed: ${error.message}`,
      };
    }
  }

  /**
   * Extract duration from transcript text by looking for "Transcription ended after" pattern
   * @param transcriptText The raw transcript text
   * @returns Duration in minutes as number, or null if not found
   */
  extractDurationFromTranscript(transcriptText: string): number | null {
    try {
      // Look for pattern like "Transcription ended after 08:00:02" or "Transcription ended after 1:23:45"
      const durationMatch = transcriptText.match(/Transcription ended after (\d{1,2}):(\d{2}):(\d{2})/i);

      if (durationMatch) {
        const [_, hours, minutes, seconds] = durationMatch;

        // Convert to total minutes
        const totalMinutes = parseInt(hours) * 60 + parseInt(minutes) + Math.round(parseInt(seconds) / 60);

        console.log(`Extracted duration: ${hours}:${minutes}:${seconds} = ${totalMinutes} minutes`);
        return totalMinutes;
      }

      // Also try simpler patterns like "Duration: 45 minutes" or similar
      const simpleDurationMatch = transcriptText.match(/(?:Duration|Meeting length|Call duration):\s*(\d+)\s*minutes?/i);
      if (simpleDurationMatch) {
        const duration = parseInt(simpleDurationMatch[1]);
        console.log(`Extracted simple duration: ${duration} minutes`);
        return duration;
      }

      console.log('No duration pattern found in transcript');
      return null;
    } catch (error: any) {
      console.error('Error extracting duration from transcript:', error);
      return null;
    }
  }

  /**
   * List ALL files in a Google Drive folder for reference aggregation
   * This replaces the transcript-only approach with comprehensive file discovery
   * @param auth Authorized OAuth2 client
   * @param folderId Folder ID to scan (can be root or specific folder)
   * @param recursive Whether to scan subfolders recursively
   * @returns Array of all files with comprehensive metadata
   */
  async listAllFiles(
    auth: OAuth2Client,
    folderId: string = 'root',
    recursive: boolean = true,
  ): Promise<drive_v3.Schema$File[]> {
    try {
      const drive = google.drive({ version: "v3", auth });
      let allFiles: drive_v3.Schema$File[] = [];

      // Get files in current folder - include all file types, not just documents
      const query = `'${folderId}' in parents and trashed = false`;
      console.log(`Scanning Google Drive folder: ${folderId}`);

      const response = await drive.files.list({
        q: query,
        fields: "files(id, name, mimeType, modifiedTime, createdTime, size, parents, owners, permissions, webViewLink, webContentLink, thumbnailLink, shared, fileExtension)",
        orderBy: "modifiedTime desc",
        pageSize: 1000, // Get up to 1000 files per request
        // Include shared drives support
        includeItemsFromAllDrives: true,
        supportsAllDrives: true,
      });

      const files = response.data.files || [];
      console.log(`Found ${files.length} items in folder ${folderId}`);

      // Separate files from folders
      const folders: drive_v3.Schema$File[] = [];
      const regularFiles: drive_v3.Schema$File[] = [];

      for (const file of files) {
        if (file.mimeType === 'application/vnd.google-apps.folder') {
          folders.push(file);
        } else {
          regularFiles.push(file);
        }
      }

      allFiles.push(...regularFiles);
      console.log(`Added ${regularFiles.length} files from current folder`);

      // Recursively scan subfolders if requested
      if (recursive && folders.length > 0) {
        console.log(`Recursively scanning ${folders.length} subfolders...`);
        for (const folder of folders) {
          if (folder.id) {
            try {
              console.log(`Scanning subfolder: ${folder.name} (${folder.id})`);
              const subfolderFiles = await this.listAllFiles(auth, folder.id, true);
              allFiles.push(...subfolderFiles);
              console.log(`Added ${subfolderFiles.length} files from subfolder: ${folder.name}`);
            } catch (error) {
              console.error(`Error scanning subfolder ${folder.name} (${folder.id}):`, error);
              // Continue with other folders even if one fails
            }
          }
        }
      } else if (recursive && folders.length === 0) {
        console.log(`No subfolders found in ${folderId}`);
      }

      return allFiles;
    } catch (error) {
      console.error(`Error listing all files in folder ${folderId}:`, error);
      throw error;
    }
  }

  /**
   * Extract comprehensive metadata from a Google Drive file
   * @param file Google Drive file object
   * @returns Standardized file metadata for our files table
   */
  extractFileMetadata(file: drive_v3.Schema$File): any {
    try {
      // Determine file type based on mimeType and extension
      let fileType = 'document'; // default

      if (file.mimeType) {
        if (file.mimeType.includes('document') || file.mimeType.includes('text') ||
            file.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.mimeType === 'application/msword') {
          fileType = 'document';
        } else if (file.mimeType.includes('spreadsheet') ||
                   file.mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                   file.mimeType === 'application/vnd.ms-excel' ||
                   file.mimeType === 'text/csv') {
          fileType = 'spreadsheet';
        } else if (file.mimeType.includes('presentation') ||
                   file.mimeType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                   file.mimeType === 'application/vnd.ms-powerpoint') {
          fileType = 'presentation';
        } else if (file.mimeType.startsWith('image/')) {
          fileType = 'image';
        } else if (file.mimeType.startsWith('video/')) {
          fileType = 'video';
        } else if (file.mimeType.startsWith('audio/')) {
          fileType = 'audio';
        } else if (file.mimeType === 'application/pdf') {
          fileType = 'pdf';
        } else if (file.mimeType.includes('zip') || file.mimeType.includes('archive')) {
          fileType = 'archive';
        }
      }

      // Check if this might be a meeting transcript based on filename patterns
      const isMeetingTranscript = this.isMeetingTranscript(file.name || '');
      if (isMeetingTranscript && fileType === 'document') {
        fileType = 'transcript';
      }

      // Extract owner information
      const owner = file.owners && file.owners.length > 0 ? file.owners[0] : null;

      // Extract sharing information
      const sharedWith: string[] = [];
      const isShared = file.shared || false;

      if (file.permissions) {
        for (const permission of file.permissions) {
          if (permission.emailAddress && permission.emailAddress !== owner?.emailAddress) {
            sharedWith.push(permission.emailAddress);
          }
        }
      }

      // Build comprehensive metadata
      const metadata = {
        platform: 'google_drive',
        externalId: file.id,
        fileName: file.name || 'Untitled',
        fileType,
        mimeType: file.mimeType,
        fileSize: file.size ? parseInt(file.size) : null,
        sourceUrl: file.webViewLink || '',
        downloadUrl: file.webContentLink || null,
        thumbnailUrl: file.thumbnailLink || null,
        userId: owner?.emailAddress || null,
        lastModified: file.modifiedTime ? new Date(file.modifiedTime) : null,
        isShared,
        sharedWith,
        extractedMetadata: {
          createdTime: file.createdTime,
          fileExtension: file.fileExtension || null,
          owners: file.owners || [],
          permissions: file.permissions || [],
          isMeetingTranscript,
          driveFileId: file.id,
        }
      };

      return metadata;
    } catch (error) {
      console.error(`Error extracting metadata for file ${file.name}:`, error);
      return null;
    }
  }

  /**
   * Check if a filename indicates this is a meeting transcript
   * @param filename File name to check
   * @returns true if this appears to be a meeting transcript
   */
  private isMeetingTranscript(filename: string): boolean {
    const transcriptPatterns = [
      /meeting.*transcript/i,
      /transcript.*meeting/i,
      /notes by gemini/i,
      /meeting started.*\d{4}\/\d{2}\/\d{2}/i,
      /\d{4}\/\d{2}\/\d{2}.*meeting/i,
    ];

    return transcriptPatterns.some(pattern => pattern.test(filename));
  }

  /**
   * Download PDF content from Google Drive
   */
  async downloadPDFContent(auth: OAuth2Client, fileId: string, fileName: string): Promise<Buffer | null> {
    const drive = google.drive({ version: 'v3', auth });

    try {
      console.log(`[GOOGLE] Downloading PDF content for: ${fileName} (ID: ${fileId})`);

      const response = await drive.files.get({
        fileId: fileId,
        alt: 'media',
      }, {
        responseType: 'arraybuffer'
      });

      if (!response.data) {
        console.log(`[GOOGLE] No content received for PDF: ${fileName}`);
        return null;
      }

      // Convert ArrayBuffer to Buffer
      const buffer = Buffer.from(response.data as ArrayBuffer);
      console.log(`[GOOGLE] Downloaded ${buffer.length} bytes for PDF: ${fileName}`);

      // Verify PDF header
      const pdfHeader = buffer.subarray(0, 4).toString();
      if (!pdfHeader.startsWith('%PDF')) {
        console.log(`[GOOGLE] Downloaded content doesn't appear to be a PDF (header: ${pdfHeader})`);
        return null;
      }

      return buffer;

    } catch (error: any) {
      console.error(`[GOOGLE] Error downloading PDF content for ${fileName}:`, error.message);
      return null;
    }
  }

  /**
   * Download Word document content from Google Drive
   */
  async downloadWordContent(auth: OAuth2Client, fileId: string, fileName: string): Promise<Buffer | null> {
    const drive = google.drive({ version: 'v3', auth });

    try {
      console.log(`[GOOGLE] Downloading Word document content for: ${fileName} (ID: ${fileId})`);

      const response = await drive.files.get({
        fileId: fileId,
        alt: 'media',
      }, {
        responseType: 'arraybuffer'
      });

      if (!response.data) {
        console.log(`[GOOGLE] No content received for Word document: ${fileName}`);
        return null;
      }

      // Convert ArrayBuffer to Buffer
      const buffer = Buffer.from(response.data as ArrayBuffer);
      console.log(`[GOOGLE] Downloaded ${buffer.length} bytes for Word document: ${fileName}`);

      // Basic validation for Word documents
      if (fileName.toLowerCase().endsWith('.docx')) {
        // Check for ZIP signature (DOCX files are ZIP archives)
        const zipHeader = buffer.subarray(0, 4);
        if (zipHeader[0] === 0x50 && zipHeader[1] === 0x4B) {
          console.log(`[GOOGLE] Valid DOCX file detected: ${fileName}`);
          return buffer;
        } else {
          console.log(`[GOOGLE] Downloaded content doesn't appear to be a valid DOCX file: ${fileName}`);
          return null;
        }
      } else if (fileName.toLowerCase().endsWith('.doc')) {
        // Check for DOC signature
        const docHeader = buffer.subarray(0, 8);
        if (docHeader[0] === 0xD0 && docHeader[1] === 0xCF) {
          console.log(`[GOOGLE] Valid DOC file detected: ${fileName}`);
          return buffer;
        } else {
          console.log(`[GOOGLE] Downloaded content doesn't appear to be a valid DOC file: ${fileName}`);
          return null;
        }
      }

      // If we can't validate the format, return the buffer anyway
      console.log(`[GOOGLE] Returning Word document buffer without validation: ${fileName}`);
      return buffer;

    } catch (error: any) {
      console.error(`[GOOGLE] Error downloading Word document content for ${fileName}:`, error.message);
      return null;
    }
  }
}

// DEPRECATED: Use the new modular Google service instead
// This is kept for backward compatibility during migration

// Use the new modular Google service facade
export const googleService = new GoogleServiceFacade();

// Legacy Google service class (kept for reference during migration)
export const legacyGoogleService = new GoogleService();
