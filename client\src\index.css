@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
      --background: 0 0% 100%;
--foreground: 20 14.3% 4.1%;
--muted: 60 4.8% 95.9%;
--muted-foreground: 25 5.3% 44.7%;
--popover: 0 0% 100%;
--popover-foreground: 20 14.3% 4.1%;
--card: 0 0% 100%;
--card-foreground: 20 14.3% 4.1%;
--border: 20 5.9% 90%;
--input: 20 5.9% 90%;
--primary: 207 90% 54%;
--primary-foreground: 211 100% 99%;
--secondary: 60 4.8% 95.9%;
--secondary-foreground: 24 9.8% 10%;
--accent: 60 4.8% 95.9%;
--accent-foreground: 24 9.8% 10%;
--destructive: 0 84.2% 60.2%;
--destructive-foreground: 60 9.1% 97.8%;
--ring: 20 14.3% 4.1%;
--radius: 0.5rem;
--chart-1: 12 76% 61%;
--chart-2: 173 58% 39%;
--chart-3: 197 37% 24%;
--chart-4: 43 74% 66%;
--chart-5: 27 87% 67%;
--sidebar-background: 0 0% 98%;
--sidebar-foreground: 240 5.3% 26.1%;
--sidebar-primary: 240 5.9% 10%;
--sidebar-primary-foreground: 0 0% 98%;
--sidebar-accent: 240 4.8% 95.9%;
--sidebar-accent-foreground: 240 5.9% 10%;
--sidebar-border: 220 13% 91%;
--sidebar-ring: 217.2 10.6% 64.9%;
  }
  .dark {
      --background: 240 10% 3.9%;
--foreground: 0 0% 98%;
--muted: 240 3.7% 15.9%;
--muted-foreground: 240 5% 64.9%;
--popover: 240 10% 3.9%;
--popover-foreground: 0 0% 98%;
--card: 240 10% 3.9%;
--card-foreground: 0 0% 98%;
--border: 240 3.7% 15.9%;
--input: 240 3.7% 15.9%;
--primary: 207 90% 54%;
--primary-foreground: 211 100% 99%;
--secondary: 240 3.7% 15.9%;
--secondary-foreground: 0 0% 98%;
--accent: 240 3.7% 15.9%;
--accent-foreground: 0 0% 98%;
--destructive: 0 62.8% 30.6%;
--destructive-foreground: 0 0% 98%;
--ring: 240 4.9% 83.9%;
--radius: 0.5rem;
--chart-1: 220 70% 50%;
--chart-2: 160 60% 45%;
--chart-3: 30 80% 55%;
--chart-4: 280 65% 60%;
--chart-5: 340 75% 55%;
--sidebar-background: 240 5.9% 10%;
--sidebar-foreground: 240 4.8% 95.9%;
--sidebar-primary: 224.3 76.3% 94.1%;
--sidebar-primary-foreground: 220.9 39.3% 11%;
--sidebar-accent: 240 3.7% 15.9%;
--sidebar-accent-foreground: 240 4.8% 95.9%;
--sidebar-border: 240 3.7% 15.9%;
--sidebar-ring: 217.2 32.6% 17.5%;
  }

/* Spectacular Animation Keyframes for Chat Interface */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }
  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(.9, .9, .9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(.97, .97, .97);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    /* Ensure body never shows scrollbars */
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
  
  body::-webkit-scrollbar {
    display: none !important;
  }
  
  html {
    /* Ensure html never shows scrollbars */
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
  
  html::-webkit-scrollbar {
    display: none !important;
  }
}

/* Clean scrollbar styles - HIDE ALL SCROLLBARS */
@layer utilities {
  /* Hide scrollbar completely but keep functionality - GLOBAL */
  * {
    /* For WebKit browsers */
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
  
  *::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }
  
  *::-webkit-scrollbar-track {
    display: none !important;
  }
  
  *::-webkit-scrollbar-thumb {
    display: none !important;
  }
  
  *::-webkit-scrollbar-corner {
    display: none !important;
  }
  
  /* Body and html no scrollbars */
  html, body {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
  
  html::-webkit-scrollbar,
  body::-webkit-scrollbar {
    display: none !important;
  }
  
  /* For Firefox */
  .scrollbar-thin {
    scrollbar-width: none !important;
  }
  
  /* For WebKit browsers */
  .scrollbar-thin::-webkit-scrollbar {
    display: none !important;
  }
  
  /* Hide scrollbar completely but keep functionality */
  .scrollbar-hide::-webkit-scrollbar {
    display: none !important;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  /* Apply to all scrollable elements */
  .overflow-y-auto,
  .overflow-x-auto,
  .overflow-auto,
  .overflow-y-scroll,
  .overflow-x-scroll,
  .overflow-scroll {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
  
  .overflow-y-auto::-webkit-scrollbar,
  .overflow-x-auto::-webkit-scrollbar,
  .overflow-auto::-webkit-scrollbar,
  .overflow-y-scroll::-webkit-scrollbar,
  .overflow-x-scroll::-webkit-scrollbar,
  .overflow-scroll::-webkit-scrollbar {
    display: none !important;
  }

  /* Professional Animation Utilities */
  .animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-bounceIn {
    animation: bounceIn 1s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.8s ease-out;
  }

  .animate-slideInRight {
    animation: slideInRight 0.8s ease-out;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-gradientShift {
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* Professional gradient backgrounds */
  .bg-professional-gradient {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
  }

  .bg-subtle-gradient {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  }

  /* Interactive hover states */
  .interactive-scale {
    transition: transform 0.2s ease-in-out;
  }

  .interactive-scale:hover {
    transform: scale(1.02);
  }

  .interactive-scale:active {
    transform: scale(0.98);
  }
}