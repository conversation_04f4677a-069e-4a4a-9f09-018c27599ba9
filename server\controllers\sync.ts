import { Request, Response } from "express";
import { syncOrchestratorService } from "../services/sync/sync-orchestrator.service.js";

/**
 * Modular sync controller that delegates to orchestrator service
 */
class SyncController {
  /**
   * Start a synchronization job for an integration
   */
  async startSync(integrationId: number): Promise<any> {
    return await syncOrchestratorService.startSync(integrationId);
  }

  /**
   * Get sync logs for all integrations (simplified implementation)
   */
  async getSyncLogs(req: Request, res: Response) {
    try {
      // Basic implementation without the complex sync routes service
      res.json({ 
        success: true, 
        logs: [], 
        message: 'Sync logs functionality simplified. Use sync orchestrator for basic operations.' 
      });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  /**
   * Get a specific sync log by ID (simplified implementation)
   */
  async getSyncLog(req: Request, res: Response) {
    try {
      const syncLogId = req.params.syncLogId;
      res.json({ 
        success: true, 
        syncLog: { id: syncLogId, status: 'unknown' },
        message: 'Sync log details simplified. Use database queries for detailed logs.' 
      });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  /**
   * Trigger a manual sync for an integration
   */
  async syncNow(req: Request, res: Response) {
    try {
      const { integrationId } = req.body;
      if (!integrationId) {
        return res.status(400).json({ success: false, error: 'Integration ID required' });
      }
      
      const result = await syncOrchestratorService.startSync(integrationId);
      res.json({ success: true, result });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  /**
   * Re-vectorize all files for all integrations (simplified implementation)
   */
  async reVectorizeAll(req: Request, res: Response) {
    try {
      // Simplified implementation - would need proper re-vectorization logic
      res.json({ 
        success: true, 
        message: 'Re-vectorization not implemented in simplified controller. Use individual sync operations.' 
      });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  /**
   * Get sync items for monitoring (simplified implementation)
   */
  async getSyncItems(req: Request, res: Response) {
    try {
      res.json({ 
        success: true, 
        items: [], 
        message: 'Sync items functionality simplified. Use individual integration sync status.' 
      });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
}

export const syncController = new SyncController(); 