import { cn } from "@/lib/utils";

interface LoadingSkeletonProps {
  className?: string;
  children?: React.ReactNode;
}

function LoadingSkeleton({ className, ...props }: LoadingSkeletonProps) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-muted", className)}
      {...props}
    />
  );
}

interface DashboardSkeletonProps {
  cards?: number;
}

function DashboardSkeleton({ cards = 3 }: DashboardSkeletonProps) {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="space-y-2">
        <LoadingSkeleton className="h-8 w-64" />
        <LoadingSkeleton className="h-4 w-96" />
      </div>
      
      {/* Cards skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: cards }).map((_, index) => (
          <div key={index} className="space-y-3 p-6 border border-border rounded-lg">
            <LoadingSkeleton className="h-6 w-32" />
            <LoadingSkeleton className="h-4 w-24" />
            <LoadingSkeleton className="h-12 w-16" />
            <div className="space-y-2">
              <LoadingSkeleton className="h-3 w-full" />
              <LoadingSkeleton className="h-3 w-3/4" />
            </div>
          </div>
        ))}
      </div>
      
      {/* Recent activity skeleton */}
      <div className="space-y-4">
        <LoadingSkeleton className="h-6 w-48" />
        <div className="space-y-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-3 p-4 border border-border rounded-lg">
              <LoadingSkeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <LoadingSkeleton className="h-4 w-3/4" />
                <LoadingSkeleton className="h-3 w-1/2" />
              </div>
              <LoadingSkeleton className="h-6 w-16" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function TableSkeleton({ rows = 5 }: { rows?: number }) {
  return (
    <div className="space-y-3">
      {/* Table header */}
      <div className="grid grid-cols-4 gap-4 p-4 border-b border-border">
        {Array.from({ length: 4 }).map((_, index) => (
          <LoadingSkeleton key={index} className="h-4 w-24" />
        ))}
      </div>
      
      {/* Table rows */}
      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="grid grid-cols-4 gap-4 p-4">
          {Array.from({ length: 4 }).map((_, colIndex) => (
            <LoadingSkeleton key={colIndex} className="h-4 w-full" />
          ))}
        </div>
      ))}
    </div>
  );
}

function ChatSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className={cn(
          "flex gap-3",
          index % 2 === 0 ? "justify-start" : "justify-end"
        )}>
          {index % 2 === 0 && <LoadingSkeleton className="h-8 w-8 rounded-full" />}
          <div className={cn(
            "max-w-sm space-y-2 p-3 rounded-lg",
            index % 2 === 0 ? "bg-muted" : "bg-primary/10"
          )}>
            <LoadingSkeleton className="h-4 w-full" />
            <LoadingSkeleton className="h-4 w-3/4" />
          </div>
          {index % 2 === 1 && <LoadingSkeleton className="h-8 w-8 rounded-full" />}
        </div>
      ))}
    </div>
  );
}

export { 
  LoadingSkeleton, 
  DashboardSkeleton, 
  TableSkeleton, 
  ChatSkeleton 
}; 