import { BaseService } from '../base/service.interface';
import fs from 'fs';
import path from 'path';

/**
 * Unified Content Extraction Service
 * Centralized service for extracting content from all file types across all platforms
 * Based on lobe-chat's robust file processing approach
 */

export interface ContentExtractionResult {
  success: boolean;
  content?: string;
  metadata?: {
    extractionMethod: string;
    quality: 'high' | 'medium' | 'low' | 'metadata-only';
    wordCount?: number;
    pageCount?: number;
    confidence?: number;
    fileType?: string;
    platform?: string;
    warnings?: string[];
  };
  error?: string;
}

export interface PlatformMetadata {
  platform: string;
  fileName: string;
  fileType: string;
  mimeType?: string;
  sourceUrl?: string;
  lastModified?: Date;
  owner?: string;
  folderPath?: string;
  sourceContext?: string;
  [key: string]: any;
}

export class UnifiedContentExtractorService extends BaseService {
  constructor() {
    super('UnifiedContentExtractor', '1.0.0', 'Centralized content extraction for all platforms and file types');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Unified Content Extractor service initialized');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      supportedFileTypes: [
        'pdf', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt',
        'txt', 'csv', 'md', 'html', 'json', 'xml', 'code'
      ],
      status: 'healthy'
    };
  }

  /**
   * Main content extraction method - works for all platforms and file types
   */
  async extractContent(
    platformMetadata: PlatformMetadata,
    fileBuffer?: Buffer,
    filePath?: string
  ): Promise<ContentExtractionResult> {
    this.ensureInitialized();
    
    this.log('info', `Starting content extraction for: ${platformMetadata.fileName}`);

    try {
      // Special handling for Google Docs/Slides that are already extracted as text
      if (platformMetadata.platform === 'google_drive' && fileBuffer) {
        const bufferContent = fileBuffer.toString('utf-8');
        
        // Check if this is already extracted Google Docs/Slides content
        if ((platformMetadata.mimeType?.includes('google-apps.document') || 
             platformMetadata.mimeType?.includes('google-apps.presentation') ||
             platformMetadata.mimeType?.includes('google-apps.spreadsheet')) && 
             bufferContent.length > 100 && 
             !bufferContent.includes('\x00') && // Not binary
             bufferContent.includes('google_drive')) {
          
          this.log('info', `Using pre-extracted Google content for: ${platformMetadata.fileName}`);
          
          return {
            success: true,
            content: bufferContent,
            metadata: {
              extractionMethod: 'google-api-pre-extracted',
              quality: 'high',
              wordCount: this.countWords(bufferContent),
              confidence: 0.95,
              fileType: platformMetadata.fileType,
              platform: platformMetadata.platform,
            }
          };
        }
      }

      // Determine file type for regular extraction
      const fileType = this.determineFileType(platformMetadata.fileName, platformMetadata.mimeType);
      
      this.log('info', `Determined file type: ${fileType} for ${platformMetadata.fileName} (mime: ${platformMetadata.mimeType})`);

      // Extract content based on file type
      const result = await this.extractByFileType(fileType, fileBuffer || null, platformMetadata, filePath);
      
      if (result.success) {
        // Enhance content with metadata
        const enhancedContent = this.enhanceContentWithMetadata(result.content || '', platformMetadata);
        result.content = enhancedContent;
      }
      
      this.log('info', `Content extraction completed for ${platformMetadata.fileName}: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      
      return result;

    } catch (error: any) {
      this.log('error', `Content extraction failed for ${platformMetadata.fileName}`, error);
      return this.createMetadataOnlyResult(platformMetadata, `Content extraction failed: ${error.message}`);
    }
  }

  /**
   * Determine file type based on filename extension and mime type (lobe-chat approach)
   */
  private determineFileType(filename: string, mimeType?: string): string {
    const extension = path.extname(filename).toLowerCase();
    const lowerMimeType = mimeType?.toLowerCase();

    // PDF files
    if (extension === '.pdf' || lowerMimeType?.includes('pdf')) {
      return 'pdf';
    }

    // Word documents (DOCX)
    if (extension === '.docx' || 
        lowerMimeType?.includes('wordprocessingml') ||
        lowerMimeType?.includes('msword') ||
        extension === '.doc') {
      return 'docx';
    }

    // Excel spreadsheets
    if (extension === '.xlsx' || extension === '.xls' ||
        lowerMimeType?.includes('spreadsheetml') ||
        lowerMimeType?.includes('excel')) {
      return 'excel';
    }

    // PowerPoint presentations
    if (extension === '.pptx' || extension === '.ppt' ||
        lowerMimeType?.includes('presentationml') ||
        lowerMimeType?.includes('powerpoint')) {
      return 'pptx';
    }

    // CSV files
    if (extension === '.csv' || lowerMimeType?.includes('csv')) {
      return 'csv';
    }

    // Text files
    if (extension === '.txt' || 
        lowerMimeType?.includes('text/plain') ||
        lowerMimeType?.includes('text/')) {
      return 'text';
    }

    // Markdown files
    if (extension === '.md' || extension === '.markdown' || extension === '.mdx') {
      return 'markdown';
    }

    // Code files
    if (['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h', 
         '.css', '.scss', '.less', '.html', '.xml', '.json', '.yaml', '.yml',
         '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.dart'].includes(extension)) {
      return 'code';
    }

    // Google Workspace files (already extracted content)
    if (lowerMimeType?.includes('google-apps')) {
      if (lowerMimeType.includes('document')) return 'text'; // Google Docs -> text
      if (lowerMimeType.includes('spreadsheet')) return 'excel'; // Google Sheets -> excel
      if (lowerMimeType.includes('presentation')) return 'pptx'; // Google Slides -> pptx
    }

    // Default to text for unknown types
    return 'text';
  }

  /**
   * Extract content based on file type with proper fallback strategies
   */
  private async extractByFileType(
    fileType: string,
    buffer: Buffer | null,
    platformMetadata: PlatformMetadata,
    filePath?: string
  ): Promise<ContentExtractionResult> {
    
    switch (fileType) {
      case 'pdf':
        return await this.extractPDFContent(buffer, platformMetadata, filePath);
      case 'docx':
        return await this.extractDocxContent(buffer, platformMetadata, filePath);
      case 'excel':
        return await this.extractExcelContent(buffer, platformMetadata, filePath);
      case 'pptx':
        return await this.extractPptxContent(buffer, platformMetadata, filePath);
      case 'csv':
        return await this.extractCSVContent(buffer, platformMetadata);
      case 'markdown':
        return await this.extractMarkdownContent(buffer, platformMetadata);
      case 'code':
        return await this.extractCodeContent(buffer, platformMetadata);
      case 'text':
      default:
        return await this.extractTextContent(buffer, platformMetadata);
    }
  }

  /**
   * Extract PDF content using multiple extraction methods with enhanced error handling
   */
  private async extractPDFContent(buffer: Buffer | null, platformMetadata: PlatformMetadata, filePath?: string): Promise<ContentExtractionResult> {
    try {
      const pdfBuffer = this.getBuffer(buffer, filePath, platformMetadata.fileName);
      this.validatePDFBuffer(pdfBuffer, platformMetadata.fileName);

      this.log('info', `Processing PDF: ${platformMetadata.fileName} (${pdfBuffer.length} bytes)`);
      const extractionAttempts: string[] = [];

      // Method 1: Try basic PDF text extraction using buffer parsing
      try {
        this.log('info', `Attempting buffer extraction for: ${platformMetadata.fileName}`);
        const extractedText = await this.extractPDFTextFromBuffer(pdfBuffer);
        if (extractedText && extractedText.length > 10) {
          this.log('info', `✅ Buffer extraction successful: ${extractedText.length} characters`);
          return this.createExtractionResult(extractedText, 'buffer-text-extraction', 'medium', 0.7, 'pdf', platformMetadata.platform);
        }
        extractionAttempts.push('buffer-extraction: insufficient text');
      } catch (bufferError: any) {
        extractionAttempts.push(`buffer-extraction: ${bufferError.message}`);
        this.log('warn', `Buffer extraction failed for ${platformMetadata.fileName}: ${bufferError.message}`);
      }

      // Method 2: Try pdf-parse with isolated context
      try {
        this.log('info', `Attempting pdf-parse extraction for: ${platformMetadata.fileName}`);
        const { extractPDFWithLibrary } = await this.createIsolatedPDFExtractor();
        const libraryResult = await extractPDFWithLibrary(pdfBuffer);

        if (libraryResult && libraryResult.length > 10) {
          this.log('info', `✅ pdf-parse extraction successful: ${libraryResult.length} characters`);
          return this.createExtractionResult(libraryResult, 'isolated-pdf-parse', 'high', 0.9, 'pdf', platformMetadata.platform);
        }
        extractionAttempts.push('pdf-parse: insufficient text');
      } catch (libraryError: any) {
        extractionAttempts.push(`pdf-parse: ${libraryError.message}`);
        this.log('warn', `Isolated PDF extraction failed for ${platformMetadata.fileName}: ${libraryError.message}`);
      }

      // Method 3: Try pdfjs-dist as another fallback
      try {
        this.log('info', `Attempting pdfjs-dist extraction for: ${platformMetadata.fileName}`);
        const pdfjsResult = await this.extractPDFWithPDFJS(pdfBuffer);
        if (pdfjsResult && pdfjsResult.length > 10) {
          this.log('info', `✅ pdfjs-dist extraction successful: ${pdfjsResult.length} characters`);
          return this.createExtractionResult(pdfjsResult, 'pdfjs-dist', 'high', 0.8, 'pdf', platformMetadata.platform);
        }
        extractionAttempts.push('pdfjs-dist: insufficient text');
      } catch (pdfjsError: any) {
        extractionAttempts.push(`pdfjs-dist: ${pdfjsError.message}`);
        this.log('warn', `PDFJS extraction failed for ${platformMetadata.fileName}: ${pdfjsError.message}`);
      }

      // Method 4: Fallback to basic pattern matching
      try {
        this.log('info', `Attempting fallback pattern matching for: ${platformMetadata.fileName}`);
        const fallbackText = this.extractPDFTextFallback(pdfBuffer);
        if (fallbackText && fallbackText.length > 5) {
          this.log('info', `✅ Fallback extraction successful: ${fallbackText.length} characters`);
          return this.createExtractionResult(
            fallbackText,
            'fallback-pattern-matching',
            'low',
            0.3,
            'pdf',
            platformMetadata.platform,
            ['Used fallback extraction method, text quality may be limited']
          );
        }
        extractionAttempts.push('fallback: insufficient text');
      } catch (fallbackError: any) {
        extractionAttempts.push(`fallback: ${fallbackError.message}`);
        this.log('warn', `Fallback extraction failed for ${platformMetadata.fileName}: ${fallbackError.message}`);
      }

      // If all methods fail, return metadata only with detailed error info
      const errorMessage = `All PDF extraction methods failed. Attempts: ${extractionAttempts.join('; ')}`;
      this.log('error', `❌ PDF extraction completely failed for ${platformMetadata.fileName}: ${errorMessage}`);
      return this.createMetadataOnlyResult(platformMetadata, errorMessage);

    } catch (error: any) {
      this.log('error', `❌ Critical PDF extraction error for ${platformMetadata.fileName}: ${error.message}`);
      return this.createMetadataOnlyResult(platformMetadata, `PDF extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract text from PDF buffer using direct parsing
   */
  private async extractPDFTextFromBuffer(buffer: Buffer): Promise<string> {
    const pdfString = buffer.toString('latin1');

    // Look for text objects in PDF using multiple patterns
    const textPatterns = [
      /BT\s*(.*?)\s*ET/g,  // Standard text objects
      /\((.*?)\)\s*Tj/g,    // Text showing operators
      /\[(.*?)\]\s*TJ/g,    // Array text showing
      /\/F\d+\s+\d+\s+Tf\s*(.*?)(?=\/F\d+|\s*BT|\s*ET|$)/g  // Font changes
    ];

    const allMatches: RegExpMatchArray[] = [];

    for (const pattern of textPatterns) {
      let match;
      while ((match = pattern.exec(pdfString)) !== null) {
        allMatches.push(match);
      }
    }

    if (allMatches.length === 0) {
      throw new Error('No text objects found in PDF');
    }

    const extractedTexts: string[] = [];

    for (const matchResult of allMatches) {
      const text = matchResult[1]
        ?.replace(/\\[0-9]{3}/g, ' ')  // Replace octal escapes
        ?.replace(/\\[rntf]/g, ' ')    // Replace escape sequences
        ?.replace(/\s+/g, ' ')         // Normalize whitespace
        ?.trim();

      if (text && text.length > 1) {
        extractedTexts.push(text);
      }
    }

    const result = extractedTexts.join(' ').trim();
    if (result.length < 10) {
      throw new Error('Insufficient text extracted from PDF');
    }

    return result;
  }

  /**
   * Create an isolated PDF extractor to avoid library conflicts
   */
  private async createIsolatedPDFExtractor() {
    return {
      extractPDFWithLibrary: async (buffer: Buffer): Promise<string> => {
        try {
          this.log('info', `Processing PDF buffer of ${buffer.length} bytes`);

          // Dynamic import in isolated context
          const pdfParseModule = await Function('return import("pdf-parse")')();
          const pdfParse = pdfParseModule.default;

          const data = await pdfParse(buffer, {
            max: 0, // No page limit
            normalizeWhitespace: false,
            disableCombineTextItems: false
          });

          const extractedText = data.text || '';
          this.log('info', `PDF extraction successful: ${extractedText.length} characters extracted`);

          if (extractedText.length < 10) {
            throw new Error('Insufficient text extracted from PDF');
          }

          return extractedText;
        } catch (error: any) {
          this.log('error', `Isolated PDF extraction failed: ${error.message}`);
          throw new Error(`Isolated PDF extraction failed: ${error.message}`);
        }
      }
    };
  }

  /**
   * Extract PDF content using pdfjs-dist library
   */
  private async extractPDFWithPDFJS(buffer: Buffer): Promise<string> {
    try {
      // Add Promise.withResolvers polyfill if not available
      if (!Promise.withResolvers) {
        Promise.withResolvers = function<T>() {
          let resolve: (value: T | PromiseLike<T>) => void;
          let reject: (reason?: any) => void;
          const promise = new Promise<T>((res, rej) => {
            resolve = res;
            reject = rej;
          });
          return { promise, resolve: resolve!, reject: reject! };
        };
      }

      // Dynamic import to avoid module loading issues
      const pdfjsLib = await import('pdfjs-dist');

      // Convert buffer to Uint8Array
      const data = new Uint8Array(buffer);

      // Load the PDF document
      const pdf = await pdfjsLib.getDocument({ data }).promise;

      let fullText = '';

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');

        fullText += `${pageText}\n`;
      }

      const result = fullText.trim();
      this.log('info', `PDFJS extracted ${result.length} characters from ${pdf.numPages} pages`);

      return result;
    } catch (error: any) {
      this.log('error', `PDFJS extraction failed: ${error.message}`);
      throw new Error(`PDFJS extraction failed: ${error.message}`);
    }
  }

  /**
   * Fallback PDF text extraction using pattern matching
   */
  private extractPDFTextFallback(buffer: Buffer): string {
    const pdfString = buffer.toString('binary');
    
    // Try to find readable text patterns
    const patterns = [
      /stream\s*(.*?)\s*endstream/g,
      /\((.*?)\)/g,
      /\[(.*?)\]/g
    ];

    const extractedText: string[] = [];

    for (const pattern of patterns) {
      const matches: RegExpMatchArray[] = [];
      let match;
      while ((match = pattern.exec(pdfString)) !== null) {
        matches.push(match);
      }
      
      for (const matchResult of matches) {
        const text = matchResult[1]
          ?.replace(/[^\x20-\x7E]/g, ' ')
          ?.replace(/\s+/g, ' ')
          ?.trim();
        
        if (text && text.length > 3 && /[a-zA-Z]/.test(text)) {
          extractedText.push(text);
        }
      }
    }

    return extractedText.slice(0, 50).join(' ').substring(0, 1000);
  }

  /**
   * Extract DOCX content using mammoth (lobe-chat approach)
   */
  private async extractDocxContent(buffer: Buffer | null, platformMetadata: PlatformMetadata, filePath?: string): Promise<ContentExtractionResult> {
    try {
      const mammoth = await import('mammoth');
      const docxBuffer = this.getBuffer(buffer, filePath, platformMetadata.fileName);

      const result = await mammoth.extractRawText({ buffer: docxBuffer });
      
      if (result.value && result.value.length > 0) {
        return this.createExtractionResult(
          result.value,
          'mammoth',
          'high',
          0.9,
          'docx',
          platformMetadata.platform,
          result.messages?.length > 0 ? ['Some formatting may be lost'] : undefined
        );
      }

      throw new Error('No text extracted from DOCX');

    } catch (error: any) {
      this.log('error', `DOCX extraction failed for ${platformMetadata.fileName}`, error);
      return this.createMetadataOnlyResult(platformMetadata, `DOCX extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract Excel content using xlsx library (lobe-chat approach)
   */
  private async extractExcelContent(buffer: Buffer | null, platformMetadata: PlatformMetadata, filePath?: string): Promise<ContentExtractionResult> {
    try {
      const xlsx = await import('xlsx');
      const excelBuffer = this.getBuffer(buffer, filePath, platformMetadata.fileName);

      const workbook = xlsx.read(excelBuffer, { type: 'buffer' });
      const sheets: string[] = [];

      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = xlsx.utils.sheet_to_json(worksheet, { defval: '', raw: false }) as Record<string, any>[];

        if (jsonData.length > 0) {
          const markdownTable = this.convertToMarkdownTable(jsonData, sheetName);
          sheets.push(markdownTable);
        }
      }

      const content = sheets.join('\n\n');
      
      if (content.length > 0) {
        return this.createExtractionResult(content, 'xlsx', 'high', 0.9, 'excel', platformMetadata.platform);
      }

      throw new Error('No data extracted from Excel file');

    } catch (error: any) {
      this.log('error', `Excel extraction failed for ${platformMetadata.fileName}`, error);
      return this.createMetadataOnlyResult(platformMetadata, `Excel extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract PowerPoint content using officeparser with enhanced error handling
   */
  private async extractPptxContent(buffer: Buffer | null, platformMetadata: PlatformMetadata, filePath?: string): Promise<ContentExtractionResult> {
    try {
      const officeParser = await import('officeparser');
      const pptxBuffer = this.getBuffer(buffer, filePath, platformMetadata.fileName);

      // Basic validation
      if (pptxBuffer.length < 100) {
        throw new Error('PPTX buffer too small, likely corrupted');
      }

      const text = await officeParser.parseOfficeAsync(pptxBuffer);
      
      if (text && text.length > 0) {
        return this.createExtractionResult(text, 'officeparser', 'medium', 0.7, 'pptx', platformMetadata.platform);
      }

      throw new Error('No text extracted from PPTX');

    } catch (error: any) {
      this.log('warn', `❌ PPTX extraction failed for ${platformMetadata.fileName}`, error.message);
      
      // For Google Slides files, try a fallback approach
      if (platformMetadata.platform === 'google_drive' && 
          platformMetadata.mimeType?.includes('google-apps.presentation')) {
        this.log('info', `Attempting Google Slides fallback for: ${platformMetadata.fileName}`);
        
        // If buffer contains readable text (pre-extracted Google Slides), use it
        if (buffer) {
          const bufferText = buffer.toString('utf-8');
          if (bufferText.length > 50 && !bufferText.includes('\x00')) {
            return this.createExtractionResult(
              bufferText,
              'google-slides-fallback',
              'high',
              0.8,
              'pptx',
              platformMetadata.platform,
              ['Used fallback extraction due to OfficeParser incompatibility']
            );
          }
        }
      }
      
      // Return metadata-only result with helpful information
      return this.createMetadataOnlyResult(
        platformMetadata, 
        `PPTX extraction failed: ${error.message}. File is searchable by name and metadata.`
      );
    }
  }

  /**
   * Extract CSV content with proper formatting
   */
  private async extractCSVContent(buffer: Buffer | null, platformMetadata: PlatformMetadata): Promise<ContentExtractionResult> {
    if (!buffer) {
      return this.createMetadataOnlyResult(platformMetadata, 'No buffer provided for CSV extraction');
    }

    try {
      const content = buffer.toString('utf-8');
      const lines = content.split('\n').filter(line => line.trim().length > 0);
      
      if (lines.length > 0) {
        const markdownTable = this.convertCSVToMarkdown(content);
        return this.createExtractionResult(markdownTable, 'csv-parser', 'high', 0.9, 'csv', platformMetadata.platform);
      }

      throw new Error('CSV file is empty');

    } catch (error: any) {
      this.log('error', `CSV extraction failed for ${platformMetadata.fileName}`, error);
      return this.createMetadataOnlyResult(platformMetadata, `CSV extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract markdown content
   */
  private async extractMarkdownContent(buffer: Buffer | null, platformMetadata: PlatformMetadata): Promise<ContentExtractionResult> {
    if (!buffer) {
      return this.createMetadataOnlyResult(platformMetadata, 'No buffer provided for Markdown extraction');
    }

    try {
      const content = buffer.toString('utf-8');
      return this.createExtractionResult(content, 'text-decode', 'high', 1.0, 'markdown', platformMetadata.platform);

    } catch (error: any) {
      this.log('error', `Markdown extraction failed for ${platformMetadata.fileName}`, error);
      return this.createMetadataOnlyResult(platformMetadata, `Markdown extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract code content with syntax awareness
   */
  private async extractCodeContent(buffer: Buffer | null, platformMetadata: PlatformMetadata): Promise<ContentExtractionResult> {
    if (!buffer) {
      return this.createMetadataOnlyResult(platformMetadata, 'No buffer provided for code extraction');
    }

    try {
      const content = buffer.toString('utf-8');
      const extension = path.extname(platformMetadata.fileName).toLowerCase().slice(1);
      
      const formattedContent = `\`\`\`${extension}\n${content}\n\`\`\``;
      return this.createExtractionResult(formattedContent, 'code-parser', 'high', 1.0, 'code', platformMetadata.platform);

    } catch (error: any) {
      this.log('error', `Code extraction failed for ${platformMetadata.fileName}`, error);
      return this.createMetadataOnlyResult(platformMetadata, `Code extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract plain text content
   */
  private async extractTextContent(buffer: Buffer | null, platformMetadata: PlatformMetadata): Promise<ContentExtractionResult> {
    if (!buffer) {
      return this.createMetadataOnlyResult(platformMetadata, 'No buffer provided for text extraction');
    }

    try {
      // Try different encodings
      let content: string;
      try {
        content = buffer.toString('utf-8');
      } catch {
        content = buffer.toString('latin1');
      }

      if (content.length > 0) {
        return this.createExtractionResult(content, 'text-decode', 'high', 0.9, 'text', platformMetadata.platform);
      }

      throw new Error('Text file is empty');

    } catch (error: any) {
      this.log('error', `Text extraction failed for ${platformMetadata.fileName}`, error);
      return this.createMetadataOnlyResult(platformMetadata, `Text extraction failed: ${error.message}`);
    }
  }

  /**
   * Convert JSON data to markdown table (lobe-chat approach)
   */
  private convertToMarkdownTable(jsonData: Record<string, any>[], sheetName?: string): string {
    if (!jsonData || jsonData.length === 0) {
      return sheetName ? `## ${sheetName}\n*Sheet is empty or contains no data.*` : '*No data available.*';
    }

    const headers = Object.keys(jsonData[0] || {});
    if (headers.length === 0) {
      return sheetName ? `## ${sheetName}\n*Sheet has no headers.*` : '*No headers available.*';
    }

    const headerRow = `| ${headers.join(' | ')} |`;
    const separatorRow = `| ${headers.map(() => '---').join(' | ')} |`;

    const dataRows = jsonData
      .map(row => {
        const cells = headers.map(header => {
          const value = row[header];
          return value === null || value === undefined ? '' : String(value).replace(/\|/g, '\\|').trim();
        });
        return `| ${cells.join(' | ')} |`;
      })
      .join('\n');

    const table = `${headerRow}\n${separatorRow}\n${dataRows}`;
    return sheetName ? `## ${sheetName}\n\n${table}` : table;
  }

  /**
   * Convert CSV to markdown table
   */
  private convertCSVToMarkdown(csvContent: string): string {
    const lines = csvContent.split('\n').filter(line => line.trim().length > 0);
    if (lines.length === 0) return '*CSV file is empty.*';

    const parseCSVRow = (row: string): string[] => {
      const result: string[] = [];
      let current = '';
      let inQuotes = false;
      
      for (let i = 0; i < row.length; i++) {
        const char = row[i];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          result.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      result.push(current.trim());
      return result;
    };

    const rows = lines.map(parseCSVRow);
    const headers = rows[0];
    const dataRows = rows.slice(1);

    if (headers.length === 0) return '*CSV has no headers.*';

    const headerRow = `| ${headers.join(' | ')} |`;
    const separatorRow = `| ${headers.map(() => '---').join(' | ')} |`;
    const dataRowsText = dataRows
      .map(row => `| ${row.map(cell => cell.replace(/\|/g, '\\|')).join(' | ')} |`)
      .join('\n');

    return `${headerRow}\n${separatorRow}\n${dataRowsText}`;
  }

  /**
   * Create metadata-only result when content extraction fails
   */
  private createMetadataOnlyResult(platformMetadata: PlatformMetadata, reason: string): ContentExtractionResult {
    return {
      success: false,
      content: this.buildMetadataContent(platformMetadata),
      metadata: {
        extractionMethod: 'metadata-only',
        quality: 'metadata-only',
        fileType: platformMetadata.fileType,
        platform: platformMetadata.platform,
        warnings: [reason],
      },
      error: reason
    };
  }

  /**
   * Build metadata content when file content cannot be extracted
   */
  private buildMetadataContent(platformMetadata: PlatformMetadata): string {
    const parts = [
      `File: ${platformMetadata.fileName}`,
      `Type: ${platformMetadata.fileType}`,
      `Platform: ${platformMetadata.platform}`,
    ];

    if (platformMetadata.owner) parts.push(`Owner: ${platformMetadata.owner}`);
    if (platformMetadata.folderPath) parts.push(`Location: ${platformMetadata.folderPath}`);
    if (platformMetadata.lastModified) parts.push(`Modified: ${platformMetadata.lastModified.toISOString()}`);
    if (platformMetadata.sourceUrl) parts.push(`Source: ${platformMetadata.sourceUrl}`);

    return parts.join('\n');
  }

  /**
   * Enhance content with metadata context
   */
  private enhanceContentWithMetadata(content: string, platformMetadata: PlatformMetadata): string {
    const header = `[File: ${platformMetadata.fileName} | Platform: ${platformMetadata.platform}${platformMetadata.folderPath ? ` | Location: ${platformMetadata.folderPath}` : ''}]\n\n`;
    return header + content;
  }

  /**
   * Common buffer handling logic
   */
  private getBuffer(buffer: Buffer | null, filePath?: string, fileName?: string): Buffer {
    if (buffer) return buffer;

    if (filePath) {
      this.log('info', `Reading file from path: ${filePath}`);
      return fs.readFileSync(filePath);
    }

    throw new Error(`No buffer or file path provided for ${fileName || 'file'}`);
  }

  /**
   * Validate PDF buffer with common checks
   */
  private validatePDFBuffer(buffer: Buffer, fileName: string): void {
    if (buffer.length < 100) {
      throw new Error(`PDF buffer too small for ${fileName}: ${buffer.length} bytes`);
    }

    const pdfHeader = buffer.subarray(0, 4).toString();
    if (!pdfHeader.startsWith('%PDF')) {
      throw new Error(`Invalid PDF format for ${fileName}. Header: ${pdfHeader}`);
    }
  }

  /**
   * Create extraction result with consistent metadata
   */
  private createExtractionResult(
    content: string,
    method: string,
    quality: 'high' | 'medium' | 'low',
    confidence: number,
    fileType: string,
    platform: string,
    warnings?: string[]
  ): ContentExtractionResult {
    return {
      success: true,
      content,
      metadata: {
        extractionMethod: method,
        quality,
        wordCount: this.countWords(content),
        confidence,
        fileType,
        platform,
        warnings
      }
    };
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }
}

// Export singleton instance
export const unifiedContentExtractor = new UnifiedContentExtractorService(); 