import { MCPServerConfig } from '../../services/mcp/types';
/**
 * MCP Server Configurations
 */
export const mcpServers: MCPServerConfig[] = [
  // File Upload MCP Server
  {
    name: 'file-upload',
    type: 'local',
    version: '1.0.0',
    connection: {
      command: 'node',
      args: ['dist/mcp-servers/file-upload/file-upload-server.js'],
    },
    enabled: true
  },
  
  // Google Drive MCP Server
  {
    name: 'google-drive',
    type: 'local',
    version: '1.0.0',
    connection: {
      command: 'node',
      args: ['dist/mcp-servers/google-drive/google-drive-server.js'],
      credentials: 'google'
    },
    enabled: true
  },
  
  // Microsoft Teams MCP Server
  {
    name: 'microsoft-teams',
    type: 'local',
    connection: {
      command: 'node',
      args: ['dist/mcp-servers/microsoft-teams/microsoft-teams-server.js'],
      credentials: 'microsoft'
    },
    enabled: false,
    version: '1.0.0'
  },

  // Google Calendar MCP Server
  {
    name: 'google-calendar',
    type: 'local',
    connection: {
      command: 'node',
      args: ['dist/mcp-servers/google-calendar/google-calendar-server.js'],
      credentials: 'google'
    },
    enabled: false,
    version: '1.0.0'
  }
];