import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Zap, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ActiveToolIndicatorProps {
  activeTools: string[];
  className?: string;
}

export function ActiveToolIndicator({ activeTools, className }: ActiveToolIndicatorProps) {
  if (activeTools.length === 0) return null;

  return (
    <Badge 
      variant="secondary" 
      className={cn(
        "inline-flex items-center space-x-2 px-3 py-1 text-xs font-medium",
        "bg-purple-100 text-purple-800 border-purple-200",
        className
      )}
    >
      <div className="flex items-center space-x-1">
        <Loader2 className="w-3 h-3 animate-spin" />
        <Zap className="w-3 h-3" />
      </div>
      <span>
        Using {activeTools.length} tool{activeTools.length !== 1 ? 's' : ''}
      </span>
      <div className="flex space-x-1">
        <div className="w-1.5 h-1.5 bg-purple-600 rounded-full animate-bounce"></div>
        <div className="w-1.5 h-1.5 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-1.5 h-1.5 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
    </Badge>
  );
}
