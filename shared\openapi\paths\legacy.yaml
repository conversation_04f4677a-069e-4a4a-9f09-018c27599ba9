ChatSessions:
  get:
    tags:
      - Legacy
    summary: Get chat sessions (legacy)
    description: Legacy endpoint for retrieving chat sessions
    operationId: getLegacyChatSessions
    deprecated: true
    responses:
      '200':
        description: Chat sessions retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                sessions:
                  type: array
                  items:
                    $ref: '../components/schemas.yaml#/ChatSession'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

Projects:
  get:
    tags:
      - Legacy
    summary: Get projects (legacy)
    description: Legacy endpoint for retrieving projects
    operationId: getLegacyProjects
    deprecated: true
    responses:
      '200':
        description: Projects retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                projects:
                  type: array
                  items:
                    type: object
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 