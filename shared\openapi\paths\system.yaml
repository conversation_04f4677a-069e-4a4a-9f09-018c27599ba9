Info:
  get:
    tags:
      - System
    summary: Get API information
    description: Returns basic information about the MeetSync API server
    operationId: getApiInfo
    responses:
      '200':
        description: API information retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                version:
                  type: string
                status:
                  type: string
                timestamp:
                  type: string
                  format: date-time
                endpoints:
                  type: object
                  additionalProperties:
                    type: string
            examples:
              success:
                summary: Successful response
                value:
                  success: true
                  message: "MeetSync Web App API Server - UPDATED CODE LOADED"
                  version: "1.0.0"
                  status: "operational"
                  timestamp: "2024-01-01T00:00:00Z"
                  endpoints:
                    health: "/api/health"
                    integrations: "/api/integrations"
                    files: "/api/files"
                    chat: "/api/chat/sessions"
                    upload: "/api/files/upload"

Health:
  get:
    tags:
      - System
    summary: Get application health status
    description: Returns comprehensive health status of the application and its services
    operationId: getHealth
    responses:
      '200':
        $ref: '../components/responses.yaml#/HealthResponse'
      '503':
        $ref: '../components/responses.yaml#/ServiceUnavailable'

HealthLive:
  get:
    tags:
      - System
    summary: Liveness probe
    description: Kubernetes-style liveness probe to check if the application is running
    operationId: getLiveness
    responses:
      '200':
        description: Application is alive
        content:
          application/json:
            schema:
              type: object
              properties:
                alive:
                  type: boolean
                timestamp:
                  type: string
                  format: date-time
      '503':
        description: Application is not alive
        content:
          application/json:
            schema:
              type: object
              properties:
                alive:
                  type: boolean
                timestamp:
                  type: string
                  format: date-time

HealthReady:
  get:
    tags:
      - System
    summary: Readiness probe
    description: Kubernetes-style readiness probe to check if the application is ready to serve traffic
    operationId: getReadiness
    responses:
      '200':
        description: Application is ready
        content:
          application/json:
            schema:
              type: object
              properties:
                ready:
                  type: boolean
                timestamp:
                  type: string
                  format: date-time
                services:
                  type: object
      '503':
        description: Application is not ready
        content:
          application/json:
            schema:
              type: object
              properties:
                ready:
                  type: boolean
                timestamp:
                  type: string
                  format: date-time
                services:
                  type: object

Readiness:
  get:
    tags:
      - System
    summary: Readiness check (alias)
    description: Alternative endpoint for readiness checks
    operationId: getReadinessAlias
    responses:
      '200':
        description: Application is ready
        content:
          application/json:
            schema:
              type: object
              properties:
                ready:
                  type: boolean
                timestamp:
                  type: string
                  format: date-time
      '503':
        $ref: '../components/responses.yaml#/ServiceUnavailable'

Liveness:
  get:
    tags:
      - System
    summary: Liveness check (alias)
    description: Alternative endpoint for liveness checks
    operationId: getLivenessAlias
    responses:
      '200':
        description: Application is alive
        content:
          application/json:
            schema:
              type: object
              properties:
                alive:
                  type: boolean
                timestamp:
                  type: string
                  format: date-time
      '503':
        description: Application is not alive 