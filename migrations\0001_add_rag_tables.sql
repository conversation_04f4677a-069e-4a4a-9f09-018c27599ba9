-- Add missing status column to files table
ALTER TABLE "files" ADD COLUMN "status" text DEFAULT 'active' NOT NULL;

-- Create file_chunks table for RAG embeddings
CREATE TABLE "file_chunks" (
	"id" serial PRIMARY KEY NOT NULL,
	"file_id" integer NOT NULL,
	"chunk_index" integer NOT NULL,
	"content" text NOT NULL,
	"embedding" vector(1536),
	"token_count" integer,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Create chat_sessions table
CREATE TABLE "chat_sessions" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" varchar(255),
	"title" varchar(500),
	"enabled_sources" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create chat_messages table
CREATE TABLE "chat_messages" (
	"id" serial PRIMARY KEY NOT NULL,
	"session_id" integer NOT NULL,
	"role" varchar(20) NOT NULL,
	"content" text NOT NULL,
	"sources_used" jsonb,
	"relevant_chunks" jsonb,
	"token_count" integer,
	"model" varchar(100) DEFAULT 'gpt-4.1-nano',
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Create projects table
CREATE TABLE "projects" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"user_id" varchar(255),
	"enabled_sources" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
ALTER TABLE "file_chunks" ADD CONSTRAINT "file_chunks_file_id_files_id_fk" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_session_id_chat_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "chat_sessions"("id") ON DELETE cascade ON UPDATE no action;

-- Create indexes for better performance
CREATE INDEX "file_chunks_file_id_idx" ON "file_chunks" ("file_id");
CREATE INDEX "file_chunks_embedding_idx" ON "file_chunks" USING ivfflat ("embedding" vector_cosine_ops) WITH (lists = 100);
CREATE INDEX "chat_messages_session_id_idx" ON "chat_messages" ("session_id");
CREATE INDEX "chat_sessions_user_id_idx" ON "chat_sessions" ("user_id");
CREATE INDEX "projects_user_id_idx" ON "projects" ("user_id");
