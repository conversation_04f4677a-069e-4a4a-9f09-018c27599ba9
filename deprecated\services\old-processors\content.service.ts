import { Client } from '@microsoft/microsoft-graph-client';
import { BaseService } from "../base/service.interface";
import type { MicrosoftCredentials } from './oauth.service';

/**
 * Microsoft Content Extraction Service - handles content extraction from Microsoft files
 */
export class MicrosoftContentService extends BaseService {
  constructor() {
    super('MicrosoftContent', '1.0.0', 'Microsoft content extraction from Teams, OneDrive, and SharePoint files');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Microsoft Content service initialized');
  }

  protected getDependencies(): string[] {
    return ['MicrosoftOAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      supportedFileTypes: ['pdf', 'docx', 'doc', 'txt', 'xlsx', 'pptx'],
      extractionMethods: ['text', 'metadata'],
    };
  }

  /**
   * Download and extract file content from Microsoft Graph
   */
  async downloadAndExtractFileContent(
    client: Client,
    driveId: string,
    fileId: string,
    fileName: string,
    mimeType?: string
  ): Promise<string | null> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(driveId, 'drive ID');
    this.validateString(fileId, 'file ID');
    this.validateString(fileName, 'file name');

    try {
      // Import the centralized services and classes
          const { unifiedContentExtractor } = await import('../unified-content-extractor.service');

      // Check if it's a PDF file using static method
      const isPdf = PDFService.isPDFFile(fileName, mimeType);

      // Check if it's a Word document using static method
      const isWord = WordService.isWordFile(fileName, mimeType);

      if (!isPdf && !isWord) {
        this.log('info', `Skipping content extraction for unsupported file type: ${fileName}`);
        return null;
      }
      
      const fileType = isPdf ? 'PDF' : 'Word document';
      this.log('info', `Downloading ${fileType} content for: ${fileName}`);
      this.log('info', `Using driveId: ${driveId}, fileId: ${fileId}`);

      // Download the file content with proper response type
      const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
        .responseType('arraybuffer' as any)
        .get();

      this.log('info', `Download response type: ${typeof downloadResponse}`);
      this.log('info', `Download response constructor: ${downloadResponse?.constructor?.name}`);

      if (!downloadResponse) {
        this.log('info', `No content received for file: ${fileName}`);
        return null;
      }

      // Convert response to buffer
      let buffer: Buffer;

      if (Buffer.isBuffer(downloadResponse)) {
        this.log('info', `Response is already a Buffer`);
        buffer = downloadResponse;
      } else if (downloadResponse instanceof ArrayBuffer) {
        this.log('info', `Response is ArrayBuffer, converting to Buffer`);
        buffer = Buffer.from(downloadResponse);
      } else if (downloadResponse instanceof Uint8Array) {
        this.log('info', `Response is Uint8Array, converting to Buffer`);
        buffer = Buffer.from(downloadResponse);
      } else if (typeof downloadResponse === 'string') {
        this.log('info', `Response is string, converting to Buffer`);
        buffer = Buffer.from(downloadResponse, 'binary');
      } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
        this.log('info', `Response has buffer property, using that`);
        buffer = Buffer.from(downloadResponse.buffer);
      } else {
        this.log('info', `Unexpected response type for file: ${fileName}`);
        this.log('info', `Response details:`, {
          type: typeof downloadResponse,
          constructor: downloadResponse?.constructor?.name,
          hasBuffer: !!(downloadResponse && typeof downloadResponse === 'object' && 'buffer' in downloadResponse),
          keys: downloadResponse && typeof downloadResponse === 'object' ? Object.keys(downloadResponse).slice(0, 5) : []
        });
        return null;
      }

      this.log('info', `Downloaded ${buffer.length} bytes for: ${fileName}`);

      // Extract text based on file type
      let extractionResult;

      if (isPdf) {
        // Use centralized PDF service to extract text
        extractionResult = await pdfService.extractTextFromPDF(buffer, fileName);
      } else if (isWord) {
        // Use centralized Word service to extract text
        extractionResult = await wordService.extractTextFromWord(buffer, fileName);
      }

      if (!extractionResult || !extractionResult.success || !extractionResult.extractedText) {
        this.log('info', `${fileType} text extraction failed: ${extractionResult?.error || 'Unknown error'}`);
        return null;
      }

      this.log('info', `Successfully extracted ${extractionResult.extractedText.length} characters from ${fileType}: ${fileName}`);
      return extractionResult.extractedText;
      
    } catch (error: any) {
      this.handleError(error, `downloading/extracting content for ${fileName}`);
    }
  }

  /**
   * Get enhanced meeting metadata from Microsoft Graph
   */
  async getEnhancedMeetingMetadata(
    client: Client,
    fileName: string,
    fileCreatedDate?: Date
  ): Promise<Record<string, any>> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(fileName, 'file name');

    try {
      this.log('info', `Getting enhanced meeting metadata for: ${fileName}`);
      
      const meetingData: Record<string, any> = {};

      // Try to find related calendar events
      if (fileCreatedDate) {
        try {
          // Search for calendar events around the file creation time
          const startTime = new Date(fileCreatedDate.getTime() - 2 * 60 * 60 * 1000); // 2 hours before
          const endTime = new Date(fileCreatedDate.getTime() + 2 * 60 * 60 * 1000); // 2 hours after

          const eventsResponse = await client.api('/me/calendar/events')
            .filter(`start/dateTime ge '${startTime.toISOString()}' and end/dateTime le '${endTime.toISOString()}'`)
            .select('subject,start,end,organizer,attendees,isOnlineMeeting,onlineMeeting')
            .get();

          const events = eventsResponse.value || [];
          
          // Try to find the most relevant event
          for (const event of events) {
            if (this.isRelatedMeetingEvent(fileName, event)) {
              meetingData.relatedEvent = {
                subject: event.subject,
                startDateTime: event.start?.dateTime,
                endDateTime: event.end?.dateTime,
                organizer: event.organizer,
                attendees: event.attendees || [],
                isOnlineMeeting: event.isOnlineMeeting,
                onlineMeetingJoinUrl: event.onlineMeeting?.joinUrl
              };
              break;
            }
          }
        } catch (eventsError: any) {
          this.log('info', `Could not search calendar events: ${eventsError.message}`);
        }
      }

      // Get user's Teams and channels for context
      try {
        const teams = await client.api('/me/joinedTeams').select('id,displayName').get();
        meetingData.userTeams = teams.value || [];
      } catch (teamsError: any) {
        this.log('info', `Could not get user teams: ${teamsError.message}`);
      }

      return meetingData;
    } catch (error: any) {
      this.log('error', `Error getting enhanced meeting metadata: ${error.message}`);
      return {};
    }
  }

  /**
   * Check if a calendar event is related to the meeting file
   */
  private isRelatedMeetingEvent(fileName: string, event: any): boolean {
    const fileNameLower = fileName.toLowerCase();
    const subject = event.subject?.toLowerCase() || '';
    
    // Check for direct subject match
    if (subject && fileNameLower.includes(subject)) {
      return true;
    }

    // Check if it's an online meeting
    if (event.isOnlineMeeting) {
      // Extract date from filename and compare
      const dateMatch = fileName.match(/(\d{4})-?(\d{2})-?(\d{2})/);
      if (dateMatch && event.start?.dateTime) {
        const fileDate = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
        const eventDate = event.start.dateTime.split('T')[0];
        if (fileDate === eventDate) {
          return true;
        }
      }

      // Check for time patterns
      const timeMatch = fileName.match(/(\d{2}):?(\d{2})/);
      if (timeMatch && event.start?.dateTime) {
        const fileTime = `${timeMatch[1]}:${timeMatch[2]}`;
        const eventTime = new Date(event.start.dateTime).toTimeString().substring(0, 5);
        if (fileTime === eventTime) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Extract metadata from Microsoft file properties
   */
  async extractFileMetadata(
    client: Client,
    driveId: string,
    fileId: string
  ): Promise<Record<string, any>> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(driveId, 'drive ID');
    this.validateString(fileId, 'file ID');

    try {
      // Get detailed file properties
      const fileResponse = await client.api(`/drives/${driveId}/items/${fileId}`)
        .select('id,name,file,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference,shared,permissions')
        .expand('permissions')
        .get();

      const metadata: Record<string, any> = {
        fileId: fileResponse.id,
        fileName: fileResponse.name,
        size: fileResponse.size,
        createdDateTime: fileResponse.createdDateTime,
        lastModifiedDateTime: fileResponse.lastModifiedDateTime,
        webUrl: fileResponse.webUrl,
        mimeType: fileResponse.file?.mimeType,
        driveId: driveId,
        parentReference: fileResponse.parentReference,
      };

      // Add creator information
      if (fileResponse.createdBy?.user) {
        metadata.createdBy = {
          displayName: fileResponse.createdBy.user.displayName,
          email: fileResponse.createdBy.user.userPrincipalName,
          id: fileResponse.createdBy.user.id,
        };
      }

      // Add last modifier information
      if (fileResponse.lastModifiedBy?.user) {
        metadata.lastModifiedBy = {
          displayName: fileResponse.lastModifiedBy.user.displayName,
          email: fileResponse.lastModifiedBy.user.userPrincipalName,
          id: fileResponse.lastModifiedBy.user.id,
        };
      }

      // Add sharing information
      if (fileResponse.shared) {
        metadata.shared = true;
        metadata.permissions = fileResponse.permissions?.map((perm: any) => ({
          grantedTo: perm.grantedTo,
          roles: perm.roles,
        })) || [];
      }

      return metadata;
    } catch (error: any) {
      this.handleError(error, `extracting metadata for file ${fileId}`);
    }
  }

  /**
   * Check if file type is supported for content extraction
   */
  isFileTypeSupported(fileName: string, mimeType?: string): boolean {
    const supportedExtensions = ['pdf', 'docx', 'doc', 'txt', 'xlsx', 'pptx'];
    const extension = this.getFileExtension(fileName);
    
    const supportedMimeTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ];

    return supportedExtensions.includes(extension) ||
           Boolean(mimeType && supportedMimeTypes.includes(mimeType));
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
  }

  /**
   * Get file type category
   */
  getFileTypeCategory(fileName: string, mimeType?: string): string {
    const extension = this.getFileExtension(fileName);
    
    if (['pdf'].includes(extension) || mimeType?.includes('pdf')) {
      return 'document';
    }
    
    if (['docx', 'doc'].includes(extension) || mimeType?.includes('word')) {
      return 'document';
    }
    
    if (['xlsx', 'xls'].includes(extension) || mimeType?.includes('sheet')) {
      return 'spreadsheet';
    }
    
    if (['pptx', 'ppt'].includes(extension) || mimeType?.includes('presentation')) {
      return 'presentation';
    }
    
    if (['txt'].includes(extension) || mimeType?.includes('text')) {
      return 'text';
    }
    
    return 'unknown';
  }
}
