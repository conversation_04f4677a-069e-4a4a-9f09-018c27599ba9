import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import { MCPResult } from '../../server/services/mcp/types';
import { mcpTokenService } from '../utils/mcp-token.service';

function getSupportedMimeTypes(): string[] {
  return [
    'application/vnd.google-apps.document',
    'application/vnd.google-apps.spreadsheet',
    'application/vnd.google-apps.presentation',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/pdf',
    'text/plain',
    'text/csv',
    'image/*',
    'video/*',
    'audio/*',
  ];
}

export async function listDriveFilesHandler(args: any): Promise<MCPResult> {
  const { userId, folderId, query, maxResults = 50 } = args;
  try {
    const tokens = await mcpTokenService.getTokens(userId, 'google');
    if (!tokens) {
      throw new Error('No Google authentication tokens found. Please authenticate first.');
    }
    const client = new OAuth2Client();
    client.setCredentials({
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token
    });
    const drive = google.drive({ version: 'v3', auth: client });
    const mimeTypeConditions = getSupportedMimeTypes()
      .map(mimeType => {
        if (mimeType.includes('*')) {
          return `mimeType contains '${mimeType.replace('*', '')}'`;
        }
        return `mimeType = '${mimeType}'`;
      })
      .join(' or ');
    const driveQuery = `'${folderId || 'root'}' in parents and trashed = false and (${mimeTypeConditions})`;
    const response = await drive.files.list({
      q: driveQuery,
      fields: 'files(id, name, mimeType, modifiedTime, webViewLink)',
      orderBy: 'modifiedTime desc',
      pageSize: maxResults
    });
    const files = response.data.files || [];
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            files: files.map(file => ({
              id: file.id || 'unknown',
              name: file.name || 'Unknown',
              type: file.mimeType || 'unknown',
              size: 0,
              modifiedTime: file.modifiedTime || new Date().toISOString(),
              webViewLink: file.webViewLink || ''
            })),
            total: files.length,
            query: query || 'all files',
            folder: folderId || 'root'
          }, null, 2)
        }
      ],
      metadata: { total: files.length, folder: folderId || 'root' },
      isError: false
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error listing Google Drive files: ${error instanceof Error ? error.message : String(error)}`
        }
      ],
      isError: true,
      metadata: { error: error instanceof Error ? error.message : String(error) }
    };
  }
}

export async function searchDriveFilesHandler(args: any): Promise<MCPResult> {
  const { userId, searchQuery, fileType, maxResults = 20 } = args;
  try {
    const tokens = await mcpTokenService.getTokens(userId, 'google');
    if (!tokens) {
      throw new Error('No Google authentication tokens found. Please authenticate first.');
    }
    const client = new OAuth2Client();
    client.setCredentials({
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token
    });
    const drive = google.drive({ version: 'v3', auth: client });
    let mimeTypeConditions = getSupportedMimeTypes()
      .map(mimeType => {
        if (mimeType.includes('*')) {
          return `mimeType contains '${mimeType.replace('*', '')}'`;
        }
        return `mimeType = '${mimeType}'`;
      })
      .join(' or ');
    if (fileType) {
      mimeTypeConditions = `mimeType contains '${fileType}'`;
    }
    const driveQuery = `('root' in parents) and trashed = false and (${mimeTypeConditions})`;
    const response = await drive.files.list({
      q: driveQuery,
      fields: 'files(id, name, mimeType, description, modifiedTime, webViewLink)',
      orderBy: 'modifiedTime desc',
      pageSize: maxResults
    });
    const files = response.data.files || [];
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            searchResults: files
              .filter(file => file.name?.toLowerCase().includes(searchQuery.toLowerCase()))
              .slice(0, maxResults)
              .map(file => ({
                id: file.id || 'unknown',
                name: file.name || 'Unknown',
                type: file.mimeType || 'unknown',
                snippet: file.description || '',
                lastModified: file.modifiedTime || new Date().toISOString(),
                webViewLink: file.webViewLink || '',
                relevanceScore: 1.0
              })),
            total: files.length,
            query: searchQuery,
            fileTypeFilter: fileType || 'all types'
          }, null, 2)
        }
      ],
      metadata: { total: files.length, fileType: fileType || 'all types' },
      isError: false
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error searching Google Drive: ${error instanceof Error ? error.message : String(error)}`
        }
      ],
      isError: true,
      metadata: { error: error instanceof Error ? error.message : String(error) }
    };
  }
}

export async function getFileContentHandler(args: any): Promise<MCPResult> {
  const { userId, fileId, format } = args;
  try {
    const tokens = await mcpTokenService.getTokens(userId, 'google');
    if (!tokens) {
      throw new Error('No Google authentication tokens found. Please authenticate first.');
    }
    const client = new OAuth2Client();
    client.setCredentials({
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token
    });
    const fileContent = {
      content: `File content for ${fileId} (simplified for MVP)`,
      fileName: `file-${fileId}`,
      fileType: format || 'text/plain',
      size: 0,
      lastModified: new Date().toISOString()
    };
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            fileId,
            content: fileContent.content,
            metadata: {
              name: fileContent.fileName,
              type: fileContent.fileType,
              size: fileContent.size,
              lastModified: fileContent.lastModified
            }
          }, null, 2)
        }
      ],
      metadata: { fileId, fileName: fileContent.fileName },
      isError: false
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error getting file content: ${error instanceof Error ? error.message : String(error)}`
        }
      ],
      isError: true,
      metadata: { error: error instanceof Error ? error.message : String(error) }
    };
  }
}

export async function createFileHandler(args: any): Promise<MCPResult> {
  const { userId, fileName, content, mimeType = 'text/plain' } = args;
  try {
    const tokens = await mcpTokenService.getTokens(userId, 'google');
    if (!tokens) {
      throw new Error('No Google authentication tokens found. Please authenticate first.');
    }
    const client = new OAuth2Client();
    client.setCredentials({
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token
    });
    const createdFile = {
      id: `created-${Date.now()}`,
      name: fileName,
      mimeType,
      size: content.length,
      webViewLink: `https://drive.google.com/file/d/created-${Date.now()}/view`,
      createdTime: new Date().toISOString()
    };
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            success: true,
            fileId: createdFile.id,
            fileName: createdFile.name,
            mimeType: createdFile.mimeType,
            size: createdFile.size,
            webViewLink: createdFile.webViewLink,
            createdTime: createdFile.createdTime
          }, null, 2)
        }
      ],
      metadata: { fileId: createdFile.id, fileName: createdFile.name },
      isError: false
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating file: ${error instanceof Error ? error.message : String(error)}`
        }
      ],
      isError: true,
      metadata: { error: error instanceof Error ? error.message : String(error) }
    };
  }
} 