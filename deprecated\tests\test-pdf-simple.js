require("dotenv").config(); async function testPDF() { console.log("🔍 Testing PDF Processing..."); const PDFParser = require("pdf2json"); const pdfParser = new PDFParser(); const testPDF = Buffer.from(`%PDF-1.4 1 0 obj << /Type /Catalog /Pages 2 0 R >> endobj 2 0 obj << /Type /Pages /Kids [3 0 R] /Count 1 >> endobj 3 0 obj << /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >> endobj 4 0 obj << /Length 100 >> stream BT /F1 12 Tf 100 700 Td (ANANT AGGARWAL) Tj 0 -20 Td (Software Engineer) Tj ET endstream endobj xref 0 5 0000000000 65535 f 0000000010 00000 n 0000000053 00000 n 0000000125 00000 n 0000000185 00000 n trailer << /Size 5 /Root 1 0 R >> startxref 330 %%EOF`); console.log("📄 Testing PDF2JSON..."); pdfParser.on("pdfParser_dataReady", (pdfData) => { console.log("✅ PDF2JSON extraction successful!"); console.log("Pages:", pdfData.Pages ? pdfData.Pages.length : 0); if (pdfData.Pages && pdfData.Pages[0] && pdfData.Pages[0].Texts) { console.log("Text elements found:", pdfData.Pages[0].Texts.length); } }); pdfParser.on("pdfParser_dataError", (error) => { console.log("❌ PDF2JSON failed:", error); }); pdfParser.parseBuffer(testPDF); setTimeout(() => { console.log("⏰ Test completed"); }, 2000); } testPDF();
