@echo off
REM MeetSync Database Reset Script for Windows
REM Runs the SQL reset script to clear all connected state and fetched data

echo.
echo 🔄 MeetSync Database Reset
echo This will clear all connected integrations, sync data, and files
echo Integration records (names/types) will be preserved
echo.

REM Check if DATABASE_URL is set
if "%DATABASE_URL%"=="" (
    echo ❌ Error: DATABASE_URL environment variable is not set
    echo Please set your DATABASE_URL before running this script
    pause
    exit /b 1
)

REM Confirm before proceeding
set /p "confirm=Are you sure you want to proceed? (y/N): "
if /i not "%confirm%"=="y" (
    echo ℹ️  Reset cancelled
    pause
    exit /b 0
)

echo.
echo 🔄 Running database reset...

REM Get the directory of this script
set "SCRIPT_DIR=%~dp0"

REM Run the SQL script
where psql >nul 2>nul
if %errorlevel%==0 (
    REM Use psql if available
    psql "%DATABASE_URL%" -f "%SCRIPT_DIR%reset-connected-state.sql"
) else (
    where docker >nul 2>nul
    if %errorlevel%==0 (
        REM Use docker with postgres image if psql not available
        echo ℹ️  psql not found, using Docker...
        docker run --rm -i postgres:15 psql "%DATABASE_URL%" < "%SCRIPT_DIR%reset-connected-state.sql"
    ) else (
        echo ❌ Error: Neither psql nor docker found
        echo Please install PostgreSQL client (psql) or Docker to run this script
        echo Alternatively, you can manually run the SQL file:
        echo   %SCRIPT_DIR%reset-connected-state.sql
        pause
        exit /b 1
    )
)

if %errorlevel%==0 (
    REM Clear uploads folder after successful database reset
    echo.
    echo 🗂️ Clearing uploads folder...
    if exist "uploads" (
        del /q "uploads\*" >nul 2>nul
        echo   - Removed all files from uploads folder
    ) else (
        mkdir "uploads" >nul 2>nul
        echo   - Created uploads folder
    )
    
    REM Ensure uploads folder exists
    if not exist "uploads" mkdir "uploads"
    echo   - Uploads folder is ready

    echo.
    echo ✅ Database reset completed successfully!
    echo.
    echo Next steps:
    echo 1. Start your backend: npm run dev
    echo 2. Check the integrations page - all should show 'Disconnected'
    echo 3. Check logs/files pages - should be empty
    echo 4. Uploads folder has been cleared
    echo.
    echo You can also use the API endpoint (dev only):
    echo   curl -X POST http://localhost:5000/api/dev/reset-connected-state
) else (
    echo ❌ Reset failed with error code %errorlevel%
)

pause 