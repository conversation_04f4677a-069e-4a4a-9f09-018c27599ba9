// Re-export all Google service modules
export * from './oauth.service';
export * from './drive.service';
export * from './content.service';
export * from './google.facade';

// Convenience exports for commonly used classes
export { GoogleOAuthService } from './oauth.service';
export { GoogleDriveService } from './drive.service';
export { GoogleContentService } from './content.service';
export { GoogleServiceFacade } from './google.facade';

// Create and export the default Google service instance
import { GoogleServiceFacade } from './google.facade';

// Initialize the Google service facade
export const googleService = new GoogleServiceFacade();

// Export for backward compatibility
export default googleService;
