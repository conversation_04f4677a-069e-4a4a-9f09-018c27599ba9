# =============================================================================
# Docker Ignore File for MeetSync Application
# =============================================================================

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/
.pnp.*

# Build outputs
dist/
build/
.next/
.nuxt/
.vuepress/dist

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# Environment files (will be handled via Docker secrets/config)
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# Logs
logs/
*.log
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage and testing
coverage/
*.lcov
.nyc_output
.jest/

# Dependencies cache
.npm
.eslintcache
.stylelintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Docker files (except the ones we need in container)
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation (not needed in runtime)
*.md
docs/
documentation/

# Development and testing
tests/
test/
spec/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
__tests__/

# Database files (PostgreSQL will run in separate container)
*.db
*.sqlite
*.sqlite3
data/

# Upload files (will be volume mounted)
uploads/
attached_assets/

# Scripts that are not needed in production
scripts/debug/
batch-jobs/
deprecated/

# Package manager files
.yarn/
.pnp
.pnp.js

# TypeScript cache
*.tsbuildinfo

# ESLint and Prettier
.eslintrc*
.prettierrc*
.prettierignore

# Webpack
webpack.config.js

# Rollup
rollup.config.js

# Development certificates
*.pem

# Local development overrides
.env.local
docker-compose.override.yml

# macOS
.AppleDouble
.LSOverride

# Windows
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*~

# Backup files
*.bak
*.backup
*.old

# IDE files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# JetBrains
.idea/
*.iws
*.iml
*.ipr

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json 