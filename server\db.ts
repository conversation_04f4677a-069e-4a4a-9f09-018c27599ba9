import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from "../shared/index.js";
import dotenv from "dotenv";
import { databaseConfig } from './core/config/database.config';

dotenv.config();

// Create PostgreSQL connection pool
let pool: Pool | null = null;
let db: any = null;

if (process.env.DATABASE_URL) {
  pool = new Pool({ 
    connectionString: process.env.DATABASE_URL,
    max: databaseConfig.maxConnections,
    idleTimeoutMillis: databaseConfig.idleTimeout,
    connectionTimeoutMillis: databaseConfig.connectionTimeout,
    ssl: databaseConfig.ssl ? { rejectUnauthorized: false } : false,
  });
  db = drizzle(pool, { schema });
  console.log('Database connection initialized with PostgreSQL');
} else {
  console.log('DATABASE_URL not set - using in-memory storage for development');
}

// Export a function to get the database instance
export const getDatabase = () => {
  if (!db) {
    throw new Error('Database not initialized. Ensure DATABASE_URL is set.');
  }
  return db;
};

// Export pool for direct access if needed
export { pool };