import { getDatabase } from '../../server/db';
import { oauthTokens } from '../../shared/schemas/oauth.schema';
import { decrypt } from '../../shared/utils/crypto.utils';
import { eq, and } from 'drizzle-orm';
import type { TokenData } from '../../server/services/core/token-store.service';

/**
 * Service for MCP servers to retrieve OAuth tokens
 * Provides a simplified interface for token retrieval with proper error handling
 */
export class MCPTokenService {
  private static instance: MCPTokenService;
  private db: any;

  private constructor() {
    this.db = getDatabase();
  }

  public static getInstance(): MCPTokenService {
    if (!MCPTokenService.instance) {
      MCPTokenService.instance = new MCPTokenService();
    }
    return MCPTokenService.instance;
  }

  /**
   * Retrieve OAuth tokens for an MCP server
   * @param userId - The user's ID
   * @param platform - The platform (e.g., 'google', 'microsoft')
   * @returns The decrypted tokens or null if not found/expired
   * @throws Error if tokens cannot be retrieved or decrypted
   */
  async getTokens(userId: string, platform: string): Promise<TokenData | null> {
    try {
      // Validate inputs
      if (!userId || !platform) {
        throw new Error('User ID and platform are required');
      }

      // Get tokens from database
      const result = await this.db.select()
        .from(oauthTokens)
        .where(
          and(
            eq(oauthTokens.userId, userId),
            eq(oauthTokens.platform, platform)
          )
        )
        .limit(1);

      if (!result.length) {
        return null;
      }

      const token = result[0];

      // Decrypt the tokens
      const accessToken = await decrypt(token.accessToken, 'access_token');
      const refreshToken = token.refreshToken 
        ? await decrypt(token.refreshToken, 'refresh_token')
        : undefined;

      const tokenData: TokenData = {
        access_token: accessToken,
        refresh_token: refreshToken,
        token_type: token.tokenType,
        scope: token.scope || undefined,
        expiry_date: token.expiresAt.getTime(),
      };

      // Check if token is expired
      if (this.isTokenExpired(tokenData)) {
        return null;
      }

      return tokenData;
    } catch (error) {
      console.error('Error retrieving tokens for MCP:', error);
      throw new Error('Failed to retrieve tokens');
    }
  }

  /**
   * Check if tokens are expired
   * @param tokens - The tokens to check
   * @returns boolean indicating if tokens are expired
   */
  private isTokenExpired(tokens: TokenData): boolean {
    return Date.now() >= tokens.expiry_date;
  }
}

// Export singleton instance for MCP use
export const mcpTokenService = MCPTokenService.getInstance(); 