#!/bin/bash

echo "🧪 MeetSync Docker Validation Test Suite"
echo "========================================"

# Initialize test results
TESTS_PASSED=0
TESTS_FAILED=0
TEST_LOG="docker_test_results_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}🔄 Running: $test_name${NC}"
    if eval "$test_command" >> "$TEST_LOG" 2>&1; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        ((TESTS_FAILED++))
        echo -e "${YELLOW}   Check $TEST_LOG for details${NC}"
    fi
}

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    docker-compose down > /dev/null 2>&1
    docker-compose -f docker-compose.dev.yml down > /dev/null 2>&1
    docker-compose -f docker-compose.staging.yml down > /dev/null 2>&1
}

# Trap cleanup on exit
trap cleanup EXIT

# Environment setup
echo "Setting up test environment..."
cleanup
docker system prune -f > /dev/null 2>&1

# Copy environment if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp docker/env.template .env
    echo "SESSION_SECRET=$(openssl rand -base64 32)" >> .env
    echo "ENCRYPTION_KEY=$(openssl rand -base64 32)" >> .env
    echo "REDIS_PASSWORD=$(openssl rand -base64 16)" >> .env
    echo "POSTGRES_PASSWORD=test_password_123" >> .env
    echo "OPENAI_API_KEY=sk-test_key_for_testing" >> .env
    echo "NODE_ENV=development" >> .env
    echo "LOG_LEVEL=debug" >> .env
    echo "METRICS_ENABLED=true" >> .env
    echo "APM_ENABLED=true" >> .env
fi

# Start development environment
echo "Starting development environment..."
npm run docker:dev > "$TEST_LOG" 2>&1 &
DEV_PID=$!

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 60

# Check if services are actually running
if ! docker ps | grep -q meetsync-app-dev; then
    echo -e "${RED}❌ Failed to start development environment${NC}"
    echo "Check $TEST_LOG for startup errors"
    exit 1
fi

echo "Services started. Beginning tests..."

# =============================================================================
# PHASE 1 TESTS: Critical Completions
# =============================================================================

echo ""
echo -e "${BLUE}🔧 Phase 1: Critical Completions${NC}"
echo "================================="

# Health Check Tests
run_test "Health Check Endpoint" "curl -s http://localhost:8080/health | jq -e '.status == \"healthy\"' > /dev/null"
run_test "API Health Check" "curl -s http://localhost:8080/api/health | jq -e '.checks' > /dev/null"

# Redis Session Tests
run_test "Redis Session Store" "curl -s http://localhost:8080/api/system/info | jq -e '.session.type == \"redis\"' > /dev/null"
run_test "Redis Connectivity" "docker exec meetsync-redis-dev redis-cli ping | grep -q PONG"

# Container Health Tests
run_test "Container Health Checks" "docker ps --format '{{.Status}}' | grep -q healthy"
run_test "Non-root Execution" "docker exec meetsync-app-dev whoami | grep -q meetsync"

# =============================================================================
# PHASE 2 TESTS: Production Enhancements
# =============================================================================

echo ""
echo -e "${BLUE}📊 Phase 2: Production Enhancements${NC}"
echo "==================================="

# Monitoring Tests
run_test "Prometheus Metrics Endpoint" "curl -s http://localhost:8080/metrics | grep -q 'http_requests_total'"
run_test "System Info Endpoint" "curl -s http://localhost:8080/api/system/info | jq -e '.service == \"meetsync\"' > /dev/null"
run_test "Structured Logging" "docker logs meetsync-app-dev --tail 20 | grep -q '\"level\"'"

# Database Tests
run_test "Database Connectivity" "curl -s http://localhost:8080/api/health | jq -e '.checks.database.status == \"healthy\"' > /dev/null"
run_test "PostgreSQL Ready" "docker exec meetsync-postgres-dev pg_isready -U meetsync -d meetsync"

# Security Tests
run_test "File Permissions" "docker exec meetsync-app-dev stat -c '%U:%G' /app | grep -q meetsync:nodejs"
run_test "Network Isolation" "docker exec meetsync-app-dev nslookup postgres > /dev/null 2>&1"

# =============================================================================
# PHASE 3 TESTS: Performance & Integration
# =============================================================================

echo ""
echo -e "${BLUE}🚀 Phase 3: Performance & Integration${NC}"
echo "====================================="

# Performance Tests
run_test "Basic Load Test (20 requests)" "for i in {1..20}; do curl -s http://localhost:8080/api/health > /dev/null || exit 1; done"
run_test "Response Time Test" "timeout 10s curl -s http://localhost:8080/api/health > /dev/null"
run_test "Concurrent Connections" "for i in {1..5}; do curl -s http://localhost:8080/api/system/info > /dev/null & done; wait"

# Session Integration Tests
run_test "Session Flow Test" "SESSION=\$(curl -s -c /tmp/test_session.txt http://localhost:8080/api/health); curl -s -b /tmp/test_session.txt http://localhost:8080/api/system/info > /dev/null; rm -f /tmp/test_session.txt"

# Metrics Tests
run_test "HTTP Metrics Collection" "curl -s http://localhost:8080/metrics | grep -q 'http_request_duration_seconds'"
run_test "Memory Metrics" "curl -s http://localhost:8080/api/health | jq -e '.checks.memory' > /dev/null"

# =============================================================================
# BACKUP AND RECOVERY TESTS
# =============================================================================

echo ""
echo -e "${BLUE}💾 Backup & Recovery Tests${NC}"
echo "============================"

run_test "Backup Script Executable" "docker exec meetsync-app-dev test -x /app/docker/scripts/backup.sh"
run_test "Backup Directory Creation" "docker exec meetsync-app-dev mkdir -p /tmp/test_backups"
run_test "Database Backup Test" "docker exec meetsync-app-dev /bin/sh /app/docker/scripts/backup.sh --dir /tmp/test_backups --retention 1 --quiet"

# =============================================================================
# ENVIRONMENT VALIDATION
# =============================================================================

echo ""
echo -e "${BLUE}🔧 Environment Validation${NC}"
echo "=========================="

run_test "Environment Variables Set" "docker exec meetsync-app-dev env | grep -q NODE_ENV"
run_test "Port Accessibility" "timeout 5s bash -c '</dev/tcp/localhost/8080' 2>/dev/null"
run_test "Volume Mounts Working" "docker exec meetsync-app-dev test -d /app/client && docker exec meetsync-app-dev test -d /app/server"

# =============================================================================
# PRODUCTION READINESS TESTS
# =============================================================================

echo ""
echo -e "${BLUE}🏭 Production Readiness${NC}"
echo "======================="

# Resource Usage Tests
run_test "Memory Usage Check" "docker stats --no-stream meetsync-app-dev | awk 'NR==2 {print \$4}' | grep -E '^[0-9]+(\.[0-9]+)?%$'"
run_test "CPU Usage Check" "docker stats --no-stream meetsync-app-dev | awk 'NR==2 {print \$3}' | grep -E '^[0-9]+(\.[0-9]+)?%$'"

# Final Integration Test
run_test "Complete Application Flow" "curl -s http://localhost:8080/health | jq -e '.status == \"healthy\"' && curl -s http://localhost:8080/api/system/info | jq -e '.session.type == \"redis\"' && curl -s http://localhost:8080/metrics | grep -q 'http_requests_total'"

# =============================================================================
# RESULTS AND SUMMARY
# =============================================================================

echo ""
echo -e "${BLUE}🏁 Test Results Summary${NC}"
echo "======================"
echo -e "${GREEN}✅ Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}❌ Tests Failed: $TESTS_FAILED${NC}"
echo -e "${YELLOW}📄 Detailed Log: $TEST_LOG${NC}"

# Generate test report
cat > docker_test_summary.txt << EOF
MeetSync Docker Validation Results
Generated: $(date)

Total Tests Run: $((TESTS_PASSED + TESTS_FAILED))
Tests Passed: $TESTS_PASSED
Tests Failed: $TESTS_FAILED
Success Rate: $(( TESTS_PASSED * 100 / (TESTS_PASSED + TESTS_FAILED) ))%

Environment: Development
Docker Compose: docker-compose.dev.yml
Services Tested:
- MeetSync Application
- PostgreSQL Database
- Redis Session Store
- Health Checks
- Monitoring Stack
- Backup System

EOF

echo ""
echo -e "${BLUE}📊 Test Summary Report:${NC}"
cat docker_test_summary.txt

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! Docker setup is fully functional and production-ready!${NC}"
    echo -e "${GREEN}✨ Your MeetSync application is ready for deployment.${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}⚠️  Some tests failed. Please check the detailed log for more information.${NC}"
    echo -e "${YELLOW}💡 Common issues and solutions:${NC}"
    echo "   - Services not fully started: Wait longer or check logs"
    echo "   - Port conflicts: Ensure ports 8080, 5433, 6380 are available"
    echo "   - Missing dependencies: Install curl, jq, timeout command"
    echo "   - Environment issues: Check .env file configuration"
    echo ""
    echo -e "${YELLOW}📄 Check these files for troubleshooting:${NC}"
    echo "   - $TEST_LOG (detailed test log)"
    echo "   - docker_test_summary.txt (test summary)"
    echo "   - README-Docker.md (troubleshooting guide)"
    exit 1
fi 