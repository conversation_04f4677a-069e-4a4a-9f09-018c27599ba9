import { users, type User, type InsertUser } from "../../../shared/index.js";
import { eq } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config";

/**
 * User storage operations
 */
export class UserStorage {
  private db: any;
  private memoryUsers: Map<number, User>;
  private userIdCounter: number;
  private useDatabase: boolean;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memoryUsers = new Map();
    this.userIdCounter = 1;
    // Database connection will be initialized lazily when first needed
  }

  /**
   * Ensure database connection is available
   */
  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        // Silently fall back to memory storage
        this.useDatabase = false;
      }
    }
  }

  async getUser(id: number): Promise<User | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(users).where(eq(users.id, id));
      return result[0];
    } else {
      return this.memoryUsers.get(id);
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    this.validateString(username, 'username');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(users).where(eq(users.username, username));
      return result[0];
    } else {
      return Array.from(this.memoryUsers.values()).find(
        (user) => user.username === username
      );
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    this.validateString(insertUser.username, 'username');
    this.validateString(insertUser.password, 'password');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(users).values(insertUser).returning();
      return result[0];
    } else {
      const id = this.userIdCounter++;
      const user: User = { ...insertUser, id };
      this.memoryUsers.set(id, user);
      return user;
    }
  }

  async getAllUsers(): Promise<User[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      return await this.db.select().from(users);
    } else {
      return Array.from(this.memoryUsers.values());
    }
  }

  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.update(users)
        .set(data)
        .where(eq(users.id, id))
        .returning();
      return result[0];
    } else {
      const user = this.memoryUsers.get(id);
      if (!user) return undefined;

      const updated: User = { ...user, ...data };
      this.memoryUsers.set(id, updated);
      return updated;
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      try {
        await this.db.delete(users).where(eq(users.id, id));
        return true;
      } catch (error) {
        console.error("Error deleting user:", error);
        return false;
      }
    } else {
      return this.memoryUsers.delete(id);
    }
  }

  async clearAllUsers(): Promise<void> {
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(users);
    } else {
      this.memoryUsers.clear();
      this.userIdCounter = 1;
    }
  }

  // Utility methods
  private validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid user ID provided');
    }
  }

  private validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      userCount: this.useDatabase ? 'unknown' : this.memoryUsers.size,
    };
  }
}
