// Export the main service and its interface for backward compatibility
export { FunctionToolsService, functionToolsService } from './function-tools.service';

// Export the original interface for backward compatibility
export type { FunctionTool } from './base/function-tool.interface';

// Export base components for advanced usage
export * from './base';

// Export tool factory for advanced scenarios
export { ToolFactory } from './tool-factory';

// Export specific tool categories for direct access if needed
export { FileToolsCategory } from './categories/file-tools';
export { GoogleDriveToolsCategory } from './platforms/google-drive-tools';
export { DynamicToolGeneratorCategory } from './dynamic/dynamic-tool-generator';
export { HITLPatternsCategory } from './hitl/hitl-patterns';

// Import the service instance for re-exports
import { functionToolsService } from './function-tools.service';

// Convenience re-exports for the most commonly used functionality
export const {
  getToolsForOpenAI,
  executeTool,
  hasTool,
  getToolNames,
  getToolCategories
} = functionToolsService; 