import { Request, Response, NextFunction } from 'express';
import { logger } from './logging.middleware';
import { environmentConfig } from '../config/environment.config';

/**
 * Prometheus Metrics Registry
 * Simple implementation without external dependencies
 */
class MetricsRegistry {
  private metrics: Map<string, any> = new Map();
  private enabled: boolean;

  constructor() {
    this.enabled = process.env.METRICS_ENABLED === 'true' || environmentConfig.nodeEnv === 'production';
  }

  private createMetric(name: string, type: 'counter' | 'histogram' | 'gauge', description: string) {
    const metric = {
      name,
      type,
      description,
      value: type === 'counter' ? 0 : type === 'gauge' ? 0 : [],
      labels: new Map<string, any>(),
    };
    this.metrics.set(name, metric);
    return metric;
  }

  // Counter methods
  incrementCounter(name: string, labels: Record<string, string> = {}, value: number = 1) {
    if (!this.enabled) return;
    
    let metric = this.metrics.get(name);
    if (!metric) {
      metric = this.createMetric(name, 'counter', `Auto-generated counter for ${name}`);
    }
    
    const labelKey = JSON.stringify(labels);
    const current = metric.labels.get(labelKey) || 0;
    metric.labels.set(labelKey, current + value);
  }

  // Gauge methods
  setGauge(name: string, value: number, labels: Record<string, string> = {}) {
    if (!this.enabled) return;
    
    let metric = this.metrics.get(name);
    if (!metric) {
      metric = this.createMetric(name, 'gauge', `Auto-generated gauge for ${name}`);
    }
    
    const labelKey = JSON.stringify(labels);
    metric.labels.set(labelKey, value);
  }

  // Histogram methods
  observeHistogram(name: string, value: number, labels: Record<string, string> = {}) {
    if (!this.enabled) return;
    
    let metric = this.metrics.get(name);
    if (!metric) {
      metric = this.createMetric(name, 'histogram', `Auto-generated histogram for ${name}`);
    }
    
    const labelKey = JSON.stringify(labels);
    if (!metric.labels.has(labelKey)) {
      metric.labels.set(labelKey, []);
    }
    metric.labels.get(labelKey).push(value);
  }

  // Export metrics in Prometheus format
  exportMetrics(): string {
    if (!this.enabled) return '';
    
    let output = '';
    
    for (const [_, metric] of Array.from(this.metrics)) {
      output += `# HELP ${metric.name} ${metric.description}\n`;
      output += `# TYPE ${metric.name} ${metric.type}\n`;
      
      if (metric.type === 'counter' || metric.type === 'gauge') {
        // Cast to proper type for Map iteration
        const labelEntries = Array.from(metric.labels.entries()) as [string, number][];
        for (const [labelKey, value] of labelEntries) {
          const labels = labelKey === '{}' ? '' : Object.entries(JSON.parse(labelKey))
            .map(([k, v]) => `${k}="${v}"`)
            .join(',');
          output += `${metric.name}${labels ? `{${labels}}` : ''} ${value}\n`;
        }
      } else if (metric.type === 'histogram') {
        // Cast to proper type for iteration
        const labelEntries = Array.from(metric.labels.entries()) as [string, number[]][];
        for (const [labelKey, values] of labelEntries) {
          const labels = labelKey === '{}' ? '' : Object.entries(JSON.parse(labelKey))
            .map(([k, v]) => `${k}=\"${v}\"`).join(',');
          
          // Calculate histogram buckets
          const sorted = values.sort((a: number, b: number) => a - b);
          const buckets = [0.1, 0.25, 0.5, 1, 2.5, 5, 10];
          let count = 0;
          
          for (const bucket of buckets) {
            const bucketCount = sorted.filter((v: number) => v <= bucket).length;
            output += `${metric.name}_bucket${labels ? `{${labels},` : '{'}le="${bucket}"} ${bucketCount}\n`;
          }
          
          output += `${metric.name}_bucket${labels ? `{${labels},` : '{'}le="+Inf"} ${sorted.length}\n`;
          output += `${metric.name}_count${labels ? `{${labels}}` : ''} ${sorted.length}\n`;
          output += `${metric.name}_sum${labels ? `{${labels}}` : ''} ${sorted.reduce((a: number, b: number) => a + b, 0)}\n`;
        }
      }
      output += '\n';
    }
    
    return output;
  }

  getStatus() {
    return {
      enabled: this.enabled,
      metricsCount: this.metrics.size,
    };
  }
}

// Singleton metrics registry
const metricsRegistry = new MetricsRegistry();

/**
 * Application Performance Monitoring
 */
class APMService {
  private enabled: boolean;
  private transactions: Map<string, any> = new Map();

  constructor() {
    this.enabled = process.env.APM_ENABLED === 'true' || environmentConfig.nodeEnv === 'production';
  }

  startTransaction(id: string, name: string, type: string = 'request') {
    if (!this.enabled) return;
    
    this.transactions.set(id, {
      id,
      name,
      type,
      startTime: Date.now(),
      spans: [],
    });
  }

  addSpan(transactionId: string, name: string, type: string, duration: number) {
    if (!this.enabled) return;
    
    const transaction = this.transactions.get(transactionId);
    if (transaction) {
      transaction.spans.push({
        name,
        type,
        duration,
        timestamp: Date.now(),
      });
    }
  }

  endTransaction(id: string, result: 'success' | 'error' = 'success') {
    if (!this.enabled) return;
    
    const transaction = this.transactions.get(id);
    if (transaction) {
      transaction.duration = Date.now() - transaction.startTime;
      transaction.result = result;
      transaction.endTime = Date.now();
      
      // APM transaction completed (logging disabled to avoid type issues)
      
      this.transactions.delete(id);
    }
  }

  getStatus() {
    return {
      enabled: this.enabled,
      activeTransactions: this.transactions.size,
    };
  }
}

// Singleton APM service
const apmService = new APMService();

/**
 * Structured JSON Logging Middleware
 */
export const structuredLoggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const requestId = generateRequestId();
  const transactionId = `req_${requestId}`;
  
  // Add request ID to request object
  (req as any).requestId = requestId;
  
  // Start APM transaction
  apmService.startTransaction(transactionId, `${req.method} ${req.path}`, 'http.request');
  
  // Override res.json to capture response data
  const originalJson = res.json;
  let responseData: any = null;
  
  res.json = function(data) {
    responseData = data;
    return originalJson.call(this, data);
  };
  
  // Log request start
  const requestLog = {
    timestamp: new Date().toISOString(),
    level: 'info',
    message: 'HTTP Request started',
    request: {
      id: requestId,
      method: req.method,
      url: req.url,
      path: req.path,
      query: req.query,
      headers: filterSensitiveHeaders(req.headers),
      userAgent: req.get('user-agent'),
      ip: req.ip || req.connection.remoteAddress,
      body: filterSensitiveData(req.body),
    },
    environment: environmentConfig.nodeEnv,
    service: 'meetsync',
  };
  
  if (process.env.LOG_FORMAT === 'json') {
    console.log(JSON.stringify(requestLog));
  } else {
    logger.info('HTTP Request started', requestLog.request);
  }
  
  // Handle response completion
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const statusCode = res.statusCode;
    const statusClass = Math.floor(statusCode / 100);
    
    // Update metrics
    metricsRegistry.incrementCounter('http_requests_total', {
      method: req.method,
      status: statusCode.toString(),
      path: req.route?.path || req.path,
    });
    
    metricsRegistry.observeHistogram('http_request_duration_seconds', duration / 1000, {
      method: req.method,
      status: statusCode.toString(),
    });
    
    // End APM transaction
    apmService.endTransaction(transactionId, statusClass >= 4 ? 'error' : 'success');
    
    // Log request completion
    const responseLog = {
      timestamp: new Date().toISOString(),
      level: statusClass >= 4 ? 'error' : 'info',
      message: 'HTTP Request completed',
      request: {
        id: requestId,
        method: req.method,
        url: req.url,
        path: req.path,
      },
      response: {
        statusCode,
        duration,
        size: res.get('content-length'),
        data: filterSensitiveData(responseData),
      },
      performance: {
        duration,
        memory: process.memoryUsage(),
      },
      environment: environmentConfig.nodeEnv,
      service: 'meetsync',
    };
    
    if (process.env.LOG_FORMAT === 'json') {
      console.log(JSON.stringify(responseLog));
    } else {
      logger.info('HTTP Request completed', {
        requestId,
        method: req.method,
        path: req.path,
        statusCode,
        duration,
      });
    }
  });
  
  next();
};

/**
 * Prometheus Metrics Endpoint Middleware
 */
export const metricsEndpointMiddleware = (req: Request, res: Response) => {
  // Add system metrics
  const memUsage = process.memoryUsage();
  metricsRegistry.setGauge('nodejs_memory_usage_bytes', memUsage.heapUsed, { type: 'heap_used' });
  metricsRegistry.setGauge('nodejs_memory_usage_bytes', memUsage.heapTotal, { type: 'heap_total' });
  metricsRegistry.setGauge('nodejs_memory_usage_bytes', memUsage.rss, { type: 'rss' });
  metricsRegistry.setGauge('nodejs_memory_usage_bytes', memUsage.external, { type: 'external' });
  
  const cpuUsage = process.cpuUsage();
  metricsRegistry.setGauge('nodejs_cpu_usage_seconds', cpuUsage.user / 1000000, { type: 'user' });
  metricsRegistry.setGauge('nodejs_cpu_usage_seconds', cpuUsage.system / 1000000, { type: 'system' });
  
  metricsRegistry.setGauge('nodejs_process_uptime_seconds', process.uptime());
  
  res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
  res.send(metricsRegistry.exportMetrics());
};

/**
 * Health Check with Detailed Status
 */
export const healthCheckMiddleware = async (req: Request, res: Response) => {
  const startTime = Date.now();
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    environment: environmentConfig.nodeEnv,
    checks: {} as Record<string, any>,
  };
  
  try {
    // Database health check
    try {
      const { getDatabase } = await import('../config/database.config');
      const db = getDatabase();
      if (db) {
        await db.execute('SELECT 1');
        health.checks.database = { status: 'healthy', responseTime: Date.now() - startTime };
      } else {
        health.checks.database = { status: 'unknown', message: 'Database not configured' };
      }
    } catch (error) {
      health.checks.database = { 
        status: 'unhealthy', 
        error: (error as Error).message,
        responseTime: Date.now() - startTime 
      };
      health.status = 'degraded';
    }
    
    // Session store health check
    try {
      const { getSessionStatus } = await import('./session.middleware');
      health.checks.sessions = getSessionStatus();
    } catch (error) {
      health.checks.sessions = { status: 'unknown', error: (error as Error).message };
    }
    
    // Metrics health check
    health.checks.metrics = metricsRegistry.getStatus();
    
    // APM health check
    health.checks.apm = apmService.getStatus();
    
    // Memory health check
    const memUsage = process.memoryUsage();
    const memUsedMB = memUsage.heapUsed / 1024 / 1024;
    const memTotalMB = memUsage.heapTotal / 1024 / 1024;
    
    health.checks.memory = {
      status: memUsedMB > 1000 ? 'warning' : 'healthy',
      used: `${memUsedMB.toFixed(2)}MB`,
      total: `${memTotalMB.toFixed(2)}MB`,
      usage: `${((memUsedMB / memTotalMB) * 100).toFixed(1)}%`,
    };
    
    if (memUsedMB > 1500) {
      health.status = 'degraded';
    }
    
  } catch (error) {
    health.status = 'unhealthy';
    health.checks.general = { status: 'error', error: (error as Error).message };
  }
  
  const statusCode = health.status === 'healthy' ? 200 : health.status === 'degraded' ? 200 : 503;
  res.status(statusCode).json(health);
};

/**
 * Utility Functions
 */
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

function filterSensitiveHeaders(headers: any): any {
  const filtered = { ...headers };
  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
  
  for (const header of sensitiveHeaders) {
    if (filtered[header]) {
      filtered[header] = '[REDACTED]';
    }
  }
  
  return filtered;
}

function filterSensitiveData(data: any): any {
  if (!data) return data;
  
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'credential'];
  const filtered = { ...data };
  
  for (const field of sensitiveFields) {
    if (filtered[field]) {
      filtered[field] = '[REDACTED]';
    }
  }
  
  return filtered;
}

/**
 * Performance monitoring decorator
 */
export function monitored(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;
  
  descriptor.value = async function (...args: any[]) {
    const startTime = Date.now();
    const methodName = `${target.constructor.name}.${propertyKey}`;
    
    try {
      const result = await originalMethod.apply(this, args);
      const duration = Date.now() - startTime;
      
      metricsRegistry.observeHistogram('method_duration_seconds', duration / 1000, {
        method: methodName,
        status: 'success',
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      metricsRegistry.observeHistogram('method_duration_seconds', duration / 1000, {
        method: methodName,
        status: 'error',
      });
      
      metricsRegistry.incrementCounter('method_errors_total', {
        method: methodName,
      });
      
      throw error;
    }
  };
  
  return descriptor;
}

// Export the metrics registry for custom metrics
export { metricsRegistry, apmService }; 