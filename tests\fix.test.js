const fetch = require('node-fetch');

async function testFixes() {
  const baseUrl = 'http://localhost:3000';

  console.log('🧪 Testing the credential double-encryption fix...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch(`${baseUrl}/api/diagnostic/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.status);

    // Test 2: Encryption test
    console.log('\n2. Testing basic encryption...');
    const encryptResponse = await fetch(`${baseUrl}/api/diagnostic/test-encryption`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ testData: 'Test credential fix' })
    });
    const encryptData = await encryptResponse.json();
    console.log('✅ Encryption test:', encryptData.success ? 'PASSED' : 'FAILED');

    // Test 3: Credential flow test (our new test)
    console.log('\n3. Testing credential flow (double encryption fix)...');
    const credentialResponse = await fetch(`${baseUrl}/api/diagnostic/test-credential-flow`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    const credentialData = await credentialResponse.json();
    console.log('✅ Credential flow test:', credentialData.success ? 'PASSED' : 'FAILED');
    if (credentialData.success) {
      console.log('   Token match:', credentialData.testResults.tokenMatch);
    } else {
      console.log('   Error:', credentialData.error);
    }

    // Test 4: Integration creation (schema fix)
    console.log('\n4. Testing integration creation (schema fix)...');
    const integrationResponse = await fetch(`${baseUrl}/api/integrations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'google-drive',
        name: 'Test Schema Fix',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null
      })
    });
    const integrationData = await integrationResponse.json();
    console.log('✅ Integration creation:', integrationResponse.status === 201 ? 'PASSED' : 'FAILED');
    if (integrationResponse.status !== 201) {
      console.log('   Error:', integrationData.message);
    }

    // Test 5: Test Google credentials for integration 1 (should work now)
    console.log('\n5. Testing Google credentials for integration 1...');
    const googleCredsResponse = await fetch(`${baseUrl}/api/diagnostic/test-google-credentials/1`);
    const googleCredsData = await googleCredsResponse.json();
    console.log('✅ Google credentials test:', googleCredsData.success ? 'PASSED' : 'FAILED');
    if (!googleCredsData.success) {
      console.log('   Error:', googleCredsData.error);
      console.log('   Details:', googleCredsData.details);
    }

    console.log('\n🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Wait 3 seconds for server to start, then run tests
setTimeout(testFixes, 3000);