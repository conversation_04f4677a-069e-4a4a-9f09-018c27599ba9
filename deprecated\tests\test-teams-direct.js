/**
 * Test Teams OAuth controller directly
 */

async function testDirect() {
  try {
    console.log('Testing Teams OAuth controller directly...');
    
    // Import the controller
    const { teamsOAuthController } = await import('./server/controllers/integration/microsoft/teams-oauth.controller.js');
    
    console.log('✅ Teams OAuth controller imported successfully');
    
    // Create mock request and response objects
    const mockReq = {
      params: { id: '21' },
      query: { redirectUri: 'http://localhost:5000/api/integrations/oauth/callback' }
    };
    
    const mockRes = {
      status: (code) => {
        console.log(`Response status: ${code}`);
        return mockRes;
      },
      json: (data) => {
        console.log('Response JSON:', data);
        return mockRes;
      }
    };
    
    console.log('Calling getTeamsAuthUrl directly...');
    await teamsOAuthController.getTeamsAuthUrl(mockReq, mockRes);
    
  } catch (error) {
    console.error('❌ Error in direct test:', error);
  }
}

testDirect().catch(console.error); 