// Re-export all core modules
export * from './config';
export * from './middleware';
export * from './utils';

// Convenience exports for commonly used items
export {
  // Configuration
  environmentConfig,
  initializeDatabase,
  getDatabase,
  getOpenAIClient,
  initializeAllConfigs,
} from './config';

export {
  // Middleware
  errorHandler,
  notFoundHandler,
  asyncHandler,
  logger,
  requestLogger,
  requireAuth,
  optionalAuth,
  validateBody,
  validateQuery,
  createApiMiddleware,
  createAuthenticatedApiMiddleware,
} from './middleware';

export {
  // Utilities
  performHealthCheck,
  performReadinessCheck,
  performLivenessCheck,
} from './utils';

// Application initialization helper
export const initializeCore = async () => {
  console.log('🚀 Initializing core infrastructure...');

  try {
    // Initialize all configurations
    const { initializeAllConfigs } = await import('./config');
    const configs = await initializeAllConfigs();

    console.log('✅ Core infrastructure initialized successfully');

    return {
      success: true,
      configs,
    };

  } catch (error) {
    console.error('❌ Core infrastructure initialization failed:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};
