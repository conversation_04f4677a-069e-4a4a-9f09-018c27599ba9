# Teams File Vectorization Fix

## Problem Summary
Teams files were not being vectorized (no embeddings generated), causing the chatbot to return no relevant document chunks even when querying with exact file names. The Microsoft Teams sync was only storing file metadata without generating embeddings for RAG search functionality.

## Root Cause
The Microsoft Teams sync process (`runReferenceBasedMicrosoftSync`) in `server/controllers/sync.ts` was missing the embedding generation logic that exists in the Google sync process. While Google files were being vectorized with comprehensive metadata extraction, Teams files were only stored as references without any searchable embeddings.

## Fix Applied

### 1. Added Embedding Generation to Teams Sync
**File**: `server/controllers/sync.ts` (lines ~700-850)

Added comprehensive embedding generation logic to the Microsoft Teams sync process that:

- **Extracts comprehensive metadata** from Teams files including:
  - File name, type, and platform
  - Source context (Team/Channel/SharePoint/OneDrive)
  - Meeting metadata (subject, attendees, organizer)
  - AI-extracted metadata (topics, attendees, dates)
  - Temporal context (creation, modification dates)

- **Builds rich content for vectorization** including:
  - All metadata fields formatted for semantic search
  - Meeting attendee information
  - Source location context
  - File type descriptions

- **Generates embeddings asynchronously** without blocking the sync process
- **Includes error handling** with fallback to minimal content vectorization
- **Provides detailed logging** for debugging vectorization issues

### 2. Content Examples Being Vectorized

For a Teams meeting transcript, the vectorized content includes:
```
File: Meeting_Recording_2024-01-15.mp4
Type: transcript
Platform: microsoft_teams
Source Type: teams_channel
Source: Team: Product Team / Channel: General
Team: Product Team
Channel: General
Meeting Subject: Weekly Sprint Review
Meeting Attendees: John Doe, Jane Smith, Bob Wilson
Organizer: John Doe
AI Topics: sprint review, progress updates, team planning
File Description: Microsoft Teams meeting transcript or recording file
Last Modified: 1/15/2024
Meeting Start: 1/15/2024, 2:00:00 PM
```

### 3. Debug Tool Created
**File**: `scripts/debug/debug-teams-vectorization.js`

Created a comprehensive debug script that:
- Lists all Teams files in the database
- Checks which files have embeddings vs. which don't
- Generates missing embeddings for testing
- Tests search functionality with Teams integration
- Provides direct database chunk inspection
- Shows sample vectorized content

## How to Test the Fix

### Step 1: Run the Debug Tool
```bash
npm run debug:teams-vectorization
```

This will:
- Show all Teams files in your database
- Check embedding status for each file
- Generate embeddings for files that don't have them
- Test search functionality

### Step 2: Re-sync Teams Integration (Recommended)
To ensure all existing Teams files get embeddings:

1. **Option A: Trigger a new sync**
   - Go to `/integrations` in your app
   - Click "Sync Now" on your Teams integration
   - New sync will generate embeddings for all files

2. **Option B: Force re-vectorization**
   ```bash
   # Call the re-vectorize API endpoint (with backend running)
   curl -X POST http://localhost:5000/api/sync/re-vectorize \
     -H "Content-Type: application/json" \
     -d '{"integrationId": YOUR_TEAMS_INTEGRATION_ID, "forceAll": true}'
   ```

### Step 3: Test Chat Search
1. Open the chat widget in your app
2. Enable the Teams integration as a source
3. Ask questions like:
   - "Show me files from [team name]"
   - "Find meeting transcripts from this week"
   - "What files are in the [channel name] channel?"
   - Use exact file names from your Teams files

### Step 4: Verify Results
Check that:
- The debug tool shows embeddings for Teams files
- Chat searches return relevant Teams files
- File names and metadata appear in search results
- Similarity scores are reasonable (> 0.3)

## Technical Details

### Platform Mapping
The search functionality maps integration types to platform names:
- `microsoft-teams` or `microsoft_teams` → `microsoft_teams` platform
- This ensures Teams files are included in search when Teams source is enabled

### Content Structure
Teams file embeddings include hierarchical context:
1. **File Identity**: Name, type, platform
2. **Source Context**: Team/Channel/Site/Drive location
3. **Meeting Context**: Subject, attendees, organizer, timing
4. **AI Context**: Extracted topics, attendees, dates from OpenAI processing
5. **Temporal Context**: Creation and modification dates

### Error Handling
- Graceful degradation if embedding generation fails
- Fallback to minimal content (file name + type + platform)
- Async processing to avoid blocking sync
- Detailed error logging for troubleshooting

## Validation Commands

### Check Teams Files Count
```javascript
// In your app console or debug script
const teamsFiles = await storage.getFiles('microsoft_teams');
console.log(`Teams files: ${teamsFiles.length}`);
```

### Check Embedding Status
```javascript
// Check if a specific file has embeddings
const hasEmbeddings = await embeddingService.hasEmbeddings(fileId);
console.log(`File ${fileId} has embeddings: ${hasEmbeddings}`);
```

### Test Search Directly
```javascript
// Direct search test
const results = await embeddingService.searchSimilarChunks(
  'meeting transcript', 
  ['YOUR_TEAMS_INTEGRATION_ID'], 
  10
);
console.log(`Found ${results.length} relevant chunks`);
```

## Expected Behavior After Fix

1. **Sync Process**: Teams files should show embedding generation logs during sync
2. **Debug Tool**: Should show ✅ for Teams files with embeddings
3. **Chat Search**: Should return relevant Teams files when searching
4. **File Metadata**: Teams files should be searchable by team, channel, meeting subject, attendees
5. **Semantic Search**: Should work with natural language queries about Teams content

## Troubleshooting

### If embeddings still aren't generated:
1. Check `OPENAI_API_KEY` is set in your `.env`
2. Verify embedding service initializes: check logs for "Embedding service initialized successfully"
3. Run debug tool to see specific error messages
4. Check OpenAI API usage/billing if requests are failing

### If search still returns no results:
1. Verify Teams integration ID is being passed as enabled source
2. Check platform mapping in search logic (should be `microsoft_teams`)
3. Use debug tool to verify embeddings exist and have content
4. Test with simple queries first (team names, file names)

### If content is not rich enough:
1. Check Teams metadata extraction in `microsoft-service.ts`
2. Verify AI metadata extraction is working (if enabled)
3. Look at the actual vectorized content in debug tool output

This fix ensures Teams files are now fully integrated into the RAG search system with the same level of functionality as Google Drive files. 