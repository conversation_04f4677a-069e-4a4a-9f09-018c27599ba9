// Re-export all configuration modules
export * from './environment.config';
export * from './database.config';
export * from './credentials.config';
export * from './openai.config';
export * from './mcp.config';

// Convenience exports for commonly used items
export {
  environmentConfig,
  validateEnvironment,
  isDevelopment,
  isProduction,
  isTest,
  getDatabaseUrl,
} from './environment.config';

export {
  databaseConfig,
  initializeDatabase,
  getDatabase,
  closeDatabase,
  checkDatabaseHealth,
  getDatabaseInfo,
} from './database.config';

export {
  openaiConfig,
  initializeOpenAI,
  getOpenAIClient,
  checkOpenAIHealth,
  getChatCompletion,
  getEmbeddings,
  getOpenAIInfo,
} from './openai.config';

// Combined initialization function
export const initializeAllConfigs = async () => {
  const { validateEnvironment } = await import('./environment.config');
  const { initializeDatabase } = await import('./database.config');
  const { initializeOpenAI } = await import('./openai.config');
  const { environmentConfig } = await import('./environment.config');

  console.log('🚀 Initializing application configuration...');

  // Validate environment
  const envValidation = validateEnvironment();
  if (!envValidation.valid) {
    console.error('❌ Environment validation failed:');
    envValidation.errors.forEach((error: string) => console.error(`   - ${error}`));
    throw new Error('Invalid environment configuration');
  }
  console.log('✅ Environment configuration validated');

  // Initialize database
  const database = await initializeDatabase();
  if (database) {
    console.log('✅ Database initialized');
  } else {
    console.warn('⚠️  Database not configured - using in-memory storage');
  }

  // Initialize OpenAI
  const openai = initializeOpenAI();
  if (openai) {
    console.log('✅ OpenAI client initialized');
  } else {
    console.warn('⚠️  OpenAI not configured - AI features disabled');
  }

  // Storage will be initialized lazily when first accessed

  console.log('🎉 Application configuration complete');

  return {
    database,
    openai,
    environment: environmentConfig,
  };
};

// Health check for all services
export const checkAllHealth = async () => {
  console.log('Checking health of all services...');

  // Initialize results with default unhealthy states
  let databaseResult: { healthy: boolean; error?: string } = { healthy: false, error: 'Not checked' };
  let openaiResult: { healthy: boolean; error?: string } = { healthy: false, error: 'Not checked' };
  let environmentResult: { valid: boolean; errors: string[] } = { valid: false, errors: ['Not checked'] };

  // Check database health with error handling
  try {
    const { checkDatabaseHealth } = await import('./database.config');
    const dbHealth = await checkDatabaseHealth();
    databaseResult = { healthy: dbHealth.healthy, error: dbHealth.error };
    console.log('Database health check result:', databaseResult);
  } catch (error) {
    console.error('Database health check failed:', error);
    databaseResult = {
      healthy: false,
      error: error instanceof Error ? error.message : 'Database health check failed'
    };
  }

  // Check OpenAI health with error handling
  try {
    const { checkOpenAIHealth } = await import('./openai.config');
    const aiHealth = await checkOpenAIHealth();
    openaiResult = { healthy: aiHealth.healthy, error: aiHealth.error };
    console.log('OpenAI health check result:', openaiResult);
  } catch (error) {
    console.error('OpenAI health check failed:', error);
    openaiResult = {
      healthy: false,
      error: error instanceof Error ? error.message : 'OpenAI health check failed'
    };
  }

  // Check environment validation with error handling
  try {
    const { validateEnvironment } = await import('./environment.config');
    environmentResult = validateEnvironment();
    console.log('Environment validation result:', environmentResult);
  } catch (error) {
    console.error('Environment validation failed:', error);
    environmentResult = {
      valid: false,
      errors: [error instanceof Error ? error.message : 'Environment validation failed']
    };
  }

  const results = {
    database: databaseResult,
    openai: openaiResult,
    environment: environmentResult,
  };

  const allHealthy = results.database.healthy &&
                    results.openai.healthy &&
                    results.environment.valid;

  console.log('Overall health check result:', { healthy: allHealthy, services: results });

  return {
    healthy: allHealthy,
    services: results,
  };
};

// Get all configuration info
export const getAllConfigInfo = async () => {
  const { environmentConfig } = await import('./environment.config');
  const { getDatabaseInfo } = await import('./database.config');
  const { getOpenAIInfo } = await import('./openai.config');

  return {
    environment: {
      nodeEnv: environmentConfig.nodeEnv,
      port: environmentConfig.port,
      host: environmentConfig.host,
    },
    database: getDatabaseInfo(),
    openai: getOpenAIInfo(),
  };
};
