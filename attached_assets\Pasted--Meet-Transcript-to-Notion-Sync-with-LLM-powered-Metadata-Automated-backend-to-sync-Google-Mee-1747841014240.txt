# Meet Transcript to Notion Sync (with LLM-powered Metadata)

Automated backend to sync Google Meet transcripts (from Google Docs in a shared drive) to a Notion database, running on Google Cloud Run and triggered by Google Cloud Scheduler. Now uses OpenAI's ChatGPT API (4.1 nano) to extract meeting names and attendees from the summary section of each meeting note.

---

## Features
- Scans a shared Google Drive for new Google Docs with meeting transcripts
- Extracts content from "Notes" and "Transcript" tabs
- **Uses ChatGPT 4.1 nano to extract meeting name and attendees from the summary paragraph**
- Uploads to a Notion database with metadata (Name, Date, Time, Attendees)
- Archives processed docs to a `Processed Transcripts` folder
- Runs every 30 minutes (or as scheduled)

---

## Setup Prerequisites

1. **Google Cloud Project** with APIs enabled:
    - Google Drive API
    - Google Docs API
    - Cloud Run
    - Cloud Scheduler
2. **Google Workspace** (Business Standard or higher)
3. **Notion Integration**
    - Create an integration at https://www.notion.so/my-integrations
    - Share your Notion database with the integration
4. **Prepare Notion Database**
    - Columns: `Name` (title), `Date` (date), `Time` (text), `Attendees` (text)
5. **Shared Drive**
    - Name: `Shared Meeting Transcripts Drive`
    - Docs must have tabs: `Notes` and `Transcript`
6. **Service Account Authentication**
    - In Google Cloud Console, create a service account with Drive and Docs access
    - Download the service account JSON key as `service_account.json`
    - Share your Shared Drive with the service account email as a Content manager or Editor
    - Place `service_account.json` in your project directory (do not commit to public repos)
    - Remove any `client_secret.json` and `token.pickle` files
    - The script will use the service account for all Google API access
7. **OpenAI API Key**
    - Get your API key from https://platform.openai.com/api-keys
    - Save it in `chatgptapi.json` as:
      ```json
      { "key": "sk-..." }
      ```
8. **Config Files**
    - `config.json`:
      ```json
      {
        "notion_token": "ntn_xxxxxxxxxxxxxxxxxxxxx",
        "database_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
      }
      ```

---

## Local Development

```bash
pip install -r requirements.txt
python main.py
```
- Visit [http://127.0.0.1:8080/run](http://127.0.0.1:8080/run) to trigger processing.

## Docker Build & Deploy

```dockerfile
# See Dockerfile in repo
```

### Build and Push
```bash
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/transcript-runner
```

### Deploy to Cloud Run
```bash
gcloud run deploy transcript-runner \
  --image gcr.io/YOUR_PROJECT_ID/transcript-runner \
  --platform managed \
  --region us-central1
```

### Set Up Scheduler
```bash
gcloud scheduler jobs create http transcript-sync-job \
  --schedule="*/30 * * * *" \
  --uri="https://YOUR_CLOUD_RUN_URL/run" \
  --http-method=GET \
  --oidc-service-account-email="cloud-run-invoker@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --location=us-central1
```

---

## Module Structure
- `main.py`: Orchestration and Flask app
- `google_drive_utils.py`: Google Drive/Docs helpers, summary extraction
- `notion_utils.py`: Notion API logic, LLM-powered metadata
- `metadata_utils.py`: Date/time extraction from filename
- `llm_utils.py`: OpenAI API calls for meeting name and attendees

---

## Security
- OIDC authentication for Scheduler
- No user credentials in code
- Notion token is internal and scoped
- Service account key is required for Google API access
- OpenAI API key is required for LLM features

---

## Maintainer Checklist
- [ ] Google Cloud and Notion access
- [ ] `service_account.json`, `chatgptapi.json`, and `config.json` present
- [ ] Manual run from Scheduler tested
- [ ] Notion page appears
- [ ] Processed files are moved

---

## FAQ
- See internal documentation for troubleshooting and advanced usage. 