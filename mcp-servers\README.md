# MCP Servers

## Building with TypeScript

To compile all TypeScript files to the `dist` directory using the TypeScript compiler:

```sh
npm run build
```

## Bundling with esbuild

To bundle all MCP server entry points (all `*-server.ts` files in each subfolder) into the `dist` directory using esbuild, run:

```sh
npm run bundle
```

- This uses the root-level `esbuild.mcp.config.js`.
- All dependencies are marked as external, so only your code is bundled.
- Output is written to `dist/` with the same folder structure as the source.
- Source maps are generated for easier debugging.

Use this for fast, production-ready builds or when you want a single-file output per server.
