import { storage } from '../storage/index.js';

/**
 * Function Tools Service for Agentic Actions
 * Inspired by LlamaIndex tutorial Part 3 - implements HITL pattern
 */

export interface FunctionTool {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
  handler: (params: any) => Promise<string> | string;
}

export class FunctionToolsService {
  private tools: Map<string, FunctionTool> = new Map();
  private dynamicTools: Map<string, FunctionTool> = new Map();

  constructor() {
    this.initializeTools();
    this.initializeDynamicTools();
  }

  /**
   * Initialize all available function tools
   */
  private initializeTools() {
    // File Management Tools
    this.registerTool(this.createDraftFileCreationTool());
    this.registerTool(this.createConfirmFileCreationTool());
    this.registerTool(this.createSearchFilesTool());
    this.registerTool(this.createFileInfoTool());

    // Google Drive Tools (for future implementation)
    this.registerTool(this.createDraftGoogleDriveFileTool());
    this.registerTool(this.createConfirmGoogleDriveFileTool());
  }

  /**
   * Initialize dynamic tools (inspired by Part 3.5 ActionKit patterns)
   * Creates tools based on available integrations and capabilities
   */
  private initializeDynamicTools() {
    // Create confirmation tools for different action types
    this.registerDynamicTool(this.createGenericConfirmationTool());
    this.registerDynamicTool(this.createChainedActionTool());
    this.registerDynamicTool(this.createBulkFileOperationTool());

    // Create platform-specific tools based on available integrations
    this.createPlatformSpecificTools();
  }

  /**
   * Register a function tool
   */
  private registerTool(tool: FunctionTool) {
    this.tools.set(tool.name, tool);
  }

  /**
   * Register a dynamic function tool
   */
  private registerDynamicTool(tool: FunctionTool) {
    this.dynamicTools.set(tool.name, tool);
  }

  /**
   * Get all available tools for OpenAI function calling
   * Includes both static and dynamic tools
   */
  getToolsForOpenAI(): any[] {
    const allTools = [
      ...Array.from(this.tools.values()),
      ...Array.from(this.dynamicTools.values())
    ];

    return allTools.map(tool => ({
      type: "function",
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  /**
   * Execute a function tool by name
   */
  async executeTool(name: string, parameters: any): Promise<string> {
    let tool = this.tools.get(name);
    if (!tool) {
      tool = this.dynamicTools.get(name);
    }

    if (!tool) {
      throw new Error(`Function tool '${name}' not found`);
    }

    try {
      console.log(`[FunctionTools] Executing ${name} with parameters:`, parameters);
      const result = await tool.handler(parameters);
      console.log(`[FunctionTools] ${name} result:`, result);
      return result;
    } catch (error: any) {
      console.error(`[FunctionTools] Error executing ${name}:`, error);
      throw new Error(`Failed to execute ${name}: ${error.message}`);
    }
  }

  /**
   * Check if a tool exists
   */
  hasTool(name: string): boolean {
    return this.tools.has(name) || this.dynamicTools.has(name);
  }

  /**
   * Get tool names
   */
  getToolNames(): string[] {
    return [
      ...Array.from(this.tools.keys()),
      ...Array.from(this.dynamicTools.keys())
    ];
  }

  // =============================================================================
  // TOOL DEFINITIONS - Following LlamaIndex HITL Pattern
  // =============================================================================

  /**
   * Draft File Creation Tool (Step 1 of HITL)
   */
  private createDraftFileCreationTool(): FunctionTool {
    return {
      name: "draftFileCreation",
      description: "Draft a new file creation. This is a required step before creating any file. Prompts user for confirmation before actual creation.",
      parameters: {
        type: "object",
        properties: {
          fileName: {
            type: "string",
            description: "Name of the file to create (with extension)",
          },
          content: {
            type: "string",
            description: "Content of the file to create",
          },
          fileType: {
            type: "string",
            description: "Type of file (txt, md, json, etc.)",
          },
          description: {
            type: "string",
            description: "Brief description of what this file contains",
          },
        },
        required: ["fileName", "content", "fileType"],
      },
      handler: (params) => {
        const { fileName, content, fileType, description } = params;
        
        return `📄 **File Creation Draft**

**File Name:** ${fileName}
**File Type:** ${fileType}
**Description:** ${description || 'No description provided'}

**Content Preview:**
\`\`\`
${content.substring(0, 200)}${content.length > 200 ? '...' : ''}
\`\`\`

**Full Content Length:** ${content.length} characters

⚠️ **This is a draft only.** The file has not been created yet.

Would you like me to proceed with creating this file? Please confirm by saying "yes, create the file" or similar.`;
      },
    };
  }

  /**
   * Confirm File Creation Tool (Step 2 of HITL)
   */
  private createConfirmFileCreationTool(): FunctionTool {
    return {
      name: "confirmFileCreation",
      description: "Confirm and create a file after drafting. Only use this after a draft has been created and user has given affirmative confirmation.",
      parameters: {
        type: "object",
        properties: {
          confirmation: {
            type: "string",
            description: "User's affirmative confirmation to create the file",
          },
          fileName: {
            type: "string",
            description: "Name of the file to create",
          },
          content: {
            type: "string",
            description: "Content of the file",
          },
          fileType: {
            type: "string",
            description: "Type of file",
          },
        },
        required: ["confirmation", "fileName", "content", "fileType"],
      },
      handler: async (params) => {
        const { confirmation, fileName, content, fileType } = params;
        
        // Check for affirmative confirmation
        const affirmativeWords = ['yes', 'confirm', 'proceed', 'create', 'go ahead', 'ok', 'okay'];
        const isAffirmative = affirmativeWords.some(word => 
          confirmation.toLowerCase().includes(word)
        );
        
        if (!isAffirmative) {
          return "❌ File creation cancelled. No affirmative confirmation detected.";
        }

        try {
          // Create file in uploaded_files platform
          const fileData = {
            externalId: `uploaded_${Date.now()}_${fileName}`,
            fileName,
            fileType,
            mimeType: this.getMimeType(fileType),
            platform: 'uploaded_files',
            fileContent: content,
            fileSize: content.length,
            userId: 'anonymous', // For MVP
            status: 'active',
            tags: ['ai-created'],
            extractedMetadata: {
              createdBy: 'GPT Unify',
              creationMethod: 'agentic-action',
              description: `File created via AI assistant`,
            },
          };

          const createdFile = await storage.createFile(fileData);
          
          return `✅ **File Created Successfully!**

**File ID:** ${createdFile.id}
**File Name:** ${fileName}
**File Type:** ${fileType}
**Size:** ${content.length} characters
**Platform:** Uploaded Files

The file has been created and is now available in your uploaded files. You can search for it or reference it in future conversations.`;
          
        } catch (error: any) {
          return `❌ **Error Creating File:** ${error.message}`;
        }
      },
    };
  }

  /**
   * Search Files Tool
   */
  private createSearchFilesTool(): FunctionTool {
    return {
      name: "searchFiles",
      description: "Search for files by name, content, or description across all connected sources.",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "Search query (file name, content keywords, or description)",
          },
          platform: {
            type: "string",
            description: "Optional: specific platform to search (google_drive, uploaded_files, etc.)",
          },
          fileType: {
            type: "string",
            description: "Optional: specific file type to search for",
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return (default: 10)",
          },
        },
        required: ["query"],
      },
      handler: async (params) => {
        const { query, platform, fileType, limit = 10 } = params;
        
        try {
          const files = await storage.searchFiles(query, platform, fileType);
          const limitedFiles = files.slice(0, limit);
          
          if (limitedFiles.length === 0) {
            return `🔍 **No files found** matching "${query}"${platform ? ` in ${platform}` : ''}${fileType ? ` with type ${fileType}` : ''}.`;
          }
          
          let result = `🔍 **Found ${limitedFiles.length} file(s)** matching "${query}":\n\n`;
          
          limitedFiles.forEach((file, index) => {
            result += `**${index + 1}. ${file.fileName}**\n`;
            result += `   - Platform: ${file.platform}\n`;
            result += `   - Type: ${file.fileType}\n`;
            result += `   - Size: ${file.fileSize || 'Unknown'} bytes\n`;
            result += `   - Modified: ${file.lastModified || file.updatedAt}\n`;
            if (file.extractedMetadata?.description) {
              result += `   - Description: ${file.extractedMetadata.description}\n`;
            }
            result += `\n`;
          });
          
          return result;
        } catch (error: any) {
          return `❌ **Search Error:** ${error.message}`;
        }
      },
    };
  }

  /**
   * Get File Info Tool
   */
  private createFileInfoTool(): FunctionTool {
    return {
      name: "getFileInfo",
      description: "Get detailed information about a specific file by ID or name.",
      parameters: {
        type: "object",
        properties: {
          fileId: {
            type: "number",
            description: "File ID to get information for",
          },
          fileName: {
            type: "string",
            description: "Alternative: file name to search for",
          },
        },
        required: [],
      },
      handler: async (params) => {
        const { fileId, fileName } = params;
        
        if (!fileId && !fileName) {
          return "❌ Please provide either fileId or fileName.";
        }
        
        try {
          let file;
          
          if (fileId) {
            file = await storage.getFile(fileId);
          } else if (fileName) {
            const files = await storage.searchFiles(fileName);
            file = files.find(f => f.fileName === fileName) || files[0];
          }
          
          if (!file) {
            return `❌ File not found${fileId ? ` with ID ${fileId}` : ''}${fileName ? ` with name "${fileName}"` : ''}.`;
          }
          
          return `📄 **File Information**

**Name:** ${file.fileName}
**ID:** ${file.id}
**Type:** ${file.fileType}
**Platform:** ${file.platform}
**Size:** ${file.fileSize || 'Unknown'} bytes
**Status:** ${file.status}
**Created:** ${file.createdAt}
**Modified:** ${file.lastModified || file.updatedAt}
**User:** ${file.userId || 'Unknown'}

**Metadata:**
${file.extractedMetadata ? JSON.stringify(file.extractedMetadata, null, 2) : 'No metadata available'}

**Tags:** ${file.tags?.join(', ') || 'No tags'}`;
          
        } catch (error: any) {
          return `❌ **Error getting file info:** ${error.message}`;
        }
      },
    };
  }

  // =============================================================================
  // GOOGLE DRIVE TOOLS (Future Implementation)
  // =============================================================================

  /**
   * Draft Google Drive File Tool (Step 1 of HITL)
   */
  private createDraftGoogleDriveFileTool(): FunctionTool {
    return {
      name: "draftGoogleDriveFile",
      description: "Draft creating a file in Google Drive. This is a required step before creating any Google Drive file.",
      parameters: {
        type: "object",
        properties: {
          fileName: {
            type: "string",
            description: "Name of the file to create in Google Drive",
          },
          content: {
            type: "string",
            description: "Content of the file",
          },
          mimeType: {
            type: "string",
            description: "MIME type (e.g., 'text/plain', 'application/vnd.google-apps.document')",
          },
          folderId: {
            type: "string",
            description: "Optional: Google Drive folder ID to create file in",
          },
        },
        required: ["fileName", "content", "mimeType"],
      },
      handler: (params) => {
        const { fileName, content, mimeType, folderId } = params;
        
        return `📁 **Google Drive File Creation Draft**

**File Name:** ${fileName}
**MIME Type:** ${mimeType}
**Folder:** ${folderId || 'Root folder'}
**Content Length:** ${content.length} characters

**Content Preview:**
\`\`\`
${content.substring(0, 200)}${content.length > 200 ? '...' : ''}
\`\`\`

⚠️ **This is a draft only.** The file has not been created in Google Drive yet.

Would you like me to proceed with creating this file in Google Drive? Please confirm by saying "yes, create in Google Drive" or similar.`;
      },
    };
  }

  /**
   * Confirm Google Drive File Tool (Step 2 of HITL)
   */
  private createConfirmGoogleDriveFileTool(): FunctionTool {
    return {
      name: "confirmGoogleDriveFile",
      description: "Confirm and create a file in Google Drive after drafting. Only use after draft and user confirmation.",
      parameters: {
        type: "object",
        properties: {
          confirmation: {
            type: "string",
            description: "User's affirmative confirmation",
          },
          fileName: {
            type: "string",
            description: "Name of the file to create",
          },
          content: {
            type: "string",
            description: "Content of the file",
          },
          mimeType: {
            type: "string",
            description: "MIME type of the file",
          },
          folderId: {
            type: "string",
            description: "Optional: Google Drive folder ID",
          },
        },
        required: ["confirmation", "fileName", "content", "mimeType"],
      },
      handler: async (params) => {
        // For MVP, this will return a placeholder
        // In the future, this would integrate with Google Drive API
        return `🚧 **Google Drive Integration Coming Soon**

This feature will be implemented in a future update. For now, I can only create files in the uploaded files section.

Would you like me to create this file as an uploaded file instead?`;
      },
    };
  }

  // =============================================================================
  // DYNAMIC TOOLS (Inspired by Part 3.5 ActionKit patterns)
  // =============================================================================

  /**
   * Create platform-specific tools based on available integrations
   */
  private createPlatformSpecificTools() {
    // For MVP, we'll create tools for available platforms
    const availablePlatforms = ['google_drive', 'uploaded_files'];

    availablePlatforms.forEach(platform => {
      this.registerDynamicTool(this.createPlatformSearchTool(platform));
    });
  }

  /**
   * Create a platform-specific search tool
   */
  private createPlatformSearchTool(platform: string): FunctionTool {
    return {
      name: `search${platform.charAt(0).toUpperCase() + platform.slice(1).replace('_', '')}Files`,
      description: `Search for files specifically in ${platform.replace('_', ' ')}`,
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: `Search query for ${platform} files`,
          },
          fileType: {
            type: "string",
            description: "Optional: specific file type to search for",
          },
          limit: {
            type: "number",
            description: "Maximum number of results (default: 10)",
          },
        },
        required: ["query"],
      },
      handler: async (params) => {
        const { query, fileType, limit = 10 } = params;

        try {
          const files = await storage.searchFiles(query, platform, fileType);
          const limitedFiles = files.slice(0, limit);

          if (limitedFiles.length === 0) {
            return `🔍 **No files found** in ${platform.replace('_', ' ')} matching "${query}".`;
          }

          let result = `🔍 **Found ${limitedFiles.length} file(s)** in ${platform.replace('_', ' ')} matching "${query}":\n\n`;

          limitedFiles.forEach((file, index) => {
            result += `**${index + 1}. ${file.fileName}**\n`;
            result += `   - Type: ${file.fileType}\n`;
            result += `   - Size: ${file.fileSize || 'Unknown'} bytes\n`;
            result += `   - Modified: ${file.lastModified || file.updatedAt}\n\n`;
          });

          return result;
        } catch (error: any) {
          return `❌ **Search Error in ${platform}:** ${error.message}`;
        }
      },
    };
  }

  /**
   * Generic confirmation tool for any action type
   */
  private createGenericConfirmationTool(): FunctionTool {
    return {
      name: "confirmAction",
      description: "Generic confirmation tool for any action that requires user approval",
      parameters: {
        type: "object",
        properties: {
          actionType: {
            type: "string",
            description: "Type of action being confirmed (create, delete, update, etc.)",
          },
          actionDetails: {
            type: "string",
            description: "Details of the action to be confirmed",
          },
          confirmation: {
            type: "string",
            description: "User's confirmation response",
          },
        },
        required: ["actionType", "actionDetails", "confirmation"],
      },
      handler: (params) => {
        const { actionType, actionDetails, confirmation } = params;

        const affirmativeWords = ['yes', 'confirm', 'proceed', 'go ahead', 'ok', 'okay', 'do it'];
        const isAffirmative = affirmativeWords.some(word =>
          confirmation.toLowerCase().includes(word)
        );

        if (isAffirmative) {
          return `✅ **Action Confirmed**: ${actionType}\n\n**Details**: ${actionDetails}\n\n**Status**: Proceeding with action...`;
        } else {
          return `❌ **Action Cancelled**: ${actionType}\n\n**Reason**: User did not provide affirmative confirmation.`;
        }
      },
    };
  }

  /**
   * Chained action tool for performing multiple actions in sequence
   */
  private createChainedActionTool(): FunctionTool {
    return {
      name: "chainActions",
      description: "Execute multiple actions in sequence with confirmation",
      parameters: {
        type: "object",
        properties: {
          actions: {
            type: "array",
            description: "Array of actions to execute in sequence",
            items: {
              type: "object",
              properties: {
                action: { type: "string" },
                parameters: { type: "object" },
              },
            },
          },
          confirmation: {
            type: "string",
            description: "User confirmation to execute all actions",
          },
        },
        required: ["actions", "confirmation"],
      },
      handler: async (params) => {
        const { actions, confirmation } = params;

        const affirmativeWords = ['yes', 'confirm', 'proceed', 'all', 'execute'];
        const isAffirmative = affirmativeWords.some(word =>
          confirmation.toLowerCase().includes(word)
        );

        if (!isAffirmative) {
          return `❌ **Chained Actions Cancelled**: User did not confirm execution.`;
        }

        let result = `🔗 **Executing ${actions.length} Chained Actions**:\n\n`;

        for (let i = 0; i < actions.length; i++) {
          const action = actions[i];
          result += `**Step ${i + 1}**: ${action.action}\n`;
          result += `**Parameters**: ${JSON.stringify(action.parameters, null, 2)}\n`;
          result += `**Status**: ✅ Queued for execution\n\n`;
        }

        result += `**Note**: This is a simulation. In a full implementation, these actions would be executed sequentially.`;

        return result;
      },
    };
  }

  /**
   * Bulk file operation tool
   */
  private createBulkFileOperationTool(): FunctionTool {
    return {
      name: "bulkFileOperation",
      description: "Perform operations on multiple files at once",
      parameters: {
        type: "object",
        properties: {
          operation: {
            type: "string",
            description: "Type of operation (search, info, tag, etc.)",
          },
          fileIds: {
            type: "array",
            description: "Array of file IDs to operate on",
            items: { type: "number" },
          },
          parameters: {
            type: "object",
            description: "Additional parameters for the operation",
          },
        },
        required: ["operation", "fileIds"],
      },
      handler: async (params) => {
        const { operation, fileIds, parameters = {} } = params;

        let result = `📁 **Bulk ${operation.toUpperCase()} Operation**\n\n`;
        result += `**Files**: ${fileIds.length} files\n`;
        result += `**Operation**: ${operation}\n\n`;

        if (operation === 'info') {
          result += `**Results**:\n`;
          for (const fileId of fileIds) {
            try {
              const file = await storage.getFile(fileId);
              if (file) {
                result += `✅ **${file.fileName}** (ID: ${fileId})\n`;
                result += `   - Type: ${file.fileType}\n`;
                result += `   - Platform: ${file.platform}\n\n`;
              } else {
                result += `❌ **File ID ${fileId}**: Not found\n\n`;
              }
            } catch (error) {
              result += `❌ **File ID ${fileId}**: Error - ${error}\n\n`;
            }
          }
        } else {
          result += `**Status**: Operation queued for ${fileIds.length} files\n`;
          result += `**Note**: This is a simulation. In a full implementation, the ${operation} operation would be performed on all specified files.`;
        }

        return result;
      },
    };
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(fileType: string): string {
    const mimeTypes: Record<string, string> = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'json': 'application/json',
      'csv': 'text/csv',
      'html': 'text/html',
      'xml': 'application/xml',
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };

    return mimeTypes[fileType.toLowerCase()] || 'text/plain';
  }

  /**
   * Get available tool categories
   */
  getToolCategories(): Record<string, string[]> {
    const allTools = this.getToolNames();

    return {
      'File Management': allTools.filter(name =>
        name.includes('File') || name.includes('Search') || name.includes('Info')
      ),
      'Confirmations': allTools.filter(name =>
        name.includes('confirm') || name.includes('Confirmation')
      ),
      'Advanced Actions': allTools.filter(name =>
        name.includes('chain') || name.includes('bulk') || name.includes('Action')
      ),
      'Platform Specific': allTools.filter(name =>
        name.includes('GoogleDrive') || name.includes('UploadedFiles')
      ),
    };
  }
}

// Export singleton instance
export const functionToolsService = new FunctionToolsService();