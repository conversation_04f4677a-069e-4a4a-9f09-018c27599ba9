DatabaseState:
  get:
    tags:
      - Debug
    summary: Get database state
    description: Debug endpoint to check database state and file information
    operationId: getDatabaseState
    responses:
      '200':
        description: Database state retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                files:
                  type: object
                chunks:
                  type: object
                integrations:
                  type: array
                  items:
                    type: object
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DiagnosticHealth:
  get:
    tags:
      - Debug
    summary: Diagnostic health check
    description: Extended health check with detailed diagnostic information
    operationId: getDiagnosticHealth
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestEncryption:
  post:
    tags:
      - Debug
    summary: Test encryption functionality
    description: Test the encryption and decryption functionality
    operationId: testEncryption
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestGoogleCredentials:
  get:
    tags:
      - Debug
    summary: Test Google credentials
    description: Test Google OAuth credentials for an integration
    operationId: testGoogleCredentials
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestIntegrationCreation:
  post:
    tags:
      - Debug
    summary: Test integration creation
    description: Test the integration creation process
    operationId: testIntegrationCreation
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

SeedData:
  post:
    tags:
      - Debug
    summary: Seed test data
    description: Seed the database with test data
    operationId: seedData
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ClearIntegrations:
  delete:
    tags:
      - Debug
    summary: Clear all integrations
    description: Clear all integrations and related data (DESTRUCTIVE)
    operationId: clearIntegrations
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DiagnosticIntegration:
  get:
    tags:
      - Debug
    summary: Get diagnostic integration info
    description: Get detailed diagnostic information for an integration
    operationId: getDiagnosticIntegration
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestSchema:
  post:
    tags:
      - Debug
    summary: Test database schema
    description: Test database schema validation
    operationId: testSchema
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestCredentialFlow:
  post:
    tags:
      - Debug
    summary: Test credential flow
    description: Test the OAuth credential flow
    operationId: testCredentialFlow
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ClearCredentials:
  post:
    tags:
      - Debug
    summary: Clear credentials
    description: Clear credentials for an integration
    operationId: clearCredentials
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestSync:
  post:
    tags:
      - Debug
    summary: Test sync functionality
    description: Test synchronization functionality for an integration
    operationId: testSync
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DiagnosticStorage:
  get:
    tags:
      - Debug
    summary: Get storage diagnostics
    description: Get diagnostic information about storage systems
    operationId: getDiagnosticStorage
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DiagnosticDatabase:
  get:
    tags:
      - Debug
    summary: Get database diagnostics
    description: Get diagnostic information about database connectivity and health
    operationId: getDiagnosticDatabase
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DiagnosticServices:
  get:
    tags:
      - Debug
    summary: Get services diagnostics
    description: Get diagnostic information about external services
    operationId: getDiagnosticServices
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 