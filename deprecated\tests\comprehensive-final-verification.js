/**
 * COMPREHENSIVE FINAL VERIFICATION FOR PHASE 2 MODULARIZATION
 * 
 * This script performs a complete verification that ALL functionality from the 
 * original 1,371-line integration controller has been properly transferred to 
 * the modular architecture.
 */

import { readFileSync, writeFileSync } from 'fs';
import path from 'path';

const SERVER_URL = 'http://localhost:5000';

// Complete list of all methods from the original integration controller
const ORIGINAL_METHODS = [
  // Core CRUD Operations
  'getIntegrations',
  'getIntegration', 
  'createIntegration',
  'updateIntegration',
  'deleteIntegration',
  
  // OAuth Operations
  'getAuthUrl',
  'handleOAuthCallback',
  
  // Google Drive Operations
  'getGoogleDriveFolders',
  'getDriveStructure', 
  'debugDriveFolders',
  
  // Microsoft Teams Operations
  'getTeamsAuthUrl',
  'handleTeamsOAuthCallback',
  'getTeamsSources',
  'getTeamsChannels',
  'getTeamsFolders',
  'testTeamsConnection',
  
  // Connection Testing
  'testConnection',
  
  // Scheduling
  'updateSchedule',
  'getSchedules'
];

// Verification results
const verification = {
  methodTransfer: {},
  dependencyCheck: {},
  apiEndpoints: {},
  codebaseReferences: {},
  summary: {
    totalMethods: ORIGINAL_METHODS.length,
    transferredMethods: 0,
    missingMethods: [],
    extraMethods: [],
    allDependenciesFixed: true,
    allEndpointsWork: true,
    noStaleReferences: true
  }
};

console.log('🔍 COMPREHENSIVE FINAL VERIFICATION');
console.log('===================================\\n');

/**
 * 1. VERIFY ALL METHODS TRANSFERRED TO MODULAR COMPONENTS
 */
async function verifyMethodTransfer() {
  console.log('📋 1. VERIFYING METHOD TRANSFER FROM ORIGINAL TO MODULAR...');
  
  try {
    // Import the modular facade
    const { integrationController } = await import('./server/controllers/integration/index.js');
    
    for (const method of ORIGINAL_METHODS) {
      const exists = typeof integrationController[method] === 'function';
      verification.methodTransfer[method] = {
        transferred: exists,
        location: exists ? 'Modular Facade' : 'MISSING'
      };
      
      if (exists) {
        verification.summary.transferredMethods++;
        console.log(`  ✅ ${method}: Present in modular facade`);
      } else {
        verification.summary.missingMethods.push(method);
        console.log(`  ❌ ${method}: MISSING from modular facade`);
      }
    }
    
    // Check for any extra methods that weren't in the original
    const facadeMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(integrationController))
      .filter(name => name !== 'constructor' && typeof integrationController[name] === 'function');
    
    for (const method of facadeMethods) {
      if (!ORIGINAL_METHODS.includes(method)) {
        verification.summary.extraMethods.push(method);
      }
    }
    
  } catch (error) {
    console.error(`  ❌ Error importing modular facade: ${error.message}`);
    verification.summary.allDependenciesFixed = false;
  }
  
  console.log(`\\n  📊 Transfer Summary: ${verification.summary.transferredMethods}/${verification.summary.totalMethods} methods transferred\\n`);
}

/**
 * 2. VERIFY ALL API ENDPOINTS WORK WITH MODULAR IMPLEMENTATION
 */
async function verifyApiEndpoints() {
  console.log('🌐 2. VERIFYING ALL API ENDPOINTS WITH MODULAR IMPLEMENTATION...');
  
  const endpoints = [
    { method: 'GET', path: '/api/integrations', expected: 200 },
    { method: 'GET', path: '/api/integrations/999', expected: 404 },
    { method: 'POST', path: '/api/integrations', body: {}, expected: 400 },
    { method: 'GET', path: '/api/schedules', expected: 200 },
    { method: 'POST', path: '/api/schedules', body: {}, expected: 400 }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const options = {
        method: endpoint.method,
        headers: { 'Content-Type': 'application/json' }
      };
      
      if (endpoint.body) {
        options.body = JSON.stringify(endpoint.body);
      }
      
      const response = await fetch(`${SERVER_URL}${endpoint.path}`, options);
      const success = response.status === endpoint.expected;
      
      verification.apiEndpoints[`${endpoint.method} ${endpoint.path}`] = {
        expected: endpoint.expected,
        actual: response.status,
        success
      };
      
      if (!success) {
        verification.summary.allEndpointsWork = false;
      }
      
      console.log(`  ${success ? '✅' : '❌'} ${endpoint.method} ${endpoint.path}: ${response.status} (expected ${endpoint.expected})`);
    } catch (error) {
      console.log(`  ❌ ${endpoint.method} ${endpoint.path}: ERROR - ${error.message}`);
      verification.apiEndpoints[`${endpoint.method} ${endpoint.path}`] = {
        expected: endpoint.expected,
        actual: 'ERROR',
        success: false,
        error: error.message
      };
      verification.summary.allEndpointsWork = false;
    }
  }
  
  console.log('');
}

/**
 * 3. VERIFY ALL DEPENDENCIES AND IMPORTS ARE CORRECT
 */
async function verifyDependencies() {
  console.log('🔗 3. VERIFYING ALL DEPENDENCIES AND IMPORTS...');
  
  const filesToCheck = [
    { 
      file: 'server/routes.ts',
      shouldContain: 'import { integrationController } from "./controllers/integration/index.js"',
      description: 'Routes import modular facade'
    },
    {
      file: 'server/controllers/integration/index.ts',
      shouldContain: 'export const integrationController',
      description: 'Facade exports controller instance'
    },
    {
      file: 'server/controllers/integration/shared/integration-crud.controller.ts', 
      shouldContain: 'extends BaseIntegrationController',
      description: 'CRUD controller extends base class'
    },
    {
      file: 'server/controllers/integration/google/google-oauth.controller.ts',
      shouldContain: 'extends BaseIntegrationController', 
      description: 'Google OAuth controller extends base class'
    },
    {
      file: 'server/controllers/integration/microsoft/teams-oauth.controller.ts',
      shouldContain: 'extends BaseIntegrationController',
      description: 'Teams OAuth controller extends base class'
    }
  ];
  
  for (const check of filesToCheck) {
    try {
      const content = readFileSync(check.file, 'utf8');
      const success = content.includes(check.shouldContain);
      
      verification.dependencyCheck[check.file] = {
        success,
        description: check.description,
        issue: success ? null : `Missing: ${check.shouldContain}`
      };
      
      if (!success) {
        verification.summary.allDependenciesFixed = false;
      }
      
      console.log(`  ${success ? '✅' : '❌'} ${check.description}: ${success ? 'OK' : 'FAILED'}`);
    } catch (error) {
      console.log(`  ❌ ${check.description}: ERROR - ${error.message}`);
      verification.dependencyCheck[check.file] = {
        success: false,
        description: check.description,
        issue: `File error: ${error.message}`
      };
      verification.summary.allDependenciesFixed = false;
    }
  }
  
  console.log('');
}

/**
 * 4. VERIFY NO STALE REFERENCES TO ORIGINAL FILE
 */
async function verifyNoStaleReferences() {
  console.log('🧹 4. VERIFYING NO STALE REFERENCES TO ORIGINAL FILE...');
  
  // This would normally use a more sophisticated search, but for this test we'll check key files
  const keyFiles = [
    'server/routes.ts',
    'tests/validate-modularization.js'
  ];
  
  const stalePatterns = [
    'controllers/integration.js',
    'controllers/integration.ts',
    '../integration.js',
    '../integration.ts'
  ];
  
  for (const file of keyFiles) {
    try {
      const content = readFileSync(file, 'utf8');
      let hasStaleRefs = false;
      const foundPatterns = [];
      
      for (const pattern of stalePatterns) {
        if (content.includes(pattern) && !content.includes(pattern + ' // OLD REFERENCE UPDATED')) {
          hasStaleRefs = true;
          foundPatterns.push(pattern);
        }
      }
      
      verification.codebaseReferences[file] = {
        clean: !hasStaleRefs,
        staleReferences: foundPatterns
      };
      
      if (hasStaleRefs) {
        verification.summary.noStaleReferences = false;
        console.log(`  ❌ ${file}: Found stale references: ${foundPatterns.join(', ')}`);
      } else {
        console.log(`  ✅ ${file}: No stale references found`);
      }
    } catch (error) {
      console.log(`  ❌ ${file}: Error reading file - ${error.message}`);
      verification.codebaseReferences[file] = {
        clean: false,
        error: error.message
      };
      verification.summary.noStaleReferences = false;
    }
  }
  
  console.log('');
}

/**
 * 5. INTEGRATION TESTING - CREATE/READ/UPDATE/DELETE TEST
 */
async function integrationTest() {
  console.log('🧪 5. PERFORMING INTEGRATION TEST (CRUD WORKFLOW)...');
  
  try {
    // Create integration
    const createResponse = await fetch(`${SERVER_URL}/api/integrations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'google-drive',
        name: 'Verification Test Integration',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: false,
        syncFilters: {},
        syncSchedule: null
      })
    });
    
    if (!createResponse.ok) {
      throw new Error(`Create failed: ${createResponse.status}`);
    }
    
    const createData = await createResponse.json();
    const integrationId = createData.integration.id;
    console.log(`  ✅ CREATE: Integration created with ID ${integrationId}`);
    
    // Read integration
    const readResponse = await fetch(`${SERVER_URL}/api/integrations/${integrationId}`);
    if (!readResponse.ok) {
      throw new Error(`Read failed: ${readResponse.status}`);
    }
    console.log(`  ✅ READ: Integration ${integrationId} retrieved successfully`);
    
    // Update integration
    const updateResponse = await fetch(`${SERVER_URL}/api/integrations/${integrationId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Updated Verification Test Integration',
        type: 'google-drive'
      })
    });
    
    if (!updateResponse.ok) {
      throw new Error(`Update failed: ${updateResponse.status}`);
    }
    console.log(`  ✅ UPDATE: Integration ${integrationId} updated successfully`);
    
    // Delete integration
    const deleteResponse = await fetch(`${SERVER_URL}/api/integrations/${integrationId}`, {
      method: 'DELETE'
    });
    
    if (!deleteResponse.ok) {
      throw new Error(`Delete failed: ${deleteResponse.status}`);
    }
    console.log(`  ✅ DELETE: Integration ${integrationId} deleted successfully`);
    
    verification.integrationTest = { success: true };
    
  } catch (error) {
    console.log(`  ❌ INTEGRATION TEST FAILED: ${error.message}`);
    verification.integrationTest = { success: false, error: error.message };
  }
  
  console.log('');
}

/**
 * 6. GENERATE FINAL REPORT
 */
function generateFinalReport() {
  console.log('📊 FINAL VERIFICATION REPORT');
  console.log('=============================\\n');
  
  // Calculate overall success
  const methodTransferSuccess = verification.summary.transferredMethods === verification.summary.totalMethods;
  const integrationTestSuccess = verification.integrationTest?.success === true;
  
  const overallSuccess = methodTransferSuccess && 
                        verification.summary.allDependenciesFixed && 
                        verification.summary.allEndpointsWork && 
                        verification.summary.noStaleReferences &&
                        integrationTestSuccess;
  
  console.log(`🎯 OVERALL STATUS: ${overallSuccess ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`📈 METHOD TRANSFER: ${verification.summary.transferredMethods}/${verification.summary.totalMethods} (${((verification.summary.transferredMethods/verification.summary.totalMethods)*100).toFixed(1)}%)`);
  console.log(`🔗 DEPENDENCIES: ${verification.summary.allDependenciesFixed ? '✅ All Fixed' : '❌ Issues Found'}`);
  console.log(`🌐 API ENDPOINTS: ${verification.summary.allEndpointsWork ? '✅ All Working' : '❌ Issues Found'}`);
  console.log(`🧹 STALE REFERENCES: ${verification.summary.noStaleReferences ? '✅ None Found' : '❌ Found Issues'}`);
  console.log(`🧪 INTEGRATION TEST: ${integrationTestSuccess ? '✅ Passed' : '❌ Failed'}`);
  
  if (verification.summary.missingMethods.length > 0) {
    console.log(`\\n❌ MISSING METHODS: ${verification.summary.missingMethods.join(', ')}`);
  }
  
  if (verification.summary.extraMethods.length > 0) {
    console.log(`\\n➕ EXTRA METHODS: ${verification.summary.extraMethods.join(', ')}`);
  }
  
  console.log('\\n🏆 PHASE 2 MODULARIZATION STATUS:');
  if (overallSuccess) {
    console.log('✅ 🟢 MODULARIZATION COMPLETE AND VERIFIED!');
    console.log('🗑️ ✅ SAFE TO DELETE the original integration.ts file');
    console.log('🚀 All functionality successfully transferred to modular architecture');
    console.log('🎯 Zero breaking changes - API maintains full backward compatibility');
    console.log('🔧 Improved maintainability, testability, and development experience');
  } else {
    console.log('❌ 🔴 MODULARIZATION INCOMPLETE - ISSUES FOUND');
    console.log('⚠️ DO NOT DELETE original integration.ts until issues are resolved');
    console.log('🔍 Review the detailed report above to identify and fix issues');
  }
  
  // Save detailed report
  try {
    writeFileSync('comprehensive-verification-report.json', JSON.stringify(verification, null, 2));
    console.log('\\n📄 Detailed report saved to: comprehensive-verification-report.json');
  } catch (error) {
    console.log(`\\n⚠️ Could not save report: ${error.message}`);
  }
}

// Run all verification steps
async function runComprehensiveVerification() {
  try {
    await verifyMethodTransfer();
    await verifyApiEndpoints();
    await verifyDependencies();
    await verifyNoStaleReferences();
    await integrationTest();
    generateFinalReport();
  } catch (error) {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  }
}

// Execute verification
runComprehensiveVerification(); 