import React, { useState, useEffect } from 'react';

/**
 * Demo component showing how smart processing works in your app
 */
export const SmartProcessingDemo: React.FC = () => {
  const [processingInfo, setProcessingInfo] = useState<any>(null);
  const [batchStatus, setBatchStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchProcessingInfo();
  }, []);

  const fetchProcessingInfo = async () => {
    try {
      const response = await fetch('/api/smart-processing/processing-info');
      const data = await response.json();
      setProcessingInfo(data);
    } catch (error) {
      console.error('Error fetching processing info:', error);
    }
  };

  const handleProcessPending = async (urgent: boolean = false) => {
    setLoading(true);
    try {
      const response = await fetch('/api/smart-processing/process-pending', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ urgent })
      });
      
      const data = await response.json();
      
      if (data.processing?.method === 'batch') {
        setBatchStatus(data.processing.result);
        alert(`Files submitted for background processing!\nBatch ID: ${data.processing.result.batchId}\nEstimated time: ${data.processing.result.estimatedTime}`);
      } else {
        alert('Files processed immediately!');
      }
      
      fetchProcessingInfo(); // Refresh
    } catch (error) {
      console.error('Error processing files:', error);
      alert('Error processing files');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadFile = async () => {
    // Simulate file upload
    const content = "This is a test document uploaded by the user.";
    
    setLoading(true);
    try {
      const response = await fetch('/api/smart-processing/upload-and-process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: 'test-upload.txt',
          content,
          urgent: true
        })
      });
      
      const data = await response.json();
      
      if (data.processing?.method === 'real-time') {
        alert('File uploaded and processed immediately! You can chat about it now.');
      } else {
        alert('File uploaded and submitted for background processing.');
      }
      
      fetchProcessingInfo(); // Refresh
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Error uploading file');
    } finally {
      setLoading(false);
    }
  };

  const checkBatchStatus = async () => {
    if (!batchStatus?.batchId) return;
    
    try {
      const response = await fetch(`/api/smart-processing/batch-status/${batchStatus.batchId}`);
      const data = await response.json();
      setBatchStatus({ ...batchStatus, ...data.batch });
    } catch (error) {
      console.error('Error checking batch status:', error);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Smart Processing Demo</h2>
      
      {/* Current Status */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="font-semibold mb-2">📊 Current Status</h3>
        {processingInfo ? (
          <div>
            <p><strong>Files needing processing:</strong> {processingInfo.pendingFiles}</p>
            <p><strong>Recommended method:</strong> 
              <span className={`ml-2 px-2 py-1 rounded text-sm ${
                processingInfo.recommendation === 'batch' 
                  ? 'bg-yellow-200 text-yellow-800' 
                  : 'bg-green-200 text-green-800'
              }`}>
                {processingInfo.recommendation}
              </span>
            </p>
            <p className="text-sm text-gray-600 mt-1">
              {processingInfo.reasons[processingInfo.recommendation]}
            </p>
          </div>
        ) : (
          <p>Loading...</p>
        )}
      </div>

      {/* User Scenarios */}
      <div className="grid md:grid-cols-2 gap-6 mb-6">
        
        {/* Scenario 1: Initial Sync */}
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-3">🔄 Scenario 1: Initial Google Drive Sync</h3>
          <p className="text-sm text-gray-600 mb-3">
            You just connected Google Drive and have {processingInfo?.pendingFiles || 0} files to process.
          </p>
          <div className="space-y-2">
            <button
              onClick={() => handleProcessPending(false)}
              disabled={loading}
              className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Processing...' : 'Smart Process (Recommended)'}
            </button>
            <button
              onClick={() => handleProcessPending(true)}
              disabled={loading}
              className="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
            >
              Force Real-Time (May hit rate limits)
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Smart processing will choose batch for {processingInfo?.pendingFiles || 0} files
          </p>
        </div>

        {/* Scenario 2: User Upload */}
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-3">📤 Scenario 2: User File Upload</h3>
          <p className="text-sm text-gray-600 mb-3">
            User uploads a file and wants to chat about it immediately.
          </p>
          <button
            onClick={handleUploadFile}
            disabled={loading}
            className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading ? 'Uploading...' : 'Upload & Process Now'}
          </button>
          <p className="text-xs text-gray-500 mt-2">
            Always uses real-time processing when user is waiting
          </p>
        </div>
      </div>

      {/* Batch Status */}
      {batchStatus && (
        <div className="bg-yellow-50 p-4 rounded-lg mb-6">
          <h3 className="font-semibold mb-2">⏳ Batch Processing Status</h3>
          <div className="space-y-1">
            <p><strong>Batch ID:</strong> {batchStatus.batchId}</p>
            <p><strong>Status:</strong> {batchStatus.status || 'submitted'}</p>
            <p><strong>Message:</strong> {batchStatus.message}</p>
            <p><strong>Estimated Time:</strong> {batchStatus.estimatedTime}</p>
            <p><strong>Cost:</strong> {batchStatus.cost}</p>
          </div>
          <button
            onClick={checkBatchStatus}
            className="mt-3 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Check Status
          </button>
        </div>
      )}

      {/* How It Works */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-3">🧠 How Smart Processing Works</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-start space-x-2">
            <span className="text-green-500">✅</span>
            <span><strong>Real-Time:</strong> User uploads → Process immediately → Chat right away</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="text-blue-500">⏳</span>
            <span><strong>Batch:</strong> Bulk sync → Submit to background → Process overnight → Ready next day</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="text-purple-500">🧠</span>
            <span><strong>Smart:</strong> App automatically chooses based on context</span>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-white rounded border-l-4 border-blue-500">
          <p className="text-sm">
            <strong>Your Current Situation:</strong> You have {processingInfo?.pendingFiles || 0} files from Google Drive sync. 
            The app recommends <strong>{processingInfo?.recommendation}</strong> processing because it's more efficient for bulk operations.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SmartProcessingDemo;
