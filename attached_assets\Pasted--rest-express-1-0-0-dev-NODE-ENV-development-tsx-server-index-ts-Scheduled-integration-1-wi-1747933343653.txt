
> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Scheduled integration 1 with cron: 0 9 * * * (next run: Fri May 23 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
4:49:21 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
4:49:24 PM [express] GET /api/integrations 304 in 79ms :: {"integrations":[{"id":1,"name":"Google Me…
4:49:24 PM [express] GET /api/sync-logs 304 in 355ms :: {"logs":[{"id":20,"integrationId":1,"startTi…
4:49:53 PM [express] GET /api/integrations 304 in 310ms :: {"integrations":[{"id":1,"name":"Google M…
OpenAI service initialized successfully
4:49:55 PM [express] POST /api/sync-now 200 in 287ms :: {"message":"Sync started successfully","sync…
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
4:49:55 PM [express] GET /api/integrations 200 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Found 0 files in processed folder
After filtering: 3 files remaining to process
Extracting content from document: Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (ID: 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk)
Successfully extracted content (40694 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Technical Development Meeting
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Source is not a property that exists.'
}
Error creating transcript page: APIResponseError: Source is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:50:00 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '159',
    connection: 'keep-alive',
    'cf-ray': '943dcf5f8bc553d4-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"9f-Ujz4WRnerTAVEIudJlNgZucv9ac"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '1904f4fd-bfc1-4a5b-929a-bfae0a2916e9',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=bexdlYKJvsRNR2J_nXSRDlQJVxhvPk2cCbsqDdGxx40-1747932600-*******-ASeMfnttpF6YhPoiEK0FXepMy.1a.v2T4mJKyhdTvUm8aioLMQN_ofHNorl2OVRDljzlQjEHKd3DlgcBXSeTBEHl7RR5m1qBRQAi2fDJr58; path=/; expires=Thu, 22-May-25 17:20:00 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=a17nGLLJSUHMThe21QxHmqpA7IWyH1aQdQ3R3tPpxjg-1747932600467-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Source is not a property that exists.","request_id":"1904f4fd-bfc1-4a5b-929a-bfae0a2916e9"}'
}
Error processing item Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk): APIResponseError: Source is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:50:00 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '159',
    connection: 'keep-alive',
    'cf-ray': '943dcf5f8bc553d4-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"9f-Ujz4WRnerTAVEIudJlNgZucv9ac"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '1904f4fd-bfc1-4a5b-929a-bfae0a2916e9',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=bexdlYKJvsRNR2J_nXSRDlQJVxhvPk2cCbsqDdGxx40-1747932600-*******-ASeMfnttpF6YhPoiEK0FXepMy.1a.v2T4mJKyhdTvUm8aioLMQN_ofHNorl2OVRDljzlQjEHKd3DlgcBXSeTBEHl7RR5m1qBRQAi2fDJr58; path=/; expires=Thu, 22-May-25 17:20:00 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=a17nGLLJSUHMThe21QxHmqpA7IWyH1aQdQ3R3tPpxjg-1747932600467-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Source is not a property that exists.","request_id":"1904f4fd-bfc1-4a5b-929a-bfae0a2916e9"}'
}
File Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini will remain in source folder for retry in next sync
Extracting content from document: Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini (ID: 16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E)
Successfully extracted content (35496 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Development of an Integrated Meeting Transcription and Platform Automation Tool
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Source is not a property that exists.'
}
Error creating transcript page: APIResponseError: Source is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:50:06 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '159',
    connection: 'keep-alive',
    'cf-ray': '943dcf82b8e8676c-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"9f-LE1GUQO8OVKfaw/+SSejIN9Q+/g"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '8218a25e-16c1-454b-9c72-a78cd049da80',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=8Ar1MICkG_BzfKIyJNapXTc8jEvZ7RScwpNrzRLjtAY-1747932606-*******-ZYkofuTe5MRA6QlV0v9inwufGvgxZx0lZF5RQjVlBr1xcNcZ1ZUKgJhY9AftSqeFBkWiOzjM9zx58yJUDKN0xR9OHXrsXGEnfredMMwQe0k; path=/; expires=Thu, 22-May-25 17:20:06 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=.YxDQCD1KPbEUYvF12_6ndvmdckBljTiJSLV9tQ4TtQ-1747932606024-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Source is not a property that exists.","request_id":"8218a25e-16c1-454b-9c72-a78cd049da80"}'
}
Error processing item Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini (16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E): APIResponseError: Source is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:50:06 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '159',
    connection: 'keep-alive',
    'cf-ray': '943dcf82b8e8676c-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"9f-LE1GUQO8OVKfaw/+SSejIN9Q+/g"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '8218a25e-16c1-454b-9c72-a78cd049da80',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=8Ar1MICkG_BzfKIyJNapXTc8jEvZ7RScwpNrzRLjtAY-1747932606-*******-ZYkofuTe5MRA6QlV0v9inwufGvgxZx0lZF5RQjVlBr1xcNcZ1ZUKgJhY9AftSqeFBkWiOzjM9zx58yJUDKN0xR9OHXrsXGEnfredMMwQe0k; path=/; expires=Thu, 22-May-25 17:20:06 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=.YxDQCD1KPbEUYvF12_6ndvmdckBljTiJSLV9tQ4TtQ-1747932606024-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Source is not a property that exists.","request_id":"8218a25e-16c1-454b-9c72-a78cd049da80"}'
}
File Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini will remain in source folder for retry in next sync
Extracting content from document: Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (ID: 16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ)
Successfully extracted content (24949 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Automation and Integration of Meeting Notes and Recordings
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Source is not a property that exists.'
}
Error creating transcript page: APIResponseError: Source is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:50:10 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '159',
    connection: 'keep-alive',
    'cf-ray': '943dcfa00e0106ee-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"9f-B2LK4ZY5O5+9FTe5qRcNKI/Z+CM"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'b4e7811e-96d7-44e9-a2a1-eb2e03650d86',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=bz08puySA1E7XB2jEM6C3KXRuPzSN6mEnclqlVP_3DU-1747932610-*******-X59GHOxU_LJABOcX76fgmYuvgSgGkGyqqwUaXLzZD_z73L2jb6UpG0ePv1E8P6bcV34keFoFwkRlx2PtQKhGEfQyeqZ9cKfk_AyaM482rFg; path=/; expires=Thu, 22-May-25 17:20:10 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=m_ONHjnVKXNmvfTAvkzD.0QVfwl7sAux9sYikUdTY9k-1747932610765-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Source is not a property that exists.","request_id":"b4e7811e-96d7-44e9-a2a1-eb2e03650d86"}'
}
Error processing item Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ): APIResponseError: Source is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:50:10 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '159',
    connection: 'keep-alive',
    'cf-ray': '943dcfa00e0106ee-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"9f-B2LK4ZY5O5+9FTe5qRcNKI/Z+CM"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'b4e7811e-96d7-44e9-a2a1-eb2e03650d86',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=bz08puySA1E7XB2jEM6C3KXRuPzSN6mEnclqlVP_3DU-1747932610-*******-X59GHOxU_LJABOcX76fgmYuvgSgGkGyqqwUaXLzZD_z73L2jb6UpG0ePv1E8P6bcV34keFoFwkRlx2PtQKhGEfQyeqZ9cKfk_AyaM482rFg; path=/; expires=Thu, 22-May-25 17:20:10 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=m_ONHjnVKXNmvfTAvkzD.0QVfwl7sAux9sYikUdTY9k-1747932610765-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Source is not a property that exists.","request_id":"b4e7811e-96d7-44e9-a2a1-eb2e03650d86"}'
}
File Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini will remain in source folder for retry in next sync
Sync completed with status: failed
Items processed: 3, Success: 0, Failed: 3
4:53:27 PM [express] GET /api/integrations 200 in 7094ms :: {"integrations":[{"id":1,"name":"Google …
OpenAI service initialized successfully
4:53:32 PM [express] POST /api/sync-now 200 in 258ms :: {"message":"Sync started successfully","sync…
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
4:53:32 PM [express] GET /api/integrations 200 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Found 0 files in processed folder
After filtering: 3 files remaining to process
Extracting content from document: Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (ID: 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk)
Successfully extracted content (40694 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Technical Developments Meeting
Created Notion page for "Strategic Initiatives and Technical Developments Meeting": 1fbd68e5-c2ad-81c8-b049-f41d50d51789
Splitting summary of 457 chars into 1 chunks
Created chunk with 1950 characters (max: 2000)
Created chunk with 1943 characters (max: 2000)
Created chunk with 1880 characters (max: 2000)
Created chunk with 1815 characters (max: 2000)
Created chunk with 1826 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1892 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1876 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1866 characters (max: 2000)
Created chunk with 1871 characters (max: 2000)
Created chunk with 1873 characters (max: 2000)
Created chunk with 1835 characters (max: 2000)
Created chunk with 1791 characters (max: 2000)
Created chunk with 1894 characters (max: 2000)
Created chunk with 1763 characters (max: 2000)
Created chunk with 1935 characters (max: 2000)
Created chunk with 1931 characters (max: 2000)
Created chunk with 928 characters (max: 2000)
Created chunk with 75 characters (max: 2000)
Added content blocks to page 1fbd68e5-c2ad-81c8-b049-f41d50d51789
Moved file 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Successfully processed Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini and added to Notion database
Extracting content from document: Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini (ID: 16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E)
Successfully extracted content (35496 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Meeting on Project Directions, Tool Integration, and Platform Development
Created Notion page for "Meeting on Project Directions, Tool Integration, and Platform Development": 1fbd68e5-c2ad-81dc-88a4-e1f3de047c23
Splitting summary of 399 chars into 1 chunks
Created chunk with 1903 characters (max: 2000)
Created chunk with 1756 characters (max: 2000)
Created chunk with 1807 characters (max: 2000)
Created chunk with 1809 characters (max: 2000)
Created chunk with 1904 characters (max: 2000)
Created chunk with 1878 characters (max: 2000)
Created chunk with 1952 characters (max: 2000)
Created chunk with 1933 characters (max: 2000)
Created chunk with 1769 characters (max: 2000)
Created chunk with 1903 characters (max: 2000)
Created chunk with 1788 characters (max: 2000)
Created chunk with 1873 characters (max: 2000)
Created chunk with 1853 characters (max: 2000)
Created chunk with 1952 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1775 characters (max: 2000)
Created chunk with 1942 characters (max: 2000)
Created chunk with 1724 characters (max: 2000)
Created chunk with 75 characters (max: 2000)
Added content blocks to page 1fbd68e5-c2ad-81dc-88a4-e1f3de047c23
Moved file 16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Successfully processed Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini and added to Notion database
Extracting content from document: Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (ID: 16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ)
Successfully extracted content (24949 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Automating Meeting Notes and Integration Workflow
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Invalid select option, commas not allowed: Integration of Google Drive, Google Meet, and Notion'
}
Error creating transcript page: APIResponseError: Invalid select option, commas not allowed: Integration of Google Drive, Google Meet, and Notion
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:53:50 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '217',
    connection: 'keep-alive',
    'cf-ray': '943dd4fdbce6bd4b-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"d9-1n5EHYbriRtrIjUQor0FIRb558M"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '3bc5e321-ff7e-4477-a206-cc116b35ca21',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=zEhIpc3WeWhvlhFHkuk6uJBuq9QDulkcdz9N5N2BGV8-1747932830-*******-J8EnyGinQUYK8PF6Y5Jt00aPE_wtaSXpyCQ_WDvHFV3v14MIMBJJ_ITT_GkH1vgk21j.71FA5W8W0tSGEuzvr2u7bmm9VkbbGXWMnMZSFxI; path=/; expires=Thu, 22-May-25 17:23:50 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=J7SOEXGjy6TEtTwrpYMSc6AzwWu4.dd.YC.zEwGZfjE-1747932830579-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Invalid select option, commas not allowed: Integration of Google Drive, Google Meet, and Notion","request_id":"3bc5e321-ff7e-4477-a206-cc116b35ca21"}'
}
Error processing item Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ): APIResponseError: Invalid select option, commas not allowed: Integration of Google Drive, Google Meet, and Notion
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 16:53:50 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '217',
    connection: 'keep-alive',
    'cf-ray': '943dd4fdbce6bd4b-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"d9-1n5EHYbriRtrIjUQor0FIRb558M"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '3bc5e321-ff7e-4477-a206-cc116b35ca21',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=zEhIpc3WeWhvlhFHkuk6uJBuq9QDulkcdz9N5N2BGV8-1747932830-*******-J8EnyGinQUYK8PF6Y5Jt00aPE_wtaSXpyCQ_WDvHFV3v14MIMBJJ_ITT_GkH1vgk21j.71FA5W8W0tSGEuzvr2u7bmm9VkbbGXWMnMZSFxI; path=/; expires=Thu, 22-May-25 17:23:50 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=J7SOEXGjy6TEtTwrpYMSc6AzwWu4.dd.YC.zEwGZfjE-1747932830579-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Invalid select option, commas not allowed: Integration of Google Drive, Google Meet, and Notion","request_id":"3bc5e321-ff7e-4477-a206-cc116b35ca21"}'
}
File Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini will remain in source folder for retry in next sync
Sync completed with status: partial
Items processed: 3, Success: 2, Failed: 1