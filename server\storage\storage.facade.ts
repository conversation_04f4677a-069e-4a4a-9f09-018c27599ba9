import { IStorage } from './base/storage.interface';
import {
  UserStorage,
  IntegrationStorage,
  FileStorage,
  ChatStorage,
  RAGStorage,
  SyncStorage,
  ProjectStorage,
  EmailStorage,
} from './features';

import type { 
  User, InsertUser,
  Integration, InsertIntegration,
  SyncLog, InsertSyncLog,
  SyncItem, InsertSyncItem,
  File, InsertFile,
  FileChunk, InsertFileChunk,
  ChatSession, InsertChatSession,
  ChatMessage, InsertChatMessage,
  Project, InsertProject,
  Email, InsertEmail
} from "../../shared/index.js";

/**
 * Storage Facade - provides backward compatibility with the original storage interface
 * while using the new modular storage system underneath
 */
export class StorageFacade implements IStorage {
  private userStorage: UserStorage;
  private integrationStorage: IntegrationStorage;
  private fileStorage: FileStorage;
  private chatStorage: ChatStorage;
  private ragStorage: RAGStorage;
  private syncStorage: SyncStorage;
  private projectStorage: ProjectStorage;
  private emailStorage: EmailStorage;

  constructor(useDatabase = true) {
    // Initialize all storage modules
    this.userStorage = new UserStorage(useDatabase);
    this.integrationStorage = new IntegrationStorage(useDatabase);
    this.fileStorage = new FileStorage(useDatabase);
    this.chatStorage = new ChatStorage(useDatabase);
    this.ragStorage = new RAGStorage(useDatabase);
    this.syncStorage = new SyncStorage(useDatabase);
    this.projectStorage = new ProjectStorage(useDatabase);
    this.emailStorage = new EmailStorage(useDatabase);

    console.log(`✅ Modular storage facade initialized (${useDatabase ? 'database' : 'memory'} mode)`);
    
    // Check actual database connectivity
    if (useDatabase) {
      try {
        this.userStorage.getStorageInfo(); // Test one storage class
        console.log(`📊 Storage modules using: ${useDatabase ? 'database' : 'memory'} backend`);
      } catch (error) {
        console.log(`⚠️  Storage modules falling back to memory backend`);
      }
    }
  }

  // User methods - delegate to UserStorage
  async getUser(id: number): Promise<User | undefined> {
    return this.userStorage.getUser(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.userStorage.getUserByUsername(username);
  }

  async createUser(user: InsertUser): Promise<User> {
    return this.userStorage.createUser(user);
  }

  async getUsers(): Promise<User[]> {
    return this.userStorage.getAllUsers();
  }

  // Integration methods - delegate to IntegrationStorage
  async getIntegrations(): Promise<Integration[]> {
    return this.integrationStorage.getIntegrations();
  }

  async getIntegration(id: number): Promise<Integration | undefined> {
    return this.integrationStorage.getIntegration(id);
  }

  async getIntegrationsByType(type: string): Promise<Integration[]> {
    return this.integrationStorage.getIntegrationsByType(type);
  }

  async createIntegration(integration: InsertIntegration): Promise<Integration> {
    return this.integrationStorage.createIntegration(integration);
  }

  async updateIntegration(id: number, data: Partial<Integration>): Promise<Integration | undefined> {
    return this.integrationStorage.updateIntegration(id, data);
  }

  async deleteIntegration(id: number): Promise<boolean> {
    return this.integrationStorage.deleteIntegration(id);
  }

  async updateIntegrationStatus(id: number, status: string): Promise<Integration | undefined> {
    return this.integrationStorage.updateIntegrationStatus(id, status);
  }

  async updateIntegrationLastSync(id: number, lastSyncAt: Date): Promise<Integration | undefined> {
    return this.integrationStorage.updateIntegrationLastSync(id, lastSyncAt);
  }

  // Sync Log methods - delegate to SyncStorage
  async getSyncLogs(integrationId?: number, limit?: number): Promise<SyncLog[]> {
    return this.syncStorage.getSyncLogs(integrationId, limit);
  }

  async getSyncLog(id: number): Promise<SyncLog | undefined> {
    return this.syncStorage.getSyncLog(id);
  }

  async createSyncLog(log: InsertSyncLog): Promise<SyncLog> {
    return this.syncStorage.createSyncLog(log);
  }

  async updateSyncLog(id: number, data: Partial<SyncLog>): Promise<SyncLog | undefined> {
    return this.syncStorage.updateSyncLog(id, data);
  }

  // Sync Item methods - delegate to SyncStorage
  async getSyncItems(syncLogId?: number, status?: string): Promise<SyncItem[]> {
    return this.syncStorage.getSyncItems(syncLogId, status);
  }

  async getSyncItem(id: number): Promise<SyncItem | undefined> {
    return this.syncStorage.getSyncItem(id);
  }

  async getSyncItemByExternalId(externalId: string, integrationId: number): Promise<SyncItem | undefined> {
    return this.syncStorage.getSyncItemByExternalId(externalId, integrationId);
  }

  async createSyncItem(item: InsertSyncItem): Promise<SyncItem> {
    return this.syncStorage.createSyncItem(item);
  }

  async updateSyncItem(id: number, data: Partial<SyncItem>): Promise<SyncItem | undefined> {
    return this.syncStorage.updateSyncItem(id, data);
  }

  // File methods - delegate to FileStorage
  async getFiles(platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string): Promise<File[]> {
    return this.fileStorage.getFiles(platform, userId, limit, offset, folderId);
  }

  async getFile(id: number): Promise<File | undefined> {
    return this.fileStorage.getFile(id);
  }

  async getFileByExternalId(externalId: string, platform: string): Promise<File | undefined> {
    return this.fileStorage.getFileByExternalId(externalId, platform);
  }

  async createFile(file: InsertFile): Promise<File> {
    return this.fileStorage.createFile(file);
  }

  async updateFile(id: number, data: Partial<File>): Promise<File | undefined> {
    return this.fileStorage.updateFile(id, data);
  }

  async deleteFile(id: number): Promise<boolean> {
    return this.fileStorage.deleteFile(id);
  }

  async markFileAsDeleted(id: number): Promise<File | undefined> {
    return this.fileStorage.markFileAsDeleted(id);
  }

  async searchFiles(query: string, platform?: string, fileType?: string, folderId?: string): Promise<File[]> {
    return this.fileStorage.searchFiles(query, platform, fileType, folderId);
  }

  // Incremental sync methods - delegate to FileStorage
  async getFilesForIntegration(integrationId: number, platform: string): Promise<File[]> {
    return this.fileStorage.getFilesForIntegration(integrationId, platform);
  }

  async markFilesAsDeleted(externalIds: string[], platform: string): Promise<void> {
    return this.fileStorage.markFilesAsDeleted(externalIds, platform);
  }

  async updateFileStatus(id: number, status: string): Promise<File | undefined> {
    return this.fileStorage.updateFileStatus(id, status);
  }

  // RAG and Chat methods
  // File Chunks - delegate to RAGStorage
  async getFileChunks(fileId: number): Promise<FileChunk[]> {
    return this.ragStorage.getFileChunks(fileId);
  }

  async createFileChunk(chunk: InsertFileChunk): Promise<FileChunk> {
    return this.ragStorage.createFileChunk(chunk);
  }

  async deleteFileChunks(fileId: number): Promise<void> {
    return this.ragStorage.deleteFileChunks(fileId);
  }

  async deleteChunk(chunkId: number): Promise<void> {
    return this.ragStorage.deleteChunk(chunkId);
  }

  async searchSimilarChunks(queryEmbedding: number[], enabledSources: string[], limit: number): Promise<any[]> {
    return this.ragStorage.searchSimilarChunks(queryEmbedding, enabledSources, limit);
  }

  // Chat Sessions - delegate to ChatStorage
  async getChatSessions(userId?: string, limit?: number): Promise<ChatSession[]> {
    return this.chatStorage.getChatSessions(userId, limit);
  }

  async getChatSession(id: string): Promise<ChatSession | undefined> {
    return this.chatStorage.getChatSession(id);
  }

  async createChatSession(session: InsertChatSession): Promise<ChatSession> {
    return this.chatStorage.createChatSession(session);
  }

  async updateChatSession(id: string, data: Partial<ChatSession>): Promise<ChatSession | undefined> {
    return this.chatStorage.updateChatSession(id, data);
  }

  async deleteChatSession(id: string): Promise<boolean> {
    return this.chatStorage.deleteChatSession(id);
  }

  // Chat Messages - delegate to ChatStorage
  async getChatMessages(sessionId: string, limit?: number): Promise<ChatMessage[]> {
    return this.chatStorage.getChatMessages(sessionId, limit);
  }

  async createChatMessage(message: InsertChatMessage): Promise<ChatMessage> {
    return this.chatStorage.createChatMessage(message);
  }

  // Projects - delegate to ProjectStorage
  async getProjects(userId?: string): Promise<Project[]> {
    return this.projectStorage.getProjects(userId);
  }

  async getProject(id: number): Promise<Project | undefined> {
    return this.projectStorage.getProject(id);
  }

  async createProject(project: InsertProject): Promise<Project> {
    return this.projectStorage.createProject(project);
  }

  async updateProject(id: number, data: Partial<Project>): Promise<Project | undefined> {
    return this.projectStorage.updateProject(id, data);
  }

  async deleteProject(id: number): Promise<boolean> {
    return this.projectStorage.deleteProject(id);
  }

  // Email methods - delegate to EmailStorage
  async getEmails(platform?: string, userId?: string, page?: number, pageSize?: number): Promise<Email[]> {
    return this.emailStorage.getEmails(platform, userId, page, pageSize);
  }

  async getEmail(id: number): Promise<Email | undefined> {
    return this.emailStorage.getEmail(id);
  }

  async getEmailByExternalId(externalId: string, platform: string): Promise<Email | undefined> {
    return this.emailStorage.getEmailByExternalId(externalId, platform);
  }

  async createEmail(email: InsertEmail): Promise<Email> {
    return this.emailStorage.createEmail(email);
  }

  async updateEmail(id: number, data: Partial<Email>): Promise<Email | undefined> {
    return this.emailStorage.updateEmail(id, data);
  }

  async searchEmails(query: string, platform?: string, userId?: string): Promise<Email[]> {
    return this.emailStorage.searchEmails(query, platform, userId);
  }

  async markEmailsAsDeleted(externalIds: string[], platform: string): Promise<void> {
    return this.emailStorage.markEmailsAsDeleted(externalIds, platform);
  }

  async updateEmailChunkEmbedding(chunkId: number, embedding: number[]): Promise<void> {
    return this.emailStorage.updateEmailChunkEmbedding(chunkId, embedding);
  }

  getEmailStorage(): EmailStorage {
    return this.emailStorage;
  }

  // Development utility method to clear all data
  async clearAllData(): Promise<void> {
    console.log('🧹 Clearing all data across all storage modules...');
    
    try {
      await this.chatStorage.clearAllChatData();
      await this.ragStorage.clearAllChunks();
      await this.fileStorage.clearAllFiles();
      await this.syncStorage.clearAllSyncData();
      await this.integrationStorage.clearAllIntegrations();
      await this.projectStorage.clearAllProjects();
      await this.userStorage.clearAllUsers();
      // Note: Email storage doesn't have a clearAll method yet, could be added if needed
      
      console.log('✅ Successfully cleared all data from modular storage');
    } catch (error) {
      console.error('❌ Error clearing data from modular storage:', error);
      throw error;
    }
  }

  // Get storage information
  getStorageInfo() {
    return {
      type: 'modular-facade',
      modules: {
        user: this.userStorage.getStorageInfo(),
        integration: this.integrationStorage.getStorageInfo(),
        file: this.fileStorage.getStorageInfo(),
        chat: this.chatStorage.getStorageInfo(),
        rag: this.ragStorage.getStorageInfo(),
        sync: this.syncStorage.getStorageInfo(),
        project: this.projectStorage.getStorageInfo(),
        email: this.emailStorage.getStorageInfo(),
      },
    };
  }
}
