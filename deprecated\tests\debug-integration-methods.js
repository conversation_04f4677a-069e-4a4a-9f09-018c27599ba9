#!/usr/bin/env node

/**
 * 🔧 DEBUG INTEGRATION METHODS
 * 
 * This script debugs specific integration methods that are failing
 * in the modular controller to identify the root cause.
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function debugMethods() {
  console.log('🔧 DEBUGGING INTEGRATION METHODS');
  console.log('=================================\n');

  // Test individual method endpoints
  const tests = [
    {
      name: 'GET /api/integrations/999 (should return 404)',
      method: 'GET',
      url: `${BASE_URL}/api/integrations/999`,
      expectedStatus: 404
    },
    {
      name: 'DELETE /api/integrations/999 (should return 404)',  
      method: 'DELETE',
      url: `${BASE_URL}/api/integrations/999`,
      expectedStatus: 404
    },
    {
      name: 'GET /api/schedules (should return 200)',
      method: 'GET', 
      url: `${BASE_URL}/api/schedules`,
      expectedStatus: 200
    },
    {
      name: 'POST /api/integrations (empty body - should return 400)',
      method: 'POST',
      url: `${BASE_URL}/api/integrations`,
      expectedStatus: 400,
      body: {}
    }
  ];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      
      const options = {
        method: test.method,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (test.body) {
        options.body = JSON.stringify(test.body);
      }

      const response = await fetch(test.url, options);
      const data = await response.text();
      
      console.log(`  Status: ${response.status} (expected: ${test.expectedStatus})`);
      
      if (response.status === 500) {
        console.log(`  ❌ 500 Error Response: ${data.substring(0, 200)}...`);
        
        // Try to parse as JSON to get error details
        try {
          const errorData = JSON.parse(data);
          console.log(`  Error Details: ${JSON.stringify(errorData, null, 2)}`);
        } catch (parseError) {
          console.log(`  Raw Error: ${data}`);
        }
      } else if (response.status === test.expectedStatus) {
        console.log(`  ✅ Expected status received`);
      } else {
        console.log(`  ⚠️ Unexpected status: ${response.status}`);
      }
      
      console.log();
      
    } catch (error) {
      console.log(`  ❌ Network Error: ${error.message}\n`);
    }
  }

  // Test that we can import the modular controller directly
  console.log('🔍 Testing direct import of modular controller...');
  try {
    const { integrationController } = await import('./server/controllers/integration/index.js');
    console.log(`✅ Modular controller imported successfully`);
    console.log(`  Methods available: ${Object.getOwnPropertyNames(Object.getPrototypeOf(integrationController)).filter(name => name !== 'constructor').join(', ')}`);
  } catch (importError) {
    console.log(`❌ Import error: ${importError.message}`);
  }
}

debugMethods().catch(err => {
  console.error(`Debug failed: ${err.message}`);
  process.exit(1);
}); 