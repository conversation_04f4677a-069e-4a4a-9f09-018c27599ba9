import { BaseService } from "../base/service.interface";
import { storage } from "../../storage/index.js";
import { microsoftService } from "../platform-integrations/microsoft";
import { cryptoService } from "../core/crypto-service.js";
import { getFileProcessor } from './processors';

/**
 * Microsoft Teams sync service for reference-based synchronization
 */
export class MicrosoftSyncService {
  /**
   * Run Microsoft Teams sync for an integration
   * @param integration Integration to sync
   * @param syncLogId ID of the sync log
   */
  async runSync(integration: any, syncLogId: number): Promise<void> {
    let itemsProcessed = 0;
    let itemsSuccess = 0;
    let itemsFailed = 0;
    let itemsSkipped = 0;

    try {
      console.log(`Starting comprehensive Microsoft Teams sync for integration ${integration.id}`);

      // Get Microsoft credentials (decrypt them)
      const credentialsJson = await cryptoService.decrypt(integration.credentials);
      const credentials = JSON.parse(credentialsJson);

      // Use the comprehensive sync method that aggregates from all sources
      const syncResult = await microsoftService.syncAllSources(credentials);
      
      console.log(`Found ${syncResult.files.length} total files across all Microsoft sources`);

      // Get existing files for this platform
      const existingFiles = await storage.getFilesForIntegration(integration.id, 'microsoft_teams');
      console.log(`Found ${existingFiles.length} existing files in database`);

      // Create a map of existing files by externalId for quick lookup
      const existingFileMap = new Map<string, any>();
      for (const file of existingFiles) {
        existingFileMap.set(file.externalId, file);
      }

      // Track which external IDs we've seen in this sync
      const foundExternalIds = new Set<string>();

      // Update sync log with total files count
      await storage.updateSyncLog(syncLogId, {
        details: {
          totalFiles: syncResult.files.length,
          existingFiles: existingFiles.length,
          scanMode: 'comprehensive_reference_based',
          sources: syncResult.sources,
          summary: syncResult.summary,
        },
      });

      // Process each file for metadata extraction and reference storage
      for (const file of syncResult.files) {
        itemsProcessed++;
        foundExternalIds.add(file.externalId || '');

        // Minimal delay between files (sequential processing is very safe)
        if (itemsProcessed > 1) {
          console.log('Waiting 200ms before processing next Teams file...');
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        try {
          // Check if we already have this file in our database
          const existingFile = existingFileMap.get(file.externalId || '');
          
          // Process the file and handle vectorization using the new modular processor
          const processor = await getFileProcessor('microsoft_teams');
          const result = await processor.processFile(
            file,
            existingFile,
            integration
          );

          if (result.success) {
            itemsSuccess++;
            
            // Create a successful sync item for tracking
            await storage.createSyncItem({
              type: file.fileType,
              title: file.fileName,
              status: "success",
              integrationId: integration.id,
              syncLogId,
              externalId: file.externalId,
              sourceUrl: file.sourceUrl,
              destinationUrl: null, // We're not creating Notion pages anymore
              metadata: {
                platform: 'microsoft_teams',
                fileId: result.savedFile?.id,
                referenceSync: true,
                comprehensive: true,
                wasUpdated: !!existingFile,
                sourceType: (file.extractedMetadata as any)?._sourceType,
                sourceContext: (file.extractedMetadata as any)?._sourceContext,
              },
            });
          } else {
            itemsFailed++;
            
            // Create a failed sync item for tracking
            await storage.createSyncItem({
              type: "unknown",
              title: file.fileName || "Unknown file",
              status: "failed",
              integrationId: integration.id,
              syncLogId,
              externalId: file.externalId || "",
              sourceUrl: file.sourceUrl || "",
              error: result.error || "Unknown error occurred",
              metadata: {
                platform: 'microsoft_teams',
                referenceSync: true,
                comprehensive: true,
              },
            });
          }

          if (result.skipped) {
            itemsSkipped++;
          }

        } catch (error) {
          console.error(`Error processing Microsoft file ${file.fileName} (${file.externalId}):`, error);
          itemsFailed++;
        }

        // Update sync log periodically
        if (itemsProcessed % 50 === 0) {
          await storage.updateSyncLog(syncLogId, {
            itemsProcessed,
            itemsSuccess,
            itemsFailed,
            details: {
              totalFiles: syncResult.files.length,
              existingFiles: existingFiles.length,
              scanMode: 'comprehensive_reference_based',
              sources: syncResult.sources,
              summary: syncResult.summary,
              itemsSkipped,
              lastProcessedFile: file.fileName,
            },
          });
        }
      }

      // Handle deleted files (files that exist in database but weren't found in this sync)
      const deletedCount = await this.handleDeletedFiles(existingFiles, foundExternalIds);

      // Final sync log update
      const syncStatus = itemsFailed === 0 ? "success" : itemsSuccess > 0 ? "partial" : "failed";

      await storage.updateSyncLog(syncLogId, {
        status: syncStatus,
        endTime: new Date(),
        itemsProcessed,
        itemsSuccess,
        itemsFailed,
        details: {
          totalFiles: syncResult.files.length,
          existingFiles: existingFiles.length,
          newFiles: itemsSuccess - (itemsProcessed - syncResult.files.length),
          updatedFiles: itemsProcessed - syncResult.files.length,
          skippedFiles: itemsSkipped,
          deletedFiles: deletedCount,
          scanMode: 'comprehensive_reference_based',
          sources: syncResult.sources,
          summary: syncResult.summary,
          completedAt: new Date().toISOString(),
        },
      });

      // Update integration status
      await storage.updateIntegrationStatus(
        integration.id,
        syncStatus === "failed" ? "error" : "connected"
      );

      console.log(`Microsoft Teams sync completed for integration ${integration.id}. Processed: ${itemsProcessed}, Success: ${itemsSuccess}, Failed: ${itemsFailed}, Skipped: ${itemsSkipped}, Deleted: ${deletedCount}`);

    } catch (error: any) {
      console.error(`Error in Microsoft Teams sync for integration ${integration.id}:`, error);

      // Update sync log with error
      await storage.updateSyncLog(syncLogId, {
        status: "failed",
        endTime: new Date(),
        itemsProcessed,
        itemsSuccess,
        itemsFailed,
        error: error.message || String(error),
        details: {
          errorDetails: error,
          itemsSkipped,
          failedAt: new Date().toISOString(),
        },
      });

      // Update integration status back to connected
      await storage.updateIntegrationStatus(integration.id, "connected");

      throw error;
    }
  }

  /**
   * Handle deleted files by marking them as deleted in the database
   */
  private async handleDeletedFiles(existingFiles: any[], foundExternalIds: Set<string>): Promise<number> {
    let deletedCount = 0;
    const missingExternalIds: string[] = [];

    for (const existingFile of existingFiles) {
      if (!foundExternalIds.has(existingFile.externalId) && existingFile.status === 'active') {
        missingExternalIds.push(existingFile.externalId);
        deletedCount++;
      }
    }

    if (missingExternalIds.length > 0) {
      console.log(`Marking ${missingExternalIds.length} files as deleted for platform: microsoft_teams`);
      await storage.markFilesAsDeleted(missingExternalIds, 'microsoft_teams');
      console.log(`Marked ${deletedCount} files as deleted (no longer in source)`);
    }

    return deletedCount;
  }
}

export const microsoftSyncService = new MicrosoftSyncService(); 