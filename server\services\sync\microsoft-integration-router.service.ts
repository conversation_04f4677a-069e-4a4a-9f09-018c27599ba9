import { MicrosoftCredentials } from '../../types/microsoft.types';
import { MicrosoftFacade } from '../platform-integrations/microsoft/microsoft.facade';

/**
 * Router service to handle different Microsoft integration types
 * This service determines which sync method to use based on integration type
 */
export class MicrosoftIntegrationRouter {
  private microsoftFacade: MicrosoftFacade;

  constructor() {
    this.microsoftFacade = new MicrosoftFacade();
  }

  /**
   * Route sync request to appropriate method based on integration type
   */
  async syncByIntegrationType(
    integrationType: 'teams' | 'onedrive' | 'sharepoint' | 'all',
    credentials: MicrosoftCredentials
  ): Promise<{
    files: any[];
    chats?: any[];
    sources: Record<string, number>;
    summary: {
      total: number;
      byType: Record<string, number>;
      bySource: Record<string, number>;
    };
    integrationType: string;
  }> {
    
    switch (integrationType) {
      case 'teams':
        console.log('🔄 Routing to Teams-only sync...');
        const teamsResult = await this.microsoftFacade.syncTeamsOnly(credentials);
        return {
          ...teamsResult,
          integrationType: 'teams'
        };

      case 'onedrive':
        console.log('🔄 Routing to OneDrive-only sync...');
        const onedriveResult = await this.microsoftFacade.syncOneDriveOnly(credentials);
        return {
          ...onedriveResult,
          integrationType: 'onedrive'
        };

      case 'sharepoint':
        console.log('🔄 Routing to SharePoint-only sync...');
        const sharepointResult = await this.microsoftFacade.syncSharePointOnly(credentials);
        return {
          ...sharepointResult,
          integrationType: 'sharepoint'
        };

      case 'all':
      default:
        console.log('🔄 Routing to legacy all-sources sync...');
        const allResult = await this.microsoftFacade.syncAllSources(credentials);
        return {
          ...allResult,
          integrationType: 'all'
        };
    }
  }

  /**
   * Get integration type from integration configuration
   */
  getIntegrationTypeFromConfig(integration: any): 'teams' | 'onedrive' | 'sharepoint' | 'all' {
    // Check integration name or type to determine which sync method to use
    const name = integration.name?.toLowerCase() || '';
    const type = integration.type?.toLowerCase() || '';
    
    if (name.includes('teams') || type.includes('teams')) {
      return 'teams';
    } else if (name.includes('onedrive') || type.includes('onedrive')) {
      return 'onedrive';
    } else if (name.includes('sharepoint') || type.includes('sharepoint')) {
      return 'sharepoint';
    }
    
    // Default to teams for existing Microsoft integrations
    return 'teams';
  }

  /**
   * Validate integration type
   */
  isValidIntegrationType(type: string): type is 'teams' | 'onedrive' | 'sharepoint' | 'all' {
    return ['teams', 'onedrive', 'sharepoint', 'all'].includes(type);
  }
}
