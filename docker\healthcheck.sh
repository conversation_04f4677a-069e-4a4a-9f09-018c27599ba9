#!/bin/sh
# =============================================================================
# MeetSync Application Health Check Script
# =============================================================================

set -e

# Configuration
HOST="${HOST:-localhost}"
PORT="${PORT:-8080}"
HEALTH_ENDPOINT="/api/health"
TIMEOUT=5

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [HEALTHCHECK] $1"
}

# Check if curl is available, fallback to wget
if command -v curl >/dev/null 2>&1; then
    HTTP_CLIENT="curl"
elif command -v wget >/dev/null 2>&1; then
    HTTP_CLIENT="wget"
else
    log "${RED}ERROR: Neither curl nor wget is available${NC}"
    exit 1
fi

# Perform health check
perform_health_check() {
    local url="http://${HOST}:${PORT}${HEALTH_ENDPOINT}"
    
    log "Performing health check on ${url}"
    
    if [ "$HTTP_CLIENT" = "curl" ]; then
        # Use curl for health check
        response=$(curl -s -w "%{http_code}" --max-time $TIMEOUT "$url" -o /tmp/health_response 2>/dev/null)
        http_code="${response}"
        
        if [ "$http_code" = "200" ]; then
            log "${GREEN}✓ Health check passed (HTTP $http_code)${NC}"
            
            # Optionally check response content
            if [ -f /tmp/health_response ]; then
                response_body=$(cat /tmp/health_response)
                if echo "$response_body" | grep -q '"status":"healthy"'; then
                    log "${GREEN}✓ Application reports healthy status${NC}"
                else
                    log "${YELLOW}⚠ Application responding but status unclear${NC}"
                fi
                rm -f /tmp/health_response
            fi
            
            return 0
        else
            log "${RED}✗ Health check failed (HTTP $http_code)${NC}"
            return 1
        fi
        
    elif [ "$HTTP_CLIENT" = "wget" ]; then
        # Use wget for health check
        if wget -q --timeout=$TIMEOUT --tries=1 -O /tmp/health_response "$url" 2>/dev/null; then
            log "${GREEN}✓ Health check passed${NC}"
            
            # Optionally check response content
            if [ -f /tmp/health_response ]; then
                response_body=$(cat /tmp/health_response)
                if echo "$response_body" | grep -q '"status":"healthy"'; then
                    log "${GREEN}✓ Application reports healthy status${NC}"
                else
                    log "${YELLOW}⚠ Application responding but status unclear${NC}"
                fi
                rm -f /tmp/health_response
            fi
            
            return 0
        else
            log "${RED}✗ Health check failed${NC}"
            return 1
        fi
    fi
}

# Basic port check as fallback
check_port() {
    log "Performing basic port check on ${HOST}:${PORT}"
    
    if command -v nc >/dev/null 2>&1; then
        if nc -z "$HOST" "$PORT" 2>/dev/null; then
            log "${GREEN}✓ Port $PORT is open${NC}"
            return 0
        else
            log "${RED}✗ Port $PORT is not accessible${NC}"
            return 1
        fi
    else
        log "${YELLOW}⚠ netcat not available, skipping port check${NC}"
        return 0
    fi
}

# Main health check logic
main() {
    log "Starting health check for MeetSync application"
    
    # First try the health endpoint
    if perform_health_check; then
        log "${GREEN}✓ Health check completed successfully${NC}"
        exit 0
    else
        log "${YELLOW}⚠ Health endpoint check failed, trying basic port check${NC}"
        
        # Fallback to basic port check
        if check_port; then
            log "${YELLOW}⚠ Port is accessible but health endpoint failed${NC}"
            exit 1
        else
            log "${RED}✗ Both health endpoint and port check failed${NC}"
            exit 1
        fi
    fi
}

# Run main function
main "$@" 