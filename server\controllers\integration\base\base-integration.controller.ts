import { Request, Response } from 'express';

/**
 * Base controller class for all integration controllers
 * Provides common functionality and patterns used across all integration types
 */
export abstract class BaseIntegrationController {
  constructor() {
    // Child classes will bind their specific methods
  }

  /**
   * Get storage instance dynamically
   * CRITICAL: This pattern must be preserved exactly as in the original
   */
  protected async getStorage() {
    const { storage } = await import('../../../storage/index.js');
    return storage;
  }

  /**
   * Validate integration ID from request parameters
   */
  protected validateIntegrationId(req: Request): { isValid: boolean; id?: number; error?: string } {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return { isValid: false, error: 'Invalid integration ID' };
    }
    return { isValid: true, id };
  }

  /**
   * Get integration by ID with validation
   */
  protected async getIntegrationById(id: number) {
    const storage = await this.getStorage();
    const integration = await storage.getIntegration(id);
    return integration;
  }

  /**
   * Standard error response helper
   */
  protected handleError(res: Response, error: any, message: string, statusCode: number = 500) {
    console.error(`[${this.constructor.name}] ${message}:`, error);
    console.error(`[${this.constructor.name}] Error stack:`, error.stack);
    
    return res.status(statusCode).json({
      message,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }

  /**
   * Success response helper
   */
  protected successResponse(res: Response, data: any, message?: string, statusCode: number = 200) {
    const response = message ? { message, ...data } : data;
    return res.status(statusCode).json(response);
  }

  /**
   * Validation error response helper
   */
  protected validationError(res: Response, errors: any, receivedData?: any) {
    return res.status(400).json({
      message: 'Validation failed',
      errors,
      ...(process.env.NODE_ENV === 'development' && receivedData ? { receivedData } : {})
    });
  }

  /**
   * Check if integration exists and has credentials
   */
  protected async validateIntegrationCredentials(req: Request, res: Response): Promise<{ 
    integration?: any; 
    success: boolean; 
    response?: Response 
  }> {
    const { isValid, id, error } = this.validateIntegrationId(req);
    if (!isValid) {
      return { 
        success: false, 
        response: res.status(400).json({ message: error }) 
      };
    }

    const integration = await this.getIntegrationById(id!);
    if (!integration) {
      return { 
        success: false, 
        response: res.status(404).json({ message: 'Integration not found' }) 
      };
    }

    if (!integration.credentials) {
      return { 
        success: false, 
        response: res.status(400).json({ message: 'Integration is not connected' }) 
      };
    }

    return { integration, success: true };
  }

  /**
   * Check if integration type matches expected types
   */
  protected validateIntegrationType(integration: any, expectedTypes: string[]): boolean {
    return expectedTypes.some(type => 
      integration.type === type || 
      integration.type.replace('_', '-') === type.replace('_', '-')
    );
  }

  /**
   * Generate OAuth state parameter
   */
  protected generateOAuthState(): string {
    return Math.random().toString(36).substring(2);
  }

  /**
   * Store OAuth state in integration config
   */
  protected async storeOAuthState(integrationId: number, state: string, redirectUri: string) {
    const storage = await this.getStorage();
    const integration = await storage.getIntegration(integrationId);
    
    if (!integration) {
      throw new Error('Integration not found');
    }

    const updatedConfig = integration.config || {};
    await storage.updateIntegration(integrationId, {
      config: {
        ...updatedConfig,
        oauthState: state,
        redirectUri,
      },
    });
  }

  /**
   * Validate OAuth state from callback
   */
  protected validateOAuthState(integration: any, receivedState: string): boolean {
    if (!integration.config || 
        (integration.config as Record<string, any>).oauthState === undefined || 
        receivedState !== (integration.config as Record<string, any>).oauthState) {
      return false;
    }
    return true;
  }

  /**
   * Get redirect URI from integration config
   */
  protected getRedirectUri(integration: any): string | null {
    return (integration.config as Record<string, any>)?.redirectUri || null;
  }

  /**
   * Update integration status
   */
  protected async updateIntegrationStatus(integrationId: number, status: string) {
    const storage = await this.getStorage();
    return await storage.updateIntegrationStatus(integrationId, status);
  }

  /**
   * Update integration credentials
   */
  protected async updateIntegrationCredentials(integrationId: number, credentials: string) {
    const storage = await this.getStorage();
    return await storage.updateIntegration(integrationId, {
      credentials,
      status: 'connected',
    });
  }

  /**
   * Redirect to frontend with success
   */
  protected redirectToSuccess(res: Response, integrationId: number) {
    return res.redirect(`/integrations/${integrationId}/setup?step=2&success=true`);
  }

  /**
   * Redirect to frontend with error
   */
  protected redirectToError(res: Response, integrationId: number, error: string) {
    return res.redirect(`/integrations/${integrationId}/setup?success=false&error=${encodeURIComponent(error)}`);
  }

  /**
   * Log controller action with consistent formatting
   */
  protected logAction(action: string, details?: any) {
    const controllerName = this.constructor.name;
    console.log(`[${controllerName}] ${action}`, details ? details : '');
  }

  /**
   * Log error with consistent formatting
   */
  protected logError(action: string, error: any, details?: any) {
    const controllerName = this.constructor.name;
    console.error(`[${controllerName}] ${action}:`, error);
    if (details) {
      console.error(`[${controllerName}] Details:`, details);
    }
  }
} 