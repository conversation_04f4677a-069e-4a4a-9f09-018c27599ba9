import { scheduleJob, Job, RecurrenceRule, RecurrenceSpecDateRange } from 'node-schedule';
import { syncController } from '../../controllers/sync';

/**
 * Service for scheduling and managing recurring sync jobs
 */
class SchedulerService {
  private jobs: Map<number, Job> = new Map();
  
  /**
   * Initialize the scheduler service
   * Loads all scheduled integrations from the database and schedules them
   */
  async initialize(): Promise<void> {
    try {
      // Get all integrations with schedules from storage
      const { storage } = await import('../../storage/index.js');
      const integrations = await storage.getIntegrations();
      
      let scheduledCount = 0;
      
      // Schedule each integration with a valid schedule
      for (const integration of integrations) {
        if (integration.syncSchedule) {
          this.scheduleIntegrationSync(integration.id, integration.syncSchedule);
          scheduledCount++;
        }
      }
      
      console.log(`Initialized scheduler with ${scheduledCount} jobs`);
    } catch (error) {
      console.error('Error initializing scheduler:', error);
    }
  }
  
  /**
   * Schedule an integration to sync on a recurring basis
   * @param integrationId ID of the integration to sync
   * @param cronExpression Cron expression for the schedule
   */
  scheduleIntegrationSync(integrationId: number, cronExpression: string): void {
    try {
      // Cancel any existing job for this integration
      this.cancelSchedule(integrationId);
      
      // Create a new job
      const job = scheduleJob(cronExpression, async () => {
        console.log(`Running scheduled sync for integration ${integrationId}`);
        
        try {
          // Run the sync operation
          await syncController.startSync(integrationId);
        } catch (error) {
          console.error(`Error in scheduled sync for integration ${integrationId}:`, error);
        }
      });
      
      // Store the job
      this.jobs.set(integrationId, job);
      
      const nextDate = job.nextInvocation();
      console.log(`Scheduled integration ${integrationId} with cron: ${cronExpression} (next run: ${nextDate})`);
    } catch (error) {
      console.error(`Error scheduling integration ${integrationId}:`, error);
    }
  }
  
  /**
   * Cancel a scheduled job
   * @param integrationId ID of the integration
   */
  cancelSchedule(integrationId: number): void {
    const job = this.jobs.get(integrationId);
    
    if (job) {
      job.cancel();
      this.jobs.delete(integrationId);
      console.log(`Cancelled schedule for integration ${integrationId}`);
    }
  }
  
  /**
   * Get the next execution time for a job
   * @param integrationId ID of the integration
   * @returns Date of the next execution or null if not scheduled
   */
  getNextExecutionTime(integrationId: number): Date | null {
    const job = this.jobs.get(integrationId);
    
    if (!job) {
      return null;
    }
    
    try {
      // Get the next invocation from the job
      const nextInvocation = job.nextInvocation();
      
      return nextInvocation;
    } catch (error) {
      console.error(`Error getting next execution time for integration ${integrationId}:`, error);
      return null;
    }
  }
  
  /**
   * Check if a schedule is active for an integration
   * @param integrationId ID of the integration
   * @returns True if scheduled, false otherwise
   */
  isScheduled(integrationId: number): boolean {
    return this.jobs.has(integrationId);
  }
}

export const schedulerService = new SchedulerService();