import { MCPError } from '../components/chat/MCPErrorNotification';

// Mock MCP error generator for demonstration
export class MCPErrorManager {
  private static errorTemplates = [
    {
      type: 'connection' as const,
      serverName: 'Google Drive',
      messages: [
        'Unable to connect to Google Drive. Please check your internet connection.',
        'Google Drive service is temporarily unavailable. Retrying...',
        'Connection timeout while accessing Google Drive files.'
      ],
      retryable: true
    },
    {
      type: 'authentication' as const,
      serverName: 'Microsoft Teams',
      messages: [
        'OAuth token has expired. Please re-authenticate with Microsoft Teams.',
        'Authentication failed. Please check your Microsoft Teams permissions.',
        'Access denied. Please sign in to Microsoft Teams again.'
      ],
      retryable: false
    },
    {
      type: 'rate_limit' as const,
      serverName: 'Google Drive',
      messages: [
        'Google Drive API rate limit exceeded. Please wait before trying again.',
        'Too many requests to Google Drive. Retrying in 30 seconds.',
        'API quota exceeded for Google Drive. Please try again later.'
      ],
      retryable: true
    },
    {
      type: 'tool_failure' as const,
      serverName: 'File Upload',
      messages: [
        'Failed to process uploaded file. File may be corrupted.',
        'File extraction failed. Unsupported file format.',
        'Unable to read file content. Please try uploading again.'
      ],
      retryable: true,
      toolNames: ['upload_file', 'get_file_content', 'process_document']
    },
    {
      type: 'server_error' as const,
      serverName: 'Microsoft Teams',
      messages: [
        'Microsoft Teams server error. Please try again.',
        'Internal server error while accessing Teams data.',
        'Teams service is experiencing issues. Please wait.'
      ],
      retryable: true,
      toolNames: ['get_messages', 'search_content', 'get_meeting_transcripts']
    }
  ];

  // Generate mock MCP errors for demonstration
  static generateMockError(): MCPError | null {
    // 15% chance of generating an error during tool usage
    if (Math.random() > 0.15) return null;

    const template = this.errorTemplates[Math.floor(Math.random() * this.errorTemplates.length)];
    const message = template.messages[Math.floor(Math.random() * template.messages.length)];
    
    return {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: template.type,
      serverName: template.serverName,
      toolName: template.toolNames ? 
        template.toolNames[Math.floor(Math.random() * template.toolNames.length)] : 
        undefined,
      message,
      timestamp: new Date(),
      retryable: template.retryable
    };
  }

  // Generate specific error types for testing
  static generateConnectionError(serverName: string): MCPError {
    return {
      id: `conn_error_${Date.now()}`,
      type: 'connection',
      serverName,
      message: `Unable to connect to ${serverName}. Please check your internet connection and try again.`,
      timestamp: new Date(),
      retryable: true
    };
  }

  static generateAuthError(serverName: string): MCPError {
    return {
      id: `auth_error_${Date.now()}`,
      type: 'authentication',
      serverName,
      message: `Authentication expired for ${serverName}. Please re-authenticate to continue using this service.`,
      timestamp: new Date(),
      retryable: false,
      actionUrl: '/settings?tab=mcp'
    };
  }

  static generateToolError(serverName: string, toolName: string, details?: string): MCPError {
    return {
      id: `tool_error_${Date.now()}`,
      type: 'tool_failure',
      serverName,
      toolName,
      message: details || `Tool "${toolName}" failed to execute. Please try again or contact support.`,
      timestamp: new Date(),
      retryable: true
    };
  }

  static generateRateLimitError(serverName: string): MCPError {
    return {
      id: `rate_error_${Date.now()}`,
      type: 'rate_limit',
      serverName,
      message: `${serverName} API rate limit exceeded. Please wait a moment before trying again.`,
      timestamp: new Date(),
      retryable: true
    };
  }

  // Simulate error resolution
  static async retryOperation(errorId: string): Promise<boolean> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // 70% success rate for retries
    return Math.random() > 0.3;
  }

  // Get user-friendly error messages with actions
  static getErrorActions(error: MCPError): Array<{
    label: string;
    action: string;
    primary?: boolean;
  }> {
    switch (error.type) {
      case 'authentication':
        return [
          { label: 'Re-authenticate', action: 'authenticate', primary: true },
          { label: 'Open Settings', action: 'settings' }
        ];
      
      case 'connection':
        return [
          { label: 'Retry Connection', action: 'retry', primary: true },
          { label: 'Check Status', action: 'status' }
        ];
      
      case 'rate_limit':
        return [
          { label: 'Wait & Retry', action: 'wait_retry', primary: true }
        ];
      
      case 'tool_failure':
        return [
          { label: 'Retry Tool', action: 'retry', primary: true },
          { label: 'Try Different Approach', action: 'alternative' }
        ];
      
      default:
        return [
          { label: 'Retry', action: 'retry', primary: true }
        ];
    }
  }
}
