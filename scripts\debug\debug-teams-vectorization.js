#!/usr/bin/env node

import { config } from 'dotenv';
import { storage } from './server/storage/index.js';
import { embeddingService } from './server/services/embedding-service.js';

// Load environment variables
config();

// Colors for console output
const colors = {
  yellow: '\x1b[33m',
  green: '\x1b[32m', 
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

console.log(`${colors.cyan}🔍 Teams File Vectorization Debug Tool${colors.reset}`);
console.log('');

async function debugTeamsVectorization() {
  try {
    // Step 1: Check Teams files in database
    console.log(`${colors.yellow}📋 Step 1: Checking Teams files in database...${colors.reset}`);
    const teamsFiles = await storage.getFiles('microsoft_teams');
    console.log(`Found ${teamsFiles.length} Teams files in database`);
    
    if (teamsFiles.length === 0) {
      console.log(`${colors.red}❌ No Teams files found. Run a Teams sync first.${colors.reset}`);
      return;
    }

    // Show sample Teams files
    console.log(`\n${colors.blue}Sample Teams files:${colors.reset}`);
    teamsFiles.slice(0, 5).forEach((file, index) => {
      console.log(`${index + 1}. ${file.fileName} (ID: ${file.id}, Type: ${file.fileType})`);
      const metadata = file.extractedMetadata;
      if (metadata && metadata._sourceContext) {
        console.log(`   Source: ${metadata._sourceContext}`);
      }
    });

    // Step 2: Check embeddings for Teams files
    console.log(`\n${colors.yellow}📊 Step 2: Checking embeddings for Teams files...${colors.reset}`);
    let filesWithEmbeddings = 0;
    let filesWithoutEmbeddings = 0;
    const filesNeedingEmbeddings = [];

    for (const file of teamsFiles) {
      const hasEmbeddings = await embeddingService.hasEmbeddings(file.id);
      if (hasEmbeddings) {
        filesWithEmbeddings++;
        const chunks = await storage.getFileChunks(file.id);
        console.log(`${colors.green}✅ ${file.fileName} - ${chunks.length} chunks${colors.reset}`);
      } else {
        filesWithoutEmbeddings++;
        filesNeedingEmbeddings.push(file);
        console.log(`${colors.red}❌ ${file.fileName} - No embeddings${colors.reset}`);
      }
    }

    console.log(`\n${colors.blue}Summary:${colors.reset}`);
    console.log(`- Files with embeddings: ${filesWithEmbeddings}`);
    console.log(`- Files without embeddings: ${filesWithoutEmbeddings}`);

    // Step 3: Generate embeddings for missing files (if embedding service is available)
    if (filesWithoutEmbeddings > 0 && embeddingService.isInitialized()) {
      console.log(`\n${colors.yellow}🔀 Step 3: Generating embeddings for files without them...${colors.reset}`);
      
      for (const file of filesNeedingEmbeddings.slice(0, 3)) { // Limit to first 3 for testing
        console.log(`\nProcessing: ${file.fileName}`);
        
        // Build content similar to the sync logic
        const metadataContent = [
          `File: ${file.fileName}`,
          `Type: ${file.fileType}`,
          `Platform: microsoft_teams`,
        ];

        if (file.extractedMetadata) {
          const metadata = file.extractedMetadata;
          if (metadata._sourceContext) metadataContent.push(`Source: ${metadata._sourceContext}`);
          if (metadata._teamName) metadataContent.push(`Team: ${metadata._teamName}`);
          if (metadata._channelName) metadataContent.push(`Channel: ${metadata._channelName}`);
          if (metadata.meetingSubject) metadataContent.push(`Meeting Subject: ${metadata.meetingSubject}`);
        }

        const contentToVectorize = metadataContent.join('\n') + '\n\nFile Description: Microsoft Teams file';
        
        try {
          console.log(`  Content to vectorize (${contentToVectorize.length} chars): ${contentToVectorize.slice(0, 100)}...`);
          await embeddingService.processFileForEmbeddings(file.id, contentToVectorize);
          console.log(`  ${colors.green}✅ Generated embeddings${colors.reset}`);
        } catch (error) {
          console.log(`  ${colors.red}❌ Error generating embeddings: ${error.message}${colors.reset}`);
        }
      }
    } else if (!embeddingService.isInitialized()) {
      console.log(`${colors.red}⚠️ Embedding service not initialized (check OPENAI_API_KEY)${colors.reset}`);
    }

    // Step 4: Test search functionality
    console.log(`\n${colors.yellow}🔍 Step 4: Testing search functionality...${colors.reset}`);
    
    if (!embeddingService.isInitialized()) {
      console.log(`${colors.red}❌ Cannot test search - embedding service not initialized${colors.reset}`);
      return;
    }

    // Get integrations to test with proper source IDs
    const integrations = await storage.getIntegrations();
    const teamsIntegration = integrations.find(i => i.type === 'microsoft-teams' || i.type === 'microsoft_teams');
    
    if (!teamsIntegration) {
      console.log(`${colors.red}❌ No Teams integration found${colors.reset}`);
      return;
    }

    const enabledSources = [teamsIntegration.id.toString()];
    console.log(`Using Teams integration ID: ${teamsIntegration.id} as enabled source`);

    // Test searches
    const testQueries = [
      'meeting',
      'teams',
      teamsFiles[0]?.fileName?.split('.')[0] || 'test', // First part of first file name
    ];

    for (const query of testQueries) {
      console.log(`\n🔎 Testing search: "${query}"`);
      try {
        const results = await embeddingService.searchSimilarChunks(query, enabledSources, 5);
        console.log(`  Found ${results.length} results`);
        
        results.forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.fileName} (similarity: ${result.similarity?.toFixed(3)})`);
          console.log(`     Platform: ${result.platform}`);
          console.log(`     Content preview: ${result.content?.slice(0, 100)}...`);
        });
        
        if (results.length === 0) {
          console.log(`  ${colors.red}❌ No results found${colors.reset}`);
        }
      } catch (error) {
        console.log(`  ${colors.red}❌ Search error: ${error.message}${colors.reset}`);
      }
    }

    // Step 5: Direct database check
    console.log(`\n${colors.yellow}🗄️ Step 5: Direct database chunks check...${colors.reset}`);
    
    try {
      // Get all file chunks for Teams files
      let totalTeamsChunks = 0;
      for (const file of teamsFiles.slice(0, 5)) { // Check first 5 files
        const chunks = await storage.getFileChunks(file.id);
        totalTeamsChunks += chunks.length;
        
        if (chunks.length > 0) {
          console.log(`${file.fileName}: ${chunks.length} chunks`);
          console.log(`  Sample chunk: ${chunks[0].content?.slice(0, 100)}...`);
          console.log(`  Has embedding: ${!!chunks[0].embedding}`);
          console.log(`  Embedding length: ${chunks[0].embedding?.length || 0}`);
        }
      }
      
      console.log(`\nTotal Teams chunks found: ${totalTeamsChunks}`);
      
    } catch (error) {
      console.log(`${colors.red}❌ Database check error: ${error.message}${colors.reset}`);
    }

  } catch (error) {
    console.error(`${colors.red}❌ Debug script error:${colors.reset}`, error);
  }
}

// Run the debug script
debugTeamsVectorization()
  .then(() => {
    console.log(`\n${colors.green}✅ Debug script completed${colors.reset}`);
    process.exit(0);
  })
  .catch((error) => {
    console.error(`${colors.red}❌ Debug script failed:${colors.reset}`, error);
    process.exit(1);
  }); 