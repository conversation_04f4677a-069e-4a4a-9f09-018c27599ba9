rest-express@1.0.0 dev
> NODE_ENV=development tsx server/indeScheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
10:02:50 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
10:02:53 PM [express] GET /api/integrations 200 in 78ms :: {"integrations":[{"id":1,"name":"Google Me…
10:02:53 PM [express] GET /api/integrations 200 in 323ms :: {"integrations":[{"id":1,"name":"Google M…
10:02:53 PM [express] GET /api/sync-logs 304 in 359ms :: {"logs":[]}
10:03:24 PM [express] GET /api/integrations 304 in 338ms :: {"integrations":[{"id":1,"name":"Google M…
10:03:24 PM [express] GET /api/integrations 304 in 84ms :: {"integrations":[{"id":1,"name":"Google Me…
10:03:47 PM [express] GET /api/integrations 200 in 321ms :: {"integrations":[{"id":1,"name":"Google M…
Error starting sync: TypeError: Cannot read properties of undefined (reading 'startSync')
    at syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:34)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:36:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:85:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at /home/<USER>/workspace/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)
    at invokeCallback (/home/<USER>/workspace/node_modules/raw-body/index.js:238:16)
    at done (/home/<USER>/workspace/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/home/<USER>/workspace/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:518:28)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
10:06:31 PM [express] POST /api/sync-now 500 in 6ms :: {"message":"Failed to start sync","error":"Can…
10:06:41 PM [express] GET /api/integrations 304 in 2799ms :: {"integrations":[{"id":1,"name":"Google …
Error starting sync: TypeError: Cannot read properties of undefined (reading 'startSync')
    at syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:34)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:36:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:85:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at /home/<USER>/workspace/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)
    at invokeCallback (/home/<USER>/workspace/node_modules/raw-body/index.js:238:16)
    at done (/home/<USER>/workspace/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/home/<USER>/workspace/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:518:28)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
10:06:45 PM [express] POST /api/sync-now 500 in 2ms :: {"message":"Failed to start sync","error":"Can…
Error starting sync: TypeError: Cannot read properties of undefined (reading 'startSync')
    at syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:34)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:36:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:85:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at /home/<USER>/workspace/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)
    at invokeCallback (/home/<USER>/workspace/node_modules/raw-body/index.js:238:16)
    at done (/home/<USER>/workspace/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/home/<USER>/workspace/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:518:28)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
10:06:51 PM [express] POST /api/sync-now 500 in 2ms :: {"message":"Failed to start sync","error":"Can…