# GPT Unify - Comprehensive Document AI Assistant

A full-stack application for syncing and processing all types of documents from various platforms (Google Drive, Microsoft Teams, SharePoint) with AI-powered content extraction, search, and an integrated RAG-powered chat assistant.

## Features

- **Universal Document Processing**: Support for PDF, Word, PowerPoint, Excel, Google Docs, and more
- **Multi-Platform Integration**: Connect Google Drive, Microsoft Teams, SharePoint, and other document platforms
- **AI-Powered Content Extraction**: Advanced document parsing with OpenAI integration
- **RAG-Powered Chat**: Ask questions about your documents with context-aware responses
- **Real-time Sync**: Automatic document synchronization with incremental updates
- **Vector Search**: Semantic search across all your documents and files
- **Comprehensive Security**: End-to-end encryption for all credentials and sensitive data

## Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript and Vite
- **UI Library**: shadcn/ui with Tailwind CSS
- **State Management**: TanStack Query for server state
- **Routing**: Wouter for client-side navigation

### Backend (Node.js + Express)
- **Runtime**: Node.js with Express.js API server
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: OAuth 2.0 for platform integrations
- **AI Services**: OpenAI integration for content processing
- **Vector Storage**: pgvector for semantic search

### Key Services
- **Document Processing**: Specialized services for PDF, Word, PowerPoint processing
- **Integration Services**: Google Drive, Microsoft Teams, SharePoint connectors
- **RAG Service**: Retrieval-Augmented Generation with OpenAI
- **Embedding Service**: Vector embeddings for semantic search
- **Sync Controller**: Intelligent document synchronization with change detection

## Quick Start

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL 14+ with pgvector extension
- Google Cloud Platform account (for Google Drive integration)
- Microsoft Azure account (for Teams integration)
- OpenAI API account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gpt-unify
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   npm run db:setup
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

### Configuration

#### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string with pgvector
- `OPENAI_API_KEY`: OpenAI API key for content processing
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET`: Google OAuth credentials
- `MICROSOFT_CLIENT_ID` & `MICROSOFT_CLIENT_SECRET`: Microsoft OAuth credentials
- `ENCRYPTION_KEY`: Key for encrypting stored credentials

#### Setting up Integrations

1. **Navigate to the integrations page** at `http://localhost:5000/integrations`
2. **Click "Add Integration"** → Select platform (Google Drive, Microsoft Teams)
3. **Complete OAuth authentication** and authorize access
4. **Select document sources** (folders, channels, etc.)
5. **Configure sync settings** and start synchronization

### Usage

#### Document Sync
- Documents are automatically synchronized from connected platforms
- Supports incremental sync to process only changed documents
- All file types are processed for comprehensive content extraction

#### AI-Powered Chat
- Navigate to the chat interface to ask questions about your documents
- Uses RAG (Retrieval-Augmented Generation) for context-aware responses
- Search across all synchronized documents and files

#### Document Search
- Use the files page to browse and search through synchronized documents
- Semantic search powered by vector embeddings
- Filter by platform, file type, or specific folders

## Development

### Project Structure
```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/         # Page components
│   │   ├── lib/           # Utilities and API client
│   │   └── hooks/         # Custom React hooks
├── server/                 # Node.js backend
│   ├── controllers/       # API route handlers
│   ├── services/          # Business logic services
│   ├── scripts/           # Utility scripts
│   └── storage.ts         # Database interface
├── shared/                 # Shared types and schemas
│   └── schema.ts          # Database schema definitions
└── migrations/            # Database migration files
```

### Available Scripts
- `npm run dev`: Start development server (frontend + backend)
- `npm run build`: Build for production
- `npm run db:setup`: Initialize database
- `npm run db:migrate`: Run database migrations
- `npm run test`: Run test suite

### API Documentation

#### Core Endpoints
- `GET /api/integrations`: List all integrations
- `POST /api/integrations`: Create new integration
- `POST /api/sync-now`: Trigger manual sync
- `GET /api/files`: Browse synchronized documents
- `POST /api/chat/sessions`: Create chat session
- `POST /api/chat/sessions/:id/messages`: Send chat message

## Deployment

### Production Setup
1. Set up PostgreSQL with pgvector extension
2. Configure environment variables for production
3. Build the application: `npm run build`
4. Start the production server: `npm start`

### Platform Integration Setup

#### Google Drive Integration
1. Create a Google Cloud Project
2. Enable Google Drive API
3. Set up OAuth 2.0 credentials
4. Configure authorized redirect URIs

#### Microsoft Teams Integration
1. Register app in Azure Active Directory
2. Configure Microsoft Graph API permissions
3. Set up OAuth 2.0 credentials
4. Configure redirect URIs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
