import { OAuth2Client } from "google-auth-library";
import { drive_v3 } from "googleapis";
import { BaseService } from "../../base/service.interface";
import { GoogleOAuthService } from "./oauth.service";
import { GoogleDriveService } from "./drive.service";
import { GoogleContentService } from "./content.service";

/**
 * Google Service Facade - provides backward compatibility with the original GoogleService
 * while using the new modular Google services underneath
 */
export class GoogleServiceFacade extends BaseService {
  private oauthService: GoogleOAuthService;
  private driveService: GoogleDriveService;
  private contentService: GoogleContentService;

  constructor() {
    super('GoogleServiceFacade', '1.0.0', 'Unified Google services facade for backward compatibility');
    
    // Initialize modular services
    this.oauthService = new GoogleOAuthService();
    this.driveService = new GoogleDriveService();
    this.contentService = new GoogleContentService();
  }

  protected async onInitialize(): Promise<void> {
    // Initialize all sub-services
    await this.oauthService.initialize();
    await this.driveService.initialize();
    await this.contentService.initialize();
    
    this.log('info', 'Google Service Facade initialized with all sub-services');
  }

  protected getDependencies(): string[] {
    return ['crypto-service'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    const oauthHealth = await this.oauthService.getHealthStatus();
    const driveHealth = await this.driveService.getHealthStatus();
    const contentHealth = await this.contentService.getHealthStatus();

    return {
      oauth: oauthHealth,
      drive: driveHealth,
      content: contentHealth,
      allServicesHealthy: oauthHealth.healthy && driveHealth.healthy && contentHealth.healthy,
    };
  }

  protected async onCleanup(): Promise<void> {
    await Promise.all([
      this.oauthService.cleanup?.(),
      this.driveService.cleanup?.(),
      this.contentService.cleanup?.(),
    ]);
  }

  // OAuth methods - delegate to GoogleOAuthService
  createOAuth2Client(): OAuth2Client {
    this.ensureInitialized();
    return this.oauthService.createOAuth2Client();
  }

  getAuthUrl(redirectUri: string, integrationId?: number): { url: string; state: string } {
    this.ensureInitialized();
    return this.oauthService.getAuthUrl(redirectUri, integrationId);
  }

  async getTokensFromCode(code: string, redirectUri?: string): Promise<any> {
    this.ensureInitialized();
    return this.oauthService.getTokensFromCode(code, redirectUri);
  }

  async getAuthorizedClient(encryptedCredentials: string): Promise<OAuth2Client> {
    this.ensureInitialized();
    return this.oauthService.getAuthorizedClient(encryptedCredentials);
  }

  // Drive methods - delegate to GoogleDriveService
  async listFiles(
    auth: OAuth2Client,
    folderId: string,
    processedFolderId?: string,
  ): Promise<drive_v3.Schema$File[]> {
    this.ensureInitialized();
    return this.driveService.listFiles(auth, folderId, processedFolderId);
  }

  async getOrCreateProcessedFolder(
    auth: OAuth2Client,
    parentFolderId: string,
  ): Promise<string> {
    this.ensureInitialized();
    return this.driveService.getOrCreateProcessedFolder(auth, parentFolderId);
  }

  async moveFileToProcessedFolder(
    auth: OAuth2Client,
    fileId: string,
    sourceFolderId: string,
    processedFolderId: string,
  ): Promise<void> {
    this.ensureInitialized();
    return this.driveService.moveFileToProcessedFolder(auth, fileId, sourceFolderId, processedFolderId);
  }

  async getDriveStructure(auth: OAuth2Client): Promise<{
    myDrive: drive_v3.Schema$File[];
    sharedDrives: any[];
    computers: drive_v3.Schema$File[];
    sharedWithMe: drive_v3.Schema$File[];
  }> {
    this.ensureInitialized();
    return this.driveService.getDriveStructure(auth);
  }

  async listAllFiles(
    auth: OAuth2Client,
    folderId: string = 'root',
    recursive: boolean = true,
  ): Promise<drive_v3.Schema$File[]> {
    this.ensureInitialized();
    return this.driveService.listAllFiles(auth, folderId, recursive);
  }

  async listDriveFolders(
    auth: OAuth2Client,
    parentFolderId: string | null = null,
  ): Promise<drive_v3.Schema$File[]> {
    this.ensureInitialized();
    return this.driveService.listDriveFolders(auth, parentFolderId);
  }

  extractFileMetadata(file: drive_v3.Schema$File): any {
    this.ensureInitialized();
    return this.contentService.extractFileMetadata(file);
  }

  async downloadPDFContent(auth: OAuth2Client, fileId: string, fileName: string): Promise<Buffer | null> {
    this.ensureInitialized();
    return this.contentService.downloadPDFContent(auth, fileId, fileName);
  }

  async downloadWordContent(auth: OAuth2Client, fileId: string, fileName: string): Promise<Buffer | null> {
    this.ensureInitialized();
    return this.contentService.downloadWordContent(auth, fileId, fileName);
  }

  async extractFileContent(auth: OAuth2Client, file: drive_v3.Schema$File): Promise<string> {
    this.ensureInitialized();
    return this.contentService.extractFileContent(auth, file);
  }

  // Content extraction methods - delegate to GoogleContentService
  extractMetadataFromFilename(filename: string): {
    title: string;
    date: string | null;
    time: string | null;
  } {
    this.ensureInitialized();
    return this.contentService.extractMetadataFromFilename(filename);
  }

  async extractDocContent(
    auth: OAuth2Client,
    docId: string,
  ): Promise<{
    transcriptText: string;
    notesText: string;
    summaryParagraph: string;
  }> {
    this.ensureInitialized();
    return this.contentService.extractDocContent(auth, docId);
  }

  async extractSheetsContent(
    auth: OAuth2Client,
    spreadsheetId: string,
  ): Promise<{
    content: string;
    metadata: Record<string, any>;
  }> {
    this.ensureInitialized();
    return this.contentService.extractSheetsContent(auth, spreadsheetId);
  }

  async extractSlidesContent(
    auth: OAuth2Client,
    presentationId: string,
  ): Promise<{
    content: string;
    metadata: Record<string, any>;
  }> {
    this.ensureInitialized();
    return this.contentService.extractSlidesContent(auth, presentationId);
  }

  // Utility methods
  isFileTypeSupported(mimeType: string): boolean {
    this.ensureInitialized();
    return this.driveService.isFileTypeSupported(mimeType);
  }

  validateCredentials(credentials: any): boolean {
    this.ensureInitialized();
    return this.oauthService.validateCredentials(credentials);
  }

  getRequiredScopes(): string[] {
    this.ensureInitialized();
    return this.oauthService.getRequiredScopes();
  }

  // Service information
  getServiceInfo() {
    return {
      ...super.getServiceInfo(),
      subServices: {
        oauth: this.oauthService.getServiceInfo(),
        drive: this.driveService.getServiceInfo(),
        content: this.contentService.getServiceInfo(),
      },
    };
  }

  // Direct access to sub-services (for advanced usage)
  getOAuthService(): GoogleOAuthService {
    return this.oauthService;
  }

  getDriveService(): GoogleDriveService {
    return this.driveService;
  }

  getContentService(): GoogleContentService {
    return this.contentService;
  }
}
