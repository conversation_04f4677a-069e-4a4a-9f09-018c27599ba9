import { Button } from "@/components/ui/button";
import React from "react";

interface SyncPreviewProps {
  syncItem: {
    title: string;
    sourceUrl: string;
    metadata: {
      date: string;
      time: string;
      meetingName: string;
      attendees: string;
    };
    content?: {
      notes: string;
      transcript: string;
    };
  };
  onClose: () => void;
  onConfirm: () => void;
  onSkip: () => void;
}

export default function SyncPreview({
  syncItem,
  onClose,
  onConfirm,
  onSkip,
}: SyncPreviewProps) {
  const { title, sourceUrl, metadata, content } = syncItem;
  const [activeTab, setActiveTab] = React.useState("Notes");

  return (
    <section>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Sync Preview</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onClose}>
            Close Preview
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6">
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h4 className="text-base font-medium text-gray-900 mb-1">Transcript Details</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Original filename</p>
                <p className="text-sm font-medium">{title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Source</p>
                <p className="text-sm font-medium">Shared Documents Drive</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Extracted date</p>
                <p className="text-sm font-medium">{metadata.date}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Extracted time</p>
                <p className="text-sm font-medium">{metadata.time}</p>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="text-base font-medium text-gray-900 mb-1">LLM-Extracted Metadata</h4>
            <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Meeting name</p>
                  <p className="text-sm font-medium">{metadata.meetingName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Attendees</p>
                  <p className="text-sm font-medium">{metadata.attendees}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="text-base font-medium text-gray-900 mb-2">Content Preview</h4>
            
            <div className="border border-gray-200 rounded-md overflow-hidden">
              <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                <div className="flex space-x-2">
                  <button 
                    className={`px-3 py-1 text-xs font-medium ${activeTab === "Notes" ? "bg-primary text-white" : "text-gray-600 hover:bg-gray-200"} rounded`}
                    onClick={() => setActiveTab("Notes")}
                  >
                    Notes
                  </button>
                  <button 
                    className={`px-3 py-1 text-xs font-medium ${activeTab === "Transcript" ? "bg-primary text-white" : "text-gray-600 hover:bg-gray-200"} rounded`}
                    onClick={() => setActiveTab("Transcript")}
                  >
                    Transcript
                  </button>
                </div>
              </div>
              <div className="p-4 max-h-64 overflow-y-auto text-sm text-gray-600">
                {activeTab === "Notes" && (
                  <div>
                    <p className="mb-3"><strong>{metadata.meetingName} - {metadata.date}</strong></p>
                    <p className="mb-3">Attendees: {metadata.attendees}</p>
                    <p className="mb-3"><strong>Agenda:</strong></p>
                    <ol className="list-decimal pl-5 mb-3 space-y-1">
                      <li>Project status update</li>
                      <li>Backend service planning</li>
                      <li>Roadmap review</li>
                      <li>Open discussion</li>
                    </ol>
                    <p className="mb-3"><strong>Notes:</strong></p>
                    <div className="whitespace-pre-line">
                      {content?.notes || "No notes available."}
                    </div>
                  </div>
                )}
                
                {activeTab === "Transcript" && (
                  <div className="whitespace-pre-line">
                    {content?.transcript || "No transcript available."}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={onSkip}>
              Skip Item
            </Button>
            <Button onClick={onConfirm}>
              Confirm & Push to Notion
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
