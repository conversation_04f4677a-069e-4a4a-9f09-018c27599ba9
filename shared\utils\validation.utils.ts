import { z } from "zod";

/**
 * Validation utility functions
 */

// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// URL validation
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// File type validation
export const isValidFileType = (mimeType: string, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(mimeType);
};

// File size validation (in bytes)
export const isValidFileSize = (size: number, maxSize: number): boolean => {
  return size <= maxSize;
};

// Integration type validation
export const isValidIntegrationType = (type: string): boolean => {
  const validTypes = ["google-drive", "google_drive", "microsoft_teams", "slack", "notion", "gmail", "google_calendar", "google-calendar"];
  return validTypes.includes(type);
};

// Platform validation
export const isValidPlatform = (platform: string): boolean => {
  const validPlatforms = ["google_drive", "microsoft_teams", "zoom", "slack", "uploaded_files"];
  return validPlatforms.includes(platform);
};

// Cron expression validation (basic)
export const isValidCronExpression = (cron: string): boolean => {
  const cronRegex = /^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/;
  return cronRegex.test(cron);
};

// JSON validation
export const isValidJson = (str: string): boolean => {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
};

// Safe JSON parse
export const safeJsonParse = <T = any>(str: string, defaultValue: T): T => {
  try {
    return JSON.parse(str);
  } catch {
    return defaultValue;
  }
};

// Validate object against Zod schema
export const validateWithSchema = <T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: z.ZodError } => {
  const result = schema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  } else {
    return { success: false, errors: result.error };
  }
};

// Sanitize string input
export const sanitizeString = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

// Validate pagination parameters
export const validatePagination = (page?: number, limit?: number) => {
  const validatedPage = Math.max(1, page || 1);
  const validatedLimit = Math.min(Math.max(1, limit || 10), 100);
  const offset = (validatedPage - 1) * validatedLimit;
  
  return {
    page: validatedPage,
    limit: validatedLimit,
    offset,
  };
};

// Validate sort parameters
export const validateSort = (field?: string, direction?: string) => {
  const validFields = ['id', 'name', 'createdAt', 'updatedAt', 'fileName', 'fileType'];
  const validDirections = ['asc', 'desc'];
  
  return {
    field: validFields.includes(field || '') ? field : 'createdAt',
    direction: validDirections.includes(direction || '') ? direction as 'asc' | 'desc' : 'desc',
  };
};
