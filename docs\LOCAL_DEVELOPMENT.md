# Local Development Setup (Without Docker)

This guide helps you run MeetSync locally when <PERSON><PERSON> is having issues.

## Prerequisites

1. **Node.js 20+** - [Download](https://nodejs.org/)
2. **PostgreSQL** - [Download](https://www.postgresql.org/download/)
3. **Redis** (optional) - [Download](https://redis.io/download) or use Redis Cloud

## Quick Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Database Setup

**Option A: Local PostgreSQL**
```bash
# Create database
createdb meetsync_dev

# Set environment variable
# Add to your .env file:
DATABASE_URL=postgresql://postgres:password@localhost:5432/meetsync_dev
```

**Option B: Docker PostgreSQL Only**
```bash
# If Docker works for individual containers:
docker run --name meetsync-postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=meetsync_dev -p 5432:5432 -d postgres:15-alpine
```

### 3. Redis Setup (Optional)

**Option A: Local Redis**
```bash
# Windows with Chocolatey:
choco install redis-64

# Or download from GitHub:
# https://github.com/tporadowski/redis/releases
```

**Option B: Redis Cloud (Free tier)**
1. Sign up at [Redis Cloud](https://redis.com/try-free/)
2. Create a database
3. Use the connection string in your .env

**Option C: Skip Redis**
The app will fall back to memory-based sessions.

### 4. Environment Variables

Create `.env` file:
```env
NODE_ENV=development
DATABASE_URL=postgresql://postgres:password@localhost:5432/meetsync_dev
REDIS_URL=redis://localhost:6379
PORT=5000
FRONTEND_PORT=3000

# Add your API keys
OPENAI_API_KEY=your_openai_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
```

### 5. Run Database Migrations
```bash
npm run db:push
```

### 6. Start Development

**Full Application (Frontend + Backend):**
```bash
npm run dev:local
```

**Backend Only:**
```bash
npm run dev:server:local
```

**Frontend Only:**
```bash
npm run dev:client
```

## URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/health

## Troubleshooting

### Database Connection Issues
```bash
# Test PostgreSQL connection
psql -h localhost -U postgres -d meetsync_dev
```

### Redis Connection Issues
```bash
# Test Redis connection
redis-cli ping
```

### Port Conflicts
If ports 3000 or 5000 are in use:
```bash
# Kill processes on port
npx kill-port 3000
npx kill-port 5000
```

## Docker Alternative (When Fixed)

Once Docker Desktop is fixed:
```bash
# Reset Docker Desktop first, then:
npm run docker:dev
```

## Development Tips

1. **Hot Reload**: Both frontend and backend support hot reload
2. **Debugging**: Backend runs with debugging enabled on port 9229
3. **Logs**: Check console output for both services
4. **Database Admin**: Use pgAdmin or any PostgreSQL client
5. **Redis Admin**: Use Redis Insight or redis-cli

## Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
``` 