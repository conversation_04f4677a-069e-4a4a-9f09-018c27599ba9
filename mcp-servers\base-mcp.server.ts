// MCP SDK Imports
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';

// Local imports
import {
  MCPTool,
  MCPResult,
  MCPResource,
  MCPPrompt,
  IMCPServer,
  MCPServerConfig
} from '../server/services/mcp/types';
import { logger } from '../server/core';
import { FunctionDefinition } from 'openai/resources/index.mjs';
import { makeToolFromFunctionDefinition } from './utils';

/**
 * Base MCP Server Class using MCP SDK
 *
 * This class uses the MCP SDK's McpServer to provide MeetSync-specific
 * functionality while leveraging all SDK features including:
 * - Automatic protocol handling
 * - Resource templates
 * - Tool registration with Zod validation
 * - Prompt templates
 * - Transport layer management
 */
export abstract class BaseMCPServer implements IMCPServer {
  public readonly name: string;
  public readonly version: string;
  public readonly description: string;

  protected mcpServer: McpServer;
  protected transport: StdioServerTransport | null = null;
  protected connected: boolean = false;
  protected config: MCPServerConfig;

  // Collections for tracking registered items
  protected registeredTools = new Map<string, MCPTool>();
  protected registeredResources = new Map<string, MCPResource>();
  protected registeredPrompts = new Map<string, MCPPrompt>();
  protected toolHandlers = new Map<string, (args: any) => Promise<MCPResult>>();
  protected resourceHandlers = new Map<string, (uri: string) => Promise<any>>();
  protected promptHandlers = new Map<string, (args: any) => any>();

  constructor(name: string, description: string, version: string = '1.0.0') {
    this.name = name;
    this.version = version;
    this.description = description;

    // Initialize MCP SDK server
    this.mcpServer = new McpServer({
      name: this.name,
      version: this.version
    });

    // Default configuration
    this.config = {
      name: this.name,
      version: this.version,
      type: 'local',
      connection: {},
      enabled: true,
      description: this.description,
      capabilities: {
        tools: {},
        resources: { subscribe: true, listChanged: true },
        prompts: { listChanged: true },
        logging: {}
      }
    };
  }

  /**
   * Initialize the MCP server with SDK features
   */
  async initialize(): Promise<void> {
    try {
      logger.info(`🚀 Initializing MCP Server: ${this.name} v${this.version}`);

      // Initialize server-specific setup
      await this.initializeServer();

      // Register tools, resources, and prompts
      await this.registerTools();
      await this.registerResources();
      await this.registerPrompts();

      logger.info(`✅ MCP Server initialized: ${this.name} - Tools: ${this.registeredTools.size}, Resources: ${this.registeredResources.size}, Prompts: ${this.registeredPrompts.size}`);

    } catch (error) {
      logger.error(`❌ Failed to initialize MCP Server: ${this.name}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Connect the server using appropriate transport
   */
  async connect(): Promise<void> {
    try {
      if (this.connected) {
        logger.warn(`MCP Server ${this.name} is already connected`);
        return;
      }

      // Create transport (defaulting to stdio for now)
      this.transport = new StdioServerTransport();

      // Connect the MCP server to transport
      await this.mcpServer.connect(this.transport);

      this.connected = true;
      logger.info(`🔌 MCP Server connected: ${this.name}`);

    } catch (error) {
      logger.error(`❌ Failed to connect MCP Server: ${this.name}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Disconnect the server
   */
  async disconnect(): Promise<void> {
    try {
      if (this.transport) {
        await this.transport.close();
        this.transport = null;
      }

      this.connected = false;
      logger.info(`🔌 MCP Server disconnected: ${this.name}`);

    } catch (error) {
      logger.error(`❌ Error disconnecting MCP Server: ${this.name}`, {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Check if the server is connected
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * Get server capabilities
   */
  getCapabilities(): MCPServerConfig['capabilities'] {
    return this.config.capabilities;
  }

  // Tool Management
  getTools(): MCPTool[] {
    return Array.from(this.registeredTools.values());
  }

  async callTool(toolName: string, args: any): Promise<MCPResult> {
    const tool = this.registeredTools.get(toolName);
    if (!tool) {
      throw new Error(`Tool "${toolName}" not found`);
    }

    const handler = this.toolHandlers.get(toolName);
    if (!handler) {
      throw new Error(`Tool handler for "${toolName}" not found`);
    }

    try {
      return await handler(args);
    } catch (error) {
      logger.error(`Error calling tool "${toolName}": ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  addTool(tool: FunctionDefinition, handler: (args: any) => Promise<MCPResult>, category?: string): void {
    this.registeredTools.set(tool.name, makeToolFromFunctionDefinition(tool, this.name, category));
    this.toolHandlers.set(tool.name, handler);

    // Register with SDK using Zod schema if available
    const schema = this.createZodSchema(tool.parameters);
    this.mcpServer.tool(tool.name, schema, handler);
  }

  removeTool(toolName: string): void {
    this.registeredTools.delete(toolName);
    // Note: SDK doesn't have built-in tool removal, would need dynamic server features
  }

  // Resource Management
  getResources(): MCPResource[] {
    return Array.from(this.registeredResources.values());
  }

  async readResource(uri: string): Promise<any> {
    const resource = this.registeredResources.get(uri);
    if (!resource) {
      throw new Error(`Resource "${uri}" not found`);
    }

    const handler = this.resourceHandlers.get(uri);
    if (!handler) {
      throw new Error(`Resource handler for "${uri}" not found`);
    }

    try {
      return await handler(uri);
    } catch (error) {
      logger.error(`Error reading resource "${uri}": ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  addResource(resource: MCPResource, handler: (uri: string) => Promise<any>): void {
    this.registeredResources.set(resource.uri, resource);
    this.resourceHandlers.set(resource.uri, handler);

    if (resource.template) {
      // Dynamic resource with template
      const template = new ResourceTemplate(resource.template, { list: undefined });
      this.mcpServer.resource(resource.name, template, (uri: URL) => handler(uri.href));
    } else {
      // Static resource
      this.mcpServer.resource(resource.name, resource.uri, (uri: URL) => handler(uri.href));
    }
  }

  removeResource(uri: string): void {
    this.registeredResources.delete(uri);
    // Note: SDK doesn't have built-in resource removal
  }

  // Prompt Management
  getPrompts(): MCPPrompt[] {
    return Array.from(this.registeredPrompts.values());
  }

  async getPrompt(_name: string, _args?: any): Promise<any> {
    // This will be handled by the SDK's prompt handlers
    throw new Error('Prompt getting should be handled by SDK prompt handlers');
  }

  addPrompt(prompt: MCPPrompt, handler: (args: any) => any): void {
    this.registeredPrompts.set(prompt.name, prompt);

    // Register with SDK
    const schema = this.createZodSchema(prompt.arguments);
    this.mcpServer.prompt(prompt.name, schema, handler);
  }

  removePrompt(name: string): void {
    this.registeredPrompts.delete(name);
    // Note: SDK doesn't have built-in prompt removal
  }

  // Helper Methods
  protected createZodSchema(inputSchema: any): Record<string, z.ZodType> {
    if (!inputSchema || !inputSchema.properties) {
      return {};
    }

    const schema: Record<string, z.ZodType> = {};

    for (const [key, prop] of Object.entries(inputSchema.properties as any)) {
      const property = prop as any;

      switch (property.type) {
        case 'string':
          schema[key] = z.string();
          break;
        case 'number':
          schema[key] = z.number();
          break;
        case 'boolean':
          schema[key] = z.boolean();
          break;
        case 'array':
          schema[key] = z.array(z.any());
          break;
        default:
          schema[key] = z.any();
      }
    }

    return schema;
  }

  // Abstract methods that subclasses must implement
  protected abstract initializeServer(): Promise<void>;
  protected abstract registerTools(): Promise<void>;
  protected abstract registerResources(): Promise<void>;
  protected abstract registerPrompts(): Promise<void>;
}
