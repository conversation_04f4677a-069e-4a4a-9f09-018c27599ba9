import type { Express } from 'express';
import { EmailStorage } from '../storage/features/email.storage.js';

/**
 * Register email management routes
 */
export function registerEmailRoutes(app: Express) {
  const emailStorage = new EmailStorage();

  /**
   * GET /api/emails
   * Get all emails with optional filtering
   */
  app.get('/api/emails', async (req, res) => {
    try {
      const { platform, userId, page = 1, pageSize = 20 } = req.query;
      
      const emails = await emailStorage.getEmails(
        platform as string,
        userId as string,
        parseInt(page as string),
        parseInt(pageSize as string)
      );
      
      res.json({
        success: true,
        data: emails,
        pagination: {
          page: parseInt(page as string),
          pageSize: parseInt(pageSize as string),
          total: emails.length
        }
      });
    } catch (error) {
      console.error('Error fetching emails:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch emails'
      });
    }
  });

  /**
   * GET /api/emails/:id
   * Get a specific email by ID
   */
  app.get('/api/emails/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const email = await emailStorage.getEmail(parseInt(id));
      
      if (!email) {
        return res.status(404).json({
          success: false,
          error: 'Email not found'
        });
      }
      
      res.json({
        success: true,
        data: email
      });
    } catch (error) {
      console.error('Error fetching email:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch email'
      });
    }
  });

  /**
   * POST /api/emails/search
   * Search emails
   */
  app.post('/api/emails/search', async (req, res) => {
    try {
      const { query, platform, userId } = req.body;
      
      if (!query) {
        return res.status(400).json({
          success: false,
          error: 'Search query is required'
        });
      }
      
      const emails = await emailStorage.searchEmails(query, platform, userId);
      
      res.json({
        success: true,
        data: emails
      });
    } catch (error) {
      console.error('Error searching emails:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to search emails'
      });
    }
  });

  /**
   * POST /api/emails
   * Create a new email
   */
  app.post('/api/emails', async (req, res) => {
    try {
      const email = await emailStorage.createEmail(req.body);
      
      res.status(201).json({
        success: true,
        data: email
      });
    } catch (error) {
      console.error('Error creating email:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create email'
      });
    }
  });

  /**
   * PUT /api/emails/:id
   * Update an email
   */
  app.put('/api/emails/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const email = await emailStorage.updateEmail(parseInt(id), req.body);
      
      if (!email) {
        return res.status(404).json({
          success: false,
          error: 'Email not found'
        });
      }
      
      res.json({
        success: true,
        data: email
      });
    } catch (error) {
      console.error('Error updating email:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update email'
      });
    }
  });
} 