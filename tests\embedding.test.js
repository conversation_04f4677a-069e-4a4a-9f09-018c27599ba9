import OpenAI from 'openai';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testEmbedding() {
  console.log('🔍 Testing OpenAI Embedding API...');
  
  // Check if API key exists
  if (!process.env.OPENAI_API_KEY) {
    console.error('❌ OPENAI_API_KEY not found in environment');
    return;
  }
  
  console.log('✅ API Key found:', process.env.OPENAI_API_KEY.substring(0, 10) + '...');
  
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
  
  try {
    console.log('📡 Making embedding request...');
    
    const response = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: "Hello world test",
    });
    
    console.log('✅ SUCCESS! Embedding generated');
    console.log('📊 Embedding dimensions:', response.data[0].embedding.length);
    console.log('💰 Usage:', response.usage);
    
  } catch (error) {
    console.error('❌ ERROR:', error.message);
    console.error('📋 Error details:', {
      status: error.status,
      type: error.type,
      code: error.code,
      headers: error.headers
    });
  }
}

testEmbedding();
