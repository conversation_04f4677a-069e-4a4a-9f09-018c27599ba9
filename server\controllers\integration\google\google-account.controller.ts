import { Request, Response } from 'express';
import { BaseIntegrationController } from '../base/base-integration.controller.js';
import { GoogleOAuthService } from '../../../services/platform-integrations/google/oauth.service.js';
import { IntegrationStorage } from '../../../storage/features/integration.storage.js';

export class GoogleAccountController extends BaseIntegrationController {
  private googleOAuthService: GoogleOAuthService;
  private integrationStorage: IntegrationStorage;

  constructor() {
    super();
    this.googleOAuthService = new GoogleOAuthService();
    this.integrationStorage = new IntegrationStorage();
  }

  /**
   * Get Google account status and connected services
   */
  async getGoogleStatus(req: Request, res: Response): Promise<void> {
    try {
      this.logAction('Fetching Google account status and services');

      // Get all integrations and filter for Google ones
      const allIntegrations = await this.integrationStorage.getIntegrations();
      const googleIntegrations = allIntegrations.filter(integration => 
        ['google_drive', 'google-drive', 'gmail', 'google_calendar'].includes(integration.type)
      );

      if (!googleIntegrations || googleIntegrations.length === 0) {
        res.json({
          account: {
            email: null,
            displayName: null,
            isConnected: false,
            services: []
          }
        });
        return;
      }

      // Find the first connected integration to get account info
      const connectedIntegration = googleIntegrations.find(integration => 
        integration.status === 'connected' && integration.credentials
      );

      let accountInfo = {
        email: null as string | null,
        displayName: null as string | null,
        isConnected: false,
        connectedAt: null as string | null,
      };

      if (connectedIntegration && connectedIntegration.credentials) {
        try {
          // Get user profile from Google
          const auth = await this.googleOAuthService.getAuthorizedClient(connectedIntegration.credentials);
          const oauth2 = (await import('googleapis')).google.oauth2({ version: 'v2', auth });
          const userInfo = await oauth2.userinfo.get();

          accountInfo = {
            email: userInfo.data.email || null,
            displayName: userInfo.data.name || null,
            isConnected: true,
            connectedAt: connectedIntegration.createdAt?.toISOString() || null,
          };
        } catch (error) {
          this.logError('Error fetching user profile:', error);
          // Fall back to basic connected state
          accountInfo.isConnected = true;
          accountInfo.connectedAt = connectedIntegration.createdAt?.toISOString() || null;
        }
      }

      // Map integrations to services
      const services = googleIntegrations.map(integration => ({
        type: integration.type,
        name: this.getServiceName(integration.type),
        description: this.getServiceDescription(integration.type),
        integrationId: integration.id,
        isConnected: integration.status === 'connected',
        status: integration.status,
        lastSyncAt: integration.lastSyncAt?.toISOString(),
      }));

      // Get list of connected service types
      const connectedServices = googleIntegrations
        .filter(integration => integration.status === 'connected')
        .map(integration => integration.type);

      res.json({
        account: {
          ...accountInfo,
          services,
          connectedServices
        }
      });

    } catch (error) {
      this.logError('Error getting Google status:', error);
      res.status(500).json({ 
        error: 'Failed to get Google status',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get connected accounts status for all providers
   */
  async getConnectedAccounts(req: Request, res: Response): Promise<void> {
    try {
      this.logAction('Fetching all connected accounts');

      // Get all integrations grouped by provider
      const allIntegrations = await this.integrationStorage.getIntegrations();
      
      const accountsMap = new Map<string, any>();

      for (const integration of allIntegrations) {
        if (integration.status !== 'connected' || !integration.credentials) {
          continue;
        }

        const provider = this.getProviderFromType(integration.type);
        const accountKey = `${provider}:${integration.credentials}`; // Use credentials hash as key

        if (!accountsMap.has(accountKey)) {
          let accountInfo: any = {
            provider,
            email: null,
            displayName: null,
            connectedAt: integration.createdAt?.toISOString(),
            lastUsed: integration.lastSyncAt?.toISOString(),
            services: [],
            scopes: [],
            isActive: true
          };

          // Get account info based on provider
          if (provider === 'google') {
            try {
              const auth = await this.googleOAuthService.getAuthorizedClient(integration.credentials);
              const oauth2 = (await import('googleapis')).google.oauth2({ version: 'v2', auth });
              const userInfo = await oauth2.userinfo.get();
              
              accountInfo.email = userInfo.data.email;
              accountInfo.displayName = userInfo.data.name;
              accountInfo.scopes = this.googleOAuthService.getRequiredScopes();
            } catch (error) {
              this.logError('Error fetching Google user info:', error);
              accountInfo.email = 'Unknown';
            }
          }

          accountsMap.set(accountKey, accountInfo);
        }

        // Add service to account
        const account = accountsMap.get(accountKey);
        account.services.push({
          name: this.getServiceName(integration.type),
          type: integration.type,
          status: integration.status,
          integrationId: integration.id,
          lastSyncAt: integration.lastSyncAt?.toISOString(),
          itemCount: await this.getServiceItemCount(integration.id),
        });
      }

      const accounts = Array.from(accountsMap.values());

      res.json({ accounts });

    } catch (error) {
      this.logError('Error getting connected accounts:', error);
      res.status(500).json({ 
        error: 'Failed to get connected accounts',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Disconnect an account and all its services
   */
  async disconnectAccount(req: Request, res: Response): Promise<void> {
    try {
      const { provider, email } = req.body;
      
      this.logAction(`Disconnecting ${provider} account for ${email}`);

      if (!provider || !email) {
        res.status(400).json({ error: 'Provider and email are required' });
        return;
      }

      // Find all integrations for this provider
      const providerTypes = this.getProviderTypes(provider);
      const allIntegrations = await this.integrationStorage.getIntegrations();
      const integrations = allIntegrations.filter(integration => 
        providerTypes.includes(integration.type)
      );

      let disconnectedCount = 0;
      for (const integration of integrations) {
        if (integration.status === 'connected') {
          // For now, just update status. In production, you might want to revoke tokens
          await this.integrationStorage.updateIntegrationStatus(integration.id, 'disconnected');
          disconnectedCount++;
        }
      }

      res.json({ 
        message: `Disconnected ${disconnectedCount} services for ${provider} account`,
        disconnectedServices: disconnectedCount
      });

    } catch (error) {
      this.logError('Error disconnecting account:', error);
      res.status(500).json({ 
        error: 'Failed to disconnect account',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private getServiceName(type: string): string {
    switch (type) {
      case 'google_drive':
      case 'google-drive':
        return 'Google Drive';
      case 'gmail':
        return 'Gmail';
      case 'google_calendar':
        return 'Google Calendar';
      case 'microsoft_teams':
      case 'microsoft-teams':
        return 'Microsoft Teams';
      case 'notion':
        return 'Notion';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  }

  private getServiceDescription(type: string): string {
    switch (type) {
      case 'google_drive':
      case 'google-drive':
        return 'Sync and search your Google Drive files';
      case 'gmail':
        return 'Sync and search your Gmail emails';
      case 'google_calendar':
        return 'Sync and search your Google Calendar events';
      case 'microsoft_teams':
      case 'microsoft-teams':
        return 'Sync Microsoft Teams conversations and files';
      case 'notion':
        return 'Sync Notion pages and databases';
      default:
        return `${this.getServiceName(type)} integration`;
    }
  }

  private getProviderFromType(type: string): string {
    if (type.includes('google') || type === 'gmail') {
      return 'google';
    }
    if (type.includes('microsoft')) {
      return 'microsoft';
    }
    if (type === 'notion') {
      return 'notion';
    }
    return 'unknown';
  }

  private getProviderTypes(provider: string): string[] {
    switch (provider) {
      case 'google':
        return ['google_drive', 'google-drive', 'gmail', 'google_calendar'];
      case 'microsoft':
        return ['microsoft_teams', 'microsoft-teams'];
      case 'notion':
        return ['notion'];
      default:
        return [];
    }
  }

  private async getServiceItemCount(integrationId: number): Promise<number | undefined> {
    try {
      // This would depend on your storage implementation
      // For now, return undefined. In production, implement actual counting
      return undefined;
    } catch (error) {
      return undefined;
    }
  }
} 