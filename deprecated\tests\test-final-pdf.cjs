require('dotenv').config();

async function testFinalPDFProcessing() {
  console.log('🚀 Final PDF Processing Test (No LlamaParse)...\n');
  
  try {
    // Test with a simple PDF buffer
    const testPDFContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 300
>>
stream
BT
/F1 14 Tf
50 700 Td
(ANANT AGGARWAL) Tj
0 -30 Td
(Software Engineer) Tj
0 -20 Td
(Email: <EMAIL>) Tj
0 -20 Td
(Phone: \\(555\\) 123-4567) Tj
0 -30 Td
(EXPERIENCE) Tj
0 -20 Td
(Software Engineer at Tech Company) Tj
0 -15 Td
(- Developed web applications using React and Node.js) Tj
0 -15 Td
(- Improved system performance by 40%) Tj
0 -30 Td
(SKILLS) Tj
0 -20 Td
(JavaScript, Python, React, Node.js, SQL) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000010 00000 n 
0000000053 00000 n 
0000000125 00000 n 
0000000185 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
600
%%EOF`;

    const testBuffer = Buffer.from(testPDFContent);
    
    console.log('📄 Testing Enhanced PDF Processing...');
    
    // Test the advanced PDF processor directly
    try {
      const { AdvancedPDFProcessor } = await import('./dist/index.js');
      console.log('✅ Trying to load from dist...');
    } catch (error) {
      console.log('⚠️ Dist not available, testing libraries directly...');
    }
    
    // Test PDF2JSON directly
    const PDFParser = require('pdf2json');
    const pdfParser = new PDFParser();
    
    console.log('🔧 Testing PDF2JSON processing...');
    
    return new Promise((resolve) => {
      let testCompleted = false;
      
      pdfParser.on('pdfParser_dataError', (errData) => {
        if (!testCompleted) {
          testCompleted = true;
          console.log('❌ PDF2JSON failed:', errData);
          
          // Fallback test with pdf-parse
          testPDFParse();
        }
      });
      
      pdfParser.on('pdfParser_dataReady', (pdfData) => {
        if (!testCompleted) {
          testCompleted = true;
          console.log('✅ PDF2JSON successful!');
          
          let extractedText = '';
          
          if (pdfData.Pages) {
            pdfData.Pages.forEach((page, pageIndex) => {
              if (page.Texts) {
                let pageText = '';
                page.Texts.forEach((text) => {
                  text.R.forEach((run) => {
                    if (run.T) {
                      pageText += decodeURIComponent(run.T) + ' ';
                    }
                  });
                });
                if (pageText.trim()) {
                  extractedText += pageText.trim() + ' ';
                }
              }
            });
          }
          
          console.log('📊 Extracted text length:', extractedText.length);
          console.log('📄 Sample text:', extractedText.substring(0, 200) + '...');
          
          if (extractedText.length > 50) {
            console.log('🎉 SUCCESS: Advanced PDF processing working!');
            console.log('✅ PDF text extraction functional for RAG');
          } else {
            console.log('⚠️ Limited text extracted, but processing pipeline works');
          }
          
          resolve(true);
        }
      });
      
      // Timeout fallback
      setTimeout(() => {
        if (!testCompleted) {
          testCompleted = true;
          console.log('⏰ PDF2JSON timeout, testing pdf-parse...');
          testPDFParse();
        }
      }, 5000);
      
      pdfParser.parseBuffer(testBuffer);
    });
    
    async function testPDFParse() {
      try {
        console.log('🔧 Testing pdf-parse as fallback...');
        const pdf = require('pdf-parse');
        
        const data = await pdf(testBuffer);
        
        if (data.text && data.text.length > 10) {
          console.log('✅ pdf-parse successful!');
          console.log('📊 Extracted text length:', data.text.length);
          console.log('📄 Sample text:', data.text.substring(0, 200));
          console.log('🎉 SUCCESS: Fallback PDF processing working!');
        } else {
          console.log('⚠️ pdf-parse extracted limited text');
        }
      } catch (error) {
        console.log('❌ pdf-parse also failed:', error.message);
        console.log('⚠️ PDF processing needs investigation');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFinalPDFProcessing(); 