import type { Express } from "express";
import { storage } from "../storage";

export function registerTestRoutes(app: Express) {
  // Simple test endpoint
  app.get('/api/test', async (req, res) => {
    try {
      console.log('Test endpoint called');
      res.json({ message: 'Test endpoint working', timestamp: new Date().toISOString() });
    } catch (error: any) {
      console.error('Test endpoint error:', error);
      res.status(500).json({ message: 'Test endpoint failed', error: error.message });
    }
  });

  // Test storage endpoint
  app.get('/api/test/storage', async (req, res) => {
    try {
      console.log('Testing storage...');
      
      // Test creating an integration
      const testIntegration = await storage.createIntegration({
        type: 'test',
        name: 'Test Integration',
        status: 'disconnected',
        credentials: null,
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: false,
        syncFilters: {},
        syncSchedule: null,
        syncStatus: 'idle',
      });
      
      console.log('Created test integration:', testIntegration);
      
      // Test getting integrations
      const integrations = await storage.getIntegrations();
      console.log('Retrieved integrations:', integrations.length);
      
      res.json({ 
        message: 'Storage test successful', 
        testIntegration,
        totalIntegrations: integrations.length
      });
    } catch (error: any) {
      console.error('Storage test error:', error);
      res.status(500).json({ message: 'Storage test failed', error: error.message });
    }
  });
} 