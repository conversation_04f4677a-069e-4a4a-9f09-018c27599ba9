#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { configs, listConfigs } from './contextr-config.js';

// Get command line arguments
const args = process.argv.slice(2);
const configName = args[0] || 'overview';
const outputFile = args[1];

async function runContextr() {
  try {
    // Try to import contextr
    let contextr;
    try {
      contextr = await import('contextr');
    } catch (error) {
      console.error('❌ Error importing contextr:', error.message);
      console.log('📦 Make sure contextr is installed: npm install contextr');
      return;
    }

    const { FileContextBuilder } = contextr;

    // Check if configuration exists
    if (!configs[configName]) {
      console.log('❌ Unknown configuration:', configName);
      console.log('\n📋 Available configurations:');
      listConfigs().forEach(config => {
        console.log(`  ${config.key}: ${config.description}`);
      });
      return;
    }

    const selectedConfig = configs[configName];
    console.log(`🔍 Running contextr with configuration: ${selectedConfig.name}\n`);

    // Create FileContextBuilder with the selected configuration
    const builder = new FileContextBuilder(selectedConfig.config);

    // Build the context
    console.log('📁 Building context...');
    const result = await builder.build('console');

    // Output the result
    if (outputFile) {
      fs.writeFileSync(outputFile, result.output);
      console.log(`✅ Context saved to: ${outputFile}`);
    } else {
      // Save with default filename
      const defaultFilename = `contextr-${configName}.txt`;
      fs.writeFileSync(defaultFilename, result.output);
      console.log(`✅ Context saved to: ${defaultFilename}`);
    }

    // Show some stats
    if (result.stats) {
      console.log('\n📊 Statistics:');
      console.log(`  Files processed: ${result.stats.filesProcessed || 'N/A'}`);
      console.log(`  Total size: ${result.stats.totalSize || 'N/A'}`);
    }

  } catch (error) {
    console.error('❌ Error running contextr:', error.message);
    if (error.stack) {
      console.error(error.stack);
    }
  }
}

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log('🚀 MeetSync Contextr Runner\n');
  console.log('Usage: node run-contextr.js [config] [output-file]\n');
  console.log('Available configurations:');
  listConfigs().forEach(config => {
    console.log(`  ${config.key.padEnd(12)} - ${config.description}`);
  });
  console.log('\nExamples:');
  console.log('  node run-contextr.js overview');
  console.log('  node run-contextr.js frontend frontend-context.txt');
  console.log('  node run-contextr.js backend');
} else if (args.includes('--list')) {
  console.log('📋 Available configurations:\n');
  listConfigs().forEach(config => {
    console.log(`  ${config.key.padEnd(12)} - ${config.description}`);
  });
} else {
  runContextr();
} 