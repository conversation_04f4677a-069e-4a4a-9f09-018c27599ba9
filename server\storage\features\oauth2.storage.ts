import { tokenStore, TokenData } from '../../services/core/token-store.service';

/**
 * Storage feature for managing OAuth2 tokens using the secure TokenStoreService.
 */
export class OAuth2Storage {
  /**
   * Store OAuth2 tokens for a user and platform.
   *
   * @param userId - The user's ID.
   * @param platform - The platform (e.g., 'google', 'microsoft').
   * @param tokens - The OAuth2 tokens to store.
   */
  async storeTokens(userId: string, platform: string, tokens: TokenData): Promise<void> {
    await tokenStore.storeTokens(userId, platform, tokens);
  }

  /**
   * Retrieve OAuth2 tokens for a user and platform.
   *
   * @param userId - The user's ID.
   * @param platform - The platform (e.g., 'google', 'microsoft').
   * @returns The decrypted tokens or null if not found.
   */
  async getTokens(userId: string, platform: string): Promise<TokenData | null> {
    return await tokenStore.getTokens(userId, platform);
  }

  /**
   * Delete stored OAuth2 tokens for a user and platform.
   *
   * @param userId - The user's ID.
   * @param platform - The platform (e.g., 'google', 'microsoft').
   */
  async deleteTokens(userId: string, platform: string): Promise<void> {
    await tokenStore.deleteTokens(userId, platform);
  }

  /**
   * Check if the given tokens are expired.
   *
   * @param tokens - The tokens to check.
   * @returns True if expired, false otherwise.
   */
  isTokenExpired(tokens: TokenData): boolean {
    return tokenStore.isTokenExpired(tokens);
  }
}

// Export a singleton instance
export const oauth2Storage = new OAuth2Storage(); 