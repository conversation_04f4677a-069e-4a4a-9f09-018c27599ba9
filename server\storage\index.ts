// Re-export all storage modules
export * from './base';
export * from './features';
export * from './storage.facade';

// Convenience exports for commonly used classes
export type { IStorage } from './base';
export { BaseStorage } from './base';

export {
  UserStorage,
  IntegrationStorage,
  FileStorage,
  ChatStorage,
  RAGStorage,
  SyncStorage,
  ProjectStorage,
} from './features';

export {
  StorageFacade,
} from './storage.facade';

// Create and export the default storage instance
import { StorageFacade } from './storage.facade';
import dotenv from 'dotenv';

// Ensure environment variables are loaded
dotenv.config();

// Initialize the storage facade with a simple approach
// Check if database URL is available, but don't try to connect yet
const useDatabase = !!process.env.DATABASE_URL;

// Create storage instance - it will handle database connection internally
export const storage = new StorageFacade(useDatabase);

// Export for backward compatibility
export default storage;
