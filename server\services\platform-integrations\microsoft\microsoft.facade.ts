import { BaseService } from "../../base/service.interface";
import { MicrosoftOAuthService, type MicrosoftCredentials } from "./oauth.service";
import { MicrosoftTeamsService } from "./teams.service";
import { MicrosoftOneDriveService } from "./onedrive.service";
import { MicrosoftSharePointService } from "./sharepoint.service";
import { MicrosoftContentService } from "./content.service";

/**
 * Microsoft Service Facade - provides backward compatibility with the original MicrosoftService
 * while using the new modular Microsoft services underneath
 */
export class MicrosoftServiceFacade extends BaseService {
  private oauthService: MicrosoftOAuthService;
  private teamsService: MicrosoftTeamsService;
  private oneDriveService: MicrosoftOneDriveService;
  private sharePointService: MicrosoftSharePointService;
  private contentService: MicrosoftContentService;

  constructor() {
    super('MicrosoftServiceFacade', '1.0.0', 'Unified Microsoft services facade for backward compatibility');
    
    // Initialize modular services
    this.oauthService = new MicrosoftOAuthService();
    this.teamsService = new MicrosoftTeamsService();
    this.oneDriveService = new MicrosoftOneDriveService();
    this.sharePointService = new MicrosoftSharePointService();
    this.contentService = new MicrosoftContentService();
  }

  protected async onInitialize(): Promise<void> {
    // Initialize all sub-services
    await this.oauthService.initialize();
    await this.teamsService.initialize();
    await this.oneDriveService.initialize();
    await this.sharePointService.initialize();
    await this.contentService.initialize();
    
    this.log('info', 'Microsoft Service Facade initialized with all sub-services');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    const oauthHealth = await this.oauthService.getHealthStatus();
    const teamsHealth = await this.teamsService.getHealthStatus();
    const oneDriveHealth = await this.oneDriveService.getHealthStatus();
    const sharePointHealth = await this.sharePointService.getHealthStatus();
    const contentHealth = await this.contentService.getHealthStatus();

    return {
      oauth: oauthHealth,
      teams: teamsHealth,
      oneDrive: oneDriveHealth,
      sharePoint: sharePointHealth,
      content: contentHealth,
      allServicesHealthy: oauthHealth.healthy && teamsHealth.healthy && oneDriveHealth.healthy && sharePointHealth.healthy && contentHealth.healthy,
    };
  }

  protected async onCleanup(): Promise<void> {
    await Promise.all([
      this.oauthService.cleanup?.(),
      this.teamsService.cleanup?.(),
      this.oneDriveService.cleanup?.(),
      this.sharePointService.cleanup?.(),
      this.contentService.cleanup?.(),
    ]);
  }

  // OAuth methods - delegate to MicrosoftOAuthService
  getAuthUrl(redirectUri?: string, integrationId?: number): { url: string; state: string } {
    this.ensureInitialized();
    
    // Generate state that includes integration ID if provided
    const state = integrationId ? 
      JSON.stringify({ integrationId, type: 'microsoft' }) : 
      Math.random().toString(36).substring(2);
    
    const authUrl = this.oauthService.getAuthUrl(state);
    return { url: authUrl, state };
  }

  async exchangeCodeForToken(code: string): Promise<MicrosoftCredentials> {
    this.ensureInitialized();
    return this.oauthService.exchangeCodeForToken(code);
  }

  async refreshToken(refreshToken: string): Promise<MicrosoftCredentials> {
    this.ensureInitialized();
    return this.oauthService.refreshToken(refreshToken);
  }

  async testConnection(credentials: MicrosoftCredentials): Promise<{
    success: boolean;
    user?: any;
    error?: string;
  }> {
    this.ensureInitialized();
    return this.oauthService.testConnection(credentials);
  }

  // Teams methods - delegate to MicrosoftTeamsService
  async getTeamsChannels(credentials: MicrosoftCredentials, teamId: string): Promise<any[]> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    return this.teamsService.getTeamsChannels(client, teamId);
  }

  async syncTeamsChannel(
    credentials: MicrosoftCredentials,
    teamId: string,
    channelId: string
  ): Promise<any[]> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    return this.teamsService.syncTeamsChannel(client, teamId, channelId);
  }

  async getChatHistory(
    credentials: MicrosoftCredentials,
    teamId?: string,
    channelId?: string,
    chatId?: string
  ): Promise<any[]> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    return this.teamsService.getChatHistory(client, teamId, channelId, chatId);
  }

  // OneDrive methods - delegate to MicrosoftOneDriveService
  async syncOneDriveFolder(
    credentials: MicrosoftCredentials,
    folderId: string = 'root'
  ): Promise<any[]> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    return this.oneDriveService.syncOneDriveFolder(client, folderId);
  }

  // SharePoint methods - delegate to MicrosoftSharePointService
  async syncSharePointSite(
    credentials: MicrosoftCredentials,
    siteId: string
  ): Promise<any[]> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    return this.sharePointService.syncSharePointSite(client, siteId);
  }

  // Content extraction methods - delegate to MicrosoftContentService
  async downloadAndExtractFileContent(
    credentials: MicrosoftCredentials,
    driveId: string,
    fileId: string,
    fileName: string,
    mimeType?: string
  ): Promise<string | null> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    return this.contentService.downloadAndExtractFileContent(client, driveId, fileId, fileName, mimeType);
  }

  // Comprehensive methods that use multiple services
  async getAvailableSources(credentials: MicrosoftCredentials): Promise<{
    teams: any[];
    sites: any[];
    drives: any[];
  }> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    
    try {
      // Get user's teams
      const teams = await this.teamsService.getUserTeams(client);

      // Get SharePoint sites
      const sites = await this.sharePointService.getSharePointSites(client);

      // Get user's drives (OneDrive)
      const drives = await this.oneDriveService.getUserDrives(client);

      return { teams, sites, drives };
    } catch (error: any) {
      this.handleError(error, 'getting available sources');
    }
  }

  async syncAllSources(credentials: MicrosoftCredentials): Promise<{
    files: any[];
    sources: {
      teams: number;
      sharepoint: number;
      onedrive: number;
    };
    summary: {
      total: number;
      byType: Record<string, number>;
      bySource: Record<string, number>;
    };
  }> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    const allFiles: any[] = [];
    const sources = { teams: 0, sharepoint: 0, onedrive: 0 };
    const byType: Record<string, number> = {};
    const bySource: Record<string, number> = {};

    try {
      this.log('info', 'Starting comprehensive sync of all Microsoft sources...');

      // 1. Get all available sources
      const availableSources = await this.getAvailableSources(credentials);
      this.log('info', 'Available sources:', {
        teams: availableSources.teams.length,
        sites: availableSources.sites.length,
        drives: availableSources.drives.length
      });

      // 2. Sync Teams channels
      for (const team of availableSources.teams) {
        try {
          this.log('info', `Syncing team: ${team.displayName} (${team.id})`);
          const channels = await this.teamsService.getTeamsChannels(client, team.id);
          
          for (const channel of channels) {
            try {
              this.log('info', `Syncing channel: ${channel.displayName} (${channel.id})`);
              const channelFiles = await this.teamsService.syncTeamsChannel(client, team.id, channel.id);
              
              // Add team and channel context to metadata
              channelFiles.forEach(file => {
                file.extractedMetadata = {
                  ...file.extractedMetadata,
                  _teamName: team.displayName,
                  _channelName: channel.displayName,
                  _sourceContext: `Team: ${team.displayName} / Channel: ${channel.displayName}`
                };
              });
              
              allFiles.push(...channelFiles);
              sources.teams += channelFiles.length;
              
              // Count by type
              channelFiles.forEach(file => {
                byType[file.fileType] = (byType[file.fileType] || 0) + 1;
                bySource['teams_channel'] = (bySource['teams_channel'] || 0) + 1;
              });
              
              this.log('info', `Found ${channelFiles.length} files in channel ${channel.displayName}`);
            } catch (channelError) {
              this.log('error', `Error syncing channel ${channel.displayName}`, channelError);
            }
          }
        } catch (teamError) {
          this.log('error', `Error syncing team ${team.displayName}`, teamError);
        }
      }

      // 3. Sync SharePoint sites (limit to first 10 for performance)
      for (const site of availableSources.sites.slice(0, 10)) {
        try {
          this.log('info', `Syncing SharePoint site: ${site.displayName || site.name} (${site.id})`);
          const siteFiles = await this.sharePointService.syncSharePointSite(client, site.id);
          
          // Add site context to metadata
          siteFiles.forEach(file => {
            file.extractedMetadata = {
              ...file.extractedMetadata,
              _siteName: site.displayName || site.name,
              _sourceContext: `SharePoint Site: ${site.displayName || site.name}`
            };
          });
          
          allFiles.push(...siteFiles);
          sources.sharepoint += siteFiles.length;
          
          // Count by type
          siteFiles.forEach(file => {
            byType[file.fileType] = (byType[file.fileType] || 0) + 1;
            bySource['sharepoint_site'] = (bySource['sharepoint_site'] || 0) + 1;
          });
          
          this.log('info', `Found ${siteFiles.length} files in SharePoint site ${site.displayName || site.name}`);
        } catch (siteError) {
          this.log('error', `Error syncing SharePoint site ${site.displayName || site.name}`, siteError);
        }
      }

      // 4. Sync OneDrive folders
      for (const drive of availableSources.drives) {
        try {
          this.log('info', `Syncing OneDrive: ${drive.name} (${drive.id})`);
          const driveFiles = await this.oneDriveService.syncOneDriveFolder(client, 'root');
          
          // Add drive context to metadata
          driveFiles.forEach(file => {
            file.extractedMetadata = {
              ...file.extractedMetadata,
              _driveName: drive.name,
              _sourceContext: `OneDrive: ${drive.name}`
            };
          });
          
          allFiles.push(...driveFiles);
          sources.onedrive += driveFiles.length;
          
          // Count by type
          driveFiles.forEach(file => {
            byType[file.fileType] = (byType[file.fileType] || 0) + 1;
            bySource['onedrive_personal'] = (bySource['onedrive_personal'] || 0) + 1;
          });
          
          this.log('info', `Found ${driveFiles.length} files in OneDrive ${drive.name}`);
        } catch (driveError) {
          this.log('error', `Error syncing OneDrive ${drive.name}`, driveError);
        }
      }

      const summary = {
        total: allFiles.length,
        byType,
        bySource
      };

      this.log('info', 'Comprehensive Microsoft sync completed:', {
        totalFiles: allFiles.length,
        sources,
        summary
      });

      return {
        files: allFiles,
        sources,
        summary
      };

    } catch (error: any) {
      this.handleError(error, 'comprehensive Microsoft sync');
    }
  }

  async getFoldersForSelection(credentials: MicrosoftCredentials): Promise<{
    user: {
      displayName: string;
      email: string;
      id: string;
    };
    sources: Array<{
      id: string;
      name: string;
      type: 'teams_channel' | 'onedrive_folder' | 'sharepoint_site';
      parentId?: string;
      parentName?: string;
      webUrl?: string;
      description?: string;
      itemCount?: number;
    }>;
  }> {
    this.ensureInitialized();
    const client = this.oauthService.createGraphClient(credentials);
    
    try {
      // Get user info
      const userInfo = await this.oauthService.getUserInfo(credentials);
      const user = {
        displayName: userInfo.displayName,
        email: userInfo.email,
        id: userInfo.id
      };

      const allSources: Array<{
        id: string;
        name: string;
        type: 'teams_channel' | 'onedrive_folder' | 'sharepoint_site';
        parentId?: string;
        parentName?: string;
        webUrl?: string;
        description?: string;
        itemCount?: number;
      }> = [];

      // Get OneDrive structure
      const oneDriveStructure = await this.oneDriveService.getOneDriveStructure(client);
      for (const drive of oneDriveStructure.drives) {
        for (const folder of drive.folders) {
          allSources.push({
            id: folder.id,
            name: folder.name,
            type: 'onedrive_folder',
            description: `OneDrive folder`,
            itemCount: folder.itemCount
          });
        }
      }

      // Get Teams structure
      const teamsStructure = await this.teamsService.getTeamsStructure(client);
      for (const team of teamsStructure.teams) {
        for (const channel of team.channels) {
          allSources.push({
            id: channel.id,
            name: channel.name,
            type: 'teams_channel',
            parentId: team.id,
            parentName: team.name,
            description: `Team: ${team.name}`,
            itemCount: channel.itemCount
          });
        }
      }

      // Get SharePoint structure
      const sharePointStructure = await this.sharePointService.getSharePointStructure(client);
      for (const site of sharePointStructure.sites) {
        for (const drive of site.drives) {
          allSources.push({
            id: drive.id,
            name: drive.name,
            type: 'sharepoint_site',
            parentId: site.id,
            parentName: site.name,
            webUrl: site.webUrl,
            description: `SharePoint: ${site.name}`,
            itemCount: drive.itemCount
          });
        }
      }

      return {
        user,
        sources: allSources
      };
    } catch (error: any) {
      this.handleError(error, 'getting folders for selection');
    }
  }

  // Direct access to sub-services (for advanced usage)
  getOAuthService(): MicrosoftOAuthService {
    return this.oauthService;
  }

  getTeamsService(): MicrosoftTeamsService {
    return this.teamsService;
  }

  getOneDriveService(): MicrosoftOneDriveService {
    return this.oneDriveService;
  }

  getSharePointService(): MicrosoftSharePointService {
    return this.sharePointService;
  }

  getContentService(): MicrosoftContentService {
    return this.contentService;
  }

  // Service information
  getServiceInfo() {
    return {
      ...super.getServiceInfo(),
      subServices: {
        oauth: this.oauthService.getServiceInfo(),
        teams: this.teamsService.getServiceInfo(),
        oneDrive: this.oneDriveService.getServiceInfo(),
        sharePoint: this.sharePointService.getServiceInfo(),
        content: this.contentService.getServiceInfo(),
      },
    };
  }
}
