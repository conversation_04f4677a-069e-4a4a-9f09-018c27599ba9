import type { Express, Request, Response } from "express";
import { as<PERSON><PERSON><PERSON><PERSON> } from "../core";
import { MCPManager } from "../services/mcp/mcp-manager.service";
import { MCPConnection } from "../services/mcp/types";

/**
 * Register MCP (Model Context Protocol) routes
 */
export function registerMCPRoutes(app: Express, mcpManager: MCPManager) {
  // MCP Server Status
  app.get('/api/mcp/status', asyncHandler(async (req: Request, res: Response) => {
    try {
      const connections = mcpManager.getConnectedServers();
      
      res.json({
        success: true,
        message: 'MCP status retrieved successfully',
        data: {
          servers: connections.map(conn => ({
            name: conn.serverName,
            toolCount: conn.tools.length,
            transport: conn.transport,
            connected: conn.connected,
            lastConnected: conn.lastConnected,
            error: conn.error,
            resources: conn.resources,
            prompts: conn.prompts,
          }))
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to get MCP status',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }));

  // MCP Server Health Check
  app.get('/api/mcp/health', asyncHandler(async (req: Request, res: Response) => {
    try {
      const connections = mcpManager.getConnectedServers();
      
      const healthStatus = {
        healthy: connections.every(conn => conn.connected),
        totalServers: connections.length,
        connectedServers: connections.length,
        totalTools: connections.reduce((acc, conn) => acc + conn.tools.length, 0),
        totalResources: connections.reduce((acc, conn) => acc + (conn.resources?.length || 0), 0),
        totalPrompts: connections.reduce((acc, conn) => acc + (conn.prompts?.length || 0), 0),
        timestamp: new Date().toISOString()
      };
      
      res.json({
        success: true,
        message: 'MCP health check completed',
        data: healthStatus
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'MCP health check failed',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }));
}
