import { storage } from '../../../storage/index.js';
import { 
  BaseToolCategory, 
  ToolCategory, 
  FunctionTool,
  ToolExecutionContext 
} from '../base/function-tool.interface';

/**
 * Human-in-the-Loop patterns category
 * Implements HITL patterns for confirmations, chained actions, and bulk operations
 */
export class HITLPatternsCategory extends BaseToolCategory {
  constructor() {
    super(ToolCategory.HITL);
  }

  initialize(): void {
    this.log('info', 'Initializing HITL patterns...');
    
    // Register HITL pattern tools
    this.registerTool(this.createGenericConfirmationTool());
    this.registerTool(this.createChainedActionTool());
    this.registerTool(this.createBulkFileOperationTool());
    
    this.log('info', `Initialized ${this.tools.size} HITL pattern tools`);
  }

  /**
   * Generic confirmation tool for any action type
   */
  private createGenericConfirmationTool(): FunctionTool {
    return {
      name: "confirmAction",
      description: "Generic confirmation tool for any action that requires user approval",
      parameters: this.createParameters({
        actionType: {
          type: "string",
          description: "Type of action being confirmed (create, delete, update, etc.)",
        },
        actionDetails: {
          type: "string",
          description: "Details of the action to be confirmed",
        },
        confirmation: {
          type: "string",
          description: "User's confirmation response",
        },
      }, ["actionType", "actionDetails", "confirmation"]),
      handler: (params) => this.handleGenericConfirmation(params),
    };
  }

  /**
   * Chained action tool for performing multiple actions in sequence
   */
  private createChainedActionTool(): FunctionTool {
    return {
      name: "chainActions",
      description: "Execute multiple actions in sequence with confirmation",
      parameters: this.createParameters({
        actions: {
          type: "array",
          description: "Array of actions to execute in sequence",
          items: {
            type: "object",
            properties: {
              action: { type: "string" },
              parameters: { type: "object" },
            },
          },
        },
        confirmation: {
          type: "string",
          description: "User confirmation to execute all actions",
        },
      }, ["actions", "confirmation"]),
      handler: (params) => this.handleChainedActions(params),
    };
  }

  /**
   * Bulk file operation tool
   */
  private createBulkFileOperationTool(): FunctionTool {
    return {
      name: "bulkFileOperation",
      description: "Perform operations on multiple files at once",
      parameters: this.createParameters({
        operation: {
          type: "string",
          description: "Type of operation (search, info, tag, etc.)",
        },
        fileIds: {
          type: "array",
          description: "Array of file IDs to operate on",
          items: { type: "number" },
        },
        parameters: {
          type: "object",
          description: "Additional parameters for the operation",
        },
      }, ["operation", "fileIds"]),
      handler: (params) => this.handleBulkFileOperation(params),
    };
  }

  // =============================================================================
  // TOOL HANDLERS
  // =============================================================================

  /**
   * Handle generic confirmation
   */
  private handleGenericConfirmation(params: any): string {
    const { actionType, actionDetails, confirmation } = params;

    this.log('info', `Processing generic confirmation for action: ${actionType}`);

    if (this.isAffirmativeConfirmation(confirmation)) {
      this.log('info', `Action confirmed: ${actionType}`);
      return `✅ **Action Confirmed**: ${actionType}\n\n**Details**: ${actionDetails}\n\n**Status**: Proceeding with action...`;
    } else {
      this.log('info', `Action cancelled: ${actionType}`);
      return `❌ **Action Cancelled**: ${actionType}\n\n**Reason**: User did not provide affirmative confirmation.`;
    }
  }

  /**
   * Handle chained actions
   */
  private async handleChainedActions(params: any): Promise<string> {
    const { actions, confirmation } = params;

    this.log('info', `Processing chained actions request for ${actions.length} actions`);

    if (!this.isAffirmativeConfirmation(confirmation)) {
      this.log('info', 'Chained actions cancelled by user');
      return `❌ **Chained Actions Cancelled**: User did not confirm execution.`;
    }

    let result = `🔗 **Executing ${actions.length} Chained Actions**:\n\n`;

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];
      result += `**Step ${i + 1}**: ${action.action}\n`;
      result += `**Parameters**: ${JSON.stringify(action.parameters, null, 2)}\n`;
      result += `**Status**: ✅ Queued for execution\n\n`;
    }

    result += `**Note**: This is a simulation. In a full implementation, these actions would be executed sequentially with the function tools system.`;

    this.log('info', `Chained actions processed: ${actions.length} actions queued`);
    return result;
  }

  /**
   * Handle bulk file operations
   */
  private async handleBulkFileOperation(params: any): Promise<string> {
    const { operation, fileIds, parameters: opParams = {} } = params;

    this.log('info', `Processing bulk ${operation} operation on ${fileIds.length} files`);

    let result = `📁 **Bulk ${operation.toUpperCase()} Operation**\n\n`;
    result += `**Files**: ${fileIds.length} files\n`;
    result += `**Operation**: ${operation}\n\n`;

    if (operation === 'info') {
      result += `**Results**:\n`;
      for (const fileId of fileIds) {
        try {
          const file = await storage.getFile(fileId);
          if (file) {
            result += `✅ **${file.fileName}** (ID: ${fileId})\n`;
            result += `   - Type: ${file.fileType}\n`;
            result += `   - Platform: ${file.platform}\n\n`;
          } else {
            result += `❌ **File ID ${fileId}**: Not found\n\n`;
          }
        } catch (error) {
          result += `❌ **File ID ${fileId}**: Error - ${error}\n\n`;
        }
      }
    } else {
      result += `**Status**: Operation queued for ${fileIds.length} files\n`;
      result += `**Note**: This is a simulation. In a full implementation, the ${operation} operation would be performed on all specified files.`;
    }

    this.log('info', `Bulk operation completed: ${operation} on ${fileIds.length} files`);
    return result;
  }

  /**
   * Create a confirmation workflow
   */
  createConfirmationWorkflow(
    workflowName: string,
    steps: Array<{
      stepName: string;
      description: string;
      requiresConfirmation: boolean;
    }>
  ): FunctionTool {
    return {
      name: `confirm${workflowName}`,
      description: `Confirmation workflow for ${workflowName}`,
      parameters: this.createParameters({
        step: {
          type: "string",
          description: "Current step in the workflow",
        },
        confirmation: {
          type: "string",
          description: "User's confirmation for the current step",
        },
        workflowData: {
          type: "object",
          description: "Data associated with the workflow",
        },
      }, ["step", "confirmation"]),
      handler: (params) => this.handleWorkflowConfirmation(params, workflowName, steps),
    };
  }

  /**
   * Handle workflow confirmation
   */
  private handleWorkflowConfirmation(
    params: any,
    workflowName: string,
    steps: Array<{ stepName: string; description: string; requiresConfirmation: boolean }>
  ): string {
    const { step, confirmation, workflowData } = params;

    this.log('info', `Processing workflow confirmation for ${workflowName}, step: ${step}`);

    const currentStep = steps.find(s => s.stepName === step);
    if (!currentStep) {
      return `❌ **Invalid Step**: Step '${step}' not found in workflow '${workflowName}'.`;
    }

    if (currentStep.requiresConfirmation && !this.isAffirmativeConfirmation(confirmation)) {
      return `❌ **Step Cancelled**: ${currentStep.stepName}\n\n**Reason**: User did not provide affirmative confirmation for this step.`;
    }

    const stepIndex = steps.indexOf(currentStep);
    const nextStep = steps[stepIndex + 1];

    let result = `✅ **Workflow Step Completed**: ${currentStep.stepName}\n\n`;
    result += `**Description**: ${currentStep.description}\n\n`;

    if (nextStep) {
      result += `**Next Step**: ${nextStep.stepName}\n`;
      result += `**Next Description**: ${nextStep.description}\n`;
      if (nextStep.requiresConfirmation) {
        result += `\n⚠️ **Confirmation Required**: Please confirm to proceed with the next step.`;
      }
    } else {
      result += `**Status**: Workflow '${workflowName}' completed successfully!`;
    }

    return result;
  }

  /**
   * Validate action parameters for HITL patterns
   */
  validateActionParameters(action: string, parameters: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (action) {
      case 'confirmAction':
        if (!parameters.actionType) errors.push('actionType is required');
        if (!parameters.actionDetails) errors.push('actionDetails is required');
        if (!parameters.confirmation) errors.push('confirmation is required');
        break;
      
      case 'chainActions':
        if (!Array.isArray(parameters.actions)) errors.push('actions must be an array');
        if (!parameters.confirmation) errors.push('confirmation is required');
        break;
      
      case 'bulkFileOperation':
        if (!parameters.operation) errors.push('operation is required');
        if (!Array.isArray(parameters.fileIds)) errors.push('fileIds must be an array');
        break;
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
} 