// Re-export all File Upload service modules
export * from './config.service';
export * from './processing.service';
export * from './content.service';
export * from './management.service';
export * from './file-upload.facade';

// Convenience exports for commonly used classes
export { FileUploadConfigService } from './config.service';
export { FileUploadProcessingService } from './processing.service';
export { FileUploadContentService } from './content.service';
export { FileUploadManagementService } from './management.service';
export { FileUploadServiceFacade } from './file-upload.facade';

// Create and export the default File Upload service instance
import { FileUploadServiceFacade } from './file-upload.facade';

// Initialize the File Upload service facade
export const fileUploadService = new FileUploadServiceFacade();

// Export for backward compatibility
export default fileUploadService;
