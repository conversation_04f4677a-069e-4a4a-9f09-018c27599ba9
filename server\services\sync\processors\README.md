# File Processors

This directory contains the modularized file processing system for MeetSync. The original monolithic `file-processing.service.ts` has been broken down into platform-specific processors for better maintainability and extensibility.

## Architecture

### Base Components
- **`base/file-processor.interface.ts`** - Core interfaces and base classes
- **`base/content-extractor.interface.ts`** - Content extraction interfaces
- **`processor-factory.ts`** - Factory for creating processors

### Platform Processors
- **`google/`** - Google Drive file processing
- **`microsoft/`** - Microsoft Teams file processing  
- **`uploaded/`** - Uploaded files processing

## Usage

### Processing a File
```typescript
import { getFileProcessor } from './processors';

// Get processor for a platform
const processor = getFileProcessor('google_drive');

// Process a file
const result = await processor.processFile(fileData, existingFile, integration);
```

### Using the Factory
```typescript
import { ProcessorFactory } from './processors';

// Check if processor exists
if (ProcessorFactory.hasProcessor('google_drive')) {
  const processor = ProcessorFactory.getProcessor('google_drive');
}

// Get all registered platforms
const platforms = ProcessorFactory.getRegisteredPlatforms();
```

### Using the Main Service
```typescript
import { FileProcessingService } from '../file-processing.service';

const service = new FileProcessingService();

// New modular approach
const result = await service.processFile('google_drive', fileData, existingFile, integration);

// Legacy methods still work
const result = await service.processGoogleFile(fileData, fileMetadata, existingFile, integration);
```

## Platform Processors

### Google Drive Processor
- **File**: `google/google-file-processor.ts`
- **Features**:
  - AI metadata extraction for transcripts
  - Comprehensive content extraction (PDF, Word, Docs)
  - Embedding generation
  - File record management

### Microsoft Teams Processor  
- **File**: `microsoft/microsoft-file-processor.ts`
- **Features**:
  - Enhanced Teams metadata processing
  - AI metadata extraction
  - Graph API integration
  - Meeting transcript handling

### Uploaded Files Processor
- **File**: `uploaded/uploaded-file-processor.ts`
- **Features**:
  - Simple processing for uploaded files
  - Direct content handling
  - Immediate processing workflow

## Content Extraction

Each processor uses specialized content extractors:

### Google Content Extractor
- PDF processing via `pdf-service`
- Word document processing via `word-service`
- Google Docs API integration
- Fallback to metadata-only content

### Microsoft Embedding Processor
- Teams metadata vectorization
- Attendee information extraction
- Meeting context processing
- Graph API enhanced data

## Embedding Processing

Embedding processors handle RAG functionality:
- Check for existing embeddings
- Extract content for vectorization
- Generate embeddings via `simple-embedding-service`
- Platform-specific content optimization

## Migration Notes

### Backward Compatibility
The original `FileProcessingService` methods are preserved:
- `processGoogleFile()` - delegates to Google processor
- `processMicrosoftFile()` - delegates to Microsoft processor

### New Methods
- `processFile(platform, ...)` - unified processing interface
- `hasProcessorForPlatform(platform)` - check processor availability
- `getAvailablePlatforms()` - list supported platforms

### Legacy Methods
Original implementations are preserved as `*Legacy` methods for reference and testing.

## Benefits

1. **Modularity**: Each platform has its own focused processor
2. **Maintainability**: Smaller, focused files are easier to maintain
3. **Extensibility**: Easy to add new platforms
4. **Testability**: Individual processors can be unit tested
5. **Reusability**: Processors can be used independently
6. **Performance**: Better tree-shaking and lazy loading

## Adding New Platforms

1. Create processor directory: `platforms/new-platform/`
2. Implement `IFileProcessor` interface
3. Create content extractor if needed
4. Register in `processor-factory.ts`
5. Export from platform index file
6. Add to main processors index

Example:
```typescript
// Register in processor-factory.ts
ProcessorFactory.registerProcessor('new_platform', () => {
  const { NewPlatformProcessor } = require('./new-platform/new-platform-processor');
  return new NewPlatformProcessor();
});
```

## Testing

Each processor should have comprehensive tests:
- Unit tests for individual methods
- Integration tests with storage
- Mock tests for external services
- Error handling tests

## Future Improvements

1. **Async Content Extraction**: Background processing for large files
2. **Caching**: Cache extracted content and embeddings
3. **Retry Logic**: Robust error handling and retries
4. **Metrics**: Processing time and success rate tracking
5. **Configuration**: Platform-specific configuration options
