import type { IFileProcessor } from './base/file-processor.interface';

/**
 * Factory for creating file processors based on platform
 * Provides a centralized way to get the appropriate processor for each platform
 */
export class ProcessorFactory {
  private static processors = new Map<string, () => Promise<IFileProcessor>>();
  private static instances = new Map<string, IFileProcessor>();

  /**
   * Register a processor for a platform
   * @param platform Platform identifier
   * @param processorFactory Function that creates the processor instance
   */
  static registerProcessor(platform: string, processorFactory: () => Promise<IFileProcessor>): void {
    this.processors.set(platform, processorFactory);
  }

  /**
   * Get processor for a platform (singleton pattern)
   * @param platform Platform identifier
   * @returns File processor instance
   */
  static async getProcessor(platform: string): Promise<IFileProcessor> {
    // Return cached instance if available
    if (this.instances.has(platform)) {
      return this.instances.get(platform)!;
    }

    // Create new instance
    const processorFactory = this.processors.get(platform);
    if (!processorFactory) {
      throw new Error(`No processor registered for platform: ${platform}`);
    }

    const processor = await processorFactory();
    this.instances.set(platform, processor);
    return processor;
  }

  /**
   * Check if a processor is available for a platform
   * @param platform Platform identifier
   * @returns True if processor is available
   */
  static hasProcessor(platform: string): boolean {
    return this.processors.has(platform);
  }

  /**
   * Get all registered platforms
   * @returns Array of platform identifiers
   */
  static getRegisteredPlatforms(): string[] {
    return Array.from(this.processors.keys());
  }

  /**
   * Clear all cached processor instances (useful for testing)
   */
  static clearCache(): void {
    this.instances.clear();
  }

  /**
   * Initialize all default processors
   * This method will be called during application startup
   */
  static initializeDefaultProcessors(): void {
    // Register Google Drive processor
    this.registerProcessor('google_drive', async () => {
      // Dynamic import to avoid circular dependencies
      const { GoogleFileProcessor } = await import('./google/google-file-processor');
      return new GoogleFileProcessor();
    });

    // Register Microsoft Teams processor
    this.registerProcessor('microsoft_teams', async () => {
      // Dynamic import to avoid circular dependencies
      const { MicrosoftFileProcessor } = await import('./microsoft/microsoft-file-processor');
      return new MicrosoftFileProcessor();
    });

    // Register uploaded files processor
    this.registerProcessor('uploaded_files', async () => {
      // Dynamic import to avoid circular dependencies
      const { UploadedFileProcessor } = await import('./uploaded/uploaded-file-processor');
      return new UploadedFileProcessor();
    });

    console.log('✅ File processors initialized for platforms:', this.getRegisteredPlatforms());
  }
}

/**
 * Convenience function to get a processor
 * @param platform Platform identifier
 * @returns File processor instance
 */
export async function getFileProcessor(platform: string): Promise<IFileProcessor> {
  return ProcessorFactory.getProcessor(platform);
}

/**
 * Convenience function to process a file with the appropriate processor
 * @param platform Platform identifier
 * @param fileData File data
 * @param existingFile Existing file record
 * @param integration Integration configuration
 * @returns Processing result
 */
export async function processFile(
  platform: string,
  fileData: any,
  existingFile: any,
  integration: any
) {
  const processor = await getFileProcessor(platform);
  return processor.processFile(fileData, existingFile, integration);
}
