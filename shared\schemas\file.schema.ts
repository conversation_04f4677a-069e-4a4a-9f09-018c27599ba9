import { pgTable, text, serial, integer, bigint, boolean, timestamp, jsonb, vector } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { syncItems } from "./integration.schema";

// Files table schema
export const files = pgTable("files", {
  id: serial("id").primaryKey(),
  externalId: text("external_id").notNull(), // platform-native file ID
  fileName: text("file_name").notNull(),
  fileType: text("file_type").notNull(), // "transcript", "doc", "ppt", "pdf", "video", "audio", etc.
  mimeType: text("mime_type"),
  fileSize: bigint("file_size", { mode: "number" }),
  platform: text("platform").notNull(), // "google_drive", "microsoft_teams", "zoom", "slack"
  fileContent: text("file_content"), // for backwards compatibility, but we'll phase this out
  fileUrl: text("file_url"),
  downloadUrl: text("download_url"),
  thumbnailUrl: text("thumbnail_url"),
  parentFolder: text("parent_folder"),
  tags: text("tags").array(),
  extractedMetadata: jsonb("extracted_metadata"), // AI-extracted metadata
  sourceUrl: text("source_url"), // direct link to file
  userId: text("user_id"), // owner
  organizationId: text("organization_id"),
  syncItemId: integer('sync_item_id').references(() => syncItems.id),
  notionPageId: text("notion_page_id"), // for backwards compatibility
  lastModified: timestamp("last_modified"),
  isShared: boolean("is_shared").default(false),
  sharedWith: text("shared_with").array(), // array of user emails
  status: text("status").notNull().default("active"), // "active", "deleted", "inactive"
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// File chunks table schema for RAG
export const fileChunks = pgTable("file_chunks", {
  id: serial("id").primaryKey(),
  fileId: integer("file_id").notNull().references(() => files.id, { onDelete: "cascade" }),
  chunkIndex: integer("chunk_index").notNull(),
  content: text("content").notNull(),
  embedding: vector("embedding", { dimensions: 1536 }), // OpenAI text-embedding-3-small
  tokenCount: integer("token_count"),
  metadata: jsonb("metadata"), // chunk-specific metadata
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Insert schemas
export const insertFileSchema = createInsertSchema(files).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertFileChunkSchema = createInsertSchema(fileChunks).omit({
  id: true,
  createdAt: true,
});

// Export types
export type File = typeof files.$inferSelect;
export type InsertFile = typeof insertFileSchema._type;

export type FileChunk = typeof fileChunks.$inferSelect;
export type InsertFileChunk = typeof insertFileChunkSchema._type;
