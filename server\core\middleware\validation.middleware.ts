import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';
import { createError } from './error.middleware';

/**
 * Validation middleware using Zod schemas
 */

export type ValidationTarget = 'body' | 'query' | 'params' | 'headers';

export interface ValidationOptions {
  stripUnknown?: boolean;
  allowUnknown?: boolean;
  abortEarly?: boolean;
}

/**
 * Create validation middleware for a specific schema and target
 */
export const validate = (
  schema: ZodSchema,
  target: ValidationTarget = 'body',
  options: ValidationOptions = {}
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const data = req[target];
      
      // Parse and validate data
      const result = schema.safeParse(data);
      
      if (!result.success) {
        const errors = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
          received: (err as any).received || 'unknown',
        }));

        throw createError.validation(`Validation failed for ${target}`, errors);
      }

      // Replace the original data with validated data
      (req as any)[target] = result.data;
      
      next();

    } catch (error) {
      next(error);
    }
  };
};

/**
 * Validate request body
 */
export const validateBody = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate(schema, 'body', options);
};

/**
 * Validate query parameters
 */
export const validateQuery = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate(schema, 'query', options);
};

/**
 * Validate route parameters
 */
export const validateParams = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate(schema, 'params', options);
};

/**
 * Validate headers
 */
export const validateHeaders = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate(schema, 'headers', options);
};

/**
 * Common validation schemas
 */
export const commonSchemas = {
  // ID parameter validation
  id: z.object({
    id: z.string().regex(/^\d+$/, 'ID must be a number').transform(Number),
  }),

  // Pagination query validation
  pagination: z.object({
    page: z.string().optional().transform(val => val ? parseInt(val) : 1),
    limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
    offset: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  }).refine(data => {
    if (data.page && data.page < 1) return false;
    if (data.limit && (data.limit < 1 || data.limit > 100)) return false;
    if (data.offset && data.offset < 0) return false;
    return true;
  }, {
    message: 'Invalid pagination parameters',
  }),

  // Search query validation
  search: z.object({
    q: z.string().min(1).max(500).optional(),
    query: z.string().min(1).max(500).optional(),
    search: z.string().min(1).max(500).optional(),
  }),

  // Sort query validation
  sort: z.object({
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
    orderBy: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional(),
  }),

  // Filter query validation
  filter: z.object({
    status: z.string().optional(),
    type: z.string().optional(),
    platform: z.string().optional(),
    dateFrom: z.string().datetime().optional(),
    dateTo: z.string().datetime().optional(),
  }),

  // File upload validation
  fileUpload: z.object({
    filename: z.string().min(1).max(255),
    mimetype: z.string().min(1),
    size: z.number().min(1).max(100 * 1024 * 1024), // 100MB max
  }),
};

/**
 * Validate file upload
 */
export const validateFileUpload = (
  allowedTypes: string[] = [],
  maxSize: number = 100 * 1024 * 1024 // 100MB
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.file && !req.files) {
        throw createError.badRequest('No file uploaded');
      }

      const file = req.file || (Array.isArray(req.files) ? req.files[0] : req.files);

      if (!file || Array.isArray(file)) {
        throw createError.badRequest('Invalid file upload');
      }

      // Check file size
      if (typeof file.size === 'number' && file.size > maxSize) {
        throw createError.badRequest(
          `File too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB`
        );
      }

      // Check file type
      if (allowedTypes.length > 0 && typeof file.mimetype === 'string' && !allowedTypes.includes(file.mimetype)) {
        throw createError.badRequest(
          `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
        );
      }

      next();

    } catch (error) {
      next(error);
    }
  };
};

/**
 * Validate JSON content type
 */
export const validateJsonContentType = (req: Request, res: Response, next: NextFunction): void => {
  if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
    const contentType = req.headers['content-type'];
    
    if (!contentType || !contentType.includes('application/json')) {
      throw createError.badRequest('Content-Type must be application/json');
    }
  }
  
  next();
};

/**
 * Sanitize input data
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      // Basic XSS prevention
      return obj.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/javascript:/gi, '')
                .trim();
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};

/**
 * Validate API version
 */
export const validateApiVersion = (supportedVersions: string[] = ['v1']) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const version = req.headers['api-version'] as string || 'v1';
    
    if (!supportedVersions.includes(version)) {
      throw createError.badRequest(
        `Unsupported API version: ${version}. Supported versions: ${supportedVersions.join(', ')}`
      );
    }
    
    (req as any).apiVersion = version;
    next();
  };
};
