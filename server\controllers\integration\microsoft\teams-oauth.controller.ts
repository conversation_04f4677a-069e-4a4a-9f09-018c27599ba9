import { Request, Response } from 'express';
import { BaseIntegrationController } from '../base/base-integration.controller.js';
import { microsoftService } from '../../../services/platform-integrations/microsoft/index.js';
import { cryptoService } from '../../../services/core/crypto-service.js';

/**
 * Controller for Microsoft Teams OAuth operations
 * Handles Microsoft Teams authentication flows
 */
export class TeamsOAuthController extends BaseIntegrationController {
  constructor() {
    super();
    // Bind all methods to preserve 'this' context
    this.getTeamsAuthUrl = this.getTeamsAuthUrl.bind(this);
    this.handleTeamsOAuthCallback = this.handleTeamsOAuthCallback.bind(this);
  }

  /**
   * Generate Microsoft Teams OAuth authorization URL
   */
  async getTeamsAuthUrl(req: Request, res: Response) {
    console.log('[TeamsOAuthController] Starting getTeamsAuthUrl method');
    
    try {
      console.log('[TeamsOAuthController] Validating integration ID...');
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        console.log('[TeamsOAuthController] Invalid integration ID:', error);
        return res.status(400).json({ message: error });
      }
      console.log('[TeamsOAuthController] Integration ID validated:', id);

      // Check if integration exists
      console.log('[TeamsOAuthController] Getting integration by ID...');
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        console.log('[TeamsOAuthController] Integration not found for ID:', id);
        return res.status(404).json({ message: 'Integration not found' });
      }
      console.log('[TeamsOAuthController] Integration found:', { id: integration.id, type: integration.type });

      // Validate that this is a Teams integration
      console.log('[TeamsOAuthController] Validating integration type...');
      const isTeamsIntegration = this.validateIntegrationType(integration, [
        'microsoft_teams', 'microsoft-teams'
      ]);

      if (!isTeamsIntegration) {
        console.log('[TeamsOAuthController] Invalid integration type:', integration.type);
        return res.status(400).json({
          message: `This endpoint is only for Microsoft Teams integrations. Received: ${integration.type}`
        });
      }
      console.log('[TeamsOAuthController] Integration type validated');

      // Get the redirect URI from the query parameters
      console.log('[TeamsOAuthController] Getting redirect URI from query params...');
      const redirectUri = req.query.redirectUri as string;
      if (!redirectUri) {
        console.log('[TeamsOAuthController] Redirect URI missing');
        return res.status(400).json({ message: 'Redirect URI is required' });
      }
      console.log('[TeamsOAuthController] Redirect URI:', redirectUri);

      try {
        this.logAction(`Processing Microsoft Teams OAuth for integration type: ${integration.type}`);

        // Check Microsoft environment variables first
        const requiredEnvVars = ['MICROSOFT_CLIENT_ID', 'MICROSOFT_CLIENT_SECRET', 'MICROSOFT_TENANT_ID'];
        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
        
        if (missingVars.length > 0) {
          console.error('[TeamsOAuthController] Missing Microsoft environment variables:', missingVars);
          return res.status(500).json({
            message: `Microsoft Teams integration is not configured. Missing environment variables: ${missingVars.join(', ')}`,
            error: 'MISSING_MICROSOFT_CREDENTIALS'
          });
        }

        // Initialize Microsoft service if needed
        console.log('[TeamsOAuthController] Initializing Microsoft service...');
        try {
          await microsoftService.initialize();
          console.log('[TeamsOAuthController] Microsoft service initialized successfully');
        } catch (initError: any) {
          console.error('[TeamsOAuthController] Microsoft service initialization failed:', initError.message);
          throw new Error(`Microsoft Teams integration is not properly configured: ${initError.message}`);
        }

        // Generate Microsoft Teams OAuth URL with integration ID included
        this.logAction('Calling microsoftService.getAuthUrl with:', { redirectUri, id: id! });
        console.log('[TeamsOAuthController] About to call microsoftService.getAuthUrl...');
        const result = microsoftService.getAuthUrl(redirectUri, id!);
        this.logAction('Received result from microsoftService.getAuthUrl:', result);
        console.log('[TeamsOAuthController] Result from getAuthUrl:', result);
        
        const authUrl = result.url;
        const oauthState = result.state;
        console.log('[TeamsOAuthController] Extracted authUrl and state:', { authUrlExists: !!authUrl, stateExists: !!oauthState });

        // Store the state temporarily in the integration config
        console.log('[TeamsOAuthController] Storing OAuth state...');
        await this.storeOAuthState(id!, oauthState, redirectUri);

        this.logAction('Microsoft Teams OAuth URL generated successfully');
        console.log('[TeamsOAuthController] About to return success response...');

        const responseData = { authUrl };
        console.log('[TeamsOAuthController] Response data:', responseData);
        
        return this.successResponse(res, responseData);
      } catch (error: any) {
        console.error('[TeamsOAuthController] Error in inner try block:', error);
        this.logError("Error generating Microsoft Teams OAuth URL:", error);
        this.logError("Error stack:", error.stack);
        return res.status(500).json({
          message: 'Failed to generate Microsoft Teams authorization URL. Please check your Microsoft API credentials.',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
      }
    } catch (error: any) {
      console.error('[TeamsOAuthController] Error in outer try block:', error);
      return this.handleError(res, error, `Failed to generate Teams auth URL for integration ${req.params.id}`);
    }
  }

  /**
   * Handle Microsoft Teams OAuth callback and store tokens
   */
  async handleTeamsOAuthCallback(req: Request, res: Response) {
    try {
      const { code, error, state } = req.query;
      const integrationId = parseInt(req.params.id);

      this.logAction('Starting Teams OAuth callback for integration:', integrationId);
      this.logAction('Query params:', { code: !!code, error, state });

      if (error) {
        this.logError('Teams OAuth error:', error);
        return this.redirectToError(res, integrationId, error as string);
      }

      if (!code) {
        this.logError('No authorization code received', new Error('Missing code parameter'));
        return this.redirectToError(res, integrationId, 'no_code');
      }

      // Get the integration to verify state if needed
      const integration = await this.getIntegrationById(integrationId);
      if (!integration) {
        this.logError('Integration not found:', integrationId);
        return this.redirectToError(res, integrationId, 'integration_not_found');
      }

      this.logAction('Found integration:', { 
        id: integration.id, 
        type: integration.type, 
        status: integration.status 
      });

      // Validate that this is a Teams integration
      const isTeamsIntegration = this.validateIntegrationType(integration, [
        'microsoft_teams', 'microsoft-teams'
      ]);

      if (!isTeamsIntegration) {
        this.logError(`Processing non-Teams integration type: ${integration.type}`, new Error('Invalid integration type'));
        return this.redirectToError(res, integrationId, 'invalid_integration_type');
      }

      // Validate OAuth state if provided
      if (state && !this.validateOAuthState(integration, state as string)) {
        this.logError('Invalid OAuth state parameter', new Error('Invalid state'));
        return this.redirectToError(res, integrationId, 'invalid_state');
      }

      try {
        // Exchange code for tokens
        this.logAction('Exchanging authorization code for tokens...');
        const credentials = await microsoftService.exchangeCodeForToken(code as string);
        this.logAction('Successfully received tokens:', { 
          hasAccessToken: !!credentials.access_token,
          hasRefreshToken: !!credentials.refresh_token,
          tokenType: credentials.token_type,
          expiresIn: credentials.expires_in
        });
        
        // Encrypt the credentials before storing
        this.logAction('Encrypting credentials...');
        const encryptedCredentials = await cryptoService.encrypt(JSON.stringify(credentials));
        this.logAction('Credentials encrypted, length:', encryptedCredentials.length);
        
        // Update integration in database
        this.logAction('Updating integration in database...');
        await this.updateIntegrationCredentials(integrationId, encryptedCredentials);

        this.logAction('Integration updated successfully - redirecting to success page');
        
        // Redirect to the success page
        return this.redirectToSuccess(res, integrationId);
      } catch (error: any) {
        this.logError('Error processing Teams OAuth tokens:', error);
        return this.redirectToError(res, integrationId, error.message);
      }
    } catch (error: any) {
      this.logError('Error in Teams OAuth callback:', error);
      const integrationId = parseInt(req.params.id);
      if (!isNaN(integrationId)) {
        return this.redirectToError(res, integrationId, error.message);
      } else {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }
    }
  }
}

export const teamsOAuthController = new TeamsOAuthController(); 
