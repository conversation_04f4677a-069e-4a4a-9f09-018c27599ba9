import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getIntegrations, updateSchedule } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import AppLayout from "@/components/layout/AppLayout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDate } from "@/lib/utils/date";
import { getIntegrationName } from "@/lib/utils/integrations";
import { IntegrationIcon } from "@/components/ui/integration-icons";

export default function Schedules() {
  const { toast } = useToast();
  
  const { data, isLoading } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: getIntegrations,
  });
  
  const integrations = data?.integrations || [];
  
  const updateScheduleMutation = useMutation({
    mutationFn: ({ integrationId, schedule, enabled }: { integrationId: number, schedule: string, enabled: boolean }) => 
      updateSchedule(integrationId, schedule, enabled),
    onSuccess: () => {
      toast({
        title: "Schedule updated",
        description: "The sync schedule has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: `Failed to update schedule: ${error.message}`,
        variant: "destructive",
      });
    },
  });
  
  return (
    <AppLayout title="Schedules">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Sync Schedules</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <svg className="animate-spin h-8 w-8 text-primary mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className="mt-2 text-gray-600">Loading schedules...</p>
              </div>
            ) : integrations.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No integrations found. Add an integration first.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Integration</TableHead>
                    <TableHead>Schedule</TableHead>
                    <TableHead>Last Sync</TableHead>
                    <TableHead>Next Sync</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {integrations.map((integration: any) => (
                    <TableRow key={integration.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded mr-2 bg-white border border-gray-100 flex items-center justify-center">
                            <IntegrationIcon type={integration.type} size={20} />
                          </div>
                          <div>
                            <p className="font-medium">{integration.name}</p>
                            <p className="text-xs text-gray-500">{getIntegrationName(integration.type)}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Select defaultValue="daily">
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="Schedule" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="hourly">Hourly</SelectItem>
                              <SelectItem value="daily">Daily</SelectItem>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="custom">Custom</SelectItem>
                            </SelectContent>
                          </Select>
                          <Select defaultValue="09:00">
                            <SelectTrigger className="w-24">
                              <SelectValue placeholder="Time" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="00:00">12:00 AM</SelectItem>
                              <SelectItem value="03:00">3:00 AM</SelectItem>
                              <SelectItem value="06:00">6:00 AM</SelectItem>
                              <SelectItem value="09:00">9:00 AM</SelectItem>
                              <SelectItem value="12:00">12:00 PM</SelectItem>
                              <SelectItem value="15:00">3:00 PM</SelectItem>
                              <SelectItem value="18:00">6:00 PM</SelectItem>
                              <SelectItem value="21:00">9:00 PM</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(integration.lastSyncAt)}</TableCell>
                      <TableCell>{formatDate(integration.nextSyncAt)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Switch 
                            id={`schedule-${integration.id}`} 
                            defaultChecked={!!integration.syncSchedule} 
                            onCheckedChange={(checked) => {
                              updateScheduleMutation.mutate({
                                integrationId: integration.id,
                                schedule: "0 9 * * *", // Daily at 9 AM
                                enabled: checked,
                              });
                            }}
                          />
                          <label 
                            htmlFor={`schedule-${integration.id}`} 
                            className="ml-2 text-sm text-gray-700"
                          >
                            {!!integration.syncSchedule ? "Enabled" : "Disabled"}
                          </label>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          disabled={updateScheduleMutation.isPending}
                          onClick={() => {
                            updateScheduleMutation.mutate({
                              integrationId: integration.id,
                              schedule: integration.syncSchedule || "0 9 * * *",
                              enabled: !!integration.syncSchedule,
                            });
                          }}
                        >
                          Save
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Schedule Templates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="border rounded-md p-4">
                <h3 className="font-medium mb-2">Daily Schedule</h3>
                <p className="text-sm text-gray-600 mb-2">Run sync once every day at a specific time</p>
                <p className="text-xs text-gray-500 font-mono">0 9 * * *</p>
                <p className="text-xs text-gray-500 mt-1">Runs at 9:00 AM every day</p>
                <Button variant="outline" size="sm" className="mt-3">Use Template</Button>
              </div>
              
              <div className="border rounded-md p-4">
                <h3 className="font-medium mb-2">Hourly Schedule</h3>
                <p className="text-sm text-gray-600 mb-2">Run sync every hour for frequent updates</p>
                <p className="text-xs text-gray-500 font-mono">0 * * * *</p>
                <p className="text-xs text-gray-500 mt-1">Runs at the top of every hour</p>
                <Button variant="outline" size="sm" className="mt-3">Use Template</Button>
              </div>
              
              <div className="border rounded-md p-4">
                <h3 className="font-medium mb-2">Weekly Schedule</h3>
                <p className="text-sm text-gray-600 mb-2">Run sync once a week on Monday</p>
                <p className="text-xs text-gray-500 font-mono">0 9 * * 1</p>
                <p className="text-xs text-gray-500 mt-1">Runs at 9:00 AM every Monday</p>
                <Button variant="outline" size="sm" className="mt-3">Use Template</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
