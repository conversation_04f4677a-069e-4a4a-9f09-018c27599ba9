#!/usr/bin/env npx tsx

/**
 * Minimal Gmail Tests - Only public interfaces
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🧪 Minimal Gmail Component Tests\n');

// Test 1: Gmail Service Instantiation
async function testGmailServiceBasics() {
  console.log('📧 Testing Gmail Service...');
  
  try {
    const { GmailService } = await import('../server/services/platform-integrations/google/gmail.service.js');
    const service = new GmailService();
    
    console.log('  ✅ Service imported and instantiated');
    console.log('  ✅ Service has initialize method:', typeof service.initialize === 'function');
    
    return true;
  } catch (error: any) {
    console.log('  ❌ Failed:', error.message);
    return false;
  }
}

// Test 2: OAuth Service Gmail Configuration
async function testOAuthConfig() {
  console.log('\n🔐 Testing OAuth Configuration...');
  
  try {
    const { GoogleOAuthService } = await import('../server/services/platform-integrations/google/oauth.service.js');
    const service = new GoogleOAuthService();
    
    const scopes = service.getRequiredScopes();
    const hasGmailScopes = scopes.some(scope => scope.includes('gmail'));
    
    console.log('  ✅ OAuth service instantiated');
    console.log('  ✅ Gmail scopes configured:', hasGmailScopes);
    console.log('  📋 Gmail scopes:', scopes.filter(s => s.includes('gmail')));
    
    return hasGmailScopes;
  } catch (error: any) {
    console.log('  ❌ Failed:', error.message);
    return false;
  }
}

// Test 3: Controller Methods
async function testControllerBasics() {
  console.log('\n🎮 Testing Gmail Controller...');
  
  try {
    const { GoogleGmailController } = await import('../server/controllers/integration/google/google-gmail.controller.js');
    const controller = new GoogleGmailController();
    
    const methods = ['getGmailProfile', 'syncAllEmails', 'syncRecentEmails', 'testGmailConnection'];
    const allExist = methods.every(m => typeof controller[m] === 'function');
    
    console.log('  ✅ Controller instantiated');
    console.log('  ✅ All Gmail methods exist:', allExist);
    console.log('  📋 Methods:', methods.join(', '));
    
    return allExist;
  } catch (error: any) {
    console.log('  ❌ Failed:', error.message);
    return false;
  }
}

// Test 4: Email Storage
async function testEmailStorageBasics() {
  console.log('\n💾 Testing Email Storage...');
  
  try {
    const { EmailStorage } = await import('../server/storage/features/email.storage.js');
    const storage = new EmailStorage();
    
    // Test basic operations
    const emails = await storage.getEmails('gmail', 'test', 1, 1);
    
    console.log('  ✅ Storage instantiated');
    console.log('  ✅ Can retrieve emails');
    console.log('  📊 Found emails:', emails.length);
    
    return true;
  } catch (error: any) {
    console.log('  ❌ Failed:', error.message);
    return false;
  }
}

// Test 5: Google API Libraries
async function testGoogleLibraries() {
  console.log('\n📚 Testing Google API Libraries...');
  
  try {
    const { OAuth2Client } = await import('google-auth-library');
    const { google } = await import('googleapis');
    
    const oauth = new OAuth2Client();
    const gmail = google.gmail({ version: 'v1', auth: oauth });
    
    console.log('  ✅ OAuth2Client available');
    console.log('  ✅ Gmail API available');
    console.log('  ✅ API methods:', !!(gmail.users && gmail.users.messages));
    
    return true;
  } catch (error: any) {
    console.log('  ❌ Failed:', error.message);
    return false;
  }
}

// Test 6: Environment Setup
async function testEnvironment() {
  console.log('\n🌍 Testing Environment...');
  
  const vars = ['GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET', 'DATABASE_URL'];
  const status = vars.map(v => ({
    name: v,
    present: !!(process.env[v] && process.env[v].length > 0)
  }));
  
  status.forEach(s => {
    console.log(`  ${s.present ? '✅' : '❌'} ${s.name}: ${s.present ? 'Present' : 'Missing'}`);
  });
  
  return status.every(s => s.present);
}

// Test 7: Email Data Verification
async function testEmailData() {
  console.log('\n📧 Testing Email Data...');
  
  try {
    const { EmailStorage } = await import('../server/storage/features/email.storage.js');
    const storage = new EmailStorage();
    
    // Get all emails and filter for Gmail
    const allEmails = await storage.getEmails('gmail', 'anonymous', 1, 100);
    const gmailEmails = allEmails.filter(email => email.platform === 'gmail');
    
    console.log('  📊 Total emails queried:', allEmails.length);
    console.log('  📬 Gmail emails found:', gmailEmails.length);
    
    if (gmailEmails.length > 0) {
      const sample = gmailEmails[0];
      console.log('  📋 Sample email:');
      console.log(`    Subject: ${sample.subject}`);
      console.log(`    From: ${sample.sender}`);
      console.log(`    Platform: ${sample.platform}`);
      console.log(`    Content length: ${sample.content?.length || 0} chars`);
    }
    
    return true;
  } catch (error: any) {
    console.log('  ❌ Failed:', error.message);
    return false;
  }
}

// Main test runner
async function runMinimalTests() {
  console.log('🚀 Running Minimal Gmail Integration Tests');
  console.log('='.repeat(50));
  
  const tests = [
    { name: 'Gmail Service', fn: testGmailServiceBasics },
    { name: 'OAuth Config', fn: testOAuthConfig },
    { name: 'Controller', fn: testControllerBasics },
    { name: 'Email Storage', fn: testEmailStorageBasics },
    { name: 'Google Libraries', fn: testGoogleLibraries },
    { name: 'Environment', fn: testEnvironment },
    { name: 'Email Data', fn: testEmailData }
  ];
  
  const results: Array<{ name: string; passed: boolean; error?: string }> = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      results.push({ name: test.name, passed: false, error: error.message });
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Results Summary');
  console.log('='.repeat(50));
  
  let passed = 0;
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (result.passed) passed++;
    if (result.error) console.log(`    Error: ${result.error}`);
  });
  
  console.log('\n' + '='.repeat(50));
  console.log(`📈 Final Score: ${passed}/${results.length} tests passed`);
  console.log(`🎯 Success Rate: ${Math.round((passed / results.length) * 100)}%`);
  
  if (passed === results.length) {
    console.log('🎉 ALL GMAIL COMPONENTS WORKING PERFECTLY!');
  } else {
    console.log(`⚠️ ${results.length - passed} tests need attention`);
  }
  
  console.log('='.repeat(50));
}

runMinimalTests().catch(console.error); 