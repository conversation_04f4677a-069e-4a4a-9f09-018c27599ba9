import { pgTable, serial, text, timestamp, jsonb, integer, boolean, vector } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { syncItems } from "./integration.schema";

export const emails = pgTable('emails', {
  id: serial('id').primaryKey(),
  externalId: text('external_id').notNull(),
  platform: text('platform').notNull(),
  subject: text('subject').notNull(),
  content: text('content').notNull(),
  sender: text('sender').notNull(),
  recipients: jsonb('recipients').notNull().default(sql`'[]'::jsonb`),
  cc: jsonb('cc').default(sql`'[]'::jsonb`),
  bcc: jsonb('bcc').default(sql`'[]'::jsonb`),
  threadId: text('thread_id'),
  status: text('status').notNull().default('active'),
  metadata: jsonb('metadata').default(sql`'{}'::jsonb`),
  extractedMetadata: jsonb('extracted_metadata').default(sql`'{}'::jsonb`),
  userId: text('user_id'),
  organizationId: text('organization_id'),
  syncItemId: integer('sync_item_id').references(() => syncItems.id),
  isRead: boolean('is_read').default(false),
  isStarred: boolean('is_starred').default(false),
  isArchived: boolean('is_archived').default(false),
  labels: jsonb('labels').default(sql`'[]'::jsonb`),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  receivedAt: timestamp('received_at').notNull().defaultNow(),
});

export const emailChunks = pgTable('email_chunks', {
  id: serial('id').primaryKey(),
  emailId: integer('email_id').notNull().references(() => emails.id),
  chunkIndex: integer('chunk_index').notNull(),
  content: text('content').notNull(),
  metadata: jsonb('metadata').default(sql`'{}'::jsonb`),
  embedding: vector('embedding', { dimensions: 1536 }),
  tokenCount: integer('token_count'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export type Email = typeof emails.$inferSelect;
export type InsertEmail = typeof emails.$inferInsert;
export type EmailChunk = typeof emailChunks.$inferSelect;
export type InsertEmailChunk = typeof emailChunks.$inferInsert; 