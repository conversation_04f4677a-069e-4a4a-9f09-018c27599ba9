#!/usr/bin/env node

/**
 * Utility script to kill any processes using port 8080
 * Useful for cleaning up stuck server processes
 */

import { exec } from 'child_process';
import os from 'os';

const platform = os.platform();
const port = process.env.PORT || 8080;

console.log(`🔍 Looking for processes using port ${port}...`);

if (platform === 'win32') {
  // Windows
  exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
    if (error) {
      console.log(`✅ No processes found using port ${port}`);
      return;
    }

    const lines = stdout.trim().split('\n');
    const pids = new Set();

    lines.forEach(line => {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 5) {
        const pid = parts[parts.length - 1];
        if (pid && pid !== '0') {
          pids.add(pid);
        }
      }
    });

    if (pids.size === 0) {
      console.log(`✅ No processes found using port ${port}`);
      return;
    }

    console.log(`💀 Found ${pids.size} process(es) using port ${port}:`);
    
    pids.forEach(pid => {
      console.log(`   Killing PID: ${pid}`);
      exec(`taskkill /F /PID ${pid}`, (killError) => {
        if (killError) {
          console.error(`   ❌ Failed to kill PID ${pid}:`, killError.message);
        } else {
          console.log(`   ✅ Successfully killed PID ${pid}`);
        }
      });
    });
  });

} else {
  // Linux/macOS
  exec(`lsof -ti :${port}`, (error, stdout) => {
    if (error) {
      console.log(`✅ No processes found using port ${port}`);
      return;
    }

    const pids = stdout.trim().split('\n').filter(pid => pid);
    
    if (pids.length === 0) {
      console.log(`✅ No processes found using port ${port}`);
      return;
    }

    console.log(`💀 Found ${pids.length} process(es) using port ${port}:`);
    
    pids.forEach(pid => {
      console.log(`   Killing PID: ${pid}`);
      exec(`kill -9 ${pid}`, (killError) => {
        if (killError) {
          console.error(`   ❌ Failed to kill PID ${pid}:`, killError.message);
        } else {
          console.log(`   ✅ Successfully killed PID ${pid}`);
        }
      });
    });
  });
}

// Also try to kill any node processes with our project name
setTimeout(() => {
  console.log('\n🔍 Looking for MeetSync Node.js processes...');
  
  if (platform === 'win32') {
    exec('tasklist /FI "IMAGENAME eq node.exe" /FO CSV', (error, stdout) => {
      if (!error && stdout.includes('node.exe')) {
        const lines = stdout.split('\n');
        lines.forEach(line => {
          if (line.includes('node.exe') && line.includes(process.cwd().split('\\').pop())) {
            const parts = line.split(',');
            if (parts.length >= 2) {
              const pid = parts[1].replace(/"/g, '');
              console.log(`💀 Killing MeetSync Node process PID: ${pid}`);
              exec(`taskkill /F /PID ${pid}`);
            }
          }
        });
      }
    });
  } else {
    exec('ps aux | grep node | grep -v grep', (error, stdout) => {
      if (!error) {
        const lines = stdout.split('\n');
        lines.forEach(line => {
          if (line.includes('MeetSync') || line.includes('tsx server/index.ts')) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 2) {
              const pid = parts[1];
              console.log(`💀 Killing MeetSync Node process PID: ${pid}`);
              exec(`kill -9 ${pid}`);
            }
          }
        });
      }
    });
  }
}, 1000);

console.log('\n✅ Port cleanup complete!'); 