import { integrations, type Integration, type InsertIntegration } from "../../../shared/index.js";
import { eq, desc } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config";
import { cryptoService } from "../../services/core/crypto-service";

/**
 * Integration storage operations
 */
export class IntegrationStorage {
  private db: any;
  private memoryIntegrations: Map<number, Integration>;
  private integrationIdCounter: number;
  private useDatabase: boolean;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memoryIntegrations = new Map();
    this.integrationIdCounter = 1;
    // Database connection will be initialized lazily when first needed
  }

  /**
   * Ensure database connection is available
   */
  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        // Silently fall back to memory storage
        this.useDatabase = false;
      }
    }
  }

  async getIntegrations(): Promise<Integration[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      return await this.db.select().from(integrations).orderBy(desc(integrations.createdAt));
    } else {
      return Array.from(this.memoryIntegrations.values());
    }
  }

  async getIntegration(id: number): Promise<Integration | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(integrations).where(eq(integrations.id, id));
      return result[0];
    } else {
      return this.memoryIntegrations.get(id);
    }
  }

  async getIntegrationsByType(type: string): Promise<Integration[]> {
    this.validateString(type, 'type');
    this.ensureDatabase();

    if (this.useDatabase) {
      return await this.db.select().from(integrations).where(eq(integrations.type, type));
    } else {
      return Array.from(this.memoryIntegrations.values()).filter(
        (integration) => integration.type === type
      );
    }
  }

  async createIntegration(integration: InsertIntegration): Promise<Integration> {
    this.validateString(integration.name, 'name');
    this.validateString(integration.type, 'type');
    this.ensureDatabase();

    // Encrypt credentials if provided
    let credentials: string | null = integration.credentials || null;
    if (credentials) {
      credentials = await cryptoService.encrypt(credentials);
    }

    if (this.useDatabase) {
      const integrationData = {
        ...integration,
        credentials,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastSyncAt: null,
        nextSyncAt: null,
      };

      const result = await this.db.insert(integrations).values(integrationData).returning();
      return result[0];
    } else {
      const id = this.integrationIdCounter++;
      const now = new Date();

      const newIntegration: Integration = {
        ...integration,
        id,
        credentials,
        status: integration.status || "active",
        createdAt: now,
        updatedAt: now,
        lastSyncAt: null,
        nextSyncAt: null,
        config: integration.config || null,
        sourceConfig: integration.sourceConfig || null,
        destinationConfig: integration.destinationConfig || null,
        syncFilters: integration.syncFilters || null,
        syncSchedule: integration.syncSchedule || null,
        syncStatus: integration.syncStatus || 'idle',
        isLlmEnabled: integration.isLlmEnabled || null,
      };

      this.memoryIntegrations.set(id, newIntegration);
      return newIntegration;
    }
  }

  async updateIntegration(id: number, data: Partial<Integration>): Promise<Integration | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    // Encrypt credentials if they've been updated
    let updateData = { ...data };
    if (data.credentials) {
      const existing = await this.getIntegration(id);
      if (existing && data.credentials !== existing.credentials) {
        // Check if credentials are already encrypted
        if (!cryptoService.validateEncryptedFormat(data.credentials)) {
          console.log('[INTEGRATION_STORAGE] Encrypting new credentials');
          updateData.credentials = await cryptoService.encrypt(data.credentials);
        } else {
          console.log('[INTEGRATION_STORAGE] Credentials already encrypted, skipping encryption');
        }
      }
    }

    if (this.useDatabase) {
      const result = await this.db.update(integrations)
        .set({ ...updateData, updatedAt: new Date() })
        .where(eq(integrations.id, id))
        .returning();
      return result[0];
    } else {
      const integration = this.memoryIntegrations.get(id);
      if (!integration) return undefined;

      const updated: Integration = {
        ...integration,
        ...updateData,
        updatedAt: new Date(),
      };

      this.memoryIntegrations.set(id, updated);
      return updated;
    }
  }

  async deleteIntegration(id: number): Promise<boolean> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      try {
        await this.db.delete(integrations).where(eq(integrations.id, id));
        return true;
      } catch (error) {
        console.error("Error deleting integration:", error);
        return false;
      }
    } else {
      return this.memoryIntegrations.delete(id);
    }
  }

  async updateIntegrationStatus(id: number, status: string): Promise<Integration | undefined> {
    this.validateId(id);
    this.validateString(status, 'status');

    return this.updateIntegration(id, { status });
  }

  async clearAllIntegrations(): Promise<void> {
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(integrations);
    } else {
      this.memoryIntegrations.clear();
      this.integrationIdCounter = 1;
    }
  }

  // Utility methods
  private validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid integration ID provided');
    }
  }

  private validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      integrationCount: this.useDatabase ? 'unknown' : this.memoryIntegrations.size,
    };
  }
}
