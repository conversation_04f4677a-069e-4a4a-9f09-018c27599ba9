import fs from 'fs';
import path from 'path';
import { BaseService } from "../base/service.interface";
import { storage } from "../../storage/index.js";

/**
 * File Upload Management Service - handles file lifecycle management and cleanup
 */
export class FileUploadManagementService extends BaseService {
  constructor() {
    super('FileUploadManagement', '1.0.0', 'File upload lifecycle management and cleanup');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'File Upload Management service initialized');
  }

  protected getDependencies(): string[] {
    return ['storage'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    try {
      const stats = await this.getManagementStats();
      return {
        storageConnected: true,
        totalFiles: stats.totalFiles,
        orphanedFiles: stats.orphanedFiles,
        totalSize: stats.totalSize,
        managementEnabled: true,
      };
    } catch (error) {
      return {
        storageConnected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Delete uploaded file and its database record
   */
  async deleteUploadedFile(fileId: number): Promise<{
    success: boolean;
    error?: string;
    details?: {
      databaseDeleted: boolean;
      physicalFileDeleted: boolean;
      chunksDeleted: boolean;
    };
  }> {
    this.ensureInitialized();
    this.validateNumber(fileId, 'file ID', 1);

    try {
      this.log('info', `Deleting uploaded file: ${fileId}`);

      const file = await storage.getFile(fileId);
      if (!file || file.platform !== 'uploaded_files') {
        return {
          success: false,
          error: 'File not found or not an uploaded file',
        };
      }

      const details = {
        databaseDeleted: false,
        physicalFileDeleted: false,
        chunksDeleted: false,
      };

      // Delete file chunks first
      try {
        await storage.deleteFileChunks(fileId);
        details.chunksDeleted = true;
        this.log('info', `Deleted chunks for file ${fileId}`);
      } catch (error: any) {
        this.log('warn', `Error deleting chunks for file ${fileId}:`, error);
      }

      // Delete physical file
      if (file.fileUrl && fs.existsSync(file.fileUrl)) {
        try {
          fs.unlinkSync(file.fileUrl);
          details.physicalFileDeleted = true;
          this.log('info', `Deleted physical file: ${file.fileUrl}`);
        } catch (error: any) {
          this.log('warn', `Error deleting physical file ${file.fileUrl}:`, error);
        }
      } else {
        details.physicalFileDeleted = true; // File doesn't exist, consider it "deleted"
      }

      // Delete database record
      const databaseDeleted = await storage.deleteFile(fileId);
      details.databaseDeleted = databaseDeleted;

      if (databaseDeleted) {
        this.log('info', `Successfully deleted file ${fileId}`);
      }

      return {
        success: databaseDeleted,
        details,
      };

    } catch (error: any) {
      this.log('error', `Error deleting uploaded file ${fileId}`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Delete multiple uploaded files
   */
  async deleteMultipleFiles(fileIds: number[]): Promise<{
    success: boolean;
    results: Array<{
      fileId: number;
      success: boolean;
      error?: string;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }> {
    this.ensureInitialized();
    this.validateRequired(fileIds, 'file IDs');

    const results = [];
    let successful = 0;
    let failed = 0;

    for (const fileId of fileIds) {
      try {
        const result = await this.deleteUploadedFile(fileId);
        
        results.push({
          fileId,
          success: result.success,
          error: result.error,
        });

        if (result.success) {
          successful++;
        } else {
          failed++;
        }
      } catch (error: any) {
        results.push({
          fileId,
          success: false,
          error: error.message,
        });
        failed++;
      }
    }

    return {
      success: failed === 0,
      results,
      summary: {
        total: fileIds.length,
        successful,
        failed,
      },
    };
  }

  /**
   * Clean up orphaned files (files on disk without database records)
   */
  async cleanupOrphanedFiles(uploadDir: string): Promise<{
    deletedCount: number;
    errors: string[];
    details: Array<{
      filename: string;
      deleted: boolean;
      error?: string;
    }>;
  }> {
    this.ensureInitialized();
    this.validateString(uploadDir, 'upload directory');

    const errors: string[] = [];
    const details: Array<{ filename: string; deleted: boolean; error?: string }> = [];
    let deletedCount = 0;

    try {
      if (!fs.existsSync(uploadDir)) {
        return { deletedCount: 0, errors: ['Upload directory does not exist'], details: [] };
      }

      // Get all files in upload directory
      const physicalFiles = fs.readdirSync(uploadDir);
      
      // Get all uploaded files from database
      const dbFiles = await storage.getFiles('uploaded_files');
      const dbFilePaths = new Set(dbFiles.map(f => f.fileUrl).filter(Boolean));

      this.log('info', `Found ${physicalFiles.length} physical files and ${dbFiles.length} database records`);

      for (const filename of physicalFiles) {
        const filePath = path.join(uploadDir, filename);
        
        try {
          // Check if this file has a database record
          if (!dbFilePaths.has(filePath)) {
            // This is an orphaned file
            fs.unlinkSync(filePath);
            deletedCount++;
            details.push({ filename, deleted: true });
            this.log('info', `Deleted orphaned file: ${filename}`);
          } else {
            details.push({ filename, deleted: false });
          }
        } catch (error: any) {
          const errorMsg = `Error processing file ${filename}: ${error.message}`;
          errors.push(errorMsg);
          details.push({ filename, deleted: false, error: errorMsg });
        }
      }

      this.log('info', `Cleanup completed: ${deletedCount} orphaned files deleted, ${errors.length} errors`);
      return { deletedCount, errors, details };

    } catch (error: any) {
      this.log('error', 'Error during orphaned file cleanup', error);
      return { deletedCount: 0, errors: [error.message], details: [] };
    }
  }

  /**
   * Clean up old uploaded files
   */
  async cleanupOldFiles(daysOld: number = 30): Promise<{
    deletedCount: number;
    errors: string[];
    totalSizeFreed: number;
  }> {
    this.ensureInitialized();
    this.validateNumber(daysOld, 'days old', 1);

    const errors: string[] = [];
    let deletedCount = 0;
    let totalSizeFreed = 0;

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const files = await storage.getFiles('uploaded_files');
      this.log('info', `Checking ${files.length} uploaded files for cleanup (older than ${daysOld} days)`);

      for (const file of files) {
        try {
          const fileDate = new Date(file.updatedAt);
          
          if (fileDate < cutoffDate) {
            const result = await this.deleteUploadedFile(file.id);
            
            if (result.success) {
              deletedCount++;
              totalSizeFreed += file.fileSize || 0;
              this.log('info', `Deleted old file: ${file.fileName} (${file.id})`);
            } else {
              errors.push(`Failed to delete file ${file.fileName}: ${result.error}`);
            }
          }
        } catch (error: any) {
          errors.push(`Error processing file ${file.fileName}: ${error.message}`);
        }
      }

      this.log('info', `Old file cleanup completed: ${deletedCount} files deleted, ${Math.round(totalSizeFreed / 1024 / 1024)}MB freed`);
      return { deletedCount, errors, totalSizeFreed };

    } catch (error: any) {
      this.log('error', 'Error during old file cleanup', error);
      return { deletedCount: 0, errors: [error.message], totalSizeFreed: 0 };
    }
  }

  /**
   * Get file management statistics
   */
  async getManagementStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    orphanedFiles: number;
    oldFiles: number;
    filesByAge: {
      last24h: number;
      lastWeek: number;
      lastMonth: number;
      older: number;
    };
  }> {
    this.ensureInitialized();

    try {
      const files = await storage.getFiles('uploaded_files');
      
      let totalSize = 0;
      let orphanedFiles = 0;
      let oldFiles = 0;
      
      const now = new Date();
      const day = 24 * 60 * 60 * 1000;
      const week = 7 * day;
      const month = 30 * day;
      
      const filesByAge = {
        last24h: 0,
        lastWeek: 0,
        lastMonth: 0,
        older: 0,
      };

      for (const file of files) {
        totalSize += file.fileSize || 0;
        
        // Check if physical file exists
        if (file.fileUrl && !fs.existsSync(file.fileUrl)) {
          orphanedFiles++;
        }
        
        // Check age
        const fileDate = new Date(file.updatedAt);
        const age = now.getTime() - fileDate.getTime();
        
        if (age > 30 * day) {
          oldFiles++;
          filesByAge.older++;
        } else if (age > month) {
          filesByAge.lastMonth++;
        } else if (age > week) {
          filesByAge.lastWeek++;
        } else {
          filesByAge.last24h++;
        }
      }

      return {
        totalFiles: files.length,
        totalSize,
        orphanedFiles,
        oldFiles,
        filesByAge,
      };

    } catch (error: any) {
      this.log('error', 'Error getting management stats', error);
      return {
        totalFiles: 0,
        totalSize: 0,
        orphanedFiles: 0,
        oldFiles: 0,
        filesByAge: { last24h: 0, lastWeek: 0, lastMonth: 0, older: 0 },
      };
    }
  }

  /**
   * Repair file records (update missing metadata)
   */
  async repairFileRecords(): Promise<{
    repairedCount: number;
    errors: string[];
  }> {
    this.ensureInitialized();

    const errors: string[] = [];
    let repairedCount = 0;

    try {
      const files = await storage.getFiles('uploaded_files');
      
      for (const file of files) {
        try {
          let needsUpdate = false;
          const updates: any = {};

          // Check if physical file exists and update size if missing
          if (file.fileUrl && fs.existsSync(file.fileUrl)) {
            const stats = fs.statSync(file.fileUrl);
            
            if (!file.fileSize || file.fileSize !== stats.size) {
              updates.fileSize = stats.size;
              needsUpdate = true;
            }
          }

          // Update missing metadata
          if (!file.extractedMetadata) {
            updates.extractedMetadata = {
              repairedAt: new Date().toISOString(),
              category: 'unknown',
            };
            needsUpdate = true;
          }

          if (needsUpdate) {
            await storage.updateFile(file.id, updates);
            repairedCount++;
            this.log('info', `Repaired file record: ${file.fileName} (${file.id})`);
          }

        } catch (error: any) {
          errors.push(`Error repairing file ${file.fileName}: ${error.message}`);
        }
      }

      this.log('info', `File repair completed: ${repairedCount} records repaired, ${errors.length} errors`);
      return { repairedCount, errors };

    } catch (error: any) {
      this.log('error', 'Error during file repair', error);
      return { repairedCount: 0, errors: [error.message] };
    }
  }

  /**
   * Get file by ID with management info
   */
  async getFileWithManagementInfo(fileId: number): Promise<any> {
    this.ensureInitialized();
    this.validateNumber(fileId, 'file ID', 1);

    try {
      const file = await storage.getFile(fileId);
      
      if (!file || file.platform !== 'uploaded_files') {
        return null;
      }

      // Add management information
      const managementInfo = {
        physicalFileExists: file.fileUrl ? fs.existsSync(file.fileUrl) : false,
        hasChunks: false,
        chunkCount: 0,
        age: new Date().getTime() - new Date(file.updatedAt).getTime(),
      };

      // Check for chunks
      try {
        const chunks = await storage.getFileChunks(fileId);
        managementInfo.hasChunks = chunks.length > 0;
        managementInfo.chunkCount = chunks.length;
      } catch (error) {
        // Chunks check failed, but continue
      }

      return {
        ...file,
        managementInfo,
      };

    } catch (error: any) {
      this.log('error', `Error getting file with management info for ${fileId}`, error);
      return null;
    }
  }
}
