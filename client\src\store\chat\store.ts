import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';
import { nanoid } from 'nanoid';
import isEqual from 'fast-deep-equal';

// Types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  sessionId: string;
  files?: FileItem[];
  sources?: string[];
  tokenCount?: number;
  model?: string;
  error?: ChatError;
  loading?: boolean;
}

export interface FileItem {
  id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
  uploadProgress?: number;
}

export interface ChatSession {
  id: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  enabledSources: string[];
  messageCount: number;
}

export interface ChatError {
  type: 'network' | 'api' | 'validation';
  message: string;
  code?: string;
}

// State interface
export interface ChatState {
  // Messages
  messages: ChatMessage[];
  inputMessage: string;
  isGenerating: boolean;
  
  // Sessions
  sessions: ChatSession[];
  currentSessionId: string | null;
  
  // File upload
  uploadingFiles: FileItem[];
  isUploading: boolean;
  
  // Sources
  enabledSources: string[];
  availableSources: string[];
  
  // OpenAI Configuration
  openaiConfig: {
    model: string;
    temperature: number;
    maxTokens: number;
    topP: number;
  };
}

// Actions interface
export interface ChatActions {
  // Message actions
  updateInputMessage: (message: string) => void;
  sendMessage: () => Promise<void>;
  deleteMessage: (messageId: string) => void;
  clearMessages: () => void;
  
  // Session actions
  createSession: (title?: string) => string;
  switchSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  updateSessionTitle: (sessionId: string, title: string) => void;
  
  // File actions
  uploadFiles: (files: File[]) => Promise<void>;
  removeFile: (fileId: string) => void;
  clearFiles: () => void;
  
  // Source actions
  toggleSource: (source: string) => void;
  setEnabledSources: (sources: string[]) => void;
  
  // Config actions
  updateOpenAIConfig: (config: Partial<ChatState['openaiConfig']>) => void;
  
  // Internal actions
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => string;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  setGenerating: (generating: boolean) => void;
  
  // Streaming actions
  sendStreamingMessage: (sessionId: string, content: string, aiMessageId: string) => Promise<void>;
  sendFallbackMessage: (sessionId: string, content: string, aiMessageId: string) => Promise<void>;
}

export type ChatStore = ChatState & ChatActions;

// Default OpenAI configuration
const defaultOpenAIConfig: ChatState['openaiConfig'] = {
  model: 'gpt-4.1-nano-2025-04-14', // GPT-4.1 Nano specific model
  temperature: 0.7,
  maxTokens: 2000,
  topP: 1,
};

// Initial state
const initialState: ChatState = {
  messages: [],
  inputMessage: '',
  isGenerating: false,
  
  sessions: [],
  currentSessionId: null,
  
  uploadingFiles: [],
  isUploading: false,
  
  enabledSources: ['uploaded-files'], // Default to uploaded files
  availableSources: ['uploaded-files', 'google_drive', 'microsoft_teams', 'emails'],
  
  openaiConfig: defaultOpenAIConfig,
};

// Store implementation
export const useChatStore = create<ChatStore>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        ...initialState,

        // Message actions
        updateInputMessage: (message: string) => {
          if (isEqual(message, get().inputMessage)) return;
          set((state) => {
            state.inputMessage = message;
          });
        },

        sendMessage: async () => {
          const state = get();
          if (!state.inputMessage.trim() && state.uploadingFiles.length === 0) return;
          if (state.isGenerating) return;

          // Get or create session
          let sessionId = state.currentSessionId;
          if (!sessionId) {
            sessionId = get().createSession();
          }

          const userMessage = state.inputMessage;

          // Add user message
          const userMessageId = get().addMessage({
            role: 'user',
            content: userMessage,
            sessionId,
            files: [...state.uploadingFiles],
            sources: [...state.enabledSources],
          });

          // Clear input and files
          set((state) => {
            state.inputMessage = '';
            state.uploadingFiles = [];
          });

          // Add AI message placeholder
          const aiMessageId = get().addMessage({
            role: 'assistant',
            content: '',
            sessionId,
            loading: true,
            model: state.openaiConfig.model,
          });

          // Set generating state
          get().setGenerating(true);

          try {
            // Use streaming for better UX (LobeChat style)
            await get().sendStreamingMessage(sessionId, userMessage, aiMessageId);

          } catch (error) {
            console.error('Error sending message:', error);
            get().updateMessage(aiMessageId, {
              content: '',
              loading: false,
              error: {
                type: 'api',
                message: error instanceof Error ? error.message : 'Unknown error',
              },
            });
          } finally {
            get().setGenerating(false);
          }
        },

        sendStreamingMessage: async (sessionId: string, content: string, aiMessageId: string) => {
          const state = get();
          
          try {
            const response = await fetch(`/api/chat/sessions/${sessionId}/stream`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                content,
                enabledSources: state.enabledSources,
                openaiConfig: state.openaiConfig,
              }),
            });

            if (!response.ok) {
              throw new Error('Failed to start streaming response');
            }

            const reader = response.body?.getReader();
            if (!reader) {
              throw new Error('No readable stream available');
            }

            const decoder = new TextDecoder();
            let accumulatedContent = '';

            while (true) {
              const { done, value } = await reader.read();
              
              if (done) break;

              const chunk = decoder.decode(value, { stream: true });
              accumulatedContent += chunk;

              // Update the message with streaming content
              get().updateMessage(aiMessageId, {
                content: accumulatedContent,
                loading: true, // Keep loading true while streaming
              });
            }

            // Mark as complete
            get().updateMessage(aiMessageId, {
              content: accumulatedContent,
              loading: false,
              sources: state.enabledSources,
            });

          } catch (error) {
            console.error('Streaming error:', error);
            // Fallback to regular API call
            await get().sendFallbackMessage(sessionId, content, aiMessageId);
          }
        },

        sendFallbackMessage: async (sessionId: string, content: string, aiMessageId: string) => {
          const state = get();
          
          try {
            const response = await fetch(`/api/chat/sessions/${sessionId}/messages`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                content,
                enabledSources: state.enabledSources,
                openaiConfig: state.openaiConfig,
              }),
            });

            if (!response.ok) {
              throw new Error('Failed to send message');
            }

            const data = await response.json();
            
            // Update AI message with response
            get().updateMessage(aiMessageId, {
              content: data.aiMessage?.content || 'No response received',
              loading: false,
              sources: data.sourcesUsed || [],
            });

          } catch (error) {
            console.error('Fallback message error:', error);
            throw error;
          }
        },

        deleteMessage: (messageId: string) => {
          set((state) => {
            state.messages = state.messages.filter(m => m.id !== messageId);
          });
        },

        clearMessages: () => {
          set((state) => {
            state.messages = [];
          });
        },

        // Session actions
        createSession: (title?: string) => {
          const sessionId = nanoid();
          const session: ChatSession = {
            id: sessionId,
            title: title || `Chat ${get().sessions.length + 1}`,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            enabledSources: [...get().enabledSources],
            messageCount: 0,
          };

          set((state) => {
            state.sessions.push(session);
            state.currentSessionId = sessionId;
          });

          return sessionId;
        },

        switchSession: (sessionId: string) => {
          console.log('Store switchSession called with:', sessionId);
          const state = get();
          const session = state.sessions.find(s => s.id === sessionId);
          
          if (!session) {
            console.warn('Session not found:', sessionId);
            return;
          }
          
          set((state) => {
            state.currentSessionId = sessionId;
            // Messages are filtered by the currentMessages selector
            // No need to filter state.messages here
          });
          
          console.log('Switched to session:', sessionId, 'Session found:', !!session);
        },

        deleteSession: (sessionId: string) => {
          set((state) => {
            state.sessions = state.sessions.filter(s => s.id !== sessionId);
            state.messages = state.messages.filter(m => m.sessionId !== sessionId);
            if (state.currentSessionId === sessionId) {
              state.currentSessionId = state.sessions[0]?.id || null;
            }
          });
        },

        updateSessionTitle: (sessionId: string, title: string) => {
          set((state) => {
            const session = state.sessions.find(s => s.id === sessionId);
            if (session) {
              session.title = title;
              session.updatedAt = Date.now();
            }
          });
        },

        // File actions
        uploadFiles: async (files: File[]) => {
          const fileItems: FileItem[] = files.map(file => ({
            id: nanoid(),
            name: file.name,
            type: file.type,
            size: file.size,
            uploadProgress: 0,
          }));

          set((state) => {
            state.uploadingFiles.push(...fileItems);
            state.isUploading = true;
          });

          try {
            // Use your existing file upload API
            const formData = new FormData();
            files.forEach(file => formData.append('files', file));
            formData.append('userId', 'anonymous');

            const response = await fetch('/api/files/upload', {
              method: 'POST',
              body: formData,
            });

            if (!response.ok) {
              throw new Error('Failed to upload files');
            }

            const data = await response.json();
            
            // Update file items with URLs
            set((state) => {
              data.files?.forEach((uploadedFile: any, index: number) => {
                const fileItem = state.uploadingFiles.find(f => f.name === uploadedFile.fileName);
                if (fileItem) {
                  fileItem.url = uploadedFile.url;
                  fileItem.uploadProgress = 100;
                }
              });
              state.isUploading = false;
            });

          } catch (error) {
            console.error('Error uploading files:', error);
            set((state) => {
              state.isUploading = false;
              // Remove failed uploads
              state.uploadingFiles = state.uploadingFiles.filter(f => 
                !fileItems.some(fi => fi.id === f.id)
              );
            });
          }
        },

        removeFile: (fileId: string) => {
          set((state) => {
            state.uploadingFiles = state.uploadingFiles.filter(f => f.id !== fileId);
          });
        },

        clearFiles: () => {
          set((state) => {
            state.uploadingFiles = [];
          });
        },

        // Source actions
        toggleSource: (source: string) => {
          set((state) => {
            const index = state.enabledSources.indexOf(source);
            if (index > -1) {
              state.enabledSources.splice(index, 1);
            } else {
              state.enabledSources.push(source);
            }
          });
        },

        setEnabledSources: (sources: string[]) => {
          set((state) => {
            state.enabledSources = sources;
          });
        },

        // Config actions
        updateOpenAIConfig: (config: Partial<ChatState['openaiConfig']>) => {
          set((state) => {
            Object.assign(state.openaiConfig, config);
          });
        },

        // Internal actions
        addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
          const messageId = nanoid();
          const newMessage: ChatMessage = {
            ...message,
            id: messageId,
            timestamp: Date.now(),
          };

          set((state) => {
            state.messages.push(newMessage);
            
            // Update session message count
            const session = state.sessions.find(s => s.id === message.sessionId);
            if (session) {
              session.messageCount++;
              session.updatedAt = Date.now();
            }
          });

          return messageId;
        },

        updateMessage: (messageId: string, updates: Partial<ChatMessage>) => {
          set((state) => {
            const message = state.messages.find(m => m.id === messageId);
            if (message) {
              Object.assign(message, updates);
            }
          });
        },

        setGenerating: (generating: boolean) => {
          set((state) => {
            state.isGenerating = generating;
          });
        },
      }))
    ),
    { name: 'chat-store' }
  )
);

// Selectors
export const chatSelectors = {
  currentSession: (state: ChatStore) => 
    state.sessions.find(s => s.id === state.currentSessionId),
  
  currentMessages: (state: ChatStore) => 
    state.messages.filter(m => m.sessionId === state.currentSessionId),
  
  canSend: (state: ChatStore) => 
    !state.isGenerating && (state.inputMessage.trim() || state.uploadingFiles.length > 0),
  
  hasFiles: (state: ChatStore) => 
    state.uploadingFiles.length > 0,
}; 