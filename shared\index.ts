// Main shared module exports
export * from './types';
export * from './utils';
export * from './schemas';
// Note: schemas and types are now both exported to provide full access

// Re-export commonly used items for convenience
export type {
  // Core entity types
  User,
  Integration,
  File,
  FileChunk,
  ChatSession,
  ChatMessage,
  Project,
  SyncLog,
  SyncItem,
  
  // Insert types
  InsertUser,
  InsertIntegration,
  InsertFile,
  InsertFileChunk,
  InsertChatSession,
  InsertChatMessage,
  InsertProject,
  InsertSyncLog,
  InsertSyncItem,
  
  // Common types
  ApiResponse,
  PaginatedResponse,
  AppError,
  ProcessingStatus,
  ConnectionStatus,
} from './types';

export type {
  // Email types from schemas
  Email,
  EmailChunk,
  InsertEmail,
  InsertEmailChunk,
} from './schemas';

export {
  // Database schemas
  users,
  integrations,
  files,
  fileChunks,
  chatSessions,
  chatMessages,
  projects,
  syncLogs,
  syncItems,
  emails,
  emailChunks,
  
  // Validation schemas
  insertUserSchema,
  insertIntegrationSchema,
  insertFileSchema,
  insertFileChunkSchema,
  insertChatSessionSchema,
  insertChatMessageSchema,
  insertProjectSchema,
  
  // API validation schemas
  googleOAuthSchema,
  integrationConfigSchema,
  createChatSessionSchema,
  sendChatMessageSchema,
  createProjectSchema,
} from './schemas';

export {
  // Validation utilities
  isValidEmail,
  isValidUrl,
  validateWithSchema,
  validatePagination,
  
  // Format utilities
  formatFileSize,
  formatRelativeTime,
  formatPlatformName,
  formatErrorMessage,
  
  // Crypto utilities
  generateSecureToken,
  hashPassword,
  verifyPassword,
  encrypt,
  decrypt,
} from './utils';
