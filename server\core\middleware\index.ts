// Re-export all middleware modules
export * from './error.middleware';
export * from './logging.middleware';
export * from './auth.middleware';
export * from './validation.middleware';
export * from './session.middleware';

// Convenience exports for commonly used middleware
export {
  CustomError,
  createError,
  errorHandler,
  notFound<PERSON><PERSON><PERSON>,
  async<PERSON>and<PERSON>,
} from './error.middleware';

export {
  logger,
  requestLogger,
  performanceLogger,
  apiLogger,
  logDatabaseQuery,
  logExternalAPI,
  logSecurityEvent,
} from './logging.middleware';

export {
  requireAuth,
  optionalAuth,
  requireRole,
  requireApiKey,
  rateLimitByUser,
  tokenUtils,
} from './auth.middleware';

export {
  validate,
  validateBody,
  validateQuery,
  validateParams,
  validateHeaders,
  validateFileUpload,
  validateJsonContentType,
  sanitizeInput,
  validateApiVersion,
  commonSchemas,
} from './validation.middleware';

export {
  createSessionMiddleware,
  initializeSessionManager,
  getSessionStatus,
  cleanupSessionManager,
  sessionUtils,
} from './session.middleware';

export {
  structuredLoggingMiddleware,
  metricsEndpointMiddleware,
  healthCheckMiddleware,
  monitored,
  metricsRegistry,
  apmService,
} from './monitoring.middleware';

// Common middleware stack for API routes
export const createApiMiddleware = () => {
  const { requestLogger, performanceLogger } = require('./logging.middleware');
  const { sanitizeInput, validateJsonContentType } = require('./validation.middleware');

  return [
    requestLogger,
    performanceLogger,
    sanitizeInput,
    validateJsonContentType,
  ];
};

// Common middleware stack for authenticated routes
export const createAuthenticatedApiMiddleware = () => {
  const { requireAuth } = require('./auth.middleware');

  return [
    ...createApiMiddleware(),
    requireAuth,
  ];
};

// Common middleware stack for file upload routes
export const createFileUploadMiddleware = (
  allowedTypes: string[] = [],
  maxSize: number = 100 * 1024 * 1024
) => {
  const { requestLogger, performanceLogger } = require('./logging.middleware');
  const { validateFileUpload } = require('./validation.middleware');

  return [
    requestLogger,
    performanceLogger,
    validateFileUpload(allowedTypes, maxSize),
  ];
};
