import { Request, Response } from "express";
import { storage } from "../../storage/index.js";
import { syncOrchestratorService } from "./sync-orchestrator.service.js";
import { syncNowSchema } from "../../../shared/index.js";
import { googleService } from "../google";
import { simpleEmbeddingService as embeddingService } from "../simple-embedding-service.js";
import { openaiService } from "../openai-service";
import { cryptoService } from "../crypto-service.js";

/**
 * Sync routes service for handling HTTP endpoints (matches original sync.ts exactly)
 */
export class SyncRoutesService {
  /**
   * Get sync logs for an integration
   */
  async getSyncLogs(req: Request, res: Response) {
    try {
      console.log('Getting sync logs...');
      const { integrationId, limit } = req.query;

      const parsedIntegrationId = integrationId
        ? parseInt(integrationId as string)
        : undefined;
      const parsedLimit = limit ? parseInt(limit as string) : undefined;

      if (parsedIntegrationId && isNaN(parsedIntegrationId)) {
        return res.status(400).json({ message: "Invalid integration ID" });
      }

      if (parsedLimit && isNaN(parsedLimit)) {
        return res.status(400).json({ message: "Invalid limit" });
      }

      const logs = await storage.getSyncLogs(parsedIntegrationId, parsedLimit);
      console.log(`Found ${logs.length} sync logs`);

      return res.json({ logs });
    } catch (error: any) {
      console.error("Error getting sync logs:", error);
      console.error("Error stack:", error.stack);
      return res.status(500).json({
        message: "Failed to get sync logs",
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get a specific sync log
   */
  async getSyncLog(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid sync log ID" });
      }

      const syncLog = await storage.getSyncLog(id);
      if (!syncLog) {
        return res.status(404).json({ message: "Sync log not found" });
      }

      return res.json({ syncLog });
    } catch (error: any) {
      console.error(`Error getting sync log ${req.params.id}:`, error);
      return res.status(500).json({ message: "Failed to get sync log" });
    }
  }

  /**
   * Trigger a manual sync
   */
  async syncNow(req: Request, res: Response) {
    try {
      // Validate the request body
      const validationResult = syncNowSchema.safeParse(req.body);

      if (!validationResult.success) {
        return res.status(400).json({
          message: "Invalid sync request",
          errors: validationResult.error.errors,
        });
      }

      const { integrationId } = validationResult.data;

      // Start the sync
      const syncLog = await syncOrchestratorService.startSync(integrationId);

      return res.json({
        message: "Sync started successfully",
        syncLog,
      });
    } catch (error: any) {
      console.error("Error starting sync:", error);
      return res.status(500).json({
        message: "Failed to start sync",
        error: error.message,
      });
    }
  }

  /**
   * Re-vectorize all files (force regenerate embeddings for everything)
   */
  async reVectorizeAll(req: Request, res: Response) {
    try {
      const { integrationId, forceAll } = req.body;

      if (!integrationId) {
        return res.status(400).json({ message: "Integration ID is required" });
      }

      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        return res.status(404).json({ message: "Integration not found" });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: "Integration not connected" });
      }

      console.log(`🔄 Starting re-vectorization for integration ${integrationId} (${integration.type})`);

      // Map integration type to platform name
      let platformName = integration.type;
      if (integration.type === 'google-drive' || integration.type === 'google_drive') {
        platformName = 'google_drive';
      } else if (integration.type === 'microsoft-teams' || integration.type === 'microsoft_teams') {
        platformName = 'microsoft_teams';
      }

      // Get all files for this integration
      const allFiles = await storage.getFilesForIntegration(integrationId, platformName);
      console.log(`Found ${allFiles.length} files to re-vectorize`);

      let processed = 0;
      let success = 0;
      let failed = 0;

      for (const file of allFiles) {
        try {
          processed++;
          console.log(`Re-vectorizing ${processed}/${allFiles.length}: ${file.fileName}`);

          // Delete existing embeddings if forceAll is true
          if (forceAll) {
            await embeddingService.regenerateEmbeddings(file.id, ""); // Will delete and recreate
          }

          // Check if embeddings exist
          const hasEmbeddings = await embeddingService.hasEmbeddings(file.id);

          if (!hasEmbeddings || forceAll) {
            // Use comprehensive vectorization logic with metadata
            let contentToVectorize = "";

            // 1. Always include file metadata
            const metadataContent = [
              `File: ${file.fileName}`,
              `Type: ${file.fileType}`,
              `Platform: ${file.platform}`,
            ];

            if (file.extractedMetadata) {
              const metadata = file.extractedMetadata as any;
              
              // Add common metadata fields
              if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
              if (metadata.title && metadata.title !== file.fileName) {
                metadataContent.push(`Title: ${metadata.title}`);
              }
              if (metadata.topics && Array.isArray(metadata.topics)) {
                metadataContent.push(`Topics: ${metadata.topics.join(', ')}`);
              }
              if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
              
              // Add Teams-specific metadata if available
              if (metadata.meetingAttendees && Array.isArray(metadata.meetingAttendees)) {
                metadataContent.push(`Attendees: ${metadata.meetingAttendees.map((a: any) => a.displayName || a.email).join(', ')}`);
              }
              if (metadata.calendarAttendees && Array.isArray(metadata.calendarAttendees)) {
                metadataContent.push(`Meeting Attendees: ${metadata.calendarAttendees.map((a: any) => a.emailAddress?.name || a.emailAddress?.address).join(', ')}`);
              }
              if (metadata.meetingSubject) {
                metadataContent.push(`Meeting Subject: ${metadata.meetingSubject}`);
              }
              if (metadata.calendarSubject) {
                metadataContent.push(`Calendar Subject: ${metadata.calendarSubject}`);
              }
              if (metadata.organizer) {
                metadataContent.push(`Organizer: ${metadata.organizer.displayName || metadata.organizer.email}`);
              }
              if (metadata._sourceType) {
                metadataContent.push(`Source: ${metadata._sourceType}`);
              }
              if (metadata._teamId && metadata._channelId) {
                metadataContent.push(`Teams Channel: ${metadata._teamId}/${metadata._channelId}`);
              }
            }

            contentToVectorize = metadataContent.join('\n') + '\n\n';

            // 2. Try to extract file content (for Google integrations)
            if (integration.type === "google-drive" || integration.type === "google_drive") {
              try {
                console.log(`[GOOGLE] Extracting content for ${file.fileName}...`);
                const auth = await googleService.getAuthorizedClient(integration.credentials);

                let contentExtracted = false;

                // First, try specialized document processing for supported file types
                const { pdfService } = await import('../pdf-service.js');
                const { wordService } = await import('../word-service.js');

                // Check if this is a PDF file
                if ((pdfService as any).isPDFFile && (pdfService as any).isPDFFile(file.fileName, file.mimeType)) {
                  try {
                    console.log(`[GOOGLE] Processing PDF: ${file.fileName}`);
                    const pdfBuffer = await googleService.downloadPDFContent(auth, file.externalId || "", file.fileName);

                    if (pdfBuffer) {
                      const platformMetadata = {
                        platform: 'google_drive' as const,
                        sourceType: 'google_drive_folder',
                        sourceContext: `Google Drive`,
                        folderPath: (file.extractedMetadata as any)?.folderPath || 'Root',
                        owner: (file.extractedMetadata as any)?.owner || 'Unknown',
                        lastModified: file.lastModified || new Date()
                      };

                      const pdfResult = await pdfService.processPDFFile(
                        pdfBuffer,
                        file.fileName,
                        platformMetadata,
                        Boolean(integration.isLlmEnabled) && openaiService.isInitialized()
                      );

                      if (pdfResult.success && pdfResult.vectorizationContent) {
                        console.log(`[GOOGLE] Successfully processed PDF: ${file.fileName}`);
                        console.log(`[GOOGLE] PDF content length: ${pdfResult.vectorizationContent.length} characters`);

                        // Use the comprehensive vectorization content from PDF service
                        contentToVectorize = pdfResult.vectorizationContent;
                        contentExtracted = true;

                        // Update file metadata with PDF-extracted metadata
                        if (pdfResult.metadata) {
                          file.extractedMetadata = {
                            ...(file.extractedMetadata || {}),
                            pdfMetadata: pdfResult.metadata,
                            hasExtractedContent: true,
                            contentType: 'pdf'
                          };
                        }
                      } else {
                        console.log(`[GOOGLE] PDF processing failed for ${file.fileName}: ${pdfResult.error}`);
                      }
                    } else {
                      console.log(`[GOOGLE] Could not download PDF content for: ${file.fileName}`);
                    }
                  } catch (pdfError: any) {
                    console.error(`[GOOGLE] Error processing PDF ${file.fileName}:`, pdfError.message);
                    // Continue to fallback methods
                  }
                }
                // Check if this is a Word document
                else if ((wordService as any).isWordFile && (wordService as any).isWordFile(file.fileName, file.mimeType)) {
                  try {
                    console.log(`[GOOGLE] Processing Word document: ${file.fileName}`);
                    const wordBuffer = await googleService.downloadWordContent(auth, file.externalId || "", file.fileName);

                    if (wordBuffer) {
                      const platformMetadata = {
                        platform: 'google_drive' as const,
                        sourceType: 'google_drive_folder',
                        sourceContext: `Google Drive`,
                        folderPath: (file.extractedMetadata as any)?.folderPath || 'Root',
                        owner: (file.extractedMetadata as any)?.owner || 'Unknown',
                        lastModified: file.lastModified || new Date()
                      };

                      const wordResult = await wordService.processWordFile(
                        wordBuffer,
                        file.fileName,
                        platformMetadata,
                        Boolean(integration.isLlmEnabled) && openaiService.isInitialized()
                      );

                      if (wordResult.success && wordResult.vectorizationContent) {
                        console.log(`[GOOGLE] Successfully processed Word document: ${file.fileName}`);
                        console.log(`[GOOGLE] Word content length: ${wordResult.vectorizationContent.length} characters`);

                        // Use the comprehensive vectorization content from Word service
                        contentToVectorize = wordResult.vectorizationContent;
                        contentExtracted = true;

                        // Update file metadata with Word-extracted metadata
                        if (wordResult.metadata) {
                          file.extractedMetadata = {
                            ...(file.extractedMetadata || {}),
                            wordMetadata: wordResult.metadata,
                            hasExtractedContent: true,
                            contentType: 'word'
                          };
                        }
                      } else {
                        console.log(`[GOOGLE] Word processing failed for ${file.fileName}: ${wordResult.error}`);
                      }
                    } else {
                      console.log(`[GOOGLE] Could not download Word content for: ${file.fileName}`);
                    }
                  } catch (wordError: any) {
                    console.error(`[GOOGLE] Error processing Word document ${file.fileName}:`, wordError.message);
                    // Continue to fallback methods
                  }
                }

                // If specialized processing didn't work, try universal content extraction
                if (!contentExtracted) {
                  try {
                    // Try the universal extractFileContent method as fallback
                    const fileContent = await googleService.extractFileContent(auth, { id: file.externalId, name: file.fileName });
                    if (fileContent && fileContent.trim().length > 0) {
                      contentToVectorize += `Content:\n${fileContent}`;
                      contentExtracted = true;
                      console.log(`[GOOGLE] Universal extraction: ${fileContent.length} characters from ${file.fileName}`);
                    }
                  } catch (universalError: any) {
                    console.log(`[GOOGLE] Universal extraction failed for ${file.fileName}: ${universalError.message}`);

                    // Final fallback: try Google Docs API for document types
                    try {
                      const { transcriptText } = await googleService.extractDocContent(auth, file.externalId || "");
                      if (transcriptText && transcriptText.trim().length > 0) {
                        contentToVectorize += `Content:\n${transcriptText}`;
                        contentExtracted = true;
                        console.log(`[GOOGLE] Docs API extraction: ${transcriptText.length} characters from ${file.fileName}`);
                      }
                    } catch (docsError: any) {
                      console.log(`[GOOGLE] Docs API extraction failed for ${file.fileName}: ${docsError.message}`);
                    }
                  }
                }

                if (!contentExtracted) {
                  console.log(`[GOOGLE] No content extracted for ${file.fileName}, using metadata-only vectorization`);
                }

              } catch (contentError: any) {
                console.error(`[GOOGLE] Content extraction error for ${file.fileName}:`, contentError.message);
                console.log(`[GOOGLE] Falling back to metadata-only vectorization for ${file.fileName}`);
              }
            }

            // 3. Add file type descriptions and platform-specific metadata
            if (contentToVectorize.trim().length < 200) {
              contentToVectorize += `\nMIME Type: ${file.mimeType || 'unknown'}`;
              
              if (integration.type === "microsoft-teams" || integration.type === "microsoft_teams") {
                contentToVectorize += `\nFile Description: Microsoft Teams file - ${file.fileType}`;
                if (file.extractedMetadata) {
                  const metadata = file.extractedMetadata as any;
                  if (metadata._sourceType === 'teams_channel') {
                    contentToVectorize += `\nLocation: Teams Channel Meeting Recording/Transcript`;
                  } else if (metadata._sourceType === 'onedrive_personal') {
                    contentToVectorize += `\nLocation: OneDrive Personal`;
                  } else if (metadata._sourceType === 'sharepoint_site') {
                    contentToVectorize += `\nLocation: SharePoint Site`;
                  }
                }
              } else {
                contentToVectorize += `\nFile Description: Google Drive file - ${file.fileType}`;
              }
              
              if (file.createdAt) {
                contentToVectorize += `\nCreated: ${new Date(file.createdAt).toLocaleDateString()}`;
              }
            }

            // Generate embeddings
            console.log(`🔀 Vectorizing ${contentToVectorize.length} characters for ${file.fileName}`);
            await embeddingService.processFileForEmbeddings(file.id, contentToVectorize);
            success++;
            console.log(`✅ Re-vectorized: ${file.fileName}`);
          } else {
            console.log(`⏭️ Skipping ${file.fileName} (already has embeddings)`);
            success++;
          }

          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error: any) {
          failed++;
          console.error(`❌ Failed to re-vectorize ${file.fileName}:`, error.message);
        }
      }

      console.log(`🎉 Re-vectorization complete: ${success} success, ${failed} failed out of ${processed} total`);

      return res.json({
        message: "Re-vectorization completed",
        stats: {
          totalFiles: allFiles.length,
          processed,
          success,
          failed,
        },
      });

    } catch (error: any) {
      console.error("Error in re-vectorization:", error);
      return res.status(500).json({
        message: "Failed to re-vectorize files",
        error: error.message,
      });
    }
  }

  /**
   * Get sync items for a specific sync log
   */
  async getSyncItems(req: Request, res: Response) {
    try {
      const { syncLogId, status } = req.query;

      const parsedSyncLogId = syncLogId
        ? parseInt(syncLogId as string)
        : undefined;

      if (parsedSyncLogId && isNaN(parsedSyncLogId)) {
        return res.status(400).json({ message: "Invalid sync log ID" });
      }

      const items = await storage.getSyncItems(
        parsedSyncLogId,
        (status as string) || undefined,
      );

      return res.json({ items });
    } catch (error: any) {
      console.error("Error getting sync items:", error);
      return res.status(500).json({ message: "Failed to get sync items" });
    }
  }
}

export const syncRoutesService = new SyncRoutesService(); 