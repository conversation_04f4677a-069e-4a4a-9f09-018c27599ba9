import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

export interface CalendarEvent {
  id?: string;
  summary: string;
  description?: string;
  start: {
    dateTime: string;
    timeZone?: string;
  };
  end: {
    dateTime: string;
    timeZone?: string;
  };
  attendees?: Array<{ email: string }>;
}

export class GoogleCalendarTools {
  private oauth2Client: OAuth2Client;
  private calendar: any;

  constructor(clientId: string, clientSecret: string, redirectUri: string) {
    this.oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUri);
    this.calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });
  }

  setCredentials(tokens: any) {
    this.oauth2Client.setCredentials(tokens);
  }

  async listEvents(args: {
    maxResults?: number;
    timeMin?: string;
    timeMax?: string;
    singleEvents?: boolean;
    orderBy?: string;
  }) {
    try {
      const response = await this.calendar.events.list({
        calendarId: 'primary',
        maxResults: args.maxResults || 10,
        timeMin: args.timeMin || new Date().toISOString(),
        timeMax: args.timeMax,
        singleEvents: args.singleEvents !== false,
        orderBy: args.orderBy || 'startTime',
      });

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(response.data.items, null, 2)
        }]
      };
    } catch (error) {
      console.error('Error listing events:', error);
      return {
        content: [{
          type: 'text',
          text: `Error listing events: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  async createEvent(args: CalendarEvent) {
    try {
      const response = await this.calendar.events.insert({
        calendarId: 'primary',
        resource: args,
      });

      return {
        content: [{
          type: 'text',
          text: `Event created successfully: ${response.data.htmlLink}`
        }]
      };
    } catch (error) {
      console.error('Error creating event:', error);
      return {
        content: [{
          type: 'text',
          text: `Error creating event: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  async updateEvent(args: { eventId: string; updates: Partial<CalendarEvent> }) {
    try {
      const response = await this.calendar.events.patch({
        calendarId: 'primary',
        eventId: args.eventId,
        resource: args.updates,
      });

      return {
        content: [{
          type: 'text',
          text: `Event updated successfully: ${response.data.htmlLink}`
        }]
      };
    } catch (error) {
      console.error('Error updating event:', error);
      return {
        content: [{
          type: 'text',
          text: `Error updating event: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  async deleteEvent(args: { eventId: string }) {
    try {
      await this.calendar.events.delete({
        calendarId: 'primary',
        eventId: args.eventId,
      });

      return {
        content: [{
          type: 'text',
          text: 'Event deleted successfully'
        }]
      };
    } catch (error) {
      console.error('Error deleting event:', error);
      return {
        content: [{
          type: 'text',
          text: `Error deleting event: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  getCurrentDateTime(args: { timeZone?: string; format?: string }) {
    try {
      const now = new Date();
      const timeZone = args.timeZone || 'UTC';
      const format = args.format || 'iso';

      let result: string;
      
      switch (format) {
        case 'readable':
          result = now.toLocaleString('en-US', { 
            timeZone,
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZoneName: 'short'
          });
          break;
        case 'timestamp':
          result = now.getTime().toString();
          break;
        case 'iso':
        default:
          if (timeZone === 'UTC') {
            result = now.toISOString();
          } else {
            // For non-UTC timezones, we need to format properly
            result = new Intl.DateTimeFormat('sv-SE', {
              timeZone,
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              fractionalSecondDigits: 3,
            }).format(now).replace(' ', 'T') + 'Z';
          }
          break;
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            dateTime: result,
            timeZone: timeZone,
            format: format,
            timestamp: now.getTime()
          }, null, 2)
        }]
      };
    } catch (error) {
      console.error('Error getting current datetime:', error);
      return {
        content: [{
          type: 'text',
          text: `Error getting current datetime: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }
} 