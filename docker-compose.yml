# =============================================================================
# MeetSync Production Docker Compose Configuration
# =============================================================================

services:
  # =============================================================================
  # Application Service
  # =============================================================================
  meetsync-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: meetsync-app
    restart: unless-stopped
    ports:
      - "${PORT:-8080}:8080"
    environment:
      # Application Settings
      NODE_ENV: production
      PORT: 8080
      HOST: 0.0.0.0
      SERVER_URL: ${SERVER_URL:-http://localhost:8080}
      
      # Database Settings
      DATABASE_URL: postgresql://${POSTGRES_USER:-meetsync}:${POSTGRES_PASSWORD:-meetsync123}@postgres:5432/${POSTGRES_DB:-meetsync}
      
      # Redis Settings
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
      
      # Security Settings
      SESSION_SECRET: ${SESSION_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      
      # OpenAI Settings
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      
      # Google OAuth Settings
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      
      # Microsoft OAuth Settings
      MICROSOFT_CLIENT_ID: ${MICROSOFT_CLIENT_ID}
      MICROSOFT_CLIENT_SECRET: ${MICROSOFT_CLIENT_SECRET}
      MICROSOFT_TENANT_ID: ${MICROSOFT_TENANT_ID}
      MICROSOFT_REDIRECT_URI: ${MICROSOFT_REDIRECT_URI}
      
      # Notion Settings
      NOTION_API_KEY: ${NOTION_API_KEY}
      NOTION_DATABASE_ID: ${NOTION_DATABASE_ID}
      NOTION_INTEGRATION_SECRET: ${NOTION_INTEGRATION_SECRET}
      NOTION_PAGE_URL: ${NOTION_PAGE_URL}
      
      # RAG Settings
      CHUNK_SIZE: ${CHUNK_SIZE:-512}
      CHUNK_OVERLAP: ${CHUNK_OVERLAP:-20}
      TOP_K: ${TOP_K:-50}
      SYSTEM_PROMPT: ${SYSTEM_PROMPT:-You are GPT Unify - GPT AI Assistant, an intelligent AI assistant with access to data sources from multiple platforms including Google Drive, meeting transcripts, uploaded files, and other integrated platforms.}
      ENABLE_FUNCTION_TOOLS: ${ENABLE_FUNCTION_TOOLS:-false}
      ENABLE_FILE_CREATION: ${ENABLE_FILE_CREATION:-false}
      ENABLE_GOOGLE_DRIVE_ACTIONS: ${ENABLE_GOOGLE_DRIVE_ACTIONS:-false}
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - meetsync-network
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # =============================================================================
  # PostgreSQL Database with pgvector
  # =============================================================================
  postgres:
    image: pgvector/pgvector:pg16
    container_name: meetsync-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-meetsync}
      POSTGRES_USER: ${POSTGRES_USER:-meetsync}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-meetsync123}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - meetsync-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-meetsync} -d ${POSTGRES_DB:-meetsync}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # =============================================================================
  # Redis (for sessions and caching) - Optional but recommended
  # =============================================================================
  redis:
    image: redis:latest
    container_name: meetsync-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - meetsync-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'

# =============================================================================
# Networks
# =============================================================================
networks:
  meetsync-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local 