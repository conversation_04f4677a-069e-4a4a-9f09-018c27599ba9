Integrations:
  get:
    tags:
      - Integrations
    summary: Get all integrations
    description: Retrieve a list of all configured integrations
    operationId: getIntegrations
    responses:
      '200':
        $ref: '../components/responses.yaml#/IntegrationsListResponse'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  post:
    tags:
      - Integrations
    summary: Create a new integration
    description: Create a new integration configuration
    operationId: createIntegration
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yaml#/CreateIntegration'
    responses:
      '201':
        $ref: '../components/responses.yaml#/IntegrationResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

IntegrationById:
  get:
    tags:
      - Integrations
    summary: Get integration by ID
    description: Retrieve a specific integration by its ID
    operationId: getIntegration
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/IntegrationResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  put:
    tags:
      - Integrations
    summary: Update integration
    description: Update an existing integration configuration
    operationId: updateIntegration
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yaml#/CreateIntegration'
    responses:
      '200':
        $ref: '../components/responses.yaml#/IntegrationResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  delete:
    tags:
      - Integrations
    summary: Delete integration
    description: Delete an integration and all associated data
    operationId: deleteIntegration
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '204':
        $ref: '../components/responses.yaml#/NoContent'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

AuthUrl:
  get:
    tags:
      - Integrations
    summary: Get OAuth authorization URL
    description: Get the OAuth authorization URL for Google Drive integration
    operationId: getAuthUrl
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/OAuthUrlResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

OAuthCallback:
  get:
    tags:
      - Integrations
    summary: Handle OAuth callback
    description: Handle OAuth callback for Google Drive integration
    operationId: handleOAuthCallback
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
      - $ref: '../components/parameters.yaml#/OAuthCode'
      - $ref: '../components/parameters.yaml#/OAuthState'
      - $ref: '../components/parameters.yaml#/OAuthError'
    responses:
      '302':
        $ref: '../components/responses.yaml#/RedirectResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

GoogleDriveFolders:
  get:
    tags:
      - Integrations
    summary: Get Google Drive folders
    description: Retrieve available folders from Google Drive
    operationId: getGoogleDriveFolders
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        description: Google Drive folders retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                folders:
                  type: array
                  items:
                    $ref: '../components/schemas.yaml#/GoogleDriveFolder'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DriveStructure:
  get:
    tags:
      - Integrations
    summary: Get enhanced Google Drive structure
    description: Retrieve detailed folder and file structure from Google Drive
    operationId: getDriveStructure
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        description: Drive structure retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                structure:
                  $ref: '../components/schemas.yaml#/DriveStructure'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DebugFolders:
  get:
    tags:
      - Integrations
    summary: Debug Google Drive folders
    description: Debug endpoint for Google Drive folder access
    operationId: debugDriveFolders
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TeamsAuthUrl:
  get:
    tags:
      - Integrations
    summary: Get Microsoft Teams OAuth URL
    description: Get the OAuth authorization URL for Microsoft Teams integration
    operationId: getTeamsAuthUrl
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/OAuthUrlResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TeamsOAuthCallback:
  get:
    tags:
      - Integrations
    summary: Handle Microsoft Teams OAuth callback
    description: Handle OAuth callback for Microsoft Teams integration
    operationId: handleTeamsOAuthCallback
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
      - $ref: '../components/parameters.yaml#/OAuthCode'
      - $ref: '../components/parameters.yaml#/OAuthState'
      - $ref: '../components/parameters.yaml#/OAuthError'
    responses:
      '302':
        $ref: '../components/responses.yaml#/RedirectResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TeamsSources:
  get:
    tags:
      - Integrations
    summary: Get Microsoft Teams sources
    description: Retrieve available Teams sources (teams, channels, chats)
    operationId: getTeamsSources
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        description: Teams sources retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                sources:
                  type: array
                  items:
                    $ref: '../components/schemas.yaml#/TeamsSource'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TeamsFolders:
  get:
    tags:
      - Integrations
    summary: Get Microsoft Teams folders
    description: Retrieve Teams folders and structure
    operationId: getTeamsFolders
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TeamsChannels:
  get:
    tags:
      - Integrations
    summary: Get Microsoft Teams channels
    description: Retrieve channels for a specific Teams team
    operationId: getTeamsChannels
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
      - $ref: '../components/parameters.yaml#/TeamId'
    responses:
      '200':
        description: Teams channels retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                channels:
                  type: array
                  items:
                    $ref: '../components/schemas.yaml#/TeamsChannel'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestTeamsConnection:
  post:
    tags:
      - Integrations
    summary: Test Microsoft Teams connection
    description: Test the connection to Microsoft Teams
    operationId: testTeamsConnection
    parameters:
      - $ref: '../components/parameters.yaml#/IntegrationId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/Success'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

GlobalOAuthCallback:
  get:
    tags:
      - Integrations
    summary: Global OAuth callback handler
    description: Global OAuth callback endpoint for both Google and Microsoft OAuth flows
    operationId: handleGlobalOAuthCallback
    parameters:
      - $ref: '../components/parameters.yaml#/OAuthCode'
      - $ref: '../components/parameters.yaml#/OAuthState'
      - $ref: '../components/parameters.yaml#/OAuthError'
    responses:
      '302':
        $ref: '../components/responses.yaml#/RedirectResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 