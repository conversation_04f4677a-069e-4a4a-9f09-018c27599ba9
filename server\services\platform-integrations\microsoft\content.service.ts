import { Client } from '@microsoft/microsoft-graph-client';
import { BaseService } from "../../base/service.interface";
import type { MicrosoftCredentials } from './oauth.service';

/**
 * Microsoft Content Service - Simplified version that delegates to unified content extractor
 */
export class MicrosoftContentService extends BaseService {
  constructor() {
    super('MicrosoftContent', '1.0.0', 'Microsoft content extraction via unified extractor');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Microsoft Content service initialized');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      healthy: true,
      status: 'active',
      service: 'MicrosoftContentService',
      delegatesTo: 'UnifiedContentExtractor'
    };
  }

  /**
   * Download and extract file content - delegates to unified content extractor
   * This maintains backward compatibility with the facade
   */
  async downloadAndExtractFileContent(
    client: Client,
    driveId: string,
    fileId: string,
    fileName: string,
    mimeType?: string
  ): Promise<string | null> {
    this.ensureInitialized();

    try {
      this.log('info', `Downloading and extracting content for: ${fileName}`);

      // Download the file buffer
      let buffer: Buffer | undefined;
      try {
        const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
          .responseType('arraybuffer' as any)
          .get();

        if (downloadResponse) {
          // Convert response to buffer
          if (Buffer.isBuffer(downloadResponse)) {
            buffer = downloadResponse;
          } else if (downloadResponse instanceof ArrayBuffer) {
            buffer = Buffer.from(downloadResponse);
          } else if (downloadResponse instanceof Uint8Array) {
            buffer = Buffer.from(downloadResponse);
          } else if (typeof downloadResponse === 'string') {
            buffer = Buffer.from(downloadResponse, 'binary');
          } else if (downloadResponse && typeof downloadResponse === 'object' && (downloadResponse as any).buffer) {
            buffer = Buffer.from((downloadResponse as any).buffer);
          }
        }

        if (!buffer) {
          this.log('warn', `No buffer received for file: ${fileName}`);
          return null;
        }

        this.log('info', `Downloaded ${buffer.length} bytes for: ${fileName}`);
      } catch (downloadError) {
        this.log('error', `Error downloading file ${fileName}:`, downloadError);
        return null;
      }

      // Use unified content extractor
      const { unifiedContentExtractor } = await import('../../ai/unified-content-extractor.service');
      
      const platformMetadata = {
        platform: 'microsoft_teams' as const,
        fileName,
        fileType: this.getFileTypeFromName(fileName),
        mimeType,
        sourceUrl: `https://graph.microsoft.com/v1.0/drives/${driveId}/items/${fileId}`,
        lastModified: new Date(),
        owner: 'Unknown',
        folderPath: 'Microsoft Teams'
      };

      const extractionResult = await unifiedContentExtractor.extractContent(
        platformMetadata,
        buffer
      );

      if (extractionResult.success && extractionResult.content) {
        this.log('info', `Successfully extracted ${extractionResult.content.length} characters from: ${fileName}`);
        return extractionResult.content;
      } else {
        this.log('warn', `Content extraction failed for ${fileName}: ${extractionResult.error}`);
        return null;
      }

    } catch (error: any) {
      this.log('error', `Error processing file ${fileName}:`, error);
      return null;
    }
  }

  /**
   * Check if file type is supported for content extraction
   */
  isFileTypeSupported(fileName: string, mimeType?: string): boolean {
    const extension = this.getFileExtension(fileName);
    const supportedExtensions = ['pdf', 'docx', 'doc', 'txt', 'xlsx', 'pptx', 'csv', 'md'];
    
    const supportedMimeTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ];

    return supportedExtensions.includes(extension) ||
           Boolean(mimeType && supportedMimeTypes.includes(mimeType));
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
  }

  /**
   * Get file type from filename for unified extractor
   */
  private getFileTypeFromName(fileName: string): string {
    const extension = this.getFileExtension(fileName).toLowerCase();
    
    if (extension === 'pdf') return 'pdf';
    if (['doc', 'docx'].includes(extension)) return 'docx';
    if (['xls', 'xlsx'].includes(extension)) return 'excel';
    if (['ppt', 'pptx'].includes(extension)) return 'pptx';
    if (extension === 'csv') return 'csv';
    if (['md', 'markdown'].includes(extension)) return 'markdown';
    if (['js', 'ts', 'py', 'java', 'css', 'html', 'xml', 'json', 'yaml', 'yml'].includes(extension)) return 'code';
    if (['txt', 'log', 'rtf'].includes(extension)) return 'text';
    
    return 'text'; // default fallback
  }

  /**
   * Get enhanced meeting metadata from Microsoft Graph
   */
  async getEnhancedMeetingMetadata(
    client: Client,
    fileName: string,
    fileCreatedDate?: Date
  ): Promise<Record<string, any>> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(fileName, 'file name');

    try {
      this.log('info', `Getting enhanced meeting metadata for: ${fileName}`);
      
      const meetingData: Record<string, any> = {};

      // Try to find related calendar events
      if (fileCreatedDate) {
        try {
          // Search for calendar events around the file creation time
          const startTime = new Date(fileCreatedDate.getTime() - 2 * 60 * 60 * 1000); // 2 hours before
          const endTime = new Date(fileCreatedDate.getTime() + 2 * 60 * 60 * 1000); // 2 hours after

          const eventsResponse = await client.api('/me/calendar/events')
            .filter(`start/dateTime ge '${startTime.toISOString()}' and end/dateTime le '${endTime.toISOString()}'`)
            .select('subject,start,end,organizer,attendees,isOnlineMeeting,onlineMeeting')
            .get();

          const events = eventsResponse.value || [];
          
          // Try to find the most relevant event
          for (const event of events) {
            if (this.isRelatedMeetingEvent(fileName, event)) {
              meetingData.relatedEvent = {
                subject: event.subject,
                startDateTime: event.start?.dateTime,
                endDateTime: event.end?.dateTime,
                organizer: event.organizer,
                attendees: event.attendees || [],
                isOnlineMeeting: event.isOnlineMeeting,
                onlineMeetingJoinUrl: event.onlineMeeting?.joinUrl
              };
              break;
            }
          }
        } catch (eventsError: any) {
          this.log('info', `Could not search calendar events: ${eventsError.message}`);
        }
      }

      // Get user's Teams and channels for context
      try {
        const teams = await client.api('/me/joinedTeams').select('id,displayName').get();
        meetingData.userTeams = teams.value || [];
      } catch (teamsError: any) {
        this.log('info', `Could not get user teams: ${teamsError.message}`);
      }

      return meetingData;
    } catch (error: any) {
      this.log('error', `Error getting enhanced meeting metadata: ${error.message}`);
      return {};
    }
  }

  /**
   * Check if a calendar event is related to the meeting file
   */
  private isRelatedMeetingEvent(fileName: string, event: any): boolean {
    const fileNameLower = fileName.toLowerCase();
    const subject = event.subject?.toLowerCase() || '';
    
    // Check for direct subject match
    if (subject && fileNameLower.includes(subject)) {
      return true;
    }

    // Check if it's an online meeting
    if (event.isOnlineMeeting) {
      // Extract date from filename and compare
      const dateMatch = fileName.match(/(\d{4})-?(\d{2})-?(\d{2})/);
      if (dateMatch && event.start?.dateTime) {
        const fileDate = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
        const eventDate = event.start.dateTime.split('T')[0];
        if (fileDate === eventDate) {
          return true;
        }
      }

      // Check for time patterns
      const timeMatch = fileName.match(/(\d{2}):?(\d{2})/);
      if (timeMatch && event.start?.dateTime) {
        const fileTime = `${timeMatch[1]}:${timeMatch[2]}`;
        const eventTime = new Date(event.start.dateTime).toTimeString().substring(0, 5);
        if (fileTime === eventTime) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Extract metadata from Microsoft file properties
   */
  async extractFileMetadata(
    client: Client,
    driveId: string,
    fileId: string
  ): Promise<Record<string, any>> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(driveId, 'drive ID');
    this.validateString(fileId, 'file ID');

    try {
      // Get detailed file properties
      const fileResponse = await client.api(`/drives/${driveId}/items/${fileId}`)
        .select('id,name,file,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference,shared,permissions')
        .expand('permissions')
        .get();

      const metadata: Record<string, any> = {
        fileId: fileResponse.id,
        fileName: fileResponse.name,
        size: fileResponse.size,
        createdDateTime: fileResponse.createdDateTime,
        lastModifiedDateTime: fileResponse.lastModifiedDateTime,
        webUrl: fileResponse.webUrl,
        mimeType: fileResponse.file?.mimeType,
        driveId: driveId,
        parentReference: fileResponse.parentReference,
      };

      // Add creator information
      if (fileResponse.createdBy?.user) {
        metadata.createdBy = {
          displayName: fileResponse.createdBy.user.displayName,
          email: fileResponse.createdBy.user.userPrincipalName,
          id: fileResponse.createdBy.user.id,
        };
      }

      // Add last modifier information
      if (fileResponse.lastModifiedBy?.user) {
        metadata.lastModifiedBy = {
          displayName: fileResponse.lastModifiedBy.user.displayName,
          email: fileResponse.lastModifiedBy.user.userPrincipalName,
          id: fileResponse.lastModifiedBy.user.id,
        };
      }

      // Add sharing information
      if (fileResponse.shared) {
        metadata.shared = true;
        metadata.permissions = fileResponse.permissions?.map((perm: any) => ({
          grantedTo: perm.grantedTo,
          roles: perm.roles,
        })) || [];
      }

      return metadata;
    } catch (error: any) {
      this.handleError(error, `extracting metadata for file ${fileId}`);
    }
  }

  /**
   * Get file type category
   */
  getFileTypeCategory(fileName: string, mimeType?: string): string {
    const extension = this.getFileExtension(fileName);
    
    if (['pdf'].includes(extension) || mimeType?.includes('pdf')) {
      return 'document';
    }
    
    if (['docx', 'doc'].includes(extension) || mimeType?.includes('word')) {
      return 'document';
    }
    
    if (['xlsx', 'xls'].includes(extension) || mimeType?.includes('sheet')) {
      return 'spreadsheet';
    }
    
    if (['pptx', 'ppt'].includes(extension) || mimeType?.includes('presentation')) {
      return 'presentation';
    }
    
    if (['txt'].includes(extension) || mimeType?.includes('text')) {
      return 'text';
    }
    
    return 'unknown';
  }
}
