Test:
  get:
    tags:
      - Test
    summary: Basic test endpoint
    description: Basic test endpoint to verify API connectivity
    operationId: basicTest
    responses:
      '200':
        description: Test successful
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                timestamp:
                  type: string
                  format: date-time
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

TestStorage:
  get:
    tags:
      - Test
    summary: Test storage connectivity
    description: Test storage system connectivity and functionality
    operationId: testStorage
    responses:
      '200':
        description: Storage test successful
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                storageInfo:
                  type: object
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 