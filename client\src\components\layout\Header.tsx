import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { ThemeToggle } from "@/components/ui/theme-toggle";

interface HeaderProps {
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function Header({
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: HeaderProps) {
  const [location] = useLocation();
  
  return (
    <header className="bg-background border-b border-border shadow-sm z-10">
      <div className="flex items-center justify-between px-6 py-3">
        <h2 className="text-xl font-semibold text-foreground">{title}</h2>
        <div className="flex items-center space-x-3">
          {onAddIntegration && location.includes("integrations") && (
            <button 
              type="button"
              onClick={onAddIntegration}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 flex items-center font-medium"
              aria-label="Add a new integration"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Integration
            </button>
          )}
          <ThemeToggle />
          <button 
            type="button"
            className="p-2 rounded-full hover:bg-accent"
            aria-label="View notifications"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
          </button>
        </div>
      </div>
      
      {tabs && tabs.length > 0 && (
        <div className="px-6 py-2 border-b border-border bg-muted/30">
          <div className="flex space-x-5">
            {tabs.map((tab) => (
              <button
                key={tab}
                type="button"
                className={cn(
                  "px-1 py-2 text-sm font-medium border-b-2",
                  currentTab === tab
                    ? "text-primary border-primary"
                    : "text-muted-foreground border-transparent hover:border-muted-foreground/30"
                )}
                onClick={() => onTabChange?.(tab)}
                aria-label={`Switch to ${tab} tab`}
                aria-pressed={currentTab === tab}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>
      )}
    </header>
  );
}
