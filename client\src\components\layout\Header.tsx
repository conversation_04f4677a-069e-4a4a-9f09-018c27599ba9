import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Bell, 
  Plus, 
  MoreHorizontal,
  Sparkles 
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ThemeToggle } from "@/components/ui/theme-toggle";

interface HeaderProps {
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function Header({
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: HeaderProps) {
  return (
    <header className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border sticky top-0 z-50">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Title and Search Section */}
        <div className="flex items-center space-x-6 flex-1">
          <div className="flex items-center space-x-3">
            <h1 className="text-2xl font-bold text-foreground">
              {title}
            </h1>
            {title === "Dashboard" && (
              <Badge variant="secondary" className="flex items-center space-x-1">
                <Sparkles className="h-3 w-3" />
                <span>AI Powered</span>
              </Badge>
            )}
          </div>
          
          {/* Search */}
          <div className="relative max-w-md flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search files, data, or ask AI..."
              className="pl-10 bg-muted/50 border-0 focus-visible:ring-1 focus-visible:ring-ring"
            />
          </div>
        </div>

        {/* Actions Section */}
        <div className="flex items-center space-x-3">
          {onAddIntegration && (
            <Button onClick={onAddIntegration} size="sm" className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>Add Integration</span>
            </Button>
          )}
          
          {/* Notifications */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-4 w-4" />
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
            >
              3
            </Badge>
          </Button>
          
          {/* More actions */}
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Tabs Section */}
      {tabs && tabs.length > 0 && (
        <div className="px-6 pb-4">
          <Tabs value={currentTab} onValueChange={onTabChange}>
            <TabsList className="grid w-full grid-cols-2 lg:w-auto lg:grid-cols-none">
            {tabs.map((tab) => (
                <TabsTrigger 
                key={tab}
                  value={tab}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                {tab}
                </TabsTrigger>
            ))}
            </TabsList>
          </Tabs>
        </div>
      )}
    </header>
  );
}
