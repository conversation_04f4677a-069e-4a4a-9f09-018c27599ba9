import { simpleEmbeddingService as embeddingService } from "../../../ai/simple-embedding-service.js";
import { openaiService } from "../../../ai/openai-service";
import type { IEmbeddingProcessor, PlatformMetadata } from '../base/content-extractor.interface';

/**
 * Microsoft Teams embedding processor
 * Handles generating embeddings for Microsoft Teams files
 */
export class MicrosoftEmbeddingProcessor implements IEmbeddingProcessor {

  /**
   * Check if embedding service is available
   */
  isAvailable(): boolean {
    return embeddingService.isInitialized();
  }

  /**
   * Check if embeddings already exist for a file
   */
  async hasEmbeddings(fileId: number): Promise<boolean> {
    try {
      return await embeddingService.hasEmbeddings(fileId);
    } catch (error) {
      console.error('[TEAMS-EMBEDDING] Error checking embeddings:', error);
      return false;
    }
  }

  /**
   * Process embeddings for a Microsoft Teams file with comprehensive content extraction
   */
  async processEmbeddings(
    savedFile: any,
    file: any,
    integration: any
  ): Promise<boolean> {
    try {
      // Check if file already has embeddings
      const hasExistingEmbeddings = await this.hasEmbeddings(savedFile.id);
      if (hasExistingEmbeddings) {
        console.log(`[TEAMS-EMBEDDING] File ${file.fileName} already has embeddings, skipping`);
        return true;
      }

      console.log(`[TEAMS-EMBEDDING] Generating embeddings for file: ${file.fileName}`);

      // Build comprehensive content for vectorization using multi-layered approach
      const vectorizationResult = await this.buildComprehensiveVectorizationContent(file, integration);

      if (!vectorizationResult.success || !vectorizationResult.content || vectorizationResult.content.trim().length < 10) {
        console.log(`[TEAMS-EMBEDDING] No content to vectorize for ${file.fileName}`);
        return false;
      }

      // Generate embeddings
      try {
        await embeddingService.processFileForEmbeddings(savedFile.id, vectorizationResult.content);
        console.log(`[TEAMS-EMBEDDING] Successfully generated embeddings for ${file.fileName}`);
        console.log(`[TEAMS-EMBEDDING] Content processed using ${vectorizationResult.extractionMethod} extraction`);
        return true;
      } catch (embeddingError) {
        console.error(`[TEAMS-EMBEDDING] Failed to generate embeddings for ${file.fileName}:`, embeddingError);
        return false;
      }

    } catch (error: any) {
      console.error(`[TEAMS-EMBEDDING] Error processing embeddings for ${file.fileName}:`, error);
      return false;
    }
  }

  /**
   * Build comprehensive vectorization content with multi-layered fallback strategy
   */
  private async buildComprehensiveVectorizationContent(
    file: any, 
    integration: any
  ): Promise<{ success: boolean; content?: string; extractionMethod?: string }> {
    let contentToVectorize = "";
    let extractionMethod = "metadata-only";

    try {
      // 1. Always start with comprehensive Teams metadata (enhanced version)
      const metadataContent = this.buildEnhancedTeamsMetadata(file);
      contentToVectorize = metadataContent.join('\n') + '\n\n';

      // 2. Try to extract content for PDFs and Word documents (specialized processing)
      const documentResult = await this.tryDocumentContentExtraction(file, integration);
      if (documentResult.success && documentResult.content) {
        console.log(`[TEAMS] Successfully extracted document content: ${documentResult.content.length} characters`);
        contentToVectorize = documentResult.content; // Use document content as primary content
        extractionMethod = "specialized-document";
        
        // Update file metadata with document-extracted metadata if available
        if (documentResult.metadata) {
          file.extractedMetadata = {
            ...file.extractedMetadata,
            [documentResult.contentType === 'pdf' ? 'pdfMetadata' : 'wordMetadata']: documentResult.metadata,
            hasExtractedContent: true,
            contentType: documentResult.contentType
          };
        }
      } else {
        // 3. Add enhanced file type descriptions and context (fallback)
        contentToVectorize += this.buildContextualDescriptions(file);
        extractionMethod = "enhanced-metadata";
      }

      // 4. Add temporal context for better searchability
      contentToVectorize += this.buildTemporalContext(file);

      // 5. Always add meeting time context if available
      if (file.extractedMetadata) {
        const metadata = file.extractedMetadata as any;
        if (metadata.meetingStartTime) {
          contentToVectorize += `Meeting Start: ${new Date(metadata.meetingStartTime).toLocaleString()}\n`;
        }
        if (metadata.calendarStartTime) {
          contentToVectorize += `Calendar Start: ${new Date(metadata.calendarStartTime).toLocaleString()}\n`;
        }
      }

      return {
        success: contentToVectorize.trim().length > 10,
        content: contentToVectorize,
        extractionMethod
      };

    } catch (vectorizationError: any) {
      console.error(`[TEAMS-EMBEDDING] Error in vectorization process for ${file.fileName}:`, vectorizationError);
      
      // Final fallback: minimal content
      const fallbackContent = `File: ${file.fileName}\nType: ${file.fileType}\nPlatform: microsoft_teams`;
      return {
        success: true,
        content: fallbackContent,
        extractionMethod: "fallback"
      };
    }
  }

  /**
   * Build enhanced Teams metadata with all the detailed fields from the original service
   */
  private buildEnhancedTeamsMetadata(file: any): string[] {
    const metadataContent = [
      `File: ${file.fileName}`,
      `Type: ${file.fileType}`,
      `Platform: microsoft_teams`,
    ];

    if (file.extractedMetadata) {
      const metadata = file.extractedMetadata as any;
      
      // Add general metadata fields
      if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
      if (metadata.title && metadata.title !== file.fileName) {
        metadataContent.push(`Title: ${metadata.title}`);
      }
      if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
      
      // Add Teams-specific metadata (comprehensive extraction)
      if (metadata._sourceType) {
        metadataContent.push(`Source Type: ${metadata._sourceType}`);
      }
      if (metadata._sourceContext) {
        metadataContent.push(`Source: ${metadata._sourceContext}`);
      }
      if (metadata._teamName) {
        metadataContent.push(`Team: ${metadata._teamName}`);
      }
      if (metadata._channelName) {
        metadataContent.push(`Channel: ${metadata._channelName}`);
      }
      if (metadata._siteName) {
        metadataContent.push(`SharePoint Site: ${metadata._siteName}`);
      }
      if (metadata._driveName) {
        metadataContent.push(`Drive: ${metadata._driveName}`);
      }
      
      // Add meeting-specific metadata (comprehensive)
      if (metadata.meetingSubject) {
        metadataContent.push(`Meeting Subject: ${metadata.meetingSubject}`);
      }
      if (metadata.calendarSubject) {
        metadataContent.push(`Calendar Subject: ${metadata.calendarSubject}`);
      }
      if (metadata.meetingAttendees && Array.isArray(metadata.meetingAttendees)) {
        const attendeeNames = metadata.meetingAttendees
          .map((a: any) => a.displayName || a.email)
          .filter(Boolean)
          .join(', ');
        if (attendeeNames) {
          metadataContent.push(`Meeting Attendees: ${attendeeNames}`);
        }
      }
      if (metadata.calendarAttendees && Array.isArray(metadata.calendarAttendees)) {
        const attendeeNames = metadata.calendarAttendees
          .map((a: any) => a.emailAddress?.name || a.emailAddress?.address)
          .filter(Boolean)
          .join(', ');
        if (attendeeNames) {
          metadataContent.push(`Calendar Attendees: ${attendeeNames}`);
        }
      }
      if (metadata.organizer) {
        metadataContent.push(`Organizer: ${metadata.organizer.displayName || metadata.organizer.email}`);
      }
      if (metadata.meetingOrganizer) {
        metadataContent.push(`Meeting Organizer: ${metadata.meetingOrganizer.displayName || metadata.meetingOrganizer.email}`);
      }
      if (metadata.calendarOrganizer) {
        metadataContent.push(`Calendar Organizer: ${metadata.calendarOrganizer.displayName || metadata.calendarOrganizer.email}`);
      }
      
      // Add AI-extracted metadata if available
      if (metadata.aiExtractedTitle) {
        metadataContent.push(`AI Title: ${metadata.aiExtractedTitle}`);
      }
      if (metadata.aiExtractedAttendees && Array.isArray(metadata.aiExtractedAttendees)) {
        metadataContent.push(`AI Attendees: ${metadata.aiExtractedAttendees.join(', ')}`);
      }
      if (metadata.aiExtractedTopics && Array.isArray(metadata.aiExtractedTopics)) {
        metadataContent.push(`AI Topics: ${metadata.aiExtractedTopics.join(', ')}`);
      }
      if (metadata.aiExtractedDate) {
        metadataContent.push(`AI Date: ${metadata.aiExtractedDate}`);
      }
      if (metadata.aiExtractedTime) {
        metadataContent.push(`AI Time: ${metadata.aiExtractedTime}`);
      }
    }

    return metadataContent;
  }

  /**
   * Try to extract document content for PDFs and Word files using unified content extractor
   */
  private async tryDocumentContentExtraction(
    file: any, 
    integration: any
  ): Promise<{ success: boolean; content?: string; metadata?: any; contentType?: string }> {
    try {
      const { unifiedContentExtractor } = await import('../../../ai/unified-content-extractor.service');
      const { microsoftService } = await import('../../../platform-integrations/microsoft');
      const { cryptoService } = await import('../../../core/crypto-service.js');

      const extension = file.fileName.split('.').pop()?.toLowerCase();
      const isPdf = extension === 'pdf' || (file.extractedMetadata as any)?.mimeType?.includes('pdf');
      const isWord = ['doc', 'docx'].includes(extension || '') || (file.extractedMetadata as any)?.mimeType?.includes('word');

      if (file.fileType === 'document' && (isPdf || isWord)) {
        const fileType = isPdf ? 'PDF' : 'Word document';
        console.log(`[TEAMS] Attempting ${fileType} content extraction for: ${file.fileName}`);

        // Get drive ID and file ID from metadata
        const metadata = file.extractedMetadata as any;
        const driveId = metadata?.driveId;
        const fileId = file.externalId;

        if (driveId && fileId) {
          // Download the file buffer using Microsoft Graph API
          const credentials = JSON.parse(await cryptoService.decrypt(integration.credentials));
          const client = (microsoftService as any).createGraphClient(credentials);
          const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
            .responseType('arraybuffer' as any)
            .get();

          if (downloadResponse) {
            // Convert response to buffer (complex buffer handling from original)
            let buffer: Buffer;
            if (Buffer.isBuffer(downloadResponse)) {
              buffer = downloadResponse;
            } else if (downloadResponse instanceof ArrayBuffer) {
              buffer = Buffer.from(downloadResponse);
            } else if (downloadResponse instanceof Uint8Array) {
              buffer = Buffer.from(downloadResponse);
            } else if (typeof downloadResponse === 'string') {
              buffer = Buffer.from(downloadResponse, 'binary');
            } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
              buffer = Buffer.from(downloadResponse.buffer);
            } else {
              throw new Error('Unexpected response type');
            }

            // Build platform metadata for document service
            const platformMetadata = {
              platform: 'microsoft_teams' as const,
              sourceType: metadata._sourceType || 'teams_file',
              sourceContext: metadata._sourceContext || `Microsoft Teams file`,
              folderPath: metadata.folderPath || metadata._sourceFolderId || 'Unknown',
              owner: metadata.owner || metadata.ownerName || 'Unknown',
              lastModified: file.lastModified || new Date()
            };

            // Process document using appropriate centralized service
            let documentResult;
            // Use unified content extractor for all document types
            const extractionResult = await unifiedContentExtractor.extractContent(
              {
                platform: 'microsoft_teams',
                fileName: file.fileName,
                fileType: file.fileType,
                mimeType: (file.extractedMetadata as any)?.mimeType,
                sourceUrl: file.sourceUrl,
                lastModified: file.lastModified ? new Date(file.lastModified) : new Date(),
                owner: (file.extractedMetadata as any)?.owner || 'Unknown',
                folderPath: (file.extractedMetadata as any)?.folderPath || 'Unknown'
              },
              buffer
            );
            
            if (extractionResult.success && extractionResult.content) {
              documentResult = {
                success: true,
                vectorizationContent: extractionResult.content,
                metadata: extractionResult.metadata
              };
            } else {
              documentResult = {
                success: false,
                error: extractionResult.error || 'Content extraction failed'
              };
            }

            if (documentResult && documentResult.success && documentResult.vectorizationContent) {
              console.log(`[TEAMS] Successfully processed ${fileType} using centralized service: ${file.fileName}`);
              console.log(`[TEAMS] ${fileType} content length: ${documentResult.vectorizationContent.length} characters`);

              return {
                success: true,
                content: documentResult.vectorizationContent,
                metadata: documentResult.metadata,
                contentType: isPdf ? 'pdf' : 'word'
              };
            } else {
              console.log(`[TEAMS] Centralized ${fileType} processing failed for ${file.fileName}: ${documentResult?.error || 'Unknown error'}`);
            }
          }
        } else {
          console.log(`[TEAMS] Missing driveId or fileId for ${fileType} extraction: ${file.fileName}`);
        }
      }

      return { success: false };
    } catch (documentError: any) {
      console.error(`[TEAMS] Error extracting document content for ${file.fileName}:`, documentError.message);
      return { success: false };
    }
  }

  /**
   * Build contextual descriptions for files
   */
  private buildContextualDescriptions(file: any): string {
    let context = "";
    
    // Add file type descriptions and context
    if (file.fileType === 'transcript') {
      context += `File Description: Microsoft Teams meeting transcript or recording file\n`;
    } else if (file.fileType === 'document') {
      context += `File Description: Microsoft Teams shared document\n`;
    } else {
      context += `File Description: Microsoft Teams file - ${file.fileType}\n`;
    }

    return context;
  }

  /**
   * Build temporal context for better searchability
   */
  private buildTemporalContext(file: any): string {
    let context = "";
    
    // Add temporal context
    if (file.lastModified) {
      context += `Last Modified: ${new Date(file.lastModified).toLocaleDateString()}\n`;
    }
    if (file.createdAt) {
      context += `Created: ${new Date(file.createdAt).toLocaleDateString()}\n`;
    }

    return context;
  }

// ... existing code ...
}
