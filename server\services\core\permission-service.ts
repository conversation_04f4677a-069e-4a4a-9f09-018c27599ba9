/**
 * Simple Permission Service for MVP
 * Inspired by LlamaIndex tutorial Part 2 - without enterprise tools
 * Provides basic user-based file filtering and access control
 */

// Storage will be imported dynamically when needed

export interface PermissionRule {
  userId?: string;
  platform?: string;
  fileType?: string;
  accessLevel?: 'public' | 'private' | 'restricted';
  tags?: string[];
}

export interface UserContext {
  userId: string;
  email?: string;
  accessLevel: 'basic' | 'admin';
  allowedPlatforms: string[];
  allowedFileTypes: string[];
}

export class PermissionService {
  private permissionCache: Map<string, string[]> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes cache

  /**
   * Get permitted document IDs for a user
   * Inspired by LlamaIndex FGA patterns but using simple rules
   */
  async getPermittedDocuments(userContext: UserContext): Promise<string[]> {
    const cacheKey = `user_${userContext.userId}_docs`;
    
    // Check cache first (performance optimization from Part 2.5)
    if (this.permissionCache.has(cacheKey)) {
      console.log(`[Permissions] Cache hit for user ${userContext.userId}`);
      return this.permissionCache.get(cacheKey)!;
    }

    console.log(`[Permissions] Computing permissions for user ${userContext.userId}`);
    
    try {
      // Get storage dynamically
      const { storage } = await import('../../storage/index.js');

      // Get all files and filter based on user permissions
      const allFiles = await storage.getFiles();
      const permittedFiles = allFiles.filter(file => 
        this.hasFileAccess(file, userContext)
      );
      
      const permittedIds = permittedFiles.map(file => file.id.toString());
      
      // Cache the result (inspired by Part 2.5 caching patterns)
      this.permissionCache.set(cacheKey, permittedIds);
      setTimeout(() => {
        this.permissionCache.delete(cacheKey);
      }, this.cacheTimeout);
      
      console.log(`[Permissions] User ${userContext.userId} has access to ${permittedIds.length} files`);
      return permittedIds;
      
    } catch (error) {
      console.error('[Permissions] Error getting permitted documents:', error);
      return [];
    }
  }

  /**
   * Check if user has access to a specific file
   * Simple rule-based access control for MVP
   */
  private hasFileAccess(file: any, userContext: UserContext): boolean {
    // Admin users have access to everything
    if (userContext.accessLevel === 'admin') {
      return true;
    }

    // Check platform access
    if (!userContext.allowedPlatforms.includes(file.platform)) {
      return false;
    }

    // Check file type access
    if (file.fileType && !userContext.allowedFileTypes.includes(file.fileType)) {
      return false;
    }

    // Check access level from metadata
    const accessLevel = file.extractedMetadata?.accessLevel || 'public';
    if (accessLevel === 'private' && file.userId !== userContext.userId) {
      return false;
    }

    // Check if file is marked as restricted
    if (accessLevel === 'restricted') {
      return false; // For MVP, restricted files are not accessible
    }

    return true;
  }

  /**
   * Filter RAG chunks based on user permissions
   * Inspired by Part 2 permission-aware RAG
   */
  async filterChunksByPermissions(
    chunks: any[], 
    userContext: UserContext
  ): Promise<any[]> {
    const permittedDocIds = await this.getPermittedDocuments(userContext);
    const permittedDocIdsSet = new Set(permittedDocIds);
    
    const filteredChunks = chunks.filter(chunk => {
      const fileId = chunk.fileId?.toString() || chunk.file_id?.toString();
      return permittedDocIdsSet.has(fileId);
    });
    
    console.log(`[Permissions] Filtered ${chunks.length} chunks to ${filteredChunks.length} permitted chunks`);
    return filteredChunks;
  }

  /**
   * Create default user context for MVP (no authentication)
   */
  createDefaultUserContext(userId: string = 'anonymous'): UserContext {
    return {
      userId,
      accessLevel: 'basic',
      allowedPlatforms: ['google_drive', 'uploaded_files'], // All platforms for MVP
      allowedFileTypes: ['pdf', 'txt', 'md', 'doc', 'docx', 'csv', 'json'], // All types for MVP
    };
  }

  /**
   * Create admin user context for testing
   */
  createAdminUserContext(userId: string = 'admin'): UserContext {
    return {
      userId,
      accessLevel: 'admin',
      allowedPlatforms: ['google_drive', 'uploaded_files', 'slack', 'notion'], // All platforms
      allowedFileTypes: [], // Empty means all types allowed
    };
  }

  /**
   * Update file access level (for future use)
   */
  async updateFileAccessLevel(
    fileId: number,
    accessLevel: 'public' | 'private' | 'restricted'
  ): Promise<boolean> {
    try {
      // Get storage dynamically
      const { storage } = await import('../../storage/index.js');

      const file = await storage.getFile(fileId);
      if (!file) {
        return false;
      }

      const updatedMetadata = {
        ...file.extractedMetadata,
        accessLevel,
        updatedAt: new Date().toISOString(),
      };

      await storage.updateFile(fileId, {
        extractedMetadata: updatedMetadata,
      });

      // Clear cache for all users since permissions changed
      this.clearPermissionCache();
      
      console.log(`[Permissions] Updated file ${fileId} access level to ${accessLevel}`);
      return true;
      
    } catch (error) {
      console.error('[Permissions] Error updating file access level:', error);
      return false;
    }
  }

  /**
   * Clear permission cache (useful when permissions change)
   */
  clearPermissionCache(): void {
    this.permissionCache.clear();
    console.log('[Permissions] Permission cache cleared');
  }

  /**
   * Get permission statistics for debugging
   */
  async getPermissionStats(userContext: UserContext): Promise<any> {
    try {
      // Get storage dynamically
      const { storage } = await import('../../storage/index.js');

      const allFiles = await storage.getFiles();
      const permittedFiles = allFiles.filter(file => 
        this.hasFileAccess(file, userContext)
      );

      const stats = {
        totalFiles: allFiles.length,
        permittedFiles: permittedFiles.length,
        deniedFiles: allFiles.length - permittedFiles.length,
        platformBreakdown: this.getFilesByPlatform(permittedFiles),
        typeBreakdown: this.getFilesByType(permittedFiles),
        accessLevelBreakdown: this.getFilesByAccessLevel(permittedFiles),
      };

      return stats;
    } catch (error) {
      console.error('[Permissions] Error getting permission stats:', error);
      return null;
    }
  }

  /**
   * Helper: Group files by platform
   */
  private getFilesByPlatform(files: any[]): Record<string, number> {
    return files.reduce((acc, file) => {
      acc[file.platform] = (acc[file.platform] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Helper: Group files by file type
   */
  private getFilesByType(files: any[]): Record<string, number> {
    return files.reduce((acc, file) => {
      acc[file.fileType] = (acc[file.fileType] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Helper: Group files by access level
   */
  private getFilesByAccessLevel(files: any[]): Record<string, number> {
    return files.reduce((acc, file) => {
      const accessLevel = file.extractedMetadata?.accessLevel || 'public';
      acc[accessLevel] = (acc[accessLevel] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Check if user can perform write actions
   */
  canPerformWriteActions(userContext: UserContext): boolean {
    // For MVP, all users can perform write actions
    // In production, this would check specific permissions
    return true;
  }

  /**
   * Check if user can access specific platform
   */
  canAccessPlatform(userContext: UserContext, platform: string): boolean {
    return userContext.allowedPlatforms.includes(platform);
  }

  /**
   * Get allowed sources for user (for RAG filtering)
   */
  getAllowedSources(userContext: UserContext): string[] {
    // Return integration IDs that user has access to
    // For MVP, this maps platforms to integration IDs
    const platformToIntegrationId: Record<string, string> = {
      'google_drive': '36', // Your Google Drive integration ID
      'uploaded_files': 'uploaded-files',
    };

    return userContext.allowedPlatforms
      .map(platform => platformToIntegrationId[platform])
      .filter(Boolean);
  }
}

// Export singleton instance
export const permissionService = new PermissionService();
