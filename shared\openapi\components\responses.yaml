# Standard Response Patterns
Success:
  description: Successful operation
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ApiResponse'

Created:
  description: Resource created successfully
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ApiResponse'

NoContent:
  description: Successful operation with no content
  
BadRequest:
  description: Invalid request parameters
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'
      examples:
        invalid_params:
          summary: Invalid parameters
          value:
            success: false
            message: "Invalid request parameters"
            error: "Validation failed"

Unauthorized:
  description: Authentication credentials were missing or incorrect
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'
      examples:
        missing_auth:
          summary: Missing authentication
          value:
            success: false
            message: "Authentication required"
            error: "Missing or invalid API key"

Forbidden:
  description: Access to the resource is forbidden
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'

NotFound:
  description: The requested resource was not found
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'
      examples:
        resource_not_found:
          summary: Resource not found
          value:
            success: false
            message: "Resource not found"
            error: "The requested resource does not exist"

Conflict:
  description: Request conflicts with current state of resource
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'

UnprocessableEntity:
  description: Request was well-formed but was unable to be followed due to semantic errors
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'

InternalServerError:
  description: Internal server error
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'
      examples:
        server_error:
          summary: Internal server error
          value:
            success: false
            message: "Internal server error"
            error: "An unexpected error occurred"

ServiceUnavailable:
  description: Service temporarily unavailable
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/ErrorResponse'

# Specific Response Types
HealthResponse:
  description: Health status of the application
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/HealthStatus'

IntegrationResponse:
  description: Integration details
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          integration:
            $ref: '../components/schemas.yaml#/Integration'

IntegrationsListResponse:
  description: List of integrations
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          integrations:
            type: array
            items:
              $ref: '../components/schemas.yaml#/Integration'

FilesListResponse:
  description: List of files
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          files:
            type: array
            items:
              $ref: '../components/schemas.yaml#/File'
          total:
            type: integer
          limit:
            type: integer
          offset:
            type: integer

FileResponse:
  description: File details
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          file:
            $ref: '../components/schemas.yaml#/File'

ChatSessionResponse:
  description: Chat session details
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          session:
            $ref: '../components/schemas.yaml#/ChatSession'

ChatSessionsListResponse:
  description: List of chat sessions
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          sessions:
            type: array
            items:
              $ref: '../components/schemas.yaml#/ChatSession'

ChatMessagesListResponse:
  description: List of chat messages
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          messages:
            type: array
            items:
              $ref: '../components/schemas.yaml#/ChatMessage'

SyncLogsListResponse:
  description: List of sync logs
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          logs:
            type: array
            items:
              $ref: '../components/schemas.yaml#/SyncLog'

SyncItemsListResponse:
  description: List of sync items
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          items:
            type: array
            items:
              $ref: '../components/schemas.yaml#/SyncItem'

SearchResponse:
  description: Search results
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/SearchResult'

RagSearchResponse:
  description: RAG search results
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/RagSearchResult'

OAuthUrlResponse:
  description: OAuth authorization URL
  content:
    application/json:
      schema:
        $ref: '../components/schemas.yaml#/OAuthUrl'

UploadStatsResponse:
  description: Upload statistics
  content:
    application/json:
      schema:
        type: object
        properties:
          success:
            type: boolean
          stats:
            $ref: '../components/schemas.yaml#/UploadStats'

FileDownloadResponse:
  description: File download
  content:
    application/octet-stream:
      schema:
        type: string
        format: binary

RedirectResponse:
  description: Redirect to external URL
  headers:
    Location:
      description: The URL to redirect to
      schema:
        type: string
        format: uri 