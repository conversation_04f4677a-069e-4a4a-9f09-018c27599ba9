import type { Express } from "express";
import { chat<PERSON><PERSON>roller } from "../controllers/chat";
import { fileUploadController } from "../controllers/file-upload";
import { fileUploadService } from "../services/file-upload";
import { embeddingService } from "../services/ai/embedding-service";
import { googleService } from "../services/platform-integrations/google";

/**
 * Register file management and upload routes
 */
export function registerFileRoutes(app: Express) {
  // File processing for embeddings
  app.post('/api/files/:fileId/embeddings', (req, res) => chatController.processFileForEmbeddings(req, res));

  // Files Routes (NEW for reference-based model)
  app.get('/api/files', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");

      const platform = req.query.platform as string;
      const userId = req.query.userId as string;
      const folderId = req.query.folderId as string;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
      const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;

      const files = await storage.getFiles(platform, userId, limit, offset, folderId);
      
      // Enhance files with properly resolved metadata
      const enhancedFiles = files.map(file => {
        const metadata = file.extractedMetadata as any || {};
        return {
          ...file,
          // Use resolved names from metadata instead of IDs
          folderName: metadata.folderName || file.parentFolder || 'Unknown',
          ownerName: metadata.ownerName || metadata.owner || 'Unknown',
          ownerEmail: metadata.ownerEmail || null,
          // Format attendees if available
          attendees: metadata.meetingAttendees || metadata.calendarAttendees || 
                     metadata.aiExtractedAttendees || null,
          // Meeting context if available
          meetingSubject: metadata.meetingSubject || metadata.calendarSubject || null,
          sourceContext: metadata._sourceContext || null
        };
      });
      
      res.json({
        files: enhancedFiles,
        count: enhancedFiles.length,
        limit,
        offset,
        folderId,
      });
    } catch (error: any) {
      console.error('Error fetching files:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.get('/api/files/search', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");

      const query = req.query.q as string;
      const platform = req.query.platform as string;
      const fileType = req.query.fileType as string;
      const folderId = req.query.folderId as string;

      if (!query) {
        return res.status(400).json({ message: 'Search query (q) is required' });
      }

      const files = await storage.searchFiles(query, platform, fileType, folderId);

      res.json({
        files,
        count: files.length,
        query,
        platform,
        fileType,
        folderId
      });
    } catch (error: any) {
      console.error('Error searching files:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.get('/api/files/:id', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");

      const file = await storage.getFile(parseInt(req.params.id));
      if (!file) {
        return res.status(404).json({ message: 'File not found' });
      }
      res.json({ file });
    } catch (error: any) {
      console.error('Error fetching file:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.delete('/api/files/:id', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");

      const success = await storage.deleteFile(parseInt(req.params.id));
      if (!success) {
        return res.status(404).json({ message: 'File not found' });
      }
      res.json({ message: 'File deleted successfully' });
    } catch (error: any) {
      console.error('Error deleting file:', error);
      res.status(500).json({ message: error.message });
    }
  });

  // File Upload Routes
  app.post('/api/files/upload', fileUploadService.getUploadMiddleware().array('files', 10), (req, res) => fileUploadController.uploadFiles(req, res));
  app.get('/api/files/uploaded', (req, res) => fileUploadController.getUploadedFiles(req, res));
  app.delete('/api/files/uploaded/:id', (req, res) => fileUploadController.deleteUploadedFile(req, res));
  app.get('/api/files/uploaded/:id/download', (req, res) => fileUploadController.downloadFile(req, res));
  app.get('/api/files/upload-stats', (req, res) => fileUploadController.getUploadStats(req, res));

  // Reprocess Google Drive files endpoint
  app.post('/api/files/reprocess-google-drive', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");

      console.log('🔄 Starting Google Drive file reprocessing...');

      // Get all files
      const filesResult = await storage.getFiles();
      const files = filesResult;

      // Filter for Google Drive files that need reprocessing
      const googleDriveFiles = files.filter((file: any) =>
        file.platform === 'google_drive' && (
          file.fileType === 'pdf' ||
          file.fileType === 'doc' ||
          file.fileName?.toLowerCase().endsWith('.pdf') ||
          file.fileName?.toLowerCase().endsWith('.docx') ||
          file.mimeType?.includes('pdf') ||
          file.mimeType?.includes('wordprocessingml')
        )
      );

      console.log(`Found ${googleDriveFiles.length} Google Drive files to reprocess`);

      let processed = 0;
      const results = [];

      for (const file of googleDriveFiles) {
        try {
          console.log(`📄 Reprocessing: ${file.fileName}`);

          // Check if file already has proper content (not just metadata)
          const existingChunks = await storage.getFileChunks(file.id);

          // Check if existing chunks contain actual content or just metadata
          const hasRealContent = existingChunks.some(chunk =>
            chunk.content &&
            chunk.content.length > 200 &&
            !chunk.content.includes('text extraction failed') &&
            !chunk.content.includes('metadata only')
          );

          if (hasRealContent) {
            console.log(`  ✅ File already has proper content, skipping`);
            results.push({
              fileName: file.fileName,
              status: 'skipped',
              reason: 'already_has_content',
              chunks: existingChunks.length
            });
            continue;
          }

          // Get the integration for this file
          const integrations = await storage.getIntegrations();
          const integration = integrations.find(i => i.type === 'google_drive' || i.type === 'google-drive');

          if (!integration) {
            console.log(`  ❌ No Google Drive integration found`);
            results.push({
              fileName: file.fileName,
              status: 'error',
              reason: 'no_integration_found'
            });
            continue;
          }

          // Extract content using the Google service
          if (!integration.credentials) {
            console.log(`  ❌ No credentials found for integration`);
            results.push({
              fileName: file.fileName,
              status: 'error',
              reason: 'no_credentials'
            });
            continue;
          }
          const auth = await googleService.getAuthorizedClient(integration.credentials);
          const driveFile = {
            id: file.externalId,
            name: file.fileName,
            mimeType: file.mimeType,
            size: file.fileSize?.toString(),
            modifiedTime: file.lastModified?.toISOString()
          };

          console.log(`  🔍 Extracting content for ${file.fileName}...`);
          const extractedContent = await googleService.extractFileContent(auth, driveFile);

          // Delete existing chunks if they exist
          if (existingChunks.length > 0) {
            console.log(`  🗑️ Removing ${existingChunks.length} old chunks...`);
            for (const chunk of existingChunks) {
              await storage.deleteChunk(chunk.id);
            }
          }

          // Process the new content for embeddings
          console.log(`  ⚡ Processing new content (${extractedContent.length} chars)...`);
          await embeddingService.processFileForEmbeddings(file.id, extractedContent);

          processed++;
          results.push({
            fileName: file.fileName,
            status: 'reprocessed',
            reason: 'content_extracted',
            contentLength: extractedContent.length
          });

          console.log(`✅ Reprocessed: ${file.fileName} (${extractedContent.length} chars)`);

        } catch (error: any) {
          console.error(`❌ Error reprocessing ${file.fileName}:`, error);
          results.push({
            fileName: file.fileName,
            status: 'error',
            reason: error.message
          });
        }
      }

      return res.json({
        message: 'Google Drive file reprocessing completed',
        summary: {
          totalFiles: googleDriveFiles.length,
          reprocessed: processed,
          skipped: googleDriveFiles.length - processed
        },
        results
      });

    } catch (error: any) {
      console.error('Error in Google Drive file reprocessing:', error);
      return res.status(500).json({
        message: 'Failed to reprocess Google Drive files',
        error: error.message
      });
    }
  });
} 