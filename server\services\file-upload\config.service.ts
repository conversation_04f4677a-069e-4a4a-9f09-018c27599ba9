import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { BaseService } from "../base/service.interface";

/**
 * File Upload Configuration Service - handles multer setup and file validation
 */
export class FileUploadConfigService extends BaseService {
  private uploadDir!: string; // Will be initialized in onInitialize
  private upload: multer.Multer | null = null;

  constructor() {
    super('FileUploadConfig', '1.0.0', 'File upload configuration and middleware setup');
  }

  protected async onInitialize(): Promise<void> {
    this.uploadDir = path.join(process.cwd(), 'uploads');
    this.ensureUploadDirectory();
    this.setupMulter();
    this.log('info', 'File Upload Config service initialized');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      uploadDirExists: fs.existsSync(this.uploadDir),
      uploadDirPath: this.uploadDir,
      multerConfigured: !!this.upload,
      maxFileSize: this.getMaxFileSize(),
      maxFiles: this.getMaxFiles(),
      supportedTypes: this.getSupportedFileTypes().length,
    };
  }

  /**
   * Ensure upload directory exists
   */
  private ensureUploadDirectory(): void {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
      this.log('info', `Created upload directory: ${this.uploadDir}`);
    }
  }

  /**
   * Setup multer configuration
   */
  private setupMulter(): void {
    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        cb(null, this.uploadDir);
      },
      filename: (req, file, cb) => {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        const name = path.basename(file.originalname, ext);
        cb(null, `${name}-${uniqueSuffix}${ext}`);
      }
    });

    this.upload = multer({
      storage,
      fileFilter: this.createFileFilter(),
      limits: {
        fileSize: this.getMaxFileSize(),
        files: this.getMaxFiles()
      }
    });

    this.log('info', 'Multer configuration setup complete');
  }

  /**
   * Create file filter for multer
   */
  private createFileFilter(): multer.Options['fileFilter'] {
    return (req, file, cb) => {
      const allowedTypes = this.getSupportedFileTypes();

      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error(`File type ${file.mimetype} not supported. Please upload business documents, images, audio, or video files.`));
      }
    };
  }

  /**
   * Get supported file types
   */
  getSupportedFileTypes(): string[] {
    return [
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv',
      'application/rtf',
      
      // Spreadsheets
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
      
      // Presentations
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp',
      'image/svg+xml',
      
      // Audio
      'audio/mpeg',
      'audio/wav',
      'audio/mp4',
      'audio/aac',
      'audio/ogg',
      'audio/webm',
      
      // Video
      'video/mp4',
      'video/avi',
      'video/mov',
      'video/wmv',
      'video/webm',
      'video/mkv',
      
      // Archives
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
      
      // Other
      'application/json',
      'application/xml',
      'text/xml'
    ];
  }

  /**
   * Get file type category from mime type
   */
  getFileTypeCategory(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('text/')) return 'text';
    
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'document';
    if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'spreadsheet';
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'presentation';
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) return 'archive';
    
    return 'other';
  }

  /**
   * Get file extension from filename
   */
  getFileExtension(filename: string): string {
    return path.extname(filename).toLowerCase().substring(1);
  }

  /**
   * Validate file before upload
   */
  validateFile(file: Express.Multer.File): {
    isValid: boolean;
    error?: string;
    warnings?: string[];
  } {
    const warnings: string[] = [];

    // Check file size
    if (file.size > this.getMaxFileSize()) {
      return {
        isValid: false,
        error: `File size ${Math.round(file.size / 1024 / 1024)}MB exceeds maximum allowed size of ${Math.round(this.getMaxFileSize() / 1024 / 1024)}MB`
      };
    }

    // Check file type
    if (!this.getSupportedFileTypes().includes(file.mimetype)) {
      return {
        isValid: false,
        error: `File type ${file.mimetype} is not supported`
      };
    }

    // Check for potentially large files
    if (file.size > 10 * 1024 * 1024) { // 10MB
      warnings.push('Large file detected - processing may take longer');
    }

    // Check for binary files that may not extract well
    const binaryTypes = ['image/', 'audio/', 'video/', 'application/zip'];
    if (binaryTypes.some(type => file.mimetype.startsWith(type))) {
      warnings.push('Binary file - text extraction may be limited to metadata');
    }

    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Get multer upload middleware
   */
  getUploadMiddleware(): multer.Multer {
    if (!this.upload) {
      throw new Error('Multer not configured. Call initialize() first.');
    }
    return this.upload;
  }

  /**
   * Get upload directory path
   */
  getUploadDirectory(): string {
    return this.uploadDir;
  }

  /**
   * Get maximum file size
   */
  getMaxFileSize(): number {
    return parseInt(process.env.MAX_FILE_SIZE || '52428800'); // 50MB default
  }

  /**
   * Get maximum number of files
   */
  getMaxFiles(): number {
    return parseInt(process.env.MAX_FILES || '10');
  }

  /**
   * Generate unique filename
   */
  generateUniqueFilename(originalName: string): string {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    return `${name}-${uniqueSuffix}${ext}`;
  }

  /**
   * Get file info from uploaded file
   */
  getFileInfo(file: Express.Multer.File): {
    originalName: string;
    filename: string;
    path: string;
    size: number;
    mimeType: string;
    extension: string;
    category: string;
  } {
    return {
      originalName: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size,
      mimeType: file.mimetype,
      extension: this.getFileExtension(file.originalname),
      category: this.getFileTypeCategory(file.mimetype),
    };
  }

  /**
   * Clean up old uploaded files
   */
  async cleanupOldFiles(daysOld: number = 30): Promise<{
    deletedCount: number;
    errors: string[];
  }> {
    this.ensureInitialized();
    this.validateNumber(daysOld, 'days old', 1);

    const errors: string[] = [];
    let deletedCount = 0;

    try {
      const files = fs.readdirSync(this.uploadDir);
      const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);

      for (const file of files) {
        try {
          const filePath = path.join(this.uploadDir, file);
          const stats = fs.statSync(filePath);

          if (stats.mtime.getTime() < cutoffTime) {
            fs.unlinkSync(filePath);
            deletedCount++;
            this.log('info', `Deleted old file: ${file}`);
          }
        } catch (error: any) {
          errors.push(`Error processing file ${file}: ${error.message}`);
        }
      }

      this.log('info', `Cleanup completed: ${deletedCount} files deleted, ${errors.length} errors`);
      return { deletedCount, errors };

    } catch (error: any) {
      this.log('error', 'Error during file cleanup', error);
      return { deletedCount: 0, errors: [error.message] };
    }
  }

  /**
   * Get upload statistics
   */
  getUploadStats(): {
    uploadDir: string;
    totalFiles: number;
    totalSize: number;
    oldestFile?: Date;
    newestFile?: Date;
  } {
    try {
      const files = fs.readdirSync(this.uploadDir);
      let totalSize = 0;
      let oldestFile: Date | undefined;
      let newestFile: Date | undefined;

      for (const file of files) {
        try {
          const filePath = path.join(this.uploadDir, file);
          const stats = fs.statSync(filePath);
          
          totalSize += stats.size;
          
          if (!oldestFile || stats.mtime < oldestFile) {
            oldestFile = stats.mtime;
          }
          
          if (!newestFile || stats.mtime > newestFile) {
            newestFile = stats.mtime;
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }

      return {
        uploadDir: this.uploadDir,
        totalFiles: files.length,
        totalSize,
        oldestFile,
        newestFile,
      };
    } catch (error: any) {
      this.log('error', 'Error getting upload stats', error);
      return {
        uploadDir: this.uploadDir,
        totalFiles: 0,
        totalSize: 0,
      };
    }
  }
}
