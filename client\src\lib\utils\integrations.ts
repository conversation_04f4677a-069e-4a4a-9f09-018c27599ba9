/**
 * @deprecated Use IntegrationIcon component instead
 * Get the official PNG logo URL for an integration type
 * Uses official logos from Wikimedia Commons with transparent backgrounds
 * Handles various formats: 'google_drive', 'google-drive', 'google_meet', 'google-meet' (for backward compatibility), etc.
 * @param type - The integration type in any format
 * @returns URL string for the official PNG logo
 */
export function getIntegrationLogo(type: string): string {
  if (!type || typeof type !== 'string') {
    return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1'/%3E%3C/svg%3E";
  }

  // Normalize the type: lowercase, replace spaces and dashes with underscores
  const normalizedType = type.toLowerCase().replace(/[\s-]/g, '_');
  
  switch (normalizedType) {
    case 'google_drive':
    case 'google-drive':
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24' fill='%234285f4'%3E%3Cpath d='M12 0L6 10h12L12 0zM6 10L0 20h6l6-10H6zm12 0l-6 10h12l6-10h-12z'/%3E%3C/svg%3E";

    case 'gmail':
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24'%3E%3Cpath fill='%23ea4335' d='M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h1.628L12 11.182l8.736-7.361h1.628c.904 0 1.636.732 1.636 1.636z'/%3E%3Cpath fill='%2334a853' d='M0 6.545v1.429l6.545 4.91v-1.428z'/%3E%3Cpath fill='%23fbbc04' d='M24 6.545v1.429l-6.545 4.91v-1.428z'/%3E%3Cpath fill='%23ea4335' d='M0 5.457A1.636 1.636 0 0 1 1.636 3.82h1.629L12 11.182 20.735 3.82h1.629A1.636 1.636 0 0 1 24 5.457v1.088l-12 9.103L0 6.545z'/%3E%3C/svg%3E";

    case 'google_calendar':
    case 'google-calendar':
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24'%3E%3Cpath fill='%234285f4' d='M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z'/%3E%3C/svg%3E";

    case 'microsoft_teams':
    case 'microsoftteams':
    case 'teams':
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24'%3E%3Cpath fill='%235059c9' d='M21.53 8.89c-.67-.22-1.39-.34-2.15-.34-2.22 0-4.22.94-5.65 2.44V9.33A3.33 3.33 0 0010.4 6H3.33A3.33 3.33 0 000 9.33v5.34A3.33 3.33 0 003.33 18h7.07a3.33 3.33 0 003.33-3.33v-1.66c1.43 1.5 3.43 2.44 5.65 2.44.76 0 1.48-.12 2.15-.34A2.89 2.89 0 0024 12.22V11.78a2.89 2.89 0 00-2.47-2.89z'/%3E%3C/svg%3E";

    case 'slack':
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24'%3E%3Cpath fill='%23e01e5a' d='M5.042 15.165a2.528 2.528 0 01-2.52 2.523A2.528 2.528 0 010 15.165a2.527 2.527 0 012.522-2.52h2.52v2.52z'/%3E%3Cpath fill='%2336c5f0' d='M6.313 15.165a2.527 2.527 0 012.521-2.52 2.527 2.527 0 012.521 2.52v6.313A2.528 2.528 0 018.834 24a2.528 2.528 0 01-2.521-2.522v-6.313z'/%3E%3Cpath fill='%232eb67d' d='M8.834 5.042a2.528 2.528 0 01-2.521-2.52A2.528 2.528 0 018.834 0a2.528 2.528 0 012.521 2.522v2.52H8.834z'/%3E%3Cpath fill='%23ecb22e' d='M8.834 6.313a2.528 2.528 0 012.521 2.521 2.528 2.528 0 01-2.521 2.521H2.522A2.528 2.528 0 010 8.834a2.528 2.528 0 012.522-2.521h6.312z'/%3E%3C/svg%3E";
      
    case 'uploaded_files':
    case 'uploaded-files':
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z'/%3E%3Cpolyline points='14,2 14,8 20,8'/%3E%3Cline x1='16' y1='13' x2='8' y2='13'/%3E%3Cline x1='16' y1='17' x2='8' y2='17'/%3E%3Cpolyline points='10,9 9,9 8,9'/%3E%3C/svg%3E";
      
    default:
      // Return a generic integration icon for unknown types
      return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1'/%3E%3C/svg%3E";
  }
}

export function getIntegrationName(type: string) {
  switch (type) {
    case 'google_drive':
    case 'google-drive':
      return 'Google Drive';

    case 'gmail':
      return 'Gmail';

    case 'google_calendar':
    case 'google-calendar':
      return 'Google Calendar';

    case 'microsoft_teams':
      return 'Microsoft Teams';

    case 'slack':
      return 'Slack';
      
    case 'uploaded_files':
    case 'uploaded-files':
      return 'Uploaded Files';
      
    default:
      return type.replace('_', ' ');
  }
}

export function getSourceName(type: string, sourceConfig: any) {
  switch (type) {
    case 'google_drive':
    case 'google-drive':
      return sourceConfig?.driveName || 'Google Drive Files';

    case 'gmail':
      return 'Gmail Messages';

    case 'google_calendar':
    case 'google-calendar':
      return 'Google Calendar Events';

    case 'microsoft_teams':
      return 'Microsoft Teams Chats';

    case 'slack':
      return 'Slack Messages';
    default:
      return 'Files';
  }
}

export function getDestinationName(destinationConfig: any) {
  return destinationConfig?.notionDatabaseName || 'GPT Unify Files DB';
}

export function getStatusBadgeColor(status: string) {
  switch (status) {
    case 'connected':
    case 'configured':
      return 'bg-green-500';
    case 'disconnected':
      return 'bg-gray-300';
    case 'error':
      return 'bg-red-500';
    case 'running':
    case 'syncing':
      return 'bg-blue-500';
    default:
      return 'bg-gray-300';
  }
}

export function getStatusText(status: string) {
  switch (status) {
    case 'connected':
      return 'Connected';
    case 'configured':
      return 'Configured';
    case 'disconnected':
      return 'Not Connected';
    case 'error':
      return 'Error';
    case 'running':
    case 'syncing':
      return 'Syncing';
    default:
      return 'Unknown';
  }
}
