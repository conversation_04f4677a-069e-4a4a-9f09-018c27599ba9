import { drizzle } from "drizzle-orm/postgres-js";
import { drizzle as drizzleNode } from "drizzle-orm/node-postgres";
import postgres from "postgres";
import { Pool } from "pg";
import { environmentConfig, getDatabaseUrl } from "./environment.config";

/**
 * Database configuration and connection management
 */

export interface DatabaseConfig {
  url: string | null;
  maxConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  ssl: boolean;
}

// Database configuration
export const databaseConfig: DatabaseConfig = {
  url: getDatabaseUrl(),
  maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
  connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
  idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '10000'),
  ssl: process.env.DB_SSL === 'true' || environmentConfig.nodeEnv === 'production',
};

// Connection instances
let postgresConnection: postgres.Sql | null = null;
let pgPool: Pool | null = null;
let drizzleDb: any = null;

/**
 * Initialize database connection
 */
export const initializeDatabase = async (): Promise<any> => {
  if (drizzleDb) {
    return drizzleDb;
  }

  const dbUrl = databaseConfig.url;
  
  if (!dbUrl) {
    console.warn('⚠️  No database URL configured - using in-memory storage');
    return null;
  }

  try {
    console.log('🔌 Connecting to database...');
    
    // Use postgres.js for better performance
    postgresConnection = postgres(dbUrl, {
      max: databaseConfig.maxConnections,
      idle_timeout: databaseConfig.idleTimeout / 1000,
      connect_timeout: 5, // Shorter timeout for faster startup
      ssl: databaseConfig.ssl ? 'require' : false,
    });

    // Initialize Drizzle with postgres.js
    drizzleDb = drizzle(postgresConnection);
    
    // Test the connection
    await postgresConnection`SELECT 1`;
    console.log('✅ Database connected successfully');
    
    return drizzleDb;
    
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    
    // Fallback to node-postgres if postgres.js fails
    try {
      console.log('🔄 Trying fallback connection with node-postgres...');
      
      pgPool = new Pool({
        connectionString: dbUrl,
        max: databaseConfig.maxConnections,
        idleTimeoutMillis: databaseConfig.idleTimeout,
        connectionTimeoutMillis: databaseConfig.connectionTimeout,
        ssl: databaseConfig.ssl ? { rejectUnauthorized: false } : false,
      });
      
      drizzleDb = drizzleNode(pgPool);
      
      // Test the connection
      const client = await pgPool.connect();
      await client.query('SELECT 1');
      client.release();
      
      console.log('✅ Database connected successfully (fallback)');
      return drizzleDb;
      
    } catch (fallbackError) {
      console.error('❌ Fallback database connection also failed:', fallbackError);
      throw new Error('Unable to connect to database');
    }
  }
};

/**
 * Get database instance
 */
export const getDatabase = (): any => {
  if (!drizzleDb) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return drizzleDb;
};

/**
 * Close database connections
 */
export const closeDatabase = async (): Promise<void> => {
  try {
    if (postgresConnection) {
      await postgresConnection.end();
      postgresConnection = null;
      console.log('✅ Postgres.js connection closed');
    }
    
    if (pgPool) {
      await pgPool.end();
      pgPool = null;
      console.log('✅ Node-postgres pool closed');
    }
    
    drizzleDb = null;
    
  } catch (error) {
    console.error('❌ Error closing database connections:', error);
  }
};

/**
 * Check database health
 */
export const checkDatabaseHealth = async (): Promise<{ healthy: boolean; error?: string }> => {
  try {
    console.log('Checking database health...');

    // Check if database URL is configured
    const dbUrl = databaseConfig.url;
    if (!dbUrl) {
      console.log('Database URL not configured - using in-memory storage');
      return { healthy: true }; // In-memory storage is considered healthy
    }

    // Try to initialize database if not already done
    if (!drizzleDb) {
      console.log('Database not initialized, attempting to initialize...');
      try {
        await initializeDatabase();
      } catch (initError) {
        console.error('Failed to initialize database during health check:', initError);
        return {
          healthy: false,
          error: `Database initialization failed: ${initError instanceof Error ? initError.message : 'Unknown error'}`
        };
      }
    }

    // If still no database connection, return error
    if (!drizzleDb) {
      return { healthy: false, error: 'Database not initialized' };
    }

    // Test the connection
    if (postgresConnection) {
      console.log('Testing postgres.js connection...');
      await postgresConnection`SELECT 1`;
      console.log('Database health check passed (postgres.js)');
    } else if (pgPool) {
      console.log('Testing node-postgres connection...');
      const client = await pgPool.connect();
      await client.query('SELECT 1');
      client.release();
      console.log('Database health check passed (node-postgres)');
    } else {
      return { healthy: false, error: 'No active database connection' };
    }

    return { healthy: true };

  } catch (error) {
    console.error('Database health check failed:', error);
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown database error'
    };
  }
};

/**
 * Get connection info
 */
export const getDatabaseInfo = () => {
  return {
    url: databaseConfig.url ? databaseConfig.url.replace(/:[^:@]*@/, ':***@') : null,
    maxConnections: databaseConfig.maxConnections,
    connectionType: postgresConnection ? 'postgres.js' : pgPool ? 'node-postgres' : 'none',
    isConnected: !!drizzleDb,
  };
};

// Export the database instance getter as default
export default getDatabase;
