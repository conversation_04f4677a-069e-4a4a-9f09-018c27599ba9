import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getSyncLogs, getSyncLog, getSyncItems } from "@/lib/api";
import { formatDate, formatRelativeTime } from "@/lib/utils/date";
import { getIntegrationName } from "@/lib/utils/integrations";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

export default function Logs() {
  const [selectedLogId, setSelectedLogId] = useState<number | null>(null);
  
  const { data: logsData, isLoading: isLoadingLogs } = useQuery({
    queryKey: ['/api/sync-logs'],
    queryFn: () => getSyncLogs({ limit: 50 }),
  });
  
  const { data: logDetailData, isLoading: isLoadingDetail } = useQuery({
    queryKey: [`/api/sync-logs/${selectedLogId}`],
    queryFn: () => getSyncLog(selectedLogId!),
    enabled: selectedLogId !== null,
  });
  
  const logs = logsData?.logs || [];
  const selectedLog = logDetailData?.log;
  const logItems = logDetailData?.items || [];
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-500">Success</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-500">Partial</Badge>;
      case 'failed':
        return <Badge className="bg-red-500">Failed</Badge>;
      case 'running':
        return <Badge className="bg-blue-500">Running</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };
  
  return (
    <AppLayout title="Sync Logs">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Logs List */}
        <div className="lg:w-2/5">
          <Card>
            <CardHeader>
              <CardTitle>Sync History</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {isLoadingLogs ? (
                <div className="text-center py-8">
                  <svg className="animate-spin h-8 w-8 text-primary mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p className="mt-2 text-gray-600">Loading logs...</p>
                </div>
              ) : logs.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No sync logs found</p>
                </div>
              ) : (
                <div className="divide-y max-h-[600px] overflow-y-auto">
                  {logs.map((log: any) => (
                    <div 
                      key={log.id}
                      className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedLogId === log.id ? 'bg-gray-50' : ''}`}
                      onClick={() => setSelectedLogId(log.id)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            <span className={`h-2 w-2 rounded-full mr-2 ${
                              log.status === 'success' ? 'bg-green-500' : 
                              log.status === 'partial' ? 'bg-yellow-500' :
                              log.status === 'running' ? 'bg-blue-500' : 'bg-red-500'
                            }`}></span>
                            <p className="font-medium">{`Integration #${log.integrationId}`}</p>
                          </div>
                          <p className="text-sm text-gray-500">
                            {log.itemsSuccess} of {log.itemsProcessed} items processed
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{formatDate(log.startTime)}</p>
                          <p className="text-xs text-gray-500">{formatRelativeTime(log.startTime)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Log Details */}
        <div className="lg:w-3/5">
          <Card>
            <CardHeader>
              <CardTitle>Sync Details</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedLogId === null ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Select a sync log to view details</p>
                </div>
              ) : isLoadingDetail ? (
                <div className="text-center py-8">
                  <svg className="animate-spin h-8 w-8 text-primary mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p className="mt-2 text-gray-600">Loading details...</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Log Summary */}
                  <div className="grid grid-cols-2 gap-4 border-b pb-4">
                    <div>
                      <p className="text-sm text-gray-500">Status</p>
                      <div className="mt-1">{getStatusBadge(selectedLog.status)}</div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Integration ID</p>
                      <p className="font-medium">{selectedLog.integrationId}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Start Time</p>
                      <p className="font-medium">{formatDate(selectedLog.startTime)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">End Time</p>
                      <p className="font-medium">{selectedLog.endTime ? formatDate(selectedLog.endTime) : 'Running'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Items Processed</p>
                      <p className="font-medium">{selectedLog.itemsProcessed}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Success Rate</p>
                      <p className="font-medium">{
                        selectedLog.itemsProcessed > 0
                          ? `${Math.round((selectedLog.itemsSuccess / selectedLog.itemsProcessed) * 100)}%`
                          : '0%'
                      }</p>
                    </div>
                  </div>
                  
                  {/* Error Message */}
                  {selectedLog.error && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                      <p className="font-medium">Error</p>
                      <p className="text-sm">{selectedLog.error}</p>
                    </div>
                  )}
                  
                  {/* Items Table */}
                  <div>
                    <h3 className="text-base font-medium mb-3">Processed Items</h3>
                    {logItems.length === 0 ? (
                      <div className="text-center py-4 bg-gray-50 rounded-md">
                        <p className="text-gray-500">No items found</p>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Title</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Processed At</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {logItems.map((item: any) => (
                            <TableRow key={item.id}>
                              <TableCell className="font-medium">{item.title}</TableCell>
                              <TableCell>{item.type === 'transcript' ? 'Meeting Recording' : item.type === 'document' ? 'Document' : 'Chat Log'}</TableCell>
                              <TableCell>{getStatusBadge(item.status)}</TableCell>
                              <TableCell>{formatDate(item.processedAt)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
