import React, { useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  Settings,
  X,
  Wifi,
  Clock,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface MCPError {
  id: string;
  type: 'connection' | 'authentication' | 'rate_limit' | 'tool_failure' | 'server_error';
  serverName: string;
  toolName?: string;
  message: string;
  timestamp: Date;
  retryable: boolean;
  actionUrl?: string;
}

interface MCPErrorNotificationProps {
  error: MCPError;
  onRetry?: () => void;
  onDismiss?: () => void;
  onOpenSettings?: () => void;
  className?: string;
}

export function MCPErrorNotification({ 
  error, 
  onRetry, 
  onDismiss, 
  onOpenSettings,
  className 
}: MCPErrorNotificationProps) {
  const [isRetrying, setIsRetrying] = useState(false);

  const getErrorIcon = () => {
    switch (error.type) {
      case 'connection':
        return <Wifi className="w-4 h-4" />;
      case 'authentication':
        return <Shield className="w-4 h-4" />;
      case 'rate_limit':
        return <Clock className="w-4 h-4" />;
      case 'tool_failure':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getErrorColor = () => {
    switch (error.type) {
      case 'connection':
        return 'border-orange-200 bg-orange-50 text-orange-800';
      case 'authentication':
        return 'border-red-200 bg-red-50 text-red-800';
      case 'rate_limit':
        return 'border-yellow-200 bg-yellow-50 text-yellow-800';
      case 'tool_failure':
        return 'border-red-200 bg-red-50 text-red-800';
      default:
        return 'border-gray-200 bg-gray-50 text-gray-800';
    }
  };

  const getErrorTitle = () => {
    switch (error.type) {
      case 'connection':
        return 'Connection Error';
      case 'authentication':
        return 'Authentication Required';
      case 'rate_limit':
        return 'Rate Limit Exceeded';
      case 'tool_failure':
        return 'Tool Execution Failed';
      default:
        return 'MCP Error';
    }
  };

  const getServerIcon = (serverName: string) => {
    switch (serverName.toLowerCase()) {
      case 'google drive':
        return '📁';
      case 'microsoft teams':
        return '👥';
      case 'file upload':
        return '📤';
      default:
        return '🔧';
    }
  };

  const handleRetry = async () => {
    if (!onRetry) return;
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  return (
    <Alert className={cn(
      "border-l-4 shadow-sm transition-all duration-200 hover:shadow-md",
      getErrorColor(),
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex items-center space-x-2 mt-0.5">
            {getErrorIcon()}
            <span className="text-lg">{getServerIcon(error.serverName)}</span>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-semibold text-sm">{getErrorTitle()}</h4>
              <Badge variant="outline" className="text-xs">
                {error.serverName}
              </Badge>
              {error.toolName && (
                <Badge variant="secondary" className="text-xs">
                  {error.toolName}
                </Badge>
              )}
            </div>
            
            <AlertDescription className="text-sm leading-relaxed">
              {error.message}
            </AlertDescription>
            
            <div className="flex items-center space-x-3 mt-3">
              {error.retryable && onRetry && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRetry}
                  disabled={isRetrying}
                  className="h-7 px-3 text-xs"
                >
                  <RefreshCw className={cn("w-3 h-3 mr-1", isRetrying && "animate-spin")} />
                  {isRetrying ? 'Retrying...' : 'Retry'}
                </Button>
              )}
              
              {(error.type === 'authentication' || error.type === 'connection') && onOpenSettings && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onOpenSettings}
                  className="h-7 px-3 text-xs"
                >
                  <Settings className="w-3 h-3 mr-1" />
                  Settings
                </Button>
              )}
              
              <div className="text-xs text-gray-500">
                {error.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
        
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
          >
            <X className="w-3 h-3" />
          </Button>
        )}
      </div>
    </Alert>
  );
}
