import { BaseService } from "../base/service.interface";
import { storage } from "../../storage/index.js";
import { embeddingService } from "../ai/embedding-service";

/**
 * RAG Context Service - handles context building and source management
 */
export class RAGContextService extends BaseService {
  constructor() {
    super('RAGContext', '1.0.0', 'RAG context building and source management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'RAG Context service initialized');
  }

  protected getDependencies(): string[] {
    return ['storage', 'embedding-service'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    try {
      const integrations = await storage.getIntegrations();
      const topK = this.getTopK();
      
      return {
        storageConnected: true,
        embeddingServiceAvailable: !!embeddingService,
        integrationCount: integrations.length,
        topK,
        contextEnabled: true,
      };
    } catch (error) {
      return {
        storageConnected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Build context string from relevant chunks
   */
  buildContextFromChunks(chunks: any[]): string {
    this.ensureInitialized();

    if (chunks.length === 0) {
      return "No relevant documents found.";
    }

    let context = "Relevant information from your documents:\n\n";

    chunks.forEach((chunk, index) => {
      context += `Document: ${chunk.fileName}\n`;
      context += `Platform: ${chunk.platform}\n`;
      if (chunk.similarity) {
        context += `Relevance: ${Math.round(chunk.similarity * 100)}%\n`;
      }
      context += `Content: ${chunk.content}\n`;
      if (index < chunks.length - 1) {
        context += "\n---\n\n";
      }
    });

    this.log('info', `Built context from ${chunks.length} chunks, total length: ${context.length} characters`);
    return context;
  }

  /**
   * Get relevant context from enabled sources with configurable similarity
   */
  async getRelevantContext(
    query: string,
    enabledSources: string[] = [],
    topK?: number
  ): Promise<{
    chunks: any[];
    context: string;
    sourceCount: number;
  }> {
    this.ensureInitialized();
    this.validateString(query, 'query');

    try {
      const actualTopK = topK || this.getTopK();
      
      this.log('info', `Searching for relevant context with query: "${query.substring(0, 100)}..."`);
      this.log('info', `Enabled sources: ${enabledSources.join(", ")}`);
      this.log('info', `Using TOP_K=${actualTopK} for retrieval`);

      // Get relevant chunks from embedding service
      const rawChunks = await embeddingService.searchSimilarChunks(
        query,
        enabledSources,
        actualTopK
      );

      // Skip permission filtering for MVP (all files belong to same user)
      const relevantChunks = rawChunks;

      this.log('info', `Permission filtering: DISABLED for MVP - using all ${relevantChunks.length} chunks`);
      this.log('info', `Found ${relevantChunks.length} relevant chunks`);

      // Build context from chunks
      const context = this.buildContextFromChunks(relevantChunks);

      return {
        chunks: relevantChunks,
        context,
        sourceCount: enabledSources.length,
      };

    } catch (error: any) {
      this.log('error', 'Error getting relevant context', error);
      return {
        chunks: [],
        context: "No relevant documents found.",
        sourceCount: 0,
      };
    }
  }

  /**
   * Get available sources for RAG operations
   */
  async getAvailableSources(userId?: string): Promise<any[]> {
    this.ensureInitialized();

    try {
      const integrations = await storage.getIntegrations();

      // Filter to connected integrations only
      const connectedIntegrations = integrations.filter(
        integration => integration.status === "connected" || integration.status === "configured"
      );

      const sources = await Promise.all(connectedIntegrations.map(async integration => {
        // Get file count for each integration
        let fileCount = 0;
        try {
          // Map integration type to storage platform key
          let storageKey = integration.type;
          
          // Handle Google Drive type mapping
          if (integration.type === 'google-drive' || integration.type === 'google_drive') {
            storageKey = 'google_drive';
          }
          // Handle Microsoft Teams type mapping
          else if (integration.type === 'microsoft-teams' || integration.type === 'microsoft_teams') {
            storageKey = 'microsoft_teams';
          }
          // Handle other potential mappings
          else if (integration.type.includes('-')) {
            storageKey = integration.type.replace(/-/g, '_');
          }

          this.log('info', `Checking file count for integration: ${integration.name} (${integration.type}) using storage key: ${storageKey}`);
          
          const files = await storage.getFiles(storageKey, undefined, 1000, 0);
          fileCount = files.length;
          
          this.log('info', `Found ${fileCount} files for ${integration.name}`);
        } catch (error) {
          this.log('warn', `Error getting file count for ${integration.type}`, error);
        }

        return {
          id: integration.id.toString(),
          name: integration.name,
          type: integration.type,
          platform: integration.type,
          status: integration.status,
          connected: integration.status === "connected",
          fileCount
        };
      }));

      // Add uploaded files as a virtual source if there are any uploaded files
      try {
        const uploadedFiles = await storage.getFiles('uploaded_files', undefined, 1000, 0);
        if (uploadedFiles && uploadedFiles.length > 0) {
          sources.push({
            id: 'uploaded-files',
            name: 'Uploaded Files',
            type: 'uploaded-files',
            platform: 'uploaded_files',
            status: 'connected',
            connected: true,
            fileCount: uploadedFiles.length
          });
        }
      } catch (error) {
        this.log('info', 'No uploaded files found or error checking:', error);
      }

      // Add emails as a virtual source if there are any emails
      try {
        const { storage } = await import('../../storage/index.js');
        const emailStorage = storage.getEmailStorage();
        const emailCount = await emailStorage.getEmailCount();
        if (emailCount > 0) {
          sources.push({
            id: 'emails',
            name: 'Emails',
            type: 'emails',
            platform: 'emails',
            status: 'connected',
            connected: true,
            fileCount: emailCount
          });
        }
      } catch (error) {
        this.log('info', 'No emails found or error checking emails:', error);
      }

      this.log('info', `Found ${sources.length} available sources for user ${userId || 'anonymous'}`);
      return sources;

    } catch (error: any) {
      this.log('error', 'Error getting available sources', error);
      return [];
    }
  }

  /**
   * Search for files by description or content
   */
  async searchFilesByDescription(
    description: string,
    enabledSources: string[] = [],
    limit: number = 25
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateString(description, 'description');
    this.validateNumber(limit, 'limit', 1, 100);

    try {
      this.log('info', `Searching files by description: "${description}"`);

      // First, try vector search to find relevant content
      const relevantChunks = await embeddingService.searchSimilarChunks(
        description,
        enabledSources,
        limit * 4 // Increased multiplier for better file discovery
      );

      // Group chunks by file and get file metadata
      const fileMap = new Map();

      for (const chunk of relevantChunks) {
        if (!fileMap.has(chunk.fileId)) {
          try {
            const file = await storage.getFile(chunk.fileId);
            if (file) {
              fileMap.set(chunk.fileId, {
                ...file,
                relevantChunks: [chunk],
                maxSimilarity: chunk.similarity || 0
              });
            }
          } catch (error) {
            this.log('warn', `Error getting file ${chunk.fileId}`, error);
          }
        } else {
          const existingFile = fileMap.get(chunk.fileId);
          existingFile.relevantChunks.push(chunk);
          existingFile.maxSimilarity = Math.max(existingFile.maxSimilarity, chunk.similarity || 0);
        }
      }

      // Convert to array and sort by relevance
      const results = Array.from(fileMap.values())
        .sort((a, b) => b.maxSimilarity - a.maxSimilarity)
        .slice(0, limit);

      this.log('info', `Found ${results.length} files matching description`);
      return results;

    } catch (error: any) {
      this.log('error', 'Error searching files by description', error);
      return [];
    }
  }

  /**
   * Check if a file has embeddings
   */
  async hasEmbeddings(fileId: number): Promise<boolean> {
    this.ensureInitialized();
    this.validateNumber(fileId, 'file ID');

    try {
      const chunks = await storage.getFileChunks(fileId);
      return chunks.length > 0 && chunks.some(chunk => chunk.embedding && chunk.embedding.length > 0);
    } catch (error: any) {
      this.log('error', `Error checking embeddings for file ${fileId}`, error);
      return false;
    }
  }

  /**
   * Get source statistics
   */
  async getSourceStatistics(enabledSources: string[] = []): Promise<{
    totalSources: number;
    enabledSources: number;
    sourceBreakdown: Record<string, number>;
    fileCount: number;
  }> {
    this.ensureInitialized();

    try {
      const allSources = await this.getAvailableSources();
      const sourceBreakdown: Record<string, number> = {};

      // Count files by platform
      let totalFileCount = 0;
      for (const source of allSources) {
        try {
          const files = await storage.getFiles(source.platform, undefined, 1000, 0);
          sourceBreakdown[source.platform] = files.length;
          totalFileCount += files.length;
        } catch (error) {
          sourceBreakdown[source.platform] = 0;
        }
      }

      return {
        totalSources: allSources.length,
        enabledSources: enabledSources.length,
        sourceBreakdown,
        fileCount: totalFileCount,
      };

    } catch (error: any) {
      this.log('error', 'Error getting source statistics', error);
      return {
        totalSources: 0,
        enabledSources: 0,
        sourceBreakdown: {},
        fileCount: 0,
      };
    }
  }

  /**
   * Validate enabled sources
   */
  async validateEnabledSources(enabledSources: string[]): Promise<{
    valid: string[];
    invalid: string[];
    warnings: string[];
  }> {
    this.ensureInitialized();

    try {
      const availableSources = await this.getAvailableSources();
      const availableSourceIds = new Set(availableSources.map(s => s.id));

      const valid: string[] = [];
      const invalid: string[] = [];
      const warnings: string[] = [];

      for (const sourceId of enabledSources) {
        if (availableSourceIds.has(sourceId)) {
          valid.push(sourceId);
        } else {
          invalid.push(sourceId);
        }
      }

      // Check for disconnected sources
      for (const sourceId of valid) {
        const source = availableSources.find(s => s.id === sourceId);
        if (source && source.status !== 'connected') {
          warnings.push(`Source ${source.name} is ${source.status}, may have limited functionality`);
        }
      }

      return { valid, invalid, warnings };

    } catch (error: any) {
      this.log('error', 'Error validating enabled sources', error);
      return {
        valid: [],
        invalid: enabledSources,
        warnings: ['Error validating sources'],
      };
    }
  }

  /**
   * Get configurable TOP_K for similarity search (inspired by LlamaIndex)
   */
  private getTopK(): number {
    return parseInt(process.env.TOP_K || "50");
  }

  /**
   * Get context window size limit
   */
  getContextWindowLimit(): number {
    return parseInt(process.env.CONTEXT_WINDOW_LIMIT || "8000");
  }

  /**
   * Truncate context to fit within limits
   */
  truncateContext(context: string, maxLength?: number): string {
    const limit = maxLength || this.getContextWindowLimit();
    
    if (context.length <= limit) {
      return context;
    }

    const truncated = context.substring(0, limit - 100); // Leave room for truncation message
    const lastNewline = truncated.lastIndexOf('\n');
    
    // Try to truncate at a natural break point
    const finalContext = lastNewline > limit * 0.8 ? 
      truncated.substring(0, lastNewline) : 
      truncated;

    this.log('warn', `Context truncated from ${context.length} to ${finalContext.length} characters`);
    return finalContext + '\n\n[Context truncated due to length limits]';
  }
}
