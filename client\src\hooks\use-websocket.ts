import { useEffect, useRef, useCallback, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from './use-toast';

interface WebSocketMessage {
  type: string;
  data: any;
}

interface IntegrationStatusUpdate {
  integrationId: number;
  status: string;
  message?: string;
  syncLogId?: number;
}

interface UseWebSocketOptions {
  enabled?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const {
    enabled = true,
    reconnectInterval = 5000,
    maxReconnectAttempts = 5
  } = options;

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const lastInvalidationRef = useRef<{ [key: string]: number }>({});
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');

  // Debounce function to prevent excessive invalidations
  const debounceInvalidation = useCallback((queryKey: string, delay: number = 1000) => {
    const now = Date.now();
    const lastInvalidation = lastInvalidationRef.current[queryKey] || 0;
    
    if (now - lastInvalidation > delay) {
      lastInvalidationRef.current[queryKey] = now;
      return true;
    }
    return false;
  }, []);

  const handleIntegrationStatusUpdate = useCallback((update: IntegrationStatusUpdate) => {
    console.log('[WebSocket] Integration status update:', update);
    
    // Only invalidate if enough time has passed since last invalidation
    if (debounceInvalidation('/api/integrations', 2000)) {
      console.log('[WebSocket] Invalidating integrations cache');
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
    }
    
    // Show toast notification for status changes (debounced)
    const toastKey = `integration-${update.integrationId}-${update.status}`;
    if (debounceInvalidation(toastKey, 3000)) {
      if (update.status === 'connected') {
        toast({
          title: "Sync completed",
          description: update.message || `Integration ${update.integrationId} sync completed successfully.`,
        });
      } else if (update.status === 'error') {
        toast({
          title: "Sync failed",
          description: update.message || `Integration ${update.integrationId} sync encountered an error.`,
          variant: "destructive",
        });
      } else if (update.status === 'syncing') {
        toast({
          title: "Sync started",
          description: update.message || `Integration ${update.integrationId} sync has started.`,
        });
      }
    }
  }, [queryClient, toast, debounceInvalidation]);

  const handleSyncLogUpdate = useCallback((syncLog: any) => {
    console.log('[WebSocket] Sync log update:', syncLog);
    
    // Only invalidate if enough time has passed since last invalidation
    if (debounceInvalidation('/api/sync-logs', 2000)) {
      console.log('[WebSocket] Invalidating sync logs cache');
      queryClient.invalidateQueries({ queryKey: ['/api/sync-logs'] });
    }
  }, [queryClient, debounceInvalidation]);

  const handleNotification = useCallback((notification: any) => {
    console.log('[WebSocket] Notification:', notification);
    
    // Debounce notifications to prevent spam
    const notificationKey = `notification-${notification.title}`;
    if (debounceInvalidation(notificationKey, 2000)) {
      toast({
        title: notification.title,
        description: notification.message,
        variant: notification.type === 'error' ? 'destructive' : 'default',
      });
    }
  }, [toast, debounceInvalidation]);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'connection':
        console.log('[WebSocket] Connection established:', message.data.message);
        break;
        
      case 'pong':
        console.log('[WebSocket] Pong received');
        break;
        
      case 'integration_status_update':
        handleIntegrationStatusUpdate(message.data as IntegrationStatusUpdate);
        break;
        
      case 'sync_log_update':
        handleSyncLogUpdate(message.data);
        break;
        
      case 'notification':
        handleNotification(message.data);
        break;
        
      default:
        console.log('[WebSocket] Unknown message type:', message.type);
    }
  }, [handleIntegrationStatusUpdate, handleSyncLogUpdate, handleNotification]);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      console.log('[WebSocket] Sent message:', message);
    } else {
      console.warn('[WebSocket] Cannot send message - connection not open');
    }
  }, []);

  const disconnect = useCallback(() => {
    console.log('[WebSocket] Disconnecting...');
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Client disconnecting');
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setConnectionStatus('disconnected');
    reconnectAttemptsRef.current = 0;
  }, []);

  // Stable connect function that doesn't change on every render
  const connect = useCallback(() => {
    if (!enabled || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      setConnectionStatus('connecting');
      
      // Determine WebSocket URL based on current location
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/ws`;
      
      console.log('[WebSocket] Connecting to:', wsUrl);
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('[WebSocket] Connected successfully');
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0;
        
        // Send ping to verify connection
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({ type: 'ping', data: {} }));
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('[WebSocket] Received message:', message);
          
          handleMessage(message);
        } catch (error) {
          console.error('[WebSocket] Error parsing message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('[WebSocket] Connection closed:', event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus('disconnected');
        wsRef.current = null;
        
        // Attempt to reconnect if not a normal closure and we haven't exceeded max attempts
        if (enabled && event.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          console.log(`[WebSocket] Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts}) in ${reconnectInterval}ms...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('[WebSocket] Connection error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('[WebSocket] Failed to create connection:', error);
      setConnectionStatus('error');
    }
  }, [enabled, reconnectInterval, maxReconnectAttempts, handleMessage]);

  // Connect when enabled, disconnect when disabled - STABLE DEPENDENCIES
  useEffect(() => {
    console.log('[WebSocket] useEffect triggered, enabled:', enabled);
    
    if (enabled) {
      connect();
    } else {
      disconnect();
    }
    
    return () => {
      console.log('[WebSocket] useEffect cleanup');
      disconnect();
    };
  }, [enabled]); // ONLY depend on enabled, not connect/disconnect

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('[WebSocket] Component unmounting, cleaning up');
      disconnect();
    };
  }, []); // Empty dependency array for unmount cleanup

  return {
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    sendMessage
  };
} 