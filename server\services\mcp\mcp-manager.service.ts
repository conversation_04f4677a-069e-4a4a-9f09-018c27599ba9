import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { credentials } from '../../core/config/credentials.config';
import { MCPTool, MCPResult, MCPServerConfig, MCPConnection } from './types';

export class MCPManager {
  private connections = new Map<string, MCPConnection>();
  private allTools: MCPTool[] = [];

  async initializeServers(configs: MCPServerConfig[]): Promise<void> {
    console.log(`🔌 Initializing ${configs.length} MCP servers...`);
    
    const enabledConfigs = configs.filter(config => config.enabled);
    
    for (const config of enabledConfigs) {
      try {
        await this.connectToServer(config);
      } catch (error) {
        console.error(`❌ Failed to connect to MCP server "${config.name}":`, error);
      }
    }

    // Aggregate all tools
    this.aggregateTools();
    
    console.log(`✅ Successfully connected to ${this.connections.size} MCP servers`);
    console.log(`📋 Total tools available: ${this.allTools.length}`);
  }

  private async connectToServer(config: MCPServerConfig): Promise<void> {
    let transport: StdioClientTransport | SSEClientTransport;
    
    if (config.type === 'local') {
      if (!config.connection.command) {
        throw new Error(`Local server "${config.name}" requires command`);
      }
      const baseEnv = {
        ...Object.fromEntries(
          Object.entries(process.env).filter(([_, value]) => value !== undefined)
        ) as Record<string, string>}

       const credentialsEnv = config.connection.credentials ? {
        ...Object.entries(credentials[config.connection.credentials as keyof typeof credentials])
          .reduce((acc, [key, value]) => ({
            ...acc,
            [key]: value
          }), {})
      } : {};
      transport = new StdioClientTransport({
        command: config.connection.command,
        args: config.connection.args || [],
        env: {
          ...baseEnv,
          ...credentialsEnv
        }
      });
    } else {
      if (!config.connection.url) {
        throw new Error(`Remote server "${config.name}" requires URL`);
      }
      
      transport = new SSEClientTransport(
        new URL(config.connection.url),
        config.connection.headers || {}
      );
    }

    const client = new Client(
      {
        name: `mcp-client-${config.name}`,
        version: '1.0.0'
      }
    );

    try {
      await client.connect(transport);
      
      // List available tools
      const toolsResponse = await client.listTools();
      const tools: MCPTool[] = toolsResponse.tools?.map((tool: any) => ({
        type: 'function',
        function: {
          name: tool.name,
          description: tool.description || '',
          parameters: tool.inputSchema || {}
        },
        serverName: config.name
      })) || [];

      this.connections.set(config.name, { 
        serverName: config.name,
        connected: true,
        tools,
        client,
        transport,
        resources: [],
        prompts: [],
        lastConnected: new Date(),
        error: undefined
      });

      console.log(`📅 Connected to "${config.name}" MCP server with ${tools.length} tools:`, 
        tools.map(t => t.function.name));

    } catch (error) {
      // Clean up on connection failure
      try {
        await client.close();
        await transport.close();
      } catch (cleanupError) {
        console.error('Error during cleanup:', cleanupError);
      }
      throw error;
    }
  }

  private aggregateTools(): void {
    this.allTools = [];
    
    this.connections.forEach((connection, serverName) => {
      if (connection.connected) {
        this.allTools.push(...connection.tools);
      }
    });

    // Check for tool name conflicts and warn
    const toolNames = this.allTools.map(t => t.function.name);
    const duplicates = toolNames.filter((name, index) => toolNames.indexOf(name) !== index);
    
    if (duplicates.length > 0) {
      const uniqueDuplicates = Array.from(new Set(duplicates));
      console.warn(`⚠️ Tool name conflicts detected: ${uniqueDuplicates.join(', ')}`);
      console.warn('Consider using namespaced tool names for different servers');
    }
  }

  async callTool(toolName: string, arguments_: any): Promise<MCPResult> {
    // Find which server has this tool
    const tool = this.allTools.find(t => t.function.name === toolName);
    
    if (!tool) {
      throw new Error(`Tool "${toolName}" not found in any connected MCP server`);
    }

    const connection = this.connections.get(tool.serverName);
    
    if (!connection || !connection.connected) {
      throw new Error(`MCP server "${tool.serverName}" is not connected`);
    }

    try {
        const response = await connection.client.callTool({
            name: toolName,
            arguments: arguments_
        });

      // Convert the SDK response to our MCPResult format
      if (response && 'content' in response) {
        return response as MCPResult;
      }

      return { content: [{ type: 'text', text: 'No result' }] };
    } catch (error) {
      console.error(`Error calling tool ${toolName} on server ${tool.serverName}:`, error);
      throw error;
    }
  }

  getAvailableTools(): MCPTool[] {
    return this.allTools;
  }

  getConnectedServers(): MCPConnection[] {
    return Array.from(this.connections.values()).filter(
      connection => connection.connected
    );
  }

  getServerTools(serverName: string): MCPTool[] {
    const connection = this.connections.get(serverName);
    return connection?.tools || [];
  }

  isReady(): boolean {
    return this.connections.size > 0 && 
           Array.from(this.connections.values()).some(conn => conn.connected);
  }

  async disconnect(): Promise<void> {
    const disconnectPromises: Promise<void>[] = [];

    this.connections.forEach((connection, serverName) => {
      disconnectPromises.push(this.disconnectServer(serverName, connection));
    });

    await Promise.allSettled(disconnectPromises);
    
    this.connections.clear();
    this.allTools = [];
    console.log('🔌 All MCP servers disconnected');
  }

  private async disconnectServer(serverName: string, connection: MCPConnection): Promise<void> {
    try {
      await connection.transport.close();
      connection.connected = false;
      console.log(`🔌 Disconnected from MCP server: ${serverName}`);
    } catch (error) {
      console.error(`Error disconnecting from server ${serverName}:`, error);
    }
  }

  async reconnectServer(serverName: string): Promise<boolean> {
    // This could be implemented to reconnect individual servers
    // For now, return false to indicate not implemented
    console.warn(`Reconnection for server "${serverName}" not implemented`);
    return false;
  }
} 