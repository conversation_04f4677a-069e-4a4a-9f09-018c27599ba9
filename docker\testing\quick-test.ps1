# MeetSync Docker Quick Test - Windows PowerShell Version
Write-Host "🚀 MeetSync Docker Quick Test" -ForegroundColor Blue
Write-Host "=============================" -ForegroundColor Blue

# Test results
$Passed = 0
$Failed = 0

# Test function
function Test-Check {
    param(
        [string]$Name,
        [scriptblock]$Command
    )
    
    Write-Host "Testing $Name... " -NoNewline
    try {
        $result = & $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PASS" -ForegroundColor Green
            $script:Passed++
        } else {
            Write-Host "❌ FAIL" -ForegroundColor Red
            $script:Failed++
        }
    } catch {
        Write-Host "❌ FAIL" -ForegroundColor Red
        $script:Failed++
    }
}

Write-Host "Running quick Docker validation tests..." -ForegroundColor Yellow
Write-Host ""

# Basic connectivity tests
Test-Check "Application Health" { 
    $response = curl.exe -s http://localhost:8080/health 2>$null
    if ($response) { 
        $json = $response | ConvertFrom-Json
        if ($json.status -eq "healthy") { return $true }
    }
    throw "Health check failed"
}

Test-Check "Metrics Endpoint" { 
    $response = curl.exe -s http://localhost:8080/metrics 2>$null
    if ($response -match "http_requests_total") { return $true }
    throw "Metrics endpoint failed"
}

Test-Check "System Info" { 
    $response = curl.exe -s http://localhost:8080/api/system/info 2>$null
    if ($response) {
        $json = $response | ConvertFrom-Json
        if ($json.service) { return $true }
    }
    throw "System info failed"
}

Test-Check "Redis Sessions" { 
    $response = curl.exe -s http://localhost:8080/api/system/info 2>$null
    if ($response) {
        $json = $response | ConvertFrom-Json
        if ($json.session.type -eq "redis") { return $true }
    }
    throw "Redis sessions failed"
}

Test-Check "Database Health" { 
    $response = curl.exe -s http://localhost:8080/api/health 2>$null
    if ($response) {
        $json = $response | ConvertFrom-Json
        if ($json.checks.database.status -eq "healthy") { return $true }
    }
    throw "Database health failed"
}

# Container tests
Test-Check "Container Health" { 
    $containers = docker ps --format "{{.Status}}" 2>$null
    if ($containers -match "healthy") { return $true }
    throw "No healthy containers found"
}

Test-Check "App Container Running" { 
    $containers = docker ps 2>$null
    if ($containers -match "meetsync-app-dev") { return $true }
    throw "App container not running"
}

Test-Check "Database Container" { 
    $containers = docker ps 2>$null
    if ($containers -match "meetsync-postgres-dev") { return $true }
    throw "Database container not running"
}

Test-Check "Redis Container" { 
    $containers = docker ps 2>$null
    if ($containers -match "meetsync-redis-dev") { return $true }
    throw "Redis container not running"
}

# Security tests
Test-Check "Non-root Execution" { 
    $user = docker exec meetsync-app-dev whoami 2>$null
    if ($user -match "meetsync") { return $true }
    throw "Not running as meetsync user"
}

Test-Check "File Permissions" { 
    $owner = docker exec meetsync-app-dev stat -c '%U' /app 2>$null
    if ($owner -match "meetsync") { return $true }
    throw "Incorrect file permissions"
}

Write-Host ""
Write-Host "Quick Test Results:" -ForegroundColor Blue
Write-Host "=================="
Write-Host "✅ Passed: $Passed" -ForegroundColor Green
Write-Host "❌ Failed: $Failed" -ForegroundColor Red

if ($Failed -eq 0) {
    Write-Host "🎉 All quick tests passed! Your Docker setup looks good." -ForegroundColor Green
    Write-Host "💡 Run '.\docker\testing\run-all-tests.ps1' for comprehensive testing." -ForegroundColor Blue
} else {
    Write-Host "⚠️  Some tests failed. Run '.\docker\testing\run-all-tests.ps1' for detailed analysis." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Service URLs:" -ForegroundColor Blue
Write-Host "- Application: http://localhost:8080"
Write-Host "- Health Check: http://localhost:8080/health"
Write-Host "- Metrics: http://localhost:8080/metrics"
Write-Host "- System Info: http://localhost:8080/api/system/info" 