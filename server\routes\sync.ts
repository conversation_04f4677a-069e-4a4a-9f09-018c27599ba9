import type { Express } from "express";
import { integrationController } from "../controllers/integration/index";
import { syncController } from "../controllers/sync";

/**
 * Register synchronization and scheduling routes
 */
export function registerSyncRoutes(app: Express) {
  // Schedule Routes
  app.post('/api/schedules', integrationController.updateSchedule);
  app.get('/api/schedules', integrationController.getSchedules);

  // Sync Routes
  app.get('/api/sync-logs', syncController.getSyncLogs.bind(syncController));
  app.get('/api/sync-items/:syncLogId', syncController.getSyncItems.bind(syncController));
  app.post('/api/sync-now', syncController.syncNow.bind(syncController));

  // Re-vectorization endpoint
  app.post('/api/sync/re-vectorize', (req, res) => syncController.reVectorizeAll(req, res));
} 