-- Database Reset Script for MeetSync
-- Clears all connected state and fetched data while preserving integration records
-- This allows you to test fresh integration setup without recreating integration entries

BEGIN;

-- 1. Clear integration credentials and reset connection status
UPDATE integrations
SET
  credentials = NULL,
  status = 'disconnected',
  sync_status = 'idle', 
  last_sync_at = NULL,
  next_sync_at = NULL,
  updated_at = NOW()
WHERE credentials IS NOT NULL OR status != 'disconnected';

-- 2. Clear all chat data (AI conversations that reference files)
DELETE FROM chat_messages;
DELETE FROM chat_sessions;

-- 3. Clear all files and their chunks (chunks will cascade delete due to foreign key)
DELETE FROM files;
-- Note: file_chunks will be automatically deleted due to CASCADE foreign key

-- 4. Clear all sync history 
DELETE FROM sync_items;
DELETE FROM sync_logs;

-- Display summary of what was reset
SELECT 
  'Reset completed successfully' as status,
  (SELECT COUNT(*) FROM integrations) as total_integrations,
  (SELECT COUNT(*) FROM integrations WHERE status = 'disconnected') as disconnected_integrations,
  (SELECT COUNT(*) FROM sync_logs) as remaining_sync_logs,
  (SELECT COUNT(*) FROM files) as remaining_files,
  (SELECT COUNT(*) FROM file_chunks) as remaining_file_chunks,
  (SELECT COUNT(*) FROM chat_sessions) as remaining_chat_sessions;

COMMIT; 