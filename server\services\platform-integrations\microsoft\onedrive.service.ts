import { Client } from '@microsoft/microsoft-graph-client';
import { BaseService } from "../../base/service.interface";
import type { MicrosoftCredentials } from './oauth.service';
import type { TeamsFileMetadata } from './teams.service';

/**
 * Microsoft OneDrive Service - handles OneDrive operations and file management
 */
export class MicrosoftOneDriveService extends BaseService {
  constructor() {
    super('MicrosoftOneDrive', '1.0.0', 'Microsoft OneDrive operations and file management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Microsoft OneDrive service initialized');
  }

  protected getDependencies(): string[] {
    return ['MicrosoftOAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      apiVersion: 'v1.0',
      supportedOperations: ['drives', 'files', 'folders', 'download'],
    };
  }

  /**
   * Get user's OneDrive drives
   */
  async getUserDrives(client: Client): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');

    try {
      const response = await client.api('/me/drives').get();
      return response.value || [];
    } catch (error: any) {
      this.log('error', 'Error getting user drives', error);
      return [];
    }
  }

  /**
   * Sync files from OneDrive folder with comprehensive file retrieval
   */
  async syncOneDriveFolder(
    client: Client,
    folderId: string = 'root'
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');

    try {
      this.log('info', `Starting comprehensive sync for OneDrive folder ${folderId}`);
      
      // Get the user's default drive
      const driveResponse = await client.api('/me/drive').get();
      const driveId = driveResponse.id;

      this.log('info', `OneDrive - driveId: ${driveId}, folderId: ${folderId}`);

      // Get ALL files recursively from the folder
      const allFiles = await this.getAllFilesRecursively(
        client,
        driveId,
        folderId
      );

      this.log('info', `Found ${allFiles.length} total files in OneDrive folder ${folderId}`);
      return allFiles;

    } catch (error: any) {
      this.handleError(error, `syncing OneDrive folder ${folderId}`);
    }
  }

  /**
   * Get OneDrive folder structure for selection
   */
  async getOneDriveStructure(client: Client): Promise<{
    drives: Array<{
      id: string;
      name: string;
      type: string;
      folders: Array<{
        id: string;
        name: string;
        itemCount?: number;
      }>;
    }>;
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');

    try {
      const drives = await this.getUserDrives(client);
      const driveStructure = [];

      for (const drive of drives) {
        try {
          // Get root folders for this drive
          const foldersResponse = await client.api(`/drives/${drive.id}/root/children`)
            .filter('folder ne null')
            .get();
          
          const folders = [];
          for (const folder of foldersResponse.value || []) {
            let itemCount = 0;
            try {
              const childrenResponse = await client.api(`/drives/${drive.id}/items/${folder.id}/children`).get();
              itemCount = childrenResponse.value?.length || 0;
            } catch (error) {
              // Continue if we can't get children count
            }

            folders.push({
              id: folder.id,
              name: folder.name,
              itemCount,
            });
          }

          driveStructure.push({
            id: drive.id,
            name: drive.name || 'OneDrive',
            type: drive.driveType || 'personal',
            folders,
          });
        } catch (error) {
          this.log('error', `Error processing drive ${drive.name}`, error);
        }
      }

      return { drives: driveStructure };
    } catch (error: any) {
      this.handleError(error, 'getting OneDrive structure');
    }
  }

  /**
   * Download file content from OneDrive
   */
  async downloadFileContent(
    client: Client,
    driveId: string,
    fileId: string,
    fileName: string,
    mimeType?: string
  ): Promise<Buffer | null> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(driveId, 'drive ID');
    this.validateString(fileId, 'file ID');

    try {
      this.log('info', `Downloading file content: ${fileName}`);
      this.log('info', `Using driveId: ${driveId}, fileId: ${fileId}`);

      // Download the file content with proper response type
      const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
        .responseType('arraybuffer' as any)
        .get();

      this.log('info', `Download response type: ${typeof downloadResponse}`);
      this.log('info', `Download response constructor: ${downloadResponse?.constructor?.name}`);

      if (!downloadResponse) {
        this.log('info', `No content received for file: ${fileName}`);
        return null;
      }

      // Convert response to buffer
      let buffer: Buffer;

      if (Buffer.isBuffer(downloadResponse)) {
        this.log('info', `Response is already a Buffer`);
        buffer = downloadResponse;
      } else if (downloadResponse instanceof ArrayBuffer) {
        this.log('info', `Response is ArrayBuffer, converting to Buffer`);
        buffer = Buffer.from(downloadResponse);
      } else if (downloadResponse instanceof Uint8Array) {
        this.log('info', `Response is Uint8Array, converting to Buffer`);
        buffer = Buffer.from(downloadResponse);
      } else if (typeof downloadResponse === 'string') {
        this.log('info', `Response is string, converting to Buffer`);
        buffer = Buffer.from(downloadResponse, 'binary');
      } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
        this.log('info', `Response has buffer property, using that`);
        buffer = Buffer.from(downloadResponse.buffer);
      } else {
        this.log('info', `Unexpected response type for file: ${fileName}`);
        this.log('info', `Response details:`, {
          type: typeof downloadResponse,
          constructor: downloadResponse?.constructor?.name,
          hasBuffer: !!(downloadResponse && typeof downloadResponse === 'object' && 'buffer' in downloadResponse),
          keys: downloadResponse && typeof downloadResponse === 'object' ? Object.keys(downloadResponse).slice(0, 5) : []
        });
        return null;
      }

      this.log('info', `Downloaded ${buffer.length} bytes for: ${fileName}`);
      return buffer;

    } catch (error: any) {
      this.log('error', `Error downloading file ${fileName}`, error);
      return null;
    }
  }

  /**
   * Get all files recursively from a folder, including subfolders
   */
  private async getAllFilesRecursively(
    client: Client,
    driveId: string,
    folderId: string,
    folderPath: string = ''
  ): Promise<any[]> {
    const allFiles: any[] = [];
    
    try {
      this.log('info', `Scanning OneDrive folder: ${folderId} at path: ${folderPath || 'root'}`);
      
      // Get items in current folder with expanded properties
      const response = await client.api(`/drives/${driveId}/items/${folderId}/children`)
        .select('id,name,file,folder,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference')
        .expand('thumbnails')
        .get();
      
      const items = response.value || [];
      this.log('info', `Found ${items.length} items in OneDrive folder ${folderId}`);
      
      for (const item of items) {
        if (item.file) {
          // This is a file - process it
          this.log('info', `Processing OneDrive file: ${item.name} (${item.id})`);
          
          const file = {
            externalId: item.id,
            fileName: item.name,
            fileType: this.determineFileType(item),
            platform: 'microsoft_onedrive',
            sourceUrl: item.webUrl,
            userId: item.createdBy?.user?.id || item.lastModifiedBy?.user?.id || 'unknown',
            lastModified: item.lastModifiedDateTime ? new Date(item.lastModifiedDateTime) : new Date(),
            extractedMetadata: {
              _sourceType: 'onedrive_personal',
              _sourceFolderId: folderId,
              webUrl: item.webUrl,
              driveId: driveId,
              parentReference: item.parentReference,
              folderPath: folderPath,
              folderName: folderPath ? folderPath.split('/').pop() || 'Unknown Folder' : 'Root',
              ownerName: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
              ownerEmail: item.createdBy?.user?.userPrincipalName || item.lastModifiedBy?.user?.userPrincipalName || null,
              fileExtension: this.getFileExtension(item.name),
              mimeType: item.file?.mimeType || 'unknown',
              shared: item.shared !== undefined,
              lastModifiedDateTime: item.lastModifiedDateTime,
              createdDateTime: item.createdDateTime,
              size: item.size || 0,
              createdBy: item.createdBy?.user?.displayName,
              lastModifiedBy: item.lastModifiedBy?.user?.displayName,
              owner: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
              hasContent: true,
              isDownloadable: true,
            } as TeamsFileMetadata
          };
          
          allFiles.push(file);
        } else if (item.folder) {
          // This is a folder - recurse into it
          const subFolderPath = folderPath ? `${folderPath}/${item.name}` : item.name;
          this.log('info', `Recursing into OneDrive subfolder: ${item.name} (${item.id})`);
          
          try {
            const subFiles = await this.getAllFilesRecursively(
              client,
              driveId,
              item.id,
              subFolderPath
            );
            allFiles.push(...subFiles);
          } catch (subFolderError) {
            this.log('error', `Error scanning OneDrive subfolder ${item.name}`, subFolderError);
            // Continue with other folders
          }
        }
      }
      
      return allFiles;
    } catch (error: any) {
      this.handleError(error, `getting OneDrive files from folder ${folderId}`);
    }
  }

  /**
   * Determine file type based on file properties
   */
  private determineFileType(item: any): string {
    const name = item.name?.toLowerCase() || '';
    const mimeType = item.file?.mimeType?.toLowerCase() || '';
    const extension = this.getFileExtension(name);

    // Video files
    if (mimeType.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'webm', 'mkv', 'flv'].includes(extension)) {
      return 'video';
    }

    // Audio files
    if (mimeType.startsWith('audio/') || ['mp3', 'wav', 'm4a', 'wma', 'aac', 'flac', 'ogg'].includes(extension)) {
      return 'audio';
    }

    // Documents
    if (mimeType.includes('document') || mimeType.includes('text') || 
        ['docx', 'doc', 'pdf', 'txt', 'rtf', 'odt'].includes(extension)) {
      return 'document';
    }

    // Presentations
    if (mimeType.includes('presentation') || 
        ['pptx', 'ppt', 'odp', 'key'].includes(extension)) {
      return 'presentation';
    }

    // Spreadsheets
    if (mimeType.includes('sheet') || 
        ['xlsx', 'xls', 'csv', 'ods', 'numbers'].includes(extension)) {
      return 'spreadsheet';
    }

    // Images
    if (mimeType.startsWith('image/') || 
        ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      return 'image';
    }

    return extension || 'unknown';
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
  }

  /**
   * Search files in OneDrive by name or content
   */
  async searchFiles(
    client: Client,
    query: string,
    driveId?: string
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(query, 'search query');

    try {
      const searchEndpoint = driveId 
        ? `/drives/${driveId}/root/search(q='${encodeURIComponent(query)}')`
        : `/me/drive/root/search(q='${encodeURIComponent(query)}')`;

      const response = await client.api(searchEndpoint)
        .select('id,name,file,folder,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference')
        .get();

      const items = response.value || [];
      const files = items.filter((item: any) => item.file); // Only return files, not folders

      return files.map((item: any) => ({
        externalId: item.id,
        fileName: item.name,
        fileType: this.determineFileType(item),
        platform: 'microsoft_onedrive',
        sourceUrl: item.webUrl,
        userId: item.createdBy?.user?.id || item.lastModifiedBy?.user?.id || 'unknown',
        lastModified: item.lastModifiedDateTime ? new Date(item.lastModifiedDateTime) : new Date(),
        extractedMetadata: {
          _sourceType: 'onedrive_personal',
          _sourceFolderId: item.parentReference?.id || 'root',
          webUrl: item.webUrl,
          driveId: item.parentReference?.driveId,
          parentReference: item.parentReference,
          ownerName: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
          ownerEmail: item.createdBy?.user?.userPrincipalName || item.lastModifiedBy?.user?.userPrincipalName || null,
          fileExtension: this.getFileExtension(item.name),
          mimeType: item.file?.mimeType || 'unknown',
          size: item.size || 0,
          hasContent: true,
          isDownloadable: true,
        } as TeamsFileMetadata
      }));

    } catch (error: any) {
      this.handleError(error, `searching OneDrive files with query: ${query}`);
    }
  }
}
