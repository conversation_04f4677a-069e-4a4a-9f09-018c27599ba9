import os
import json
from flask import Flask
from google_drive_utils import get_drive_service, extract_doc_content, mark_processed
from notion_utils import upload_to_notion
from googleapiclient.discovery import build
from google.oauth2 import service_account

# Load config
if not os.path.exists('config.json'):
    print("Error: config.json not found!")
    exit(1)
with open('config.json') as f:
    cfg = json.load(f)
NOTION_TOKEN = cfg['notion_token']
DATABASE_ID  = cfg['database_id']
SERVICE_ACCOUNT_FILE = 'service_account.json'
SCOPES = ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/documents.readonly']

app = Flask(__name__)

def main():
    try:
        svc = get_drive_service()
        print("Successfully created Drive service")
        # Initialize the Docs API service using the same service account credentials
        creds = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        docs_service = build('docs', 'v1', credentials=creds)
        print("Successfully created Docs service")
        # 1) find the Shared Drive
        print("Looking for Shared Drive…")
        drives_result = svc.drives().list(
            q="name='Shared Meeting Transcripts Drive'"
        ).execute()
        drives = drives_result.get('drives', [])
        print(f"Found {len(drives)} drives matching the query")
        if not drives:
            print("No shared drive named 'Shared Meeting Transcripts Drive' found")
            print("Available drives:")
            all_drives = svc.drives().list().execute().get('drives', [])
            for drive in all_drives:
                print(f"- {drive.get('name', 'Unknown')} ({drive.get('id', 'No ID')})")
            return
        drive_id = drives[0]['id']
        print(f"Using shared drive ID: {drive_id}")

        # Get the Processed Transcripts folder ID
        processed_folder_resp = svc.files().list(
            q=f"name='Processed Transcripts' and mimeType='application/vnd.google-apps.folder' and '{drive_id}' in parents",
            fields='files(id)',
            includeItemsFromAllDrives=True,
            supportsAllDrives=True
        ).execute()
        processed_folder_id = None
        processed_folders = processed_folder_resp.get('files', [])
        if processed_folders:
            processed_folder_id = processed_folders[0]['id']
            print(f"Found Processed Transcripts folder with ID: {processed_folder_id}")

        page_token = None
        while True:
            # 2) list all Google Docs in that shared drive, excluding those in Processed Transcripts folder
            query = "mimeType='application/vnd.google-apps.document'"
            if processed_folder_id:
                query += f" and not '{processed_folder_id}' in parents"
            resp = svc.files().list(
                q=query,
                corpora='drive',
                driveId=drive_id,
                includeItemsFromAllDrives=True,
                supportsAllDrives=True,
                fields='nextPageToken, files(id,name,parents)',
                pageToken=page_token
            ).execute()
            files = resp.get('files', [])
            print("Found", len(files), "docs in shared drive")
            for f in files:
                print("Processing:", f['name'], f['id'])
                # Extract content using Docs API
                doc = docs_service.documents().get(documentId=f['id'], includeTabsContent=True).execute()
                notes_structural_elements = None
                for tab in doc.get("tabs", []):
                    tab_title = tab["tabProperties"].get("title", "").lower()
                    if "notes" in tab_title:
                        notes_structural_elements = tab["documentTab"]["body"]["content"]
                        break
                notes_text, transcript_text = extract_doc_content(docs_service, f['id'])
                print(f"Found notes ({len(notes_text)} chars) and transcript ({len(transcript_text)} chars)")
                # 4) push to Notion
                upload_to_notion(f['name'], transcript_text, notes_text, NOTION_TOKEN, DATABASE_ID, notes_structural_elements=notes_structural_elements)
                # 5) archive
                file_parents = f.get('parents',[None])[0]
                print("Moving to archive folder…")
                mark_processed(svc, f['id'], file_parents, drive_id)
                print("✅ uploaded", f['name'])
            page_token = resp.get('nextPageToken')
            if not page_token:
                break
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        return

@app.route('/')
def index():
    return "Meet transcript-to-Notion service is running."

@app.route('/run')
def run_script():
    main()
    return "Processing complete."

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.environ.get("PORT", 8080))) 