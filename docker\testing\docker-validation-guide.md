# 🧪 MeetSync Docker Validation Guide

This comprehensive guide will help you test and validate all Docker implementations to ensure 100% functionality and production readiness.

## 📋 Testing Checklist Overview

- [ ] **Environment Setup & Configuration**
- [ ] **Phase 1: Critical Completions**
  - [ ] Redis Session Integration
  - [ ] Health Check Robustness
  - [ ] Documentation Consistency
- [ ] **Phase 2: Production Enhancements**
  - [ ] Monitoring & Observability
  - [ ] Backup & Recovery
  - [ ] Security Hardening
- [ ] **Phase 3: DevOps Integration**
  - [ ] Multi-Environment Support
  - [ ] Performance & Scalability
- [ ] **Production Readiness Validation**

## 🚀 Pre-Testing Setup

### 1. Environment Preparation

```bash
# 1. Clean up any existing containers
docker-compose down -v
docker system prune -f

# 2. Create environment file
cp docker/env.template .env

# 3. Generate secure secrets
echo "SESSION_SECRET=$(openssl rand -base64 32)" >> .env
echo "ENCRYPTION_KEY=$(openssl rand -base64 32)" >> .env
echo "REDIS_PASSWORD=$(openssl rand -base64 16)" >> .env

# 4. Set basic configuration
cat >> .env << EOF
POSTGRES_PASSWORD=test_secure_password_123
OPENAI_API_KEY=sk-test_key_for_testing
NODE_ENV=development
LOG_LEVEL=debug
METRICS_ENABLED=true
APM_ENABLED=true
EOF
```

### 2. Install Testing Dependencies

```bash
# Install required tools
npm install --save-dev curl-cli
npm install --save-dev wait-port

# Or use system packages
# Ubuntu/Debian: apt-get install curl jq
# macOS: brew install curl jq
# Windows: choco install curl jq
```

## 🧪 Phase 1 Testing: Critical Completions

### ✅ Test 1.1: Redis Session Integration

```bash
echo "🔄 Testing Redis Session Integration..."

# Start development environment
npm run docker:dev

# Wait for services to be ready
sleep 30

# Test 1: Verify Redis is accessible
echo "Testing Redis connectivity..."
docker exec meetsync-redis-dev redis-cli -a $(grep REDIS_PASSWORD .env | cut -d'=' -f2) ping
# Expected: PONG

# Test 2: Check session endpoint
echo "Testing session status endpoint..."
curl -s http://localhost:8080/api/system/info | jq '.session'
# Expected: {"type": "redis", "connected": true, ...}

# Test 3: Session persistence test
echo "Testing session persistence..."
SESSION_ID=$(curl -s -c cookies.txt http://localhost:8080/api/health | grep -o 'meetsync.sid=[^;]*' || echo "")
echo "Session ID: $SESSION_ID"

# Restart app container (Redis stays up)
docker-compose -f docker-compose.dev.yml restart meetsync-app

# Wait for restart
sleep 15

# Check if session persists
curl -s -b cookies.txt http://localhost:8080/api/system/info | jq '.session'
# Expected: Session should show Redis connectivity

# Cleanup
rm -f cookies.txt
```

**✅ Success Criteria:**
- Redis responds with PONG
- Session status shows "redis" type and "connected": true
- Sessions persist across app container restarts

### ✅ Test 1.2: Health Check Robustness

```bash
echo "🔄 Testing Health Check Robustness..."

# Test 1: Basic health check
echo "Testing basic health endpoint..."
curl -s http://localhost:8080/health | jq '.'
# Expected: {"status": "healthy", "checks": {...}}

# Test 2: API health check
echo "Testing API health endpoint..."
curl -s http://localhost:8080/api/health | jq '.checks'
# Expected: database, sessions, metrics, memory checks

# Test 3: Container health check
echo "Testing container health status..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep healthy
# Expected: All containers should show "healthy"

# Test 4: Health check failure simulation
echo "Testing health check resilience..."
# Stop database temporarily
docker-compose -f docker-compose.dev.yml stop postgres

# Wait a moment
sleep 10

# Check health status (should show degraded)
curl -s http://localhost:8080/health | jq '.status'
# Expected: "degraded" or "unhealthy"

# Restart database
docker-compose -f docker-compose.dev.yml start postgres
sleep 15

# Check health recovery
curl -s http://localhost:8080/health | jq '.status'
# Expected: "healthy"
```

**✅ Success Criteria:**
- Health endpoints return proper JSON responses
- Container health checks pass
- Health status correctly reflects service state
- Health recovers after service restoration

### ✅ Test 1.3: Documentation Consistency

```bash
echo "🔄 Testing Documentation Consistency..."

# Test 1: Verify development ports
echo "Testing development port mappings..."
netstat -tulpn | grep :5433  # Dev PostgreSQL
netstat -tulpn | grep :6380  # Dev Redis
netstat -tulpn | grep :8080  # Application

# Test 2: Environment variable validation
echo "Testing environment configuration..."
docker exec meetsync-app-dev env | grep -E "(POSTGRES|REDIS|SESSION)" | sort

# Test 3: Volume mounts in development
echo "Testing development volume mounts..."
docker exec meetsync-app-dev ls -la /app/client/
docker exec meetsync-app-dev ls -la /app/server/
# Expected: Source code should be present and writable
```

**✅ Success Criteria:**
- Development ports are correctly mapped (5433, 6380, 8080)
- Environment variables are properly set
- Volume mounts work for development

## 🧪 Phase 2 Testing: Production Enhancements

### ✅ Test 2.1: Monitoring & Observability

```bash
echo "🔄 Testing Monitoring & Observability..."

# Test 1: Prometheus metrics endpoint
echo "Testing Prometheus metrics..."
curl -s http://localhost:8080/metrics | head -20
# Expected: Prometheus format metrics

# Test 2: System information endpoint
echo "Testing system info endpoint..."
curl -s http://localhost:8080/api/system/info | jq '.'
# Expected: Complete system information

# Test 3: Structured logging
echo "Testing structured logging..."
docker logs meetsync-app-dev --tail 50 | grep -E '"level":|"timestamp":|"service":'
# Expected: JSON formatted logs

# Test 4: Performance monitoring
echo "Testing performance monitoring..."
# Make several requests to generate metrics
for i in {1..10}; do
  curl -s http://localhost:8080/api/health > /dev/null
  sleep 0.5
done

# Check metrics for HTTP requests
curl -s http://localhost:8080/metrics | grep "http_requests_total"
curl -s http://localhost:8080/metrics | grep "http_request_duration_seconds"
# Expected: HTTP metrics should show request counts and durations

# Test 5: APM transaction logging
echo "Testing APM transaction logging..."
curl -s http://localhost:8080/api/system/info > /dev/null
docker logs meetsync-app-dev --tail 10 | grep "APM Transaction completed"
# Expected: APM transaction logs
```

**✅ Success Criteria:**
- Metrics endpoint returns Prometheus format data
- System info endpoint provides comprehensive data
- Logs are structured in JSON format
- HTTP request metrics are collected
- APM transactions are logged

### ✅ Test 2.2: Backup & Recovery

```bash
echo "🔄 Testing Backup & Recovery..."

# Test 1: Manual backup execution
echo "Testing manual backup..."
docker exec meetsync-app-dev mkdir -p /app/backups
docker exec meetsync-app-dev /bin/sh /app/docker/scripts/backup.sh --dir /app/backups --retention 7

# Check backup files
docker exec meetsync-app-dev ls -la /app/backups/
# Expected: Backup files with timestamps

# Test 2: Backup verification
echo "Testing backup verification..."
BACKUP_FILE=$(docker exec meetsync-app-dev ls /app/backups/ | grep "meetsync_backup_.*\.sql\.gz" | head -1)
if [ ! -z "$BACKUP_FILE" ]; then
  docker exec meetsync-app-dev /bin/sh /app/docker/scripts/backup.sh --verify-only "/app/backups/$BACKUP_FILE"
fi
# Expected: Backup verification should pass

# Test 3: Database connectivity test
echo "Testing database backup connectivity..."
docker exec meetsync-postgres-dev pg_isready -U meetsync -d meetsync
# Expected: Database accepts connections

# Test 4: Test file backup (if uploads exist)
echo "Testing file backup creation..."
docker exec meetsync-app-dev touch /app/uploads/test-file.txt
docker exec meetsync-app-dev /bin/sh /app/docker/scripts/backup.sh --dir /app/backups --retention 7
docker exec meetsync-app-dev ls -la /app/backups/ | grep "_files.tar.gz"
# Expected: File backup should be created
```

**✅ Success Criteria:**
- Backup script executes without errors
- Database and file backups are created
- Backup verification passes
- Backup files have correct timestamps and naming

### ✅ Test 2.3: Security Hardening

```bash
echo "🔄 Testing Security Hardening..."

# Test 1: Non-root user execution
echo "Testing non-root user execution..."
docker exec meetsync-app-dev whoami
docker exec meetsync-app-dev id
# Expected: User should be "meetsync" with UID 1001

# Test 2: File permissions
echo "Testing file permissions..."
docker exec meetsync-app-dev ls -la /app/ | head -10
# Expected: Files should be owned by meetsync:nodejs

# Test 3: Network isolation
echo "Testing network isolation..."
docker network ls | grep meetsync
docker exec meetsync-app-dev nslookup postgres
docker exec meetsync-app-dev nslookup redis
# Expected: Services can resolve each other within network

# Test 4: Resource limits verification
echo "Testing resource limits..."
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep meetsync
# Expected: Resource usage should be within configured limits

# Test 5: Secrets handling
echo "Testing secrets handling..."
docker exec meetsync-app-dev env | grep -E "(PASSWORD|SECRET|KEY)" | grep -v "="
# Expected: Should not show actual secret values in plain text
```

**✅ Success Criteria:**
- Application runs as non-root user (meetsync:1001)
- File permissions are properly set
- Network isolation works correctly
- Resource limits are enforced
- Secrets are properly handled

## 🧪 Phase 3 Testing: Multi-Environment Support

### ✅ Test 3.1: Development Environment

```bash
echo "🔄 Testing Development Environment..."

# Stop current development environment
docker-compose -f docker-compose.dev.yml down

# Test 1: Development environment startup
echo "Testing development environment..."
npm run docker:dev

# Wait for startup
sleep 30

# Test 2: Hot reload functionality
echo "Testing hot reload..."
# Create a test change in a visible file
echo "console.log('Hot reload test - $(date)');" >> server/index.ts

# Wait for rebuild
sleep 10

# Check logs for reload indication
docker logs meetsync-app-dev --tail 20 | grep -E "(restart|reload|change)" || echo "Check for reload manually"

# Revert the change
git checkout -- server/index.ts

# Test 3: Development-specific features
echo "Testing development features..."
curl -s http://localhost:8080/api/system/info | jq '.environment'
# Expected: "development"

# Test 4: Database access on development port
echo "Testing development database access..."
timeout 5 bash -c 'cat < /dev/null > /dev/tcp/localhost/5433' && echo "Port 5433 accessible" || echo "Port 5433 not accessible"

# Test 5: Redis access on development port
echo "Testing development Redis access..."
timeout 5 bash -c 'cat < /dev/null > /dev/tcp/localhost/6380' && echo "Port 6380 accessible" || echo "Port 6380 not accessible"
```

### ✅ Test 3.2: Production Environment

```bash
echo "🔄 Testing Production Environment..."

# Stop development environment
docker-compose -f docker-compose.dev.yml down

# Test 1: Production environment startup
echo "Testing production environment..."
NODE_ENV=production npm run docker:prod

# Wait for startup
sleep 45

# Test 2: Production optimizations
echo "Testing production optimizations..."
curl -s http://localhost:8080/api/system/info | jq '.environment'
# Expected: "production"

# Test 3: Production health checks
echo "Testing production health checks..."
curl -s http://localhost:8080/health | jq '.status'
# Expected: "healthy"

# Test 4: Production metrics
echo "Testing production metrics..."
curl -s http://localhost:8080/metrics | grep -c "^# HELP"
# Expected: Should have multiple metric definitions

# Test 5: Production security headers (if implemented)
echo "Testing production response headers..."
curl -I http://localhost:8080/health | grep -E "(X-|Cache-|Security-)"
# Expected: Security headers if implemented

# Cleanup
docker-compose down
```

### ✅ Test 3.3: Staging Environment

```bash
echo "🔄 Testing Staging Environment..."

# Test 1: Staging environment startup
echo "Testing staging environment..."
docker-compose -f docker-compose.staging.yml up -d

# Wait for startup
sleep 60

# Test 2: Staging-specific monitoring
echo "Testing staging monitoring stack..."
curl -s http://localhost:9090/api/v1/label/__name__/values | jq '.' > /dev/null && echo "Prometheus accessible" || echo "Prometheus not accessible"
curl -s http://localhost:3001/api/health | jq '.' > /dev/null && echo "Grafana accessible" || echo "Grafana not accessible"

# Test 3: Staging environment configuration
echo "Testing staging configuration..."
docker exec meetsync-app-staging env | grep NODE_ENV
# Expected: NODE_ENV=staging

# Test 4: Staging backup service
echo "Testing staging backup configuration..."
docker-compose -f docker-compose.staging.yml exec backup /bin/sh /app/docker/scripts/backup.sh --help
# Expected: Backup script help output

# Cleanup
docker-compose -f docker-compose.staging.yml down
```

## 🧪 Performance & Load Testing

### ✅ Test 4.1: Performance Validation

```bash
echo "🔄 Testing Performance & Load Handling..."

# Start development environment
npm run docker:dev
sleep 30

# Test 1: Basic load test
echo "Running basic load test..."
for i in {1..50}; do
  curl -s http://localhost:8080/api/health > /dev/null &
  if (( i % 10 == 0 )); then
    wait  # Wait for batch to complete
    echo "Completed $i requests"
  fi
done
wait

# Test 2: Check metrics after load
echo "Checking metrics after load test..."
curl -s http://localhost:8080/metrics | grep "http_requests_total" | tail -5
curl -s http://localhost:8080/metrics | grep "http_request_duration_seconds_bucket" | tail -5

# Test 3: Memory usage check
echo "Checking memory usage..."
docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}\t{{.MemPerc}}" | grep meetsync

# Test 4: Response time test
echo "Testing response times..."
for i in {1..5}; do
  time curl -s http://localhost:8080/api/health > /dev/null
done

# Test 5: Concurrent connections test
echo "Testing concurrent connections..."
for i in {1..10}; do
  curl -s http://localhost:8080/api/system/info > /dev/null &
done
wait
echo "Concurrent test completed"
```

## 🧪 Integration Testing

### ✅ Test 5.1: Service Integration

```bash
echo "🔄 Testing Service Integration..."

# Test 1: Database integration
echo "Testing database integration..."
curl -s http://localhost:8080/api/health | jq '.checks.database'
# Expected: {"status": "healthy", "responseTime": <number>}

# Test 2: Redis integration
echo "Testing Redis integration..."
curl -s http://localhost:8080/api/health | jq '.checks.sessions'
# Expected: {"type": "redis", "connected": true, ...}

# Test 3: Session flow test
echo "Testing complete session flow..."
# Create session
SESSION_COOKIE=$(curl -s -c /tmp/session_test.txt -b /tmp/session_test.txt http://localhost:8080/api/health | grep -o 'Set-Cookie: [^;]*' | cut -d' ' -f2 || echo "")
echo "Session cookie: $SESSION_COOKIE"

# Use session
if [ ! -z "$SESSION_COOKIE" ]; then
  curl -s -H "Cookie: $SESSION_COOKIE" http://localhost:8080/api/system/info | jq '.session' > /dev/null && echo "Session flow working" || echo "Session flow issue"
fi

# Cleanup
rm -f /tmp/session_test.txt

# Test 4: Cross-service communication
echo "Testing cross-service communication..."
docker exec meetsync-app-dev nslookup postgres
docker exec meetsync-app-dev nslookup redis
# Expected: Both should resolve successfully
```

## 🎯 Quick Validation Commands

For quick validation, run these essential tests:

```bash
# Quick health check
curl -s http://localhost:8080/health | jq '.status'

# Quick metrics check
curl -s http://localhost:8080/metrics | grep -c "^# HELP"

# Quick session check
curl -s http://localhost:8080/api/system/info | jq '.session'

# Quick container status
docker ps --format "table {{.Names}}\t{{.Status}}" | grep healthy

# Quick resource usage
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```

## 🚀 Running All Tests

Execute the complete validation suite using the next script I'll create.

## 📊 Expected Test Results

**All tests should show:**
- ✅ Health endpoints return "healthy" status
- ✅ Metrics endpoint provides Prometheus data
- ✅ Redis sessions are working and persistent
- ✅ Database connectivity is healthy
- ✅ Container health checks pass
- ✅ Non-root user execution
- ✅ Load tests complete without errors
- ✅ All environments start successfully

**If any test fails:**
1. Check the specific error logs
2. Verify environment configuration
3. Ensure all required services are running
4. Check Docker resource availability
5. Review the troubleshooting section in README-Docker.md

Your Docker setup is **production-ready** when all tests pass! 🎉 