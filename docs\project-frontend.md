# Frontend (React)

Generated on: 2025-05-26T19:05:21.808Z

## Key Files

### client/src/App.tsx
Size: 1.60 KB

```tsx
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";

// Import Pages
import Dashboard from "@/pages/dashboard";
import Integrations from "@/pages/integrations";
import IntegrationSetupPage from "@/pages/integrations/setup";
import IntegrationSetupAlt from "@/pages/integration-setup";
import Schedules from "@/pages/schedules";
import Logs from "@/pages/logs";
import Settings from "@/pages/settings";
import DebugPage from "@/pages/debug";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Dashboard} />
      <Route path="/dashboard" component={Dashboard} />
      <Route path="/integrations" component={Integrations} />
      <Route path="/integrations/:id/setup" component={IntegrationSetupPage} />
      {/* Alternative route for compatibility */}
      <Route path="/integration-setup" component={IntegrationSetupAlt} />
      <Route path="/schedules" component={Schedules} />
      <Route path="/logs" component={Logs} />
      <Route path="/settings" component={Settings} />
      <Route path="/debug" component={DebugPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;

```

### client/src/main.tsx
Size: 0.15 KB

```tsx
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

createRoot(document.getElementById("root")!).render(<App />);

```

### client/src/index.css
Size: 1.32 KB

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
      --background: 0 0% 100%;
--foreground: 20 14.3% 4.1%;
--muted: 60 4.8% 95.9%;
--muted-foreground: 25 5.3% 44.7%;
--popover: 0 0% 100%;
--popover-foreground: 20 14.3% 4.1%;
--card: 0 0% 100%;
--card-foreground: 20 14.3% 4.1%;
--border: 20 5.9% 90%;
--input: 20 5.9% 90%;
--primary: 207 90% 54%;
--primary-foreground: 211 100% 99%;
--secondary: 60 4.8% 95.9%;
--secondary-foreground: 24 9.8% 10%;
--accent: 60 4.8% 95.9%;
--accent-foreground: 24 9.8% 10%;
--destructive: 0 84.2% 60.2%;
--destructive-foreground: 60 9.1% 97.8%;
--ring: 20 14.3% 4.1%;
--radius: 0.5rem;
  }
  .dark {
      --background: 240 10% 3.9%;
--foreground: 0 0% 98%;
--muted: 240 3.7% 15.9%;
--muted-foreground: 240 5% 64.9%;
--popover: 240 10% 3.9%;
--popover-foreground: 0 0% 98%;
--card: 240 10% 3.9%;
--card-foreground: 0 0% 98%;
--border: 240 3.7% 15.9%;
--input: 240 3.7% 15.9%;
--primary: 207 90% 54%;
--primary-foreground: 211 100% 99%;
--secondary: 240 3.7% 15.9%;
--secondary-foreground: 0 0% 98%;
--accent: 240 3.7% 15.9%;
--accent-foreground: 0 0% 98%;
--destructive: 0 62.8% 30.6%;
--destructive-foreground: 0 0% 98%;
--ring: 240 4.9% 83.9%;
--radius: 0.5rem;
  }

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}
```

### vite.config.ts
Size: 0.87 KB

```ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";

export default defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...(process.env.NODE_ENV !== "production" &&
    process.env.REPL_ID !== undefined
      ? [
          await import("@replit/vite-plugin-cartographer").then((m) =>
            m.cartographer(),
          ),
        ]
      : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets"),
    },
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true,
  },
});

```

### tailwind.config.ts
Size: 2.72 KB

```ts
import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: ["./client/index.html", "./client/src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
} satisfies Config;

```

## Directory Structure

### client\src/
- App.tsx (1.60 KB)
- main.tsx (0.15 KB)

### client\src\components\debug/
- ApiTester.tsx (6.76 KB)
- SystemStatus.tsx (5.42 KB)

### client\src\components\integrations/
- IntegrationCard.tsx (12.55 KB)
- IntegrationWizard.tsx (26.36 KB)
- SyncPreview.tsx (5.25 KB)

### client\src\components\layout/
- AppLayout.tsx (0.89 KB)
- Header.tsx (2.45 KB)
- Sidebar.tsx (6.35 KB)

### client\src\components\ui/
- accordion.tsx (1.93 KB)
- alert-dialog.tsx (4.32 KB)
- alert.tsx (1.55 KB)
- aspect-ratio.tsx (0.14 KB)
- avatar.tsx (1.39 KB)
- badge.tsx (1.10 KB)
- breadcrumb.tsx (2.65 KB)
- button.tsx (1.86 KB)
- calendar.tsx (2.63 KB)
- card.tsx (1.81 KB)
- carousel.tsx (6.06 KB)
- chart.tsx (10.24 KB)
- checkbox.tsx (1.03 KB)
- collapsible.tsx (0.32 KB)
- command.tsx (4.77 KB)
- context-menu.tsx (7.25 KB)
- dialog.tsx (3.76 KB)
- drawer.tsx (2.95 KB)
- dropdown-menu.tsx (7.43 KB)
- form.tsx (4.02 KB)
- hover-card.tsx (1.22 KB)
- input-otp.tsx (2.10 KB)
- input.tsx (0.77 KB)
- label.tsx (0.69 KB)
- menubar.tsx (8.40 KB)
- navigation-menu.tsx (5.01 KB)
- pagination.tsx (2.69 KB)
- popover.tsx (1.25 KB)
- progress.tsx (0.77 KB)
- radio-group.tsx (1.43 KB)
- resizable.tsx (1.68 KB)
- scroll-area.tsx (1.60 KB)
- select.tsx (5.61 KB)
- separator.tsx (0.74 KB)
- sheet.tsx (4.18 KB)
- sidebar.tsx (23.01 KB)
- skeleton.tsx (0.25 KB)
- slider.tsx (1.05 KB)
- switch.tsx (1.11 KB)
- table.tsx (2.70 KB)
- tabs.tsx (1.84 KB)
- textarea.tsx (0.67 KB)
- toast.tsx (4.73 KB)
- toaster.tsx (0.75 KB)
- toggle-group.tsx (1.71 KB)
- toggle.tsx (1.49 KB)
- tooltip.tsx (1.18 KB)

### client\src\hooks/
- use-mobile.tsx (0.55 KB)
- use-toast.ts (3.80 KB)
- use-websocket.ts (8.76 KB)

### client\src\lib/
- api.ts (2.45 KB)
- queryClient.ts (1.34 KB)
- utils.ts (0.16 KB)

### client\src\lib\utils/
- date.ts (1.44 KB)
- integrations.ts (2.88 KB)

### client\src\pages/
- dashboard.tsx (22.96 KB)
- debug.tsx (0.44 KB)
- integration-setup.tsx (13.24 KB)
- integrations.tsx (6.06 KB)
- logs.tsx (9.19 KB)
- not-found.tsx (0.69 KB)
- schedules.tsx (9.54 KB)
- settings.tsx (16.58 KB)

### client\src\pages\integrations/
- setup.tsx (27.27 KB)

## File Contents

### client\src\App.tsx

```tsx
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";

// Import Pages
import Dashboard from "@/pages/dashboard";
import Integrations from "@/pages/integrations";
import IntegrationSetupPage from "@/pages/integrations/setup";
import IntegrationSetupAlt from "@/pages/integration-setup";
import Schedules from "@/pages/schedules";
import Logs from "@/pages/logs";
import Settings from "@/pages/settings";
import DebugPage from "@/pages/debug";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Dashboard} />
      <Route path="/dashboard" component={Dashboard} />
      <Route path="/integrations" component={Integrations} />
      <Route path="/integrations/:id/setup" component={IntegrationSetupPage} />
      {/* Alternative route for compatibility */}
      <Route path="/integration-setup" component={IntegrationSetupAlt} />
      <Route path="/schedules" component={Schedules} />
      <Route path="/logs" component={Logs} />
      <Route path="/settings" component={Settings} />
      <Route path="/debug" component={DebugPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;

```

### client\src\components\debug\ApiTester.tsx

```tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ApiResponse {
  status: number;
  data: any;
  error?: string;
  timestamp: string;
}

export default function ApiTester() {
  const [responses, setResponses] = useState<{ [key: string]: ApiResponse }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});

  const makeRequest = async (endpoint: string, method: string = 'GET', body?: any) => {
    const key = `${method} ${endpoint}`;
    setLoading(prev => ({ ...prev, [key]: true }));

    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (body) {
        options.body = JSON.stringify(body);
      }

      const response = await fetch(endpoint, options);
      const data = await response.json();

      setResponses(prev => ({
        ...prev,
        [key]: {
          status: response.status,
          data,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (error: any) {
      setResponses(prev => ({
        ...prev,
        [key]: {
          status: 0,
          data: null,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [key]: false }));
    }
  };

  const testEndpoints = [
    {
      name: 'Health Check',
      endpoint: '/api/diagnostic/health',
      method: 'GET'
    },
    {
      name: 'Test Encryption',
      endpoint: '/api/diagnostic/test-encryption',
      method: 'POST',
      body: { testData: 'Hello, encryption test!' }
    },
    {
      name: 'Test Credential Flow',
      endpoint: '/api/diagnostic/test-credential-flow',
      method: 'POST'
... (truncated)
```

### client\src\components\debug\SystemStatus.tsx

```tsx
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface SystemHealth {
  api: 'healthy' | 'error' | 'unknown';
  storage: 'working' | 'error' | 'unknown';
  integrations: number;
  lastChecked: string;
  details?: any;
}

export default function SystemStatus() {
  const [health, setHealth] = useState<SystemHealth>({
    api: 'unknown',
    storage: 'unknown',
    integrations: 0,
    lastChecked: 'Never'
  });
  const [checking, setChecking] = useState(false);

  const checkSystemHealth = async () => {
    setChecking(true);
    try {
      // Test API health
      const healthResponse = await fetch('/api/diagnostic/health');
      const healthData = await healthResponse.json();

      // Test integrations
      const integrationsResponse = await fetch('/api/integrations');
      const integrationsData = await integrationsResponse.json();

      setHealth({
        api: healthResponse.ok ? 'healthy' : 'error',
        storage: healthData.services?.storage || 'unknown',
        integrations: integrationsData.integrations?.length || 0,
        lastChecked: new Date().toLocaleTimeString(),
        details: {
          health: healthData,
          integrations: integrationsData
        }
      });

    } catch (error: any) {
      setHealth(prev => ({
        ...prev,
        api: 'error',
        storage: 'error',
        lastChecked: new Date().toLocaleTimeString(),
        details: { error: error.message }
      }));
    } finally {
      setChecking(false);
    }
  };

  useEffect(() => {
    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);
... (truncated)
```

### client\src\components\integrations\SyncPreview.tsx

```tsx
import { Button } from "@/components/ui/button";

interface SyncPreviewProps {
  syncItem: {
    title: string;
    sourceUrl: string;
    metadata: {
      date: string;
      time: string;
      meetingName: string;
      attendees: string;
    };
    content?: {
      notes: string;
      transcript: string;
    };
  };
  onClose: () => void;
  onConfirm: () => void;
  onSkip: () => void;
}

export default function SyncPreview({
  syncItem,
  onClose,
  onConfirm,
  onSkip,
}: SyncPreviewProps) {
  const { title, sourceUrl, metadata, content } = syncItem;
  const [activeTab, setActiveTab] = React.useState("Notes");

  return (
    <section>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Sync Preview</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onClose}>
            Close Preview
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6">
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h4 className="text-base font-medium text-gray-900 mb-1">Transcript Details</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Original filename</p>
                <p className="text-sm font-medium">{title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Source</p>
                <p className="text-sm font-medium">Shared Meeting Transcripts Drive</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Extracted date</p>
                <p className="text-sm font-medium">{metadata.date}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Extracted time</p>
                <p className="text-sm font-medium">{metadata.time}</p>
          
... (truncated)
```

### client\src\components\layout\AppLayout.tsx

```tsx
import { ReactNode } from "react";
import Sidebar from "./Sidebar";
import Header from "./Header";

interface AppLayoutProps {
  children: ReactNode;
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function AppLayout({
  children,
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: AppLayoutProps) {
  return (
    <div className="bg-gray-100 h-screen flex overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title={title} 
          onAddIntegration={onAddIntegration}
          tabs={tabs}
          currentTab={currentTab}
          onTabChange={onTabChange}
        />
        
        <main className="flex-1 overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}

```

### client\src\components\layout\Header.tsx

```tsx
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

interface HeaderProps {
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function Header({
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: HeaderProps) {
  const [location] = useLocation();
  
  return (
    <header className="bg-white shadow-sm z-10">
      <div className="flex items-center justify-between px-6 py-3">
        <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
        <div className="flex items-center space-x-3">
          {onAddIntegration && location.includes("integrations") && (
            <button 
              onClick={onAddIntegration}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 flex items-center font-medium"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Integration
            </button>
          )}
          <button className="p-2 rounded-full hover:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
          </button>
        </div>
      </div>
      
      {tabs && tabs.length > 0 && (
        <div className="px-6 py-2 border-b border-gray-200 bg-gray-50">
          <div className="flex space-x-5">
            {tabs.map((tab) => (
        
... (truncated)
```

### client\src\components\layout\Sidebar.tsx

```tsx
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

export default function Sidebar() {
  const [location] = useLocation();
  
  return (
    <aside className="w-64 bg-white shadow-md z-10 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h1 className="text-lg font-bold text-primary flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
          </svg>
          Transcript Repository
        </h1>
      </div>
      
      <nav className="flex-1 overflow-y-auto pt-2">
        <ul>
          <li>
            <Link href="/dashboard">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer",
                location === "/dashboard" 
                  ? "active-integration text-primary font-medium" 
                  : "text-gray-700"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/dashboard" ? "text-primary" : "text-gray-500"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                Dashboard
              </div>
            </Link>
          </li>
          <li>
            <Link href="/integrations">
              
... (truncated)
```

### client\src\components\ui\accordion.tsx

```tsx
import * as React from "react"
import * as AccordionPrimitive from "@radix-ui/react-accordion"
import { ChevronDown } from "lucide-react"

import { cn } from "@/lib/utils"

const Accordion = AccordionPrimitive.Root

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn("border-b", className)}
    {...props}
  />
))
AccordionItem.displayName = "AccordionItem"

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        "flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",
        className
      )}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
))
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    {...props}
  >
    <div className={cn("pb-4 pt-0", className)}>{children}</div>
  </AccordionPrimitive.Content>
))

AccordionContent.displayName = AccordionPrimitive.Content.displayName

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent }

```

### client\src\components\ui\alert-dialog.tsx

```tsx
import * as React from "react"
import * as AlertDialogPrimitive from "@radix-ui/react-alert-dialog"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

const AlertDialog = AlertDialogPrimitive.Root

const AlertDialogTrigger = AlertDialogPrimitive.Trigger

const AlertDialogPortal = AlertDialogPrimitive.Portal

const AlertDialogOverlay = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Overlay
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
    ref={ref}
  />
))
AlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName

const AlertDialogContent = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>
>(({ className, ...props }, ref) => (
  <AlertDialogPortal>
    <AlertDialogOverlay />
    <AlertDialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    />
  </AlertDialogPortal>
))
AlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName

const AlertDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>
... (truncated)
```

### client\src\components\ui\alert.tsx

```tsx
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }

```

