import { Client } from "@notionhq/client";
import { BaseService } from "../../base/service.interface";

/**
 * Notion Page Service - handles page operations and management
 */
export class NotionPageService extends BaseService {
  constructor() {
    super('NotionPage', '1.0.0', 'Notion page operations and management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Notion Page service initialized');
  }

  protected getDependencies(): string[] {
    return ['NotionAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      supportedOperations: ['create', 'retrieve', 'update', 'delete'],
      supportedBlocks: ['paragraph', 'heading', 'callout', 'divider'],
    };
  }

  /**
   * Get a page by ID
   */
  async getPage(client: Client, pageId: string): Promise<any> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');

    try {
      return await client.pages.retrieve({ page_id: pageId });
    } catch (error: any) {
      this.handleError(error, `retrieving page ${pageId}`);
    }
  }

  /**
   * Create a new page in a database
   */
  async createPageInDatabase(
    client: Client,
    databaseId: string,
    properties: any,
    children?: any[]
  ): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');
    this.validateRequired(properties, 'page properties');

    try {
      this.log('info', `Creating page in database ${databaseId}`);

      const response = await client.pages.create({
        parent: { database_id: databaseId },
        properties,
        children: children || [],
      });

      this.log('info', `Created page: ${response.id}`);
      return response.id;
    } catch (error: any) {
      this.handleError(error, `creating page in database ${databaseId}`);
    }
  }

  /**
   * Create a new page as a child of another page
   */
  async createChildPage(
    client: Client,
    parentPageId: string,
    title: string,
    children?: any[]
  ): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(parentPageId, 'parent page ID');
    this.validateString(title, 'page title');

    try {
      this.log('info', `Creating child page "${title}" under ${parentPageId}`);

      const response = await client.pages.create({
        parent: { page_id: parentPageId },
        properties: {
          title: [{ type: "text", text: { content: title } }],
        },
        children: children || [],
      });

      this.log('info', `Created child page: ${response.id}`);
      return response.id;
    } catch (error: any) {
      this.handleError(error, `creating child page "${title}"`);
    }
  }

  /**
   * Update page properties
   */
  async updatePage(
    client: Client,
    pageId: string,
    properties: any
  ): Promise<any> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');
    this.validateRequired(properties, 'page properties');

    try {
      const response = await client.pages.update({
        page_id: pageId,
        properties,
      });

      this.log('info', `Updated page: ${pageId}`);
      return response;
    } catch (error: any) {
      this.handleError(error, `updating page ${pageId}`);
    }
  }

  /**
   * Get page blocks (content)
   */
  async getPageBlocks(
    client: Client,
    pageId: string,
    startCursor?: string,
    pageSize: number = 100
  ): Promise<{
    results: any[];
    nextCursor?: string;
    hasMore: boolean;
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');
    this.validateNumber(pageSize, 'page size', 1, 100);

    try {
      const response = await client.blocks.children.list({
        block_id: pageId,
        start_cursor: startCursor,
        page_size: pageSize,
      });

      return {
        results: response.results,
        nextCursor: response.next_cursor || undefined,
        hasMore: response.has_more,
      };
    } catch (error: any) {
      this.handleError(error, `getting blocks for page ${pageId}`);
    }
  }

  /**
   * Add blocks to a page
   */
  async addBlocksToPage(
    client: Client,
    pageId: string,
    blocks: any[]
  ): Promise<any> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');
    this.validateRequired(blocks, 'blocks');

    try {
      // Notion has a limit of 100 blocks per request
      const maxBlocksPerRequest = 100;
      const results = [];

      for (let i = 0; i < blocks.length; i += maxBlocksPerRequest) {
        const batch = blocks.slice(i, i + maxBlocksPerRequest);
        
        this.log('info', `Adding ${batch.length} blocks to page ${pageId} (batch ${Math.floor(i / maxBlocksPerRequest) + 1})`);
        
        const response = await client.blocks.children.append({
          block_id: pageId,
          children: batch,
        });
        
        results.push(response);
      }

      this.log('info', `Added ${blocks.length} blocks to page ${pageId} in ${results.length} batches`);
      return results;
    } catch (error: any) {
      this.handleError(error, `adding blocks to page ${pageId}`);
    }
  }

  /**
   * Delete a page
   */
  async deletePage(client: Client, pageId: string): Promise<boolean> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');

    try {
      await client.pages.update({
        page_id: pageId,
        archived: true,
      });

      this.log('info', `Archived page: ${pageId}`);
      return true;
    } catch (error: any) {
      this.log('error', `Error archiving page ${pageId}`, error);
      return false;
    }
  }

  /**
   * Search pages
   */
  async searchPages(
    client: Client,
    query: string,
    filter?: any,
    sort?: any,
    startCursor?: string,
    pageSize: number = 100
  ): Promise<{
    results: any[];
    nextCursor?: string;
    hasMore: boolean;
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(query, 'search query');
    this.validateNumber(pageSize, 'page size', 1, 100);

    try {
      const response = await client.search({
        query,
        filter,
        sort,
        start_cursor: startCursor,
        page_size: pageSize,
      });

      return {
        results: response.results,
        nextCursor: response.next_cursor || undefined,
        hasMore: response.has_more,
      };
    } catch (error: any) {
      this.handleError(error, `searching pages with query: ${query}`);
    }
  }

  /**
   * Get page title
   */
  getPageTitle(page: any): string {
    if (!page || !page.properties) {
      return 'Untitled';
    }

    // For database pages
    if (page.properties.Name && page.properties.Name.title) {
      return page.properties.Name.title
        .map((t: any) => t.plain_text)
        .join('') || 'Untitled';
    }

    // For regular pages
    if (page.properties.title && page.properties.title.title) {
      return page.properties.title.title
        .map((t: any) => t.plain_text)
        .join('') || 'Untitled';
    }

    return 'Untitled';
  }

  /**
   * Get page URL
   */
  getPageUrl(pageId: string): string {
    // Remove hyphens from page ID for Notion URL format
    const cleanId = pageId.replace(/-/g, '');
    return `https://notion.so/${cleanId}`;
  }

  /**
   * Extract page ID from URL
   */
  extractPageIdFromUrl(url: string): string | null {
    try {
      // Handle various Notion URL formats
      const patterns = [
        /notion\.so\/([a-f0-9]{32})/i,
        /notion\.so\/.*-([a-f0-9]{32})/i,
        /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i,
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
          let id = match[1];
          // Add hyphens if not present
          if (id.length === 32 && !id.includes('-')) {
            id = `${id.slice(0, 8)}-${id.slice(8, 12)}-${id.slice(12, 16)}-${id.slice(16, 20)}-${id.slice(20)}`;
          }
          return id;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get page metadata
   */
  async getPageMetadata(client: Client, pageId: string): Promise<{
    id: string;
    title: string;
    url: string;
    created_time: string;
    last_edited_time: string;
    archived: boolean;
    parent: any;
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');

    try {
      const page = await this.getPage(client, pageId);
      
      return {
        id: page.id,
        title: this.getPageTitle(page),
        url: page.url || this.getPageUrl(pageId),
        created_time: page.created_time,
        last_edited_time: page.last_edited_time,
        archived: page.archived || false,
        parent: page.parent,
      };
    } catch (error: any) {
      this.handleError(error, `getting metadata for page ${pageId}`);
    }
  }

  /**
   * Check if page exists and is accessible
   */
  async pageExists(client: Client, pageId: string): Promise<boolean> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');

    try {
      await this.getPage(client, pageId);
      return true;
    } catch (error: any) {
      this.log('info', `Page ${pageId} does not exist or is not accessible`);
      return false;
    }
  }
}
