import { Client } from '@notionhq/client';
import { extractPageIdFromUrl } from './utils';

if (!process.env.NOTION_INTEGRATION_SECRET) {
    console.error('NOTION_INTEGRATION_SECRET environment variable is required');
    process.exit(1);
}

if (!process.env.NOTION_PAGE_URL) {
    console.error('NOTION_PAGE_URL environment variable is required');
    process.exit(1);
}

// Initialize Notion client
const notion = new Client({
    auth: process.env.NOTION_INTEGRATION_SECRET,
});

const PARENT_PAGE_ID = extractPageIdFromUrl(process.env.NOTION_PAGE_URL);

/**
 * Check if Meeting Transcripts database already exists
 */
async function findTranscriptsDatabase(): Promise<string | null> {
    try {
        const response = await notion.blocks.children.list({
            block_id: PARENT_PAGE_ID,
        });
        
        // Look for child database blocks
        const databaseBlocks = response.results.filter(
            (block: any) => block.type === 'child_database'
        );
        
        // Check each database to find one named "Meeting Transcripts"
        for (const block of databaseBlocks) {
            const databaseInfo = await notion.databases.retrieve({
                database_id: block.id,
            });
            
            // Extract database title
            let title = '';
            if ((databaseInfo as any).title && (databaseInfo as any).title.length > 0) {
                title = (databaseInfo as any).title
                    .map((titlePart: any) => titlePart.plain_text)
                    .join('');
            }
            
            if (title.toLowerCase().includes('meeting') || 
                title.toLowerCase().includes('transcript')) {
                console.log(`Found existing Meeting Transcripts database: ${block.id}`);
                return block.id;
            }
        }
        
        return null;
    } catch (error) {
        console.error('Error finding Meeting Transcripts database:', error);
        return null;
    }
}

/**
 * Create a database for meeting transcripts
 */
async function setupTranscriptsDatabase(): Promise<string> {
    try {
        // Check if database already exists
        const existingDatabaseId = await findTranscriptsDatabase();
        if (existingDatabaseId) {
            return existingDatabaseId;
        }
        
        console.log('Creating Meeting Transcripts database...');
        
        // Create the database
        const response = await notion.databases.create({
            parent: {
                type: 'page_id',
                page_id: PARENT_PAGE_ID,
            },
            title: [
                {
                    type: 'text',
                    text: {
                        content: 'Meeting Transcripts',
                    },
                },
            ],
            properties: {
                Title: {
                    title: {},
                },
                Date: {
                    date: {},
                },
                Platform: {
                    select: {
                        options: [
                            { name: 'Google Meet', color: 'blue' },
                            { name: 'Google Chat', color: 'green' },
                            { name: 'Microsoft Teams', color: 'purple' },
                            { name: 'Zoom', color: 'orange' },
                            { name: 'Slack', color: 'pink' },
                            { name: 'Other', color: 'gray' },
                        ],
                    },
                },
                Participants: {
                    multi_select: {
                        options: [],
                    },
                },
                Topics: {
                    multi_select: {
                        options: [],
                    },
                },
                Departments: {
                    multi_select: {
                        options: [
                            { name: 'Engineering', color: 'blue' },
                            { name: 'Product', color: 'green' },
                            { name: 'Design', color: 'orange' },
                            { name: 'Marketing', color: 'yellow' },
                            { name: 'Sales', color: 'pink' },
                            { name: 'Customer Support', color: 'purple' },
                            { name: 'Finance', color: 'gray' },
                            { name: 'Human Resources', color: 'red' },
                            { name: 'Legal', color: 'brown' },
                            { name: 'Operations', color: 'blue' },
                            { name: 'Research & Development', color: 'green' },
                            { name: 'Executive', color: 'gray' },
                            { name: 'IT', color: 'purple' },
                            { name: 'Data Science', color: 'yellow' },
                            { name: 'Uncategorized', color: 'default' },
                        ],
                    },
                },
                Tags: {
                    multi_select: {
                        options: [],
                    },
                },
                Duration: {
                    number: {},
                },
                SourceId: {
                    rich_text: {},
                },
                SourceURL: {
                    url: {},
                },
                SyncSource: {
                    rich_text: {},
                },
                Status: {
                    select: {
                        options: [
                            { name: 'Processed', color: 'green' },
                            { name: 'Needs Review', color: 'yellow' },
                            { name: 'Archived', color: 'gray' },
                        ],
                    },
                },
            },
        });
        
        console.log(`Created Meeting Transcripts database: ${response.id}`);
        
        return response.id;
    } catch (error) {
        console.error('Error setting up Transcripts database:', error);
        throw error;
    }
}

/**
 * Create a sample transcript page for demonstration
 */
async function createSampleTranscriptPage(databaseId: string): Promise<void> {
    try {
        console.log('Creating sample transcript page...');
        
        // Create a page with sample data
        const response = await notion.pages.create({
            parent: {
                database_id: databaseId,
            },
            properties: {
                Title: {
                    title: [
                        {
                            type: 'text',
                            text: {
                                content: 'Product Team Weekly Sync',
                            },
                        },
                    ],
                },
                Date: {
                    date: {
                        start: new Date().toISOString().split('T')[0],
                    },
                },
                Platform: {
                    select: {
                        name: 'Google Meet',
                    },
                },
                Participants: {
                    multi_select: [
                        { name: 'John Smith' },
                        { name: 'Emily Johnson' },
                        { name: 'David Chen' },
                        { name: 'Sarah Williams' },
                    ],
                },
                Topics: {
                    multi_select: [
                        { name: 'Roadmap Planning' },
                        { name: 'Sprint Review' },
                        { name: 'Feature Prioritization' },
                    ],
                },
                Departments: {
                    multi_select: [
                        { name: 'Product' },
                        { name: 'Engineering' },
                        { name: 'Design' },
                    ],
                },
                Tags: {
                    multi_select: [
                        { name: 'Weekly' },
                        { name: 'Internal' },
                        { name: 'Planning' },
                    ],
                },
                Duration: {
                    number: 45,
                },
                SourceId: {
                    rich_text: [
                        {
                            type: 'text',
                            text: {
                                content: 'sample-transcript-001',
                            },
                        },
                    ],
                },
                Status: {
                    select: {
                        name: 'Processed',
                    },
                },
                SyncSource: {
                    rich_text: [
                        {
                            type: 'text',
                            text: {
                                content: 'Setup Script (Sample)',
                            },
                        },
                    ],
                },
            },
        });
        
        // Add content to the page
        await notion.blocks.children.append({
            block_id: response.id,
            children: [
                {
                    object: 'block',
                    type: 'heading_2',
                    heading_2: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'Summary',
                                },
                            },
                        ],
                    },
                },
                {
                    object: 'block',
                    type: 'paragraph',
                    paragraph: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'Weekly product team sync meeting to review sprint progress, discuss roadmap priorities, and align on upcoming feature development. The team reviewed current sprint status, discussed customer feedback, and made decisions on feature prioritization.',
                                },
                            },
                        ],
                    },
                },
                {
                    object: 'block',
                    type: 'divider',
                    divider: {},
                },
                {
                    object: 'block',
                    type: 'heading_2',
                    heading_2: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'Action Items',
                                },
                            },
                        ],
                    },
                },
                {
                    object: 'block',
                    type: 'bulleted_list_item',
                    bulleted_list_item: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'John: Prepare updated roadmap document by Friday',
                                },
                            },
                        ],
                    },
                },
                {
                    object: 'block',
                    type: 'bulleted_list_item',
                    bulleted_list_item: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'Emily: Schedule user testing sessions for new feature',
                                },
                            },
                        ],
                    },
                },
                {
                    object: 'block',
                    type: 'bulleted_list_item',
                    bulleted_list_item: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'David: Address critical bugs in current sprint',
                                },
                            },
                        ],
                    },
                },
                {
                    object: 'block',
                    type: 'divider',
                    divider: {},
                },
                {
                    object: 'block',
                    type: 'heading_2',
                    heading_2: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'Transcript',
                                },
                            },
                        ],
                    },
                },
                {
                    object: 'block',
                    type: 'paragraph',
                    paragraph: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: 'This is a sample transcript page created by the setup script. In a real transcript, this section would contain the full conversation text from the meeting.',
                                },
                            },
                        ],
                    },
                },
            ],
        });
        
        console.log(`Created sample transcript page: ${response.id}`);
    } catch (error) {
        console.error('Error creating sample transcript page:', error);
    }
}

async function main() {
    try {
        console.log(`Setting up Notion workspace with parent page: ${PARENT_PAGE_ID}`);
        
        // Create the Meeting Transcripts database
        const databaseId = await setupTranscriptsDatabase();
        
        // Create a sample transcript page
        await createSampleTranscriptPage(databaseId);
        
        console.log('Notion setup completed successfully!');
    } catch (error) {
        console.error('Error during Notion setup:', error);
        process.exit(1);
    }
}

// Run the setup process
main();