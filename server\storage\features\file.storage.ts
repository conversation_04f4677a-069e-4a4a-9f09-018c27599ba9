import { files, type File, type InsertFile } from "../../../shared/index.js";
import { eq, desc, and, sql } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config";

/**
 * File storage operations
 */
export class FileStorage {
  private db: any;
  private memoryFiles: Map<number, File>;
  private fileIdCounter: number;
  private useDatabase: boolean;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memoryFiles = new Map();
    this.fileIdCounter = 1;
    // Database connection will be initialized lazily when first needed
  }

  /**
   * Ensure database connection is available
   */
  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        // Silently fall back to memory storage
        this.useDatabase = false;
      }
    }
  }

  async getFiles(platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string): Promise<File[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(files);
      
      const conditions = [];
      if (platform) conditions.push(eq(files.platform, platform));
      if (userId) conditions.push(eq(files.userId, userId));
      if (folderId) conditions.push(eq(files.parentFolder, folderId));

      if (conditions.length > 0) {
        baseQuery = baseQuery.where(and(...conditions)) as any;
      }

      baseQuery = baseQuery.orderBy(desc(files.createdAt)) as any;

      if (limit) baseQuery = baseQuery.limit(limit) as any;
      if (offset) baseQuery = baseQuery.offset(offset) as any;

      return await baseQuery;
    } else {
      let allFiles = Array.from(this.memoryFiles.values());

      // Apply filters
      if (platform) {
        allFiles = allFiles.filter(file => file.platform === platform);
      }
      if (userId) {
        allFiles = allFiles.filter(file => file.userId === userId);
      }
      if (folderId) {
        allFiles = allFiles.filter(file => {
          const metadata = file.extractedMetadata as any;
          return metadata?._sourceFolderId === folderId;
        });
      }

      // Sort by creation date (newest first)
      allFiles.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Apply pagination
      if (offset) {
        allFiles = allFiles.slice(offset);
      }
      if (limit) {
        allFiles = allFiles.slice(0, limit);
      }

      return allFiles;
    }
  }

  async getFile(id: number): Promise<File | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(files).where(eq(files.id, id));
      return result[0];
    } else {
      return this.memoryFiles.get(id);
    }
  }

  async getFileByExternalId(externalId: string, platform: string): Promise<File | undefined> {
    this.validateString(externalId, 'externalId');
    this.validateString(platform, 'platform');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(files)
        .where(and(eq(files.externalId, externalId), eq(files.platform, platform)));
      return result[0];
    } else {
      return Array.from(this.memoryFiles.values()).find(
        (file) => file.externalId === externalId && file.platform === platform
      );
    }
  }

  async createFile(file: InsertFile): Promise<File> {
    // Generate a fallback externalId if not provided
    if (!file.externalId || typeof file.externalId !== 'string' || file.externalId.trim().length === 0) {
      file.externalId = `${file.platform || 'unknown'}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    this.validateString(file.externalId, 'externalId');
    this.validateString(file.fileName, 'fileName');
    this.validateString(file.platform, 'platform');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(files).values(file).returning();
      return result[0];
    } else {
      const id = this.fileIdCounter++;
      const newFile: File = {
        ...file,
        id,
        status: file.status || "active",
        fileContent: file.fileContent || null,
        sourceUrl: file.sourceUrl || null,
        mimeType: file.mimeType || null,
        fileSize: file.fileSize || null,
        fileUrl: file.fileUrl || null,
        downloadUrl: file.downloadUrl || null,
        thumbnailUrl: file.thumbnailUrl || null,
        parentFolder: file.parentFolder || null,
        tags: file.tags || [],
        extractedMetadata: file.extractedMetadata || {},
        userId: file.userId || null,
        organizationId: file.organizationId || null,
        syncItemId: file.syncItemId || null,
        notionPageId: file.notionPageId || null,
        lastModified: file.lastModified || null,
        isShared: file.isShared || false,
        sharedWith: file.sharedWith || [],
        createdAt: new Date(),
        updatedAt: new Date()
      };
      this.memoryFiles.set(id, newFile);
      return newFile;
    }
  }

  async updateFile(id: number, data: Partial<File>): Promise<File | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.update(files)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(files.id, id))
        .returning();
      return result[0];
    } else {
      const file = this.memoryFiles.get(id);
      if (!file) return undefined;

      const updated: File = {
        ...file,
        ...data,
        updatedAt: new Date(),
      };

      this.memoryFiles.set(id, updated);
      return updated;
    }
  }

  async deleteFile(id: number): Promise<boolean> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      try {
        await this.db.delete(files).where(eq(files.id, id));
        return true;
      } catch (error) {
        console.error("Error deleting file:", error);
        return false;
      }
    } else {
      return this.memoryFiles.delete(id);
    }
  }

  async markFileAsDeleted(id: number): Promise<File | undefined> {
    return this.updateFile(id, { status: "deleted" });
  }

  async updateFileStatus(id: number, status: string): Promise<File | undefined> {
    this.validateId(id);
    this.validateString(status, 'status');

    return this.updateFile(id, { status });
  }

  async searchFiles(query: string, platform?: string, fileType?: string, folderId?: string): Promise<File[]> {
    this.validateString(query, 'query');
    this.ensureDatabase();

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(files);

      const conditions = [];
      if (platform) conditions.push(eq(files.platform, platform));
      if (fileType) conditions.push(eq(files.fileType, fileType));
      if (folderId) conditions.push(eq(files.parentFolder, folderId));

      // Simple text search in filename
      if (query && query !== '*') {
        conditions.push(sql`${files.fileName} ILIKE ${'%' + query + '%'}`);
      }

      if (conditions.length > 0) {
        baseQuery = baseQuery.where(and(...conditions)) as any;
      }

      return await (baseQuery.orderBy(desc(files.createdAt)) as any);
    } else {
      return Array.from(this.memoryFiles.values()).filter((file) => {
        // Handle wildcard search - show all files
        let searchMatch = true;
        
        if (query !== '*') {
          // Search in file name
          const nameMatch = file.fileName && file.fileName.toLowerCase().includes(query.toLowerCase());

          // Search in owner field (from extracted metadata)
          const metadata = file.extractedMetadata as Record<string, any>;
          const ownerMatch = metadata?.owners?.[0]?.displayName?.toLowerCase().includes(query.toLowerCase()) ||
                            metadata?.owners?.[0]?.emailAddress?.toLowerCase().includes(query.toLowerCase());

          // Search in userId field
          const userIdMatch = file.userId && file.userId.toLowerCase().includes(query.toLowerCase());

          // At least one search field should match
          searchMatch = nameMatch || ownerMatch || userIdMatch;
        }

        // Apply additional filters
        const platformMatch = !platform || file.platform === platform;
        const typeMatch = !fileType || file.fileType === fileType;
        const folderMatch = !folderId || (() => {
          const metadata = file.extractedMetadata as any;
          return metadata?._sourceFolderId === folderId;
        })();

        return searchMatch && platformMatch && typeMatch && folderMatch;
      });
    }
  }

  async getFilesForIntegration(integrationId: number, platform: string): Promise<File[]> {
    this.validateId(integrationId);
    this.validateString(platform, 'platform');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(files)
        .where(and(
          eq(files.platform, platform),
          eq(files.status, 'active')
        ))
        .orderBy(desc(files.createdAt));
      return result;
    } else {
      return Array.from(this.memoryFiles.values()).filter(
        (file) => file.platform === platform
      );
    }
  }

  async markFilesAsDeleted(externalIds: string[], platform: string): Promise<void> {
    this.validateString(platform, 'platform');

    if (externalIds.length === 0) return;

    this.ensureDatabase();

    if (this.useDatabase) {
      // Use a simple loop approach to avoid SQL array issues
      for (const externalId of externalIds) {
        await this.db.update(files)
          .set({ status: 'deleted', updatedAt: new Date() })
          .where(and(
            eq(files.externalId, externalId),
            eq(files.platform, platform)
          ));
      }
    } else {
      const allFiles = Array.from(this.memoryFiles.values());
      for (const file of allFiles) {
        if (file.platform === platform && externalIds.includes(file.externalId)) {
          const updated: File = {
            ...file,
            status: "deleted",
            updatedAt: new Date(),
          };
          this.memoryFiles.set(file.id, updated);
        }
      }
    }
  }

  async clearAllFiles(): Promise<void> {
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(files);
    } else {
      this.memoryFiles.clear();
      this.fileIdCounter = 1;
    }
  }

  // Utility methods
  private validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid file ID provided');
    }
  }

  private validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      fileCount: this.useDatabase ? 'unknown' : this.memoryFiles.size,
    };
  }
}
