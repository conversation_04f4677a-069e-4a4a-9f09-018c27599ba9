// Re-export all types for easy importing
export * from './auth.types';
export * from './integration.types';
export * from './file.types';
export * from './chat.types';
export * from './project.types';
export * from './common.types';

// Convenience type collections
export type {
  // Auth types
  User,
  InsertUser,
  AuthStatus,
  AuthSession,
  OAuthCredentials,
  GoogleOAuthData,
  MicrosoftOAuthData,
} from './auth.types';

export type {
  // Integration types
  Integration,
  InsertIntegration,
  SyncLog,
  InsertSyncLog,
  SyncItem,
  InsertSyncItem,
  IntegrationType,
  IntegrationStatus,
  SyncStatus,
} from './integration.types';

export type {
  // File types
  File,
  InsertFile,
  FileChunk,
  InsertFileChunk,
  FileType,
  FilePlatform,
  FileStatus,
  FileProcessingResult,
  FileUploadResult,
  ExtractedMetadata,
} from './file.types';

export type {
  // Chat types
  ChatSession,
  InsertChatSession,
  ChatMessage,
  InsertChatMessage,
  ChatRole,
  ChatResponse,
  RelevantChunk,
  ChatSource,
} from './chat.types';

export type {
  // Project types
  Project,
  InsertProject,
  ProjectConfig,
} from './project.types';



export type {
  // Common types
  ApiResponse,
  PaginatedResponse,
  PaginationOptions,
  FilterOptions,
  SortOptions,
  AppError,
  ProcessingStatus,
  ConnectionStatus,
  AppConfig,
} from './common.types';


