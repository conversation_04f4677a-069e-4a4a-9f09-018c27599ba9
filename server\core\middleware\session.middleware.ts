import session from 'express-session';
import MemoryStore from 'memorystore';
import { environmentConfig } from '../config/environment.config';
import { logger } from './logging.middleware';

/**
 * Memory-only Session Store Configuration
 * Note: Redis temporarily disabled due to connect-redis v7+ compatibility issues
 */
class SessionManager {
  private isRedisAvailable = false;

  async initialize() {
    // Using memory store only for now
    logger.info('Session store initialized with memory store');
  }

  private shouldUseRedis(): boolean {
    // Temporarily disable Redis due to connect-redis v7+ import compatibility issues
    // TODO: Fix Redis import syntax for connect-redis v7+
    return false;
  }

  private async initializeRedis() {
    // Redis functionality temporarily disabled
    this.isRedisAvailable = false;
  }

  getSessionStore() {
    // Always use memory store for now
    const MemoryStoreClass = MemoryStore(session);
    return new MemoryStoreClass({
      checkPeriod: 86400000, // Prune expired entries every 24h
      ttl: 86400000, // 24 hours
      max: 10000, // Maximum number of sessions
    });
  }

  async cleanup() {
    // No cleanup needed for memory store
    logger.info('Session store cleanup completed');
  }

  getStatus() {
    return {
      type: 'memory',
      connected: true,
      note: 'Redis temporarily disabled - using memory store',
    };
  }
}

// Singleton session manager
const sessionManager = new SessionManager();

/**
 * Create session middleware with Redis or memory store
 */
export const createSessionMiddleware = () => {
  if (!environmentConfig.sessionSecret) {
    throw new Error('SESSION_SECRET is required for session middleware');
  }

  const sessionConfig: session.SessionOptions = {
    secret: environmentConfig.sessionSecret,
    name: 'meetsync.sid',
    resave: false,
    saveUninitialized: false,
    rolling: true,
    store: sessionManager.getSessionStore(),
    cookie: {
      secure: environmentConfig.nodeEnv === 'production', // HTTPS only in production
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
      sameSite: 'lax',
    },
  };

  // Additional security in production
  if (environmentConfig.nodeEnv === 'production') {
    sessionConfig.cookie!.domain = process.env.COOKIE_DOMAIN;
    sessionConfig.proxy = true; // Trust proxy in production
  }

  return session(sessionConfig);
};

/**
 * Initialize session manager
 */
export const initializeSessionManager = async () => {
  await sessionManager.initialize();
  return sessionManager;
};

/**
 * Get session manager status
 */
export const getSessionStatus = () => {
  return sessionManager.getStatus();
};

/**
 * Cleanup session manager
 */
export const cleanupSessionManager = async () => {
  await sessionManager.cleanup();
};

/**
 * Session helper functions
 */
export const sessionUtils = {
  /**
   * Get user ID from session
   */
  getUserId: (req: any): string | null => {
    return req.session?.userId || null;
  },

  /**
   * Set user ID in session
   */
  setUserId: (req: any, userId: string): void => {
    if (!req.session) {
      throw new Error('Session not initialized');
    }
    req.session.userId = userId;
  },

  /**
   * Clear user session
   */
  clearUser: (req: any): void => {
    if (req.session) {
      delete req.session.userId;
    }
  },

  /**
   * Store integration data in session
   */
  setIntegrationData: (req: any, platform: string, data: any): void => {
    if (!req.session) {
      throw new Error('Session not initialized');
    }
    if (!req.session.integrations) {
      req.session.integrations = {};
    }
    req.session.integrations[platform] = data;
  },

  /**
   * Get integration data from session
   */
  getIntegrationData: (req: any, platform: string): any => {
    return req.session?.integrations?.[platform] || null;
  },

  /**
   * Clear integration data from session
   */
  clearIntegrationData: (req: any, platform: string): void => {
    if (req.session?.integrations) {
      delete req.session.integrations[platform];
    }
  },
}; 