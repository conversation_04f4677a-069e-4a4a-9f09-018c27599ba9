import { useState } from "react";
import AppLayout from "../components/layout/AppLayout";
import { ChatInterface } from "../features/Chat/ChatInterface";
import Sidebar from "../components/layout/Sidebar";
import Header from "../components/layout/Header";

export default function ChatPage() {
  const [currentTab, setCurrentTab] = useState("Chat");

  // For Chat tab, use custom layout without padding
  if (currentTab === "Chat") {
    return (
      <div className="bg-background h-screen flex overflow-hidden scrollbar-hide">
        <Sidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden scrollbar-hide">
          <Header 
            title="AI Chat" 
            tabs={["Chat", "Sessions"]}
            currentTab={currentTab}
            onTabChange={setCurrentTab}
          />
          
          <main className="flex-1 overflow-hidden bg-background scrollbar-hide">
            <ChatInterface 
              mode="fullpage" 
              className="h-full"
              onSessionChange={(sessionId) => {
                // Optional: Handle session changes if needed
                console.log('Active session:', sessionId);
              }}
            />
          </main>
        </div>
      </div>
    );
  }

  // For other tabs, use normal AppLayout with padding
  return (
    <AppLayout 
      title="AI Chat" 
      tabs={["Chat", "Sessions"]}
      currentTab={currentTab}
      onTabChange={setCurrentTab}
    >
      {currentTab === "Sessions" && (
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Session Management</h3>
            <p className="text-gray-600 mb-6">
              All your chat sessions and conversation history are managed in the main Chat interface. 
              Use the sidebar to switch between conversations, create new sessions, and manage your chat history.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left max-w-md mx-auto">
              <h4 className="font-medium text-blue-900 mb-2">✨ New Features:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• OpenAI GPT-4 powered responses</li>
                <li>• Smart source selection (files, Google Drive, Teams)</li>
                <li>• Drag & drop file uploads</li>
                <li>• Session persistence and search</li>
                <li>• Modern, responsive interface</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </AppLayout>
  );
} 