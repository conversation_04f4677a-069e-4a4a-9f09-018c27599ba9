import { useState } from "react";
import AppLayout from "../components/layout/AppLayout";
import { ChatInterface } from "../features/Chat/ChatInterface";
import Sidebar from "../components/layout/Sidebar";
import Header from "../components/layout/Header";

export default function ChatPage() {
    return (
      <div className="bg-background h-screen flex overflow-hidden scrollbar-hide">
        <Sidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden scrollbar-hide">
          <Header 
            title="AI Chat" 
          />
          
          <main className="flex-1 overflow-hidden bg-background scrollbar-hide">
            <ChatInterface 
              mode="fullpage" 
              className="h-full"
              onSessionChange={(sessionId) => {
                // Optional: Handle session changes if needed
                console.log('Active session:', sessionId);
              }}
            />
          </main>
        </div>
      </div>
  );
} 