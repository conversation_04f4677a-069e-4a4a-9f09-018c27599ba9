import { projects, type Project, type InsertProject } from "../../../shared/index.js";
import { eq, desc } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config";

/**
 * Project storage operations
 */
export class ProjectStorage {
  private db: any;
  private memoryProjects: Map<number, Project>;
  private projectIdCounter: number;
  private useDatabase: boolean;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memoryProjects = new Map();
    this.projectIdCounter = 1;
    // Database connection will be initialized lazily when first needed
  }

  /**
   * Ensure database connection is available
   */
  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        // Silently fall back to memory storage
        this.useDatabase = false;
      }
    }
  }

  async getProjects(userId?: string): Promise<Project[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(projects);

      if (userId) {
        baseQuery = baseQuery.where(eq(projects.userId, userId)) as any;
      }

      return await (baseQuery.orderBy(desc(projects.createdAt)) as any);
    } else {
      let projectList = Array.from(this.memoryProjects.values());

      if (userId) {
        projectList = projectList.filter(project => project.userId === userId);
      }

      projectList.sort((a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );

      return projectList;
    }
  }

  async getProject(id: number): Promise<Project | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(projects).where(eq(projects.id, id));
      return result[0];
    } else {
      return this.memoryProjects.get(id);
    }
  }

  async createProject(project: InsertProject): Promise<Project> {
    this.validateString(project.name, 'name');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(projects).values(project).returning();
      return result[0];
    } else {
      const id = this.projectIdCounter++;
      const now = new Date();
      const newProject: Project = {
        ...project,
        id,
        description: project.description || null,
        userId: project.userId || null,
        enabledSources: project.enabledSources || [],
        createdAt: now,
        updatedAt: now,
      };
      this.memoryProjects.set(id, newProject);
      return newProject;
    }
  }

  async updateProject(id: number, data: Partial<Project>): Promise<Project | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.update(projects)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(projects.id, id))
        .returning();
      return result[0];
    } else {
      const project = this.memoryProjects.get(id);
      if (!project) return undefined;

      const updated: Project = {
        ...project,
        ...data,
        updatedAt: new Date(),
      };

      this.memoryProjects.set(id, updated);
      return updated;
    }
  }

  async deleteProject(id: number): Promise<boolean> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      try {
        await this.db.delete(projects).where(eq(projects.id, id));
        return true;
      } catch (error) {
        console.error("Error deleting project:", error);
        return false;
      }
    } else {
      return this.memoryProjects.delete(id);
    }
  }

  async clearAllProjects(): Promise<void> {
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(projects);
    } else {
      this.memoryProjects.clear();
      this.projectIdCounter = 1;
    }
  }

  // Utility methods
  private validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid project ID provided');
    }
  }

  private validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      projectCount: this.useDatabase ? 'unknown' : this.memoryProjects.size,
    };
  }
}
