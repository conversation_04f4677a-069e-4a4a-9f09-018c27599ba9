# Team Setup Quick Reference

## 🚀 For New Team Members

```bash
# 1. <PERSON><PERSON> and install
git clone <repo-url>
cd MeetSync-Web-App
npm install

# 2. Setup PostgreSQL
# Install PostgreSQL, create database 'meetsync_db'

# 3. Configure environment
cp .env.example .env
# Edit .env with your PostgreSQL credentials

# 4. Setup database
npm run migrate
npm run db:status  # Verify setup

# 5. Start development
npm run dev
```

## 🔄 For Existing Team Members (URGENT)

**You MUST update your setup to continue working on the project.**

### Quick Update (5 minutes)
```bash
# 1. Pull changes
git pull origin main
npm install

# 2. Fresh database (recommended)
psql -U postgres -h localhost -p 5432 -c "DROP DATABASE IF EXISTS meetsync_db;"
psql -U postgres -h localhost -p 5432 -c "CREATE DATABASE meetsync_db;"
npm run migrate

# 3. Verify and start
npm run db:status
npm run dev
```

### Alternative: Incremental Update
```bash
git pull origin main
npm install

# Add missing constraint
psql -U postgres -h localhost -p 5432 -d meetsync_db -c "
ALTER TABLE oauth_tokens 
ADD CONSTRAINT oauth_tokens_user_platform_unique 
UNIQUE (user_id, platform);"

npm run dev
```

## ✅ Verify Everything Works

1. **Health Check:** `curl http://localhost:8080/health`
2. **Database:** `npm run db:status`
3. **Google OAuth:** Try connecting Google Drive
4. **Chat:** Send a message in chat interface

## 🆘 If Something's Broken

1. **Check your .env:** Must use local PostgreSQL, not Neon
2. **Restart PostgreSQL:** `brew services restart postgresql` (macOS)
3. **Fresh database:** Run the "Fresh database" commands above
4. **Check logs:** Look for detailed error messages in terminal

## 📊 What Was Fixed

- ✅ Google OAuth now works end-to-end
- ✅ "User not authenticated" errors fixed
- ✅ "Integration is not connected" errors fixed  
- ✅ Chat sessions work properly
- ✅ Database stability improved

## 🚨 Breaking Changes

- **No more Neon Serverless:** We now use local PostgreSQL only
- **Environment variables:** Must update .env for local PostgreSQL
- **OAuth constraint:** Prevents duplicate tokens (good thing!)

---

**Need detailed instructions?** See `SETUP.md`  
**Want to understand what changed?** See `CHANGES.md` 