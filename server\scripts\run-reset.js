#!/usr/bin/env node

import { config } from 'dotenv';
import { execSync } from 'child_process';
import { readFileSync, rmSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

// Get current script directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  yellow: '\x1b[33m',
  green: '\x1b[32m', 
  red: '\x1b[31m',
  reset: '\x1b[0m'
};

console.log(`${colors.yellow}🔄 MeetSync Database Reset${colors.reset}`);
console.log('This will clear all connected integrations, sync data, and files');
console.log('Integration records (names/types) will be preserved');
console.log('');

// Load environment variables from .env file
console.log('📁 Loading environment variables from .env file...');
config({ path: '.env' });

// Check if DATABASE_URL is now available
if (!process.env.DATABASE_URL) {
  console.error(`${colors.red}❌ Error: DATABASE_URL environment variable is not set${colors.reset}`);
  console.error('Please check that your .env file exists and contains DATABASE_URL');
  console.error('Example: DATABASE_URL="postgresql://user:pass@host:port/dbname"');
  process.exit(1);
}

console.log(`${colors.green}✅ DATABASE_URL loaded successfully${colors.reset}`);
console.log('');

// Prompt for confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to clear uploads folder
function clearUploadsFolder() {
  const uploadsPath = join(process.cwd(), 'uploads');
  
  try {
    console.log('🗂️ Clearing uploads folder...');
    
    if (existsSync(uploadsPath)) {
      // Remove all contents of uploads folder
      rmSync(uploadsPath, { recursive: true, force: true });
      console.log('  - Removed all files from uploads folder');
    }
    
    // Recreate the empty uploads folder
    mkdirSync(uploadsPath, { recursive: true });
    console.log('  - Recreated empty uploads folder');
    
  } catch (error) {
    console.error(`${colors.red}⚠️  Warning: Failed to clear uploads folder:${colors.reset}`, error.message);
    console.log('You may need to manually clear the uploads folder');
  }
}

rl.question('Are you sure you want to proceed? (y/N): ', (answer) => {
  rl.close();
  
  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log(`${colors.yellow}ℹ️  Reset cancelled${colors.reset}`);
    process.exit(0);
  }

  console.log('');
  console.log(`${colors.yellow}🔄 Running database reset...${colors.reset}`);

  try {
    // Read the SQL file
    const sqlFile = join(__dirname, 'reset-connected-state.sql');
    const sqlContent = readFileSync(sqlFile, 'utf8');

    // Check for psql first
    let psqlAvailable = false;
    try {
      execSync('psql --version', { stdio: 'ignore' });
      psqlAvailable = true;
    } catch (error) {
      // psql not available
    }

    if (psqlAvailable) {
      console.log('Using psql to execute SQL script...');
      execSync(`psql "${process.env.DATABASE_URL}" -f "${sqlFile}"`, { 
        stdio: 'inherit',
        env: { ...process.env }
      });
    } else {
      // Check for docker
      let dockerAvailable = false;
      try {
        execSync('docker --version', { stdio: 'ignore' });
        dockerAvailable = true;
      } catch (error) {
        // docker not available
      }

      if (dockerAvailable) {
        console.log('psql not found, using Docker...');
        execSync(`docker run --rm -i postgres:15 psql "${process.env.DATABASE_URL}"`, {
          input: sqlContent,
          stdio: ['pipe', 'inherit', 'inherit'],
          env: { ...process.env }
        });
      } else {
        console.error(`${colors.red}❌ Error: Neither psql nor docker found${colors.reset}`);
        console.error('Please install PostgreSQL client (psql) or Docker to run this script');
        console.error('');
        console.error('Alternatively, you can:');
        console.error('1. Install psql from: https://www.postgresql.org/download/');
        console.error(`2. Manually run SQL file: ${sqlFile}`);
        process.exit(1);
      }
    }

    // Clear uploads folder after successful database reset
    clearUploadsFolder();

    console.log('');
    console.log(`${colors.green}✅ Database reset completed successfully!${colors.reset}`);
    console.log('');
    console.log('Next steps:');
    console.log('1. Start your backend: npm run dev');
    console.log('2. Check the integrations page - all should show "Disconnected"');
    console.log('3. Check logs/files pages - should be empty');
    console.log('4. Uploads folder has been cleared');

  } catch (error) {
    console.error(`${colors.red}❌ Reset failed:${colors.reset}`, error.message);
    process.exit(1);
  }
}); 