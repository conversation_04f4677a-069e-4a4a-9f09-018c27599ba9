import { BaseService } from "../base/service.interface";
import { RAGChatService } from "./chat.service";
import { RAGAIResponseService } from "./ai-response.service";
import { RAGContextService } from "./context.service";
import { RAGBatchService } from "./batch.service";
import type { InsertChatMessage, InsertChatSession } from "../../../shared/index.js";

/**
 * RAG Service Facade - provides backward compatibility with the original RAGService
 * while using the new modular RAG services underneath
 */
export class RAGServiceFacade extends BaseService {
  private chatService: RAGChatService;
  private aiResponseService: RAGAIResponseService;
  private contextService: RAGContextService;
  private batchService: RAGBatchService;

  constructor() {
    super('RAGServiceFacade', '1.0.0', 'Unified RAG services facade for backward compatibility');
    
    // Initialize modular services
    this.chatService = new RAGChatService();
    this.aiResponseService = new RAGAIResponseService();
    this.contextService = new RAGContextService();
    this.batchService = new RAGBatchService();
  }

  protected async onInitialize(): Promise<void> {
    // Initialize all sub-services
    await this.chatService.initialize();
    await this.aiResponseService.initialize();
    await this.contextService.initialize();
    await this.batchService.initialize();
    
    this.log('info', 'RAG Service Facade initialized with all sub-services');
  }

  protected getDependencies(): string[] {
    return ['storage', 'embedding-service', 'function-tools'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    const chatHealth = await this.chatService.getHealthStatus();
    const aiHealth = await this.aiResponseService.getHealthStatus();
    const contextHealth = await this.contextService.getHealthStatus();
    const batchHealth = await this.batchService.getHealthStatus();

    return {
      chat: chatHealth,
      aiResponse: aiHealth,
      context: contextHealth,
      batch: batchHealth,
      allServicesHealthy: chatHealth.healthy && aiHealth.healthy && contextHealth.healthy && batchHealth.healthy,
    };
  }

  protected async onCleanup(): Promise<void> {
    await Promise.all([
      this.chatService.cleanup?.(),
      this.aiResponseService.cleanup?.(),
      this.contextService.cleanup?.(),
      this.batchService.cleanup?.(),
    ]);
  }

  /**
   * Check if the service is initialized (backward compatibility)
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  // Chat methods - delegate to RAGChatService
  async createChatSession(
    title?: string,
    enabledSources?: string[],
    userId?: string
  ): Promise<any> {
    this.ensureInitialized();
    return this.chatService.createChatSession(title, enabledSources, userId);
  }

  async getChatSessions(userId?: string, limit: number = 20): Promise<any[]> {
    this.ensureInitialized();
    return this.chatService.getChatSessions(userId, limit);
  }

  async getChatSession(sessionId: string): Promise<any> {
    this.ensureInitialized();
    
    try {
      // Import storage dynamically to avoid circular dependencies
      const { storage } = await import("../../storage/index.js");
      return await storage.getChatSession(sessionId);
    } catch (error: any) {
      this.handleError(error, 'getting chat session');
    }
  }

  async getChatMessages(sessionId: string, limit: number = 50): Promise<any[]> {
    this.ensureInitialized();
    return this.chatService.getChatMessages(sessionId, limit);
  }

  async deleteChatSession(sessionId: string): Promise<boolean> {
    this.ensureInitialized();
    
    try {
      const { storage } = await import("../../storage/index.js");
      return await storage.deleteChatSession(sessionId);
    } catch (error: any) {
      this.handleError(error, 'deleting chat session');
      return false;
    }
  }

  async updateChatSession(
    sessionId: string,
    updates: { title?: string; enabledSources?: string[] }
  ): Promise<any> {
    this.ensureInitialized();
    
    try {
      const { storage } = await import("../../storage/index.js");
      return await storage.updateChatSession(sessionId, updates);
    } catch (error: any) {
      this.handleError(error, 'updating chat session');
    }
  }

  // Main message processing method
  async sendMessage(
    sessionId: string,
    content: string,
    enabledSources: string[] = []
  ): Promise<any> {
    this.ensureInitialized();

    try {
      this.log('info', `Processing message for session ${sessionId}`);
      this.log('info', `Enabled sources: ${enabledSources.join(", ")}`);

      // Store user message
      const savedUserMessage = await this.chatService.storeUserMessage(
        sessionId,
        content,
        enabledSources
      );

      // Get relevant context from enabled sources
      const { chunks: relevantChunks, context } = await this.contextService.getRelevantContext(
        content,
        enabledSources
      );

      this.log('info', `Found ${relevantChunks.length} relevant chunks`);

      // Get chat history for context
      const chatHistory = await this.chatService.getChatHistory(parseInt(sessionId), 8);

      // Generate AI response
      const aiResponse = await this.aiResponseService.generateAIResponse(
        content,
        context,
        chatHistory,
        enabledSources,
        relevantChunks
      );

      // Store AI message
      const savedAiMessage = await this.chatService.storeAIMessage(
        sessionId,
        aiResponse,
        enabledSources,
        relevantChunks
      );

      return {
        userMessage: savedUserMessage,
        aiMessage: savedAiMessage,
        relevantChunks: relevantChunks.length,
        sourcesUsed: enabledSources,
      };

    } catch (error: any) {
      this.handleError(error, 'processing message');
    }
  }

  // Context methods - delegate to RAGContextService
  async getAvailableSources(userId?: string): Promise<any[]> {
    this.ensureInitialized();
    return this.contextService.getAvailableSources(userId);
  }

  async searchFilesByDescription(
    description: string,
    enabledSources: string[] = [],
    limit: number = 25
  ): Promise<any[]> {
    this.ensureInitialized();
    return this.contextService.searchFilesByDescription(description, enabledSources, limit);
  }

  async hasEmbeddings(fileId: number): Promise<boolean> {
    this.ensureInitialized();
    return this.contextService.hasEmbeddings(fileId);
  }

  async searchSimilarChunks(
    queryEmbedding: number[],
    enabledSources: string[] = [],
    limit: number = 10
  ): Promise<any[]> {
    this.ensureInitialized();
    
    try {
      // Delegate to storage for direct vector search
      const { storage } = await import("../../storage/index.js");
      return await storage.searchSimilarChunks(queryEmbedding, enabledSources, limit);
    } catch (error: any) {
      this.handleError(error, 'searching similar chunks');
      return [];
    }
  }

  // Batch processing methods - delegate to RAGBatchService
  async processBatchEmbeddings(fileIds: number[]): Promise<string> {
    this.ensureInitialized();
    return this.batchService.processBatchEmbeddings(fileIds);
  }

  async checkBatchStatus(batchId: string): Promise<any> {
    this.ensureInitialized();
    return this.batchService.checkBatchStatus(batchId);
  }

  async processFilesSmart(fileIds: number[], options: {
    urgent?: boolean;
    userWaiting?: boolean;
    bulkOperation?: boolean;
  } = {}): Promise<{ method: string; result: any }> {
    this.ensureInitialized();
    return this.batchService.processFilesSmart(fileIds, options);
  }

  async processPendingFilesSmart(options: {
    urgent?: boolean;
    userWaiting?: boolean;
  } = {}): Promise<{ method: string; result: any } | null> {
    this.ensureInitialized();
    return this.batchService.processPendingFilesSmart(options);
  }

  async processPendingFilesBatch(): Promise<string | null> {
    this.ensureInitialized();
    return this.batchService.processPendingFilesBatch();
  }

  // Utility methods
  estimateTokenCount(text: string): number {
    return this.aiResponseService.estimateTokenCount(text);
  }

  // Service information
  getServiceInfo() {
    return {
      ...super.getServiceInfo(),
      subServices: {
        chat: this.chatService.getServiceInfo(),
        aiResponse: this.aiResponseService.getServiceInfo(),
        context: this.contextService.getServiceInfo(),
        batch: this.batchService.getServiceInfo(),
      },
    };
  }

  // Direct access to sub-services (for advanced usage)
  getChatService(): RAGChatService {
    return this.chatService;
  }

  getAIResponseService(): RAGAIResponseService {
    return this.aiResponseService;
  }

  getContextService(): RAGContextService {
    return this.contextService;
  }

  getBatchService(): RAGBatchService {
    return this.batchService;
  }

  // Additional utility methods for comprehensive compatibility
  async getSourceStatistics(enabledSources: string[] = []): Promise<any> {
    this.ensureInitialized();
    return this.contextService.getSourceStatistics(enabledSources);
  }

  async validateEnabledSources(enabledSources: string[]): Promise<any> {
    this.ensureInitialized();
    return this.contextService.validateEnabledSources(enabledSources);
  }

  async getBatchStatistics(): Promise<any> {
    this.ensureInitialized();
    return this.batchService.getBatchStatistics();
  }

  async testAIConnection(): Promise<boolean> {
    this.ensureInitialized();
    return this.aiResponseService.testConnection();
  }

  async getAvailableModels(): Promise<string[]> {
    this.ensureInitialized();
    return this.aiResponseService.getAvailableModels();
  }

  getContextWindowLimit(): number {
    return this.contextService.getContextWindowLimit();
  }

  truncateContext(context: string, maxLength?: number): string {
    return this.contextService.truncateContext(context, maxLength);
  }
}
