# Path Parameters
IntegrationId:
  name: id
  in: path
  required: true
  description: The unique identifier of the integration
  schema:
    type: integer
    format: int32

FileId:
  name: id
  in: path
  required: true
  description: The unique identifier of the file
  schema:
    type: integer
    format: int32

SessionId:
  name: id
  in: path
  required: true
  description: The unique identifier of the chat session
  schema:
    type: integer
    format: int32

SyncLogId:
  name: syncLogId
  in: path
  required: true
  description: The unique identifier of the sync log
  schema:
    type: integer
    format: int32

TeamId:
  name: teamId
  in: path
  required: true
  description: The unique identifier of the Microsoft Teams team
  schema:
    type: string

UploadedFileId:
  name: id
  in: path
  required: true
  description: The unique identifier of the uploaded file
  schema:
    type: string

# Query Parameters
LimitParam:
  name: limit
  in: query
  description: Maximum number of items to return
  required: false
  schema:
    type: integer
    minimum: 1
    maximum: 100
    default: 10

OffsetParam:
  name: offset
  in: query
  description: Number of items to skip for pagination
  required: false
  schema:
    type: integer
    minimum: 0
    default: 0

SearchQuery:
  name: q
  in: query
  description: Search query string
  required: true
  schema:
    type: string
    minLength: 1

PlatformFilter:
  name: platform
  in: query
  description: Filter by platform
  required: false
  schema:
    type: string
    enum: [google_drive, microsoft_teams, zoom, slack]

FileTypeFilter:
  name: fileType
  in: query
  description: Filter by file type
  required: false
  schema:
    type: string
    enum: [transcript, doc, ppt, pdf, video, audio]

StatusFilter:
  name: status
  in: query
  description: Filter by status
  required: false
  schema:
    type: string

SortBy:
  name: sortBy
  in: query
  description: Field to sort by
  required: false
  schema:
    type: string
    enum: [createdAt, updatedAt, fileName, fileSize]
    default: createdAt

SortOrder:
  name: sortOrder
  in: query
  description: Sort order
  required: false
  schema:
    type: string
    enum: [asc, desc]
    default: desc

# OAuth Parameters
OAuthCode:
  name: code
  in: query
  description: OAuth authorization code
  required: false
  schema:
    type: string

OAuthState:
  name: state
  in: query
  description: OAuth state parameter for security
  required: false
  schema:
    type: string

OAuthError:
  name: error
  in: query
  description: OAuth error code if authorization failed
  required: false
  schema:
    type: string

# Search Parameters
SearchThreshold:
  name: threshold
  in: query
  description: Similarity threshold for search results
  required: false
  schema:
    type: number
    format: float
    minimum: 0
    maximum: 1
    default: 0.7

# Date Range Parameters
StartDate:
  name: startDate
  in: query
  description: Start date for filtering results (ISO 8601 format)
  required: false
  schema:
    type: string
    format: date-time

EndDate:
  name: endDate
  in: query
  description: End date for filtering results (ISO 8601 format)
  required: false
  schema:
    type: string
    format: date-time

# User Parameters
UserId:
  name: userId
  in: query
  description: Filter by user ID
  required: false
  schema:
    type: string 