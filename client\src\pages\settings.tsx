import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { MCPStatusPanel } from "@/components/mcp";

const apiSettingsSchema = z.object({
  openaiApiKey: z.string().min(1, { message: "API key is required" }),
  notionToken: z.string().min(1, { message: "Notion token is required" }),
  googleClientId: z.string().min(1, { message: "Client ID is required" }),
  googleClientSecret: z.string().min(1, { message: "Client secret is required" }),
});

const generalSettingsSchema = z.object({
  appName: z.string().min(1, { message: "Application name is required" }),
  adminEmail: z.string().email({ message: "Invalid email address" }),
  enableNotifications: z.boolean(),
  logLevel: z.string(),
});

type ApiSettingsValues = z.infer<typeof apiSettingsSchema>;
type GeneralSettingsValues = z.infer<typeof generalSettingsSchema>;

export default function Settings() {
  const [currentTab, setCurrentTab] = useState("general");
  
  const apiSettingsForm = useForm<ApiSettingsValues>({
    resolver: zodResolver(apiSettingsSchema),
    defaultValues: {
      openaiApiKey: "",
      notionToken: "",
      googleClientId: "",
      googleClientSecret: "",
    },
  });
  
  const generalSettingsForm = useForm<GeneralSettingsValues>({
    resolver: zodResolver(generalSettingsSchema),
    defaultValues: {
      appName: "GPT Unify",
      adminEmail: "<EMAIL>",
      enableNotifications: true,
      logLevel: "info",
    },
  });
  
  const onSubmitApiSettings = (data: ApiSettingsValues) => {
    console.log("API Settings:", data);
    // Would save to server in real app
  };
  
  const onSubmitGeneralSettings = (data: GeneralSettingsValues) => {
    console.log("General Settings:", data);
    // Would save to server in real app
  };
  
  return (
    <AppLayout title="Settings">
      <Tabs defaultValue="general" onValueChange={setCurrentTab} value={currentTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="api">API Keys</TabsTrigger>
          <TabsTrigger value="mcp">MCP Servers</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Configure the application settings</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...generalSettingsForm}>
                <form onSubmit={generalSettingsForm.handleSubmit(onSubmitGeneralSettings)} className="space-y-6">
                  <FormField
                    control={generalSettingsForm.control}
                    name="appName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Application Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormDescription>
                          Name displayed in the UI and notifications
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={generalSettingsForm.control}
                    name="adminEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Administrator Email</FormLabel>
                        <FormControl>
                          <Input {...field} type="email" />
                        </FormControl>
                        <FormDescription>
                          Email address for system notifications
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={generalSettingsForm.control}
                    name="enableNotifications"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Notifications</FormLabel>
                          <FormDescription>
                            Enable system notifications for sync events
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={generalSettingsForm.control}
                    name="logLevel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Log Level</FormLabel>
                        <select
                          {...field}
                          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                        >
                          <option value="error">Error</option>
                          <option value="warn">Warning</option>
                          <option value="info">Info</option>
                          <option value="debug">Debug</option>
                        </select>
                        <FormDescription>
                          Determines the verbosity of application logs
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button type="submit">Save Settings</Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="api">
          <Card>
            <CardHeader>
              <CardTitle>API Keys & Credentials</CardTitle>
              <CardDescription>Configure external service credentials</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...apiSettingsForm}>
                <form onSubmit={apiSettingsForm.handleSubmit(onSubmitApiSettings)} className="space-y-6">
                  <FormField
                    control={apiSettingsForm.control}
                    name="openaiApiKey"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>OpenAI API Key</FormLabel>
                        <FormControl>
                          <Input {...field} type="password" />
                        </FormControl>
                        <FormDescription>
                          Used for AI-powered metadata extraction
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={apiSettingsForm.control}
                    name="notionToken"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notion Integration Token</FormLabel>
                        <FormControl>
                          <Input {...field} type="password" />
                        </FormControl>
                        <FormDescription>
                          Used to connect and push data to Notion
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="border-t pt-6">
                    <h3 className="text-base font-medium mb-4">Google API Credentials</h3>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={apiSettingsForm.control}
                        name="googleClientId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Client ID</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={apiSettingsForm.control}
                        name="googleClientSecret"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Client Secret</FormLabel>
                            <FormControl>
                              <Input {...field} type="password" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormDescription className="mt-2">
                      OAuth 2.0 credentials for Google API access
                    </FormDescription>
                  </div>
                  
                  <Button type="submit">Save API Settings</Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mcp">
          <Card>
            <CardHeader>
              <CardTitle>MCP Server Management</CardTitle>
              <CardDescription>
                Configure and monitor Model Context Protocol (MCP) servers that provide AI tools and integrations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MCPStatusPanel showToolsList={true} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="storage">
          <Card>
            <CardHeader>
              <CardTitle>Storage Settings</CardTitle>
              <CardDescription>Configure storage options for documents and files</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Storage</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="radio" id="notion" name="storage" className="h-4 w-4 text-primary focus:ring-primary border-gray-300" checked />
                      <label htmlFor="notion" className="text-sm text-gray-700">Notion</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="radio" id="database" name="storage" className="h-4 w-4 text-primary focus:ring-primary border-gray-300" />
                      <label htmlFor="database" className="text-sm text-gray-700">Internal Database</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="radio" id="custom" name="storage" className="h-4 w-4 text-primary focus:ring-primary border-gray-300" />
                      <label htmlFor="custom" className="text-sm text-gray-700">Custom API</label>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Notion Database ID</label>
                  <Input placeholder="8a754f84b8884fa8b8f06b14c88b51b5" />
                  <p className="text-xs text-gray-500">The ID of the Notion database where documents and files will be stored</p>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Data Retention</label>
                  <select className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                    <option value="30">30 days</option>
                    <option value="60">60 days</option>
                    <option value="90" selected>90 days</option>
                    <option value="180">180 days</option>
                    <option value="365">1 year</option>
                    <option value="-1">Indefinite</option>
                  </select>
                  <p className="text-xs text-gray-500">How long to keep sync logs and temporary files</p>
                </div>
                
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Archive Processed Files</label>
                    <p className="text-xs text-gray-500">
                      Automatically move processed files to an archive folder
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <Button>Save Storage Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="advanced">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Settings</CardTitle>
              <CardDescription>Configure advanced system settings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Scheduler Worker Count</label>
                  <Input type="number" defaultValue="2" min="1" max="10" />
                  <p className="text-xs text-gray-500">Number of concurrent sync jobs allowed</p>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">API Request Timeout</label>
                  <div className="flex items-center space-x-2">
                    <Input type="number" defaultValue="30" min="5" max="120" />
                    <span className="text-sm">seconds</span>
                  </div>
                  <p className="text-xs text-gray-500">Maximum time to wait for external API responses</p>
                </div>
                
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Debug Mode</label>
                    <p className="text-xs text-gray-500">
                      Enable detailed logging for troubleshooting
                    </p>
                  </div>
                  <Switch />
                </div>
                
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Auto-Update</label>
                    <p className="text-xs text-gray-500">
                      Automatically update the application when new versions are available
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="border-t pt-6">
                  <h3 className="text-base font-medium text-red-600 mb-4">Danger Zone</h3>
                  
                  <div className="space-y-4">
                    <Button variant="outline" className="border-red-300 text-red-600 hover:bg-red-50">
                      Clear All Cache
                    </Button>
                    
                    <Button variant="outline" className="border-red-300 text-red-600 hover:bg-red-50">
                      Reset All Settings
                    </Button>
                    
                    <Button variant="outline" className="border-red-300 text-red-600 hover:bg-red-50">
                      Purge All Data
                    </Button>
                  </div>
                </div>
                
                <Button>Save Advanced Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AppLayout>
  );
}
