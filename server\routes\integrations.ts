import type { Express } from "express";
import { integration<PERSON><PERSON>roller } from "../controllers/integration/index";
import { GoogleIntegrationController } from '../controllers/integration/google/google-integration.controller.js';
import { GoogleOAuthController } from '../controllers/integration/google/google-oauth.controller.js';
import { GoogleGmailController } from '../controllers/integration/google/google-gmail.controller.js';
import { GoogleAccountController } from '../controllers/integration/google/google-account.controller.js';


/**
 * Register integration management routes
 */
export function registerIntegrationRoutes(app: Express) {
  // Initialize controllers
  const googleIntegrationController = new GoogleIntegrationController();
  const googleOAuthController = new GoogleOAuthController();
  const googleGmailController = new GoogleGmailController();
  const googleAccountController = new GoogleAccountController();

  // Integration Routes
  app.get('/api/integrations', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");
      const integrations = await storage.getIntegrations();
      res.json({ integrations });
    } catch (error: any) {
      console.error('Error getting integrations:', error);
      res.status(500).json({ message: 'Failed to get integrations', error: error.message });
    }
  });

  // Integration CRUD Routes - Fixed with proper binding
  app.get('/api/integrations/:id', integrationController.getIntegration);
  app.post('/api/integrations', integrationController.createIntegration);
  app.put('/api/integrations/:id', integrationController.updateIntegration);
  app.delete('/api/integrations/:id', integrationController.deleteIntegration);

  // Google Drive specific routes
  app.get('/api/integrations/:id/auth-url', integrationController.getAuthUrl);
  app.get('/api/integrations/:id/oauth/callback', integrationController.handleOAuthCallback);
  app.get('/api/integrations/:id/folders', integrationController.getGoogleDriveFolders);

  // Enhanced Google Drive structure
  app.get('/api/integrations/:id/drive-structure', integrationController.getDriveStructure);
  app.get('/api/integrations/:id/debug-folders', integrationController.debugDriveFolders);

  // Microsoft Teams specific routes
  app.get('/api/integrations/:id/teams-auth-url', integrationController.getTeamsAuthUrl);
  app.get('/api/integrations/:id/teams/oauth/callback', integrationController.handleTeamsOAuthCallback);
  app.get('/api/integrations/:id/teams-sources', integrationController.getTeamsSources);
  app.get('/api/integrations/:id/teams-folders', integrationController.getTeamsFolders);
  app.get('/api/integrations/:id/teams-channels/:teamId', integrationController.getTeamsChannels);
  app.post('/api/integrations/:id/test-teams-connection', integrationController.testTeamsConnection);
  
  // OAuth Start endpoint for new integrations
  app.get('/api/integrations/oauth/start', async (req, res) => {
    try {
      const { type } = req.query;
      
      if (!type) {
        return res.status(400).json({ message: 'Integration type is required' });
      }
      
      const integrationTypeStr = type as string;
      
      // Validate integration type
      const validTypes = ['gmail', 'google_calendar', 'google-calendar', 'google_drive', 'google-drive'];
      if (!validTypes.includes(integrationTypeStr)) {
        console.error('Invalid integration type:', integrationTypeStr);
        return res.redirect(`/integrations?error=invalid_integration_type`);
      }
      
      // Create a temporary integration for OAuth flow
      const { storage } = await import("../storage.js");
      const integrationName = `${integrationTypeStr.charAt(0).toUpperCase() + integrationTypeStr.slice(1)} Integration`;
      
      const newIntegration = await storage.createIntegration({
        type: integrationTypeStr,
        name: integrationName,
        status: 'disconnected',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
      });
      
      console.log('Created temporary integration for OAuth:', newIntegration.id, integrationTypeStr);
      
      // Redirect to the specific OAuth flow based on type
      if (integrationTypeStr.includes('google') || ['gmail', 'google_calendar'].includes(integrationTypeStr)) {
        // For Google services, redirect to Google OAuth
        const redirectUri = `${req.protocol}://${req.get('host')}/api/integrations/oauth/callback`;
        res.redirect(`/api/integrations/${newIntegration.id}/auth-url?redirectUri=${encodeURIComponent(redirectUri)}`);
      } else {
        res.status(400).json({ message: 'Unsupported integration type for OAuth' });
      }
      
    } catch (error: any) {
      console.error('Error starting OAuth flow:', error);
      res.redirect(`/integrations?error=${encodeURIComponent(error.message)}`);
    }
  });
  
  // Global OAuth callback endpoint (for Google Cloud Console redirect URI configuration)
  app.get('/api/integrations/oauth/callback', async (req, res) => {
    // This endpoint handles both Google and Microsoft callbacks
    // based on the 'state' parameter or other identifiers
    const { state, code, error } = req.query;
    
    console.log('[GLOBAL OAUTH] Received callback with:', { state, code: !!code, error });
    
    if (error) {
      console.log('[GLOBAL OAUTH] OAuth error received:', error);
      return res.redirect(`/integrations?error=${encodeURIComponent(error as string)}`);
    }
    
    if (!state || !code) {
      console.log('[GLOBAL OAUTH] Missing required parameters:', { state: !!state, code: !!code });
      return res.redirect('/integrations?error=missing_parameters');
    }
    
    try {
      console.log('[GLOBAL OAUTH] Parsing state parameter:', state);
      const stateStr = state as string;
      
      // Extract integration ID from state
      let integrationId: string;
      let type: 'google' | 'microsoft' = 'google';
      
      // Parse the state to determine the integration type and ID
      if (stateStr.includes('_')) {
        // Microsoft format: "integrationId_randomString"
        const parts = stateStr.split('_');
        integrationId = parts[0];
        type = 'microsoft';
        console.log('[GLOBAL OAUTH] Detected Microsoft format, integration ID:', integrationId);
      } else {
        // Try to parse as JSON (Google format)
        try {
          const stateData = JSON.parse(stateStr);
          integrationId = stateData.integrationId || stateData.integration_id;
          type = 'google';
          console.log('[GLOBAL OAUTH] Detected Google format, integration ID:', integrationId);
        } catch (parseError) {
          console.log('[GLOBAL OAUTH] Failed to parse as JSON, treating as simple string (likely Google)');
          integrationId = stateStr;
          type = 'google';
        }
      }
      
      if (!integrationId) {
        console.log('[GLOBAL OAUTH] Could not extract integration ID from state');
        return res.redirect('/integrations?error=invalid_state_format');
      }
      
      // Enhanced type detection: check the database for the actual integration type
      if (integrationId) {
        try {
          const { storage } = await import("../storage.js");
          const integration = await storage.getIntegration(parseInt(integrationId));
          if (integration) {
            // Check if it's a Google integration (including Gmail and Calendar)
            if (integration.type.includes('google') || 
                ['gmail', 'google_calendar', 'google-calendar'].includes(integration.type)) {
              type = 'google';
            } else if (integration.type === 'microsoft_teams' || integration.type === 'microsoft-teams') {
              type = 'microsoft';
            }
            console.log('[GLOBAL OAUTH] Found integration type from database:', integration.type, '-> routing type:', type);
          } else {
            console.log('[GLOBAL OAUTH] Integration not found in database:', integrationId);
          }
        } catch (dbError: any) {
          console.error('[GLOBAL OAUTH] Error checking integration in database:', dbError);
          // Fall back to the old inference logic as last resort
          if (stateStr.includes('_') && !stateStr.includes('"')) {
            type = 'microsoft'; // Simple format suggests Microsoft
          } else {
            type = 'google'; // JSON format suggests Google
          }
          console.log('[GLOBAL OAUTH] Fallback inferred type:', type);
        }
      }
      
      if (type === 'google') {
        console.log('[GLOBAL OAUTH] Redirecting to Google callback for integration:', integrationId);
        return res.redirect(`/api/integrations/${integrationId}/oauth/callback?code=${code}&state=${state}`);
      } else if (type === 'microsoft') {
        console.log('[GLOBAL OAUTH] Redirecting to Teams callback for integration:', integrationId);
        return res.redirect(`/api/integrations/${integrationId}/teams/oauth/callback?code=${code}&state=${state}`);
      }
      
      console.log('[GLOBAL OAUTH] Unknown type:', type);
      res.redirect('/integrations?error=invalid_state_type');
    } catch (error: any) {
      console.error('[GLOBAL OAUTH] Error parsing OAuth state:', error);
      res.redirect('/integrations?error=invalid_state');
    }
  });

  // ===== GOOGLE-SPECIFIC ROUTES =====
  
  // Google Account Management
  app.get('/api/integrations/google/status', (req, res) => googleAccountController.getGoogleStatus(req, res));
  app.get('/api/integrations/connected-accounts', (req, res) => googleAccountController.getConnectedAccounts(req, res));
  app.post('/api/integrations/disconnect-account', (req, res) => googleAccountController.disconnectAccount(req, res));
  
  // Account Management Routes
  app.get('/api/accounts', async (req, res) => {
    try {
      // Mock response for now - in real implementation, this would fetch from database
      const accounts = [
        {
          provider: 'google',
          email: '<EMAIL>',
          displayName: 'John Doe',
          connectedAt: '2024-01-15T10:30:00Z',
          lastUsed: '2024-01-20T14:22:00Z',
          services: [
            {
              name: 'Google Drive',
              type: 'google_drive',
              status: 'connected',
              integrationId: 57,
              lastSyncAt: '2024-01-20T14:22:00Z'
            },
            {
              name: 'Gmail',
              type: 'gmail',
              status: 'connected',
              integrationId: 60,
              lastSyncAt: '2024-01-20T12:15:00Z'
            },
            {
              name: 'Google Calendar',
              type: 'google_calendar',
              status: 'connected',
              integrationId: 61,
              lastSyncAt: '2024-01-20T13:45:00Z'
            }
          ],
          scopes: ['drive', 'gmail', 'calendar'],
          isActive: true
        }
      ];
      
      res.json({ accounts });
    } catch (error: any) {
      console.error('Error fetching accounts:', error);
      res.status(500).json({ message: 'Failed to fetch accounts', error: error.message });
    }
  });
  
  app.post('/api/accounts/disconnect', async (req, res) => {
    try {
      const { provider, email } = req.body;
      
      if (!provider || !email) {
        return res.status(400).json({ message: 'Provider and email are required' });
      }
      
      // Mock implementation - in real implementation, this would:
      // 1. Find all integrations for this account
      // 2. Delete/disconnect them
      // 3. Remove stored tokens
      // 4. Revoke OAuth permissions
      
      console.log(`Disconnecting account: ${email} (${provider})`);
      
      res.json({ 
        message: 'Account disconnected successfully',
        disconnectedServices: 3
      });
    } catch (error: any) {
      console.error('Error disconnecting account:', error);
      res.status(500).json({ message: 'Failed to disconnect account', error: error.message });
    }
  });
  
  // Gmail-specific routes
  app.get('/api/integrations/:id/gmail/profile', (req, res) => googleGmailController.getGmailProfile(req, res));
  app.post('/api/integrations/:id/gmail/sync-all', (req, res) => googleGmailController.syncAllEmails(req, res));
  app.post('/api/integrations/:id/gmail/sync-recent', (req, res) => googleGmailController.syncRecentEmails(req, res));
  app.get('/api/integrations/:id/gmail/stats', (req, res) => googleGmailController.getEmailStats(req, res));
  app.post('/api/integrations/:id/gmail/test', (req, res) => googleGmailController.testGmailConnection(req, res));

  // Calendar-specific routes (placeholder - using Gmail controller pattern)
  app.get('/api/integrations/:id/calendar/profile', (req, res) => {
    // For now, return a mock response - this can be implemented later
    res.json({ 
      user: { email: '<EMAIL>', displayName: 'Calendar User' },
      timeZone: 'America/New_York'
    });
  });
  app.post('/api/integrations/:id/calendar/sync-all', (req, res) => {
    res.json({ message: 'Calendar sync started', status: 'success' });
  });
  app.post('/api/integrations/:id/calendar/sync-recent', (req, res) => {
    res.json({ message: 'Calendar sync started', status: 'success' });
  });
  app.get('/api/integrations/:id/calendar/stats', (req, res) => {
    res.json({ 
      totalEvents: 150,
      upcomingEvents: 15,
      meetingEvents: 45,
      allDayEvents: 12,
      calendarsCount: 3
    });
  });
  app.post('/api/integrations/:id/calendar/test', (req, res) => {
    res.json({ success: true, message: 'Calendar connection successful' });
  });
} 