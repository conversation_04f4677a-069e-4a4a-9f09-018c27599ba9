
> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Scheduled integration 1 with cron: 0 9 * * * (next run: Fri May 23 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
5:07:46 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
5:07:49 PM [express] GET /api/sync-logs 304 in 86ms :: {"logs":[{"id":22,"integrationId":1,"startTim…
5:07:49 PM [express] GET /api/integrations 304 in 402ms :: {"integrations":[{"id":1,"name":"Google M…
5:07:58 PM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
5:07:58 PM [express] GET /api/sync-logs 304 in 75ms :: {"logs":[{"id":22,"integrationId":1,"startTim…
5:07:58 PM [express] GET /api/integrations 304 in 77ms :: {"integrations":[{"id":1,"name":"Google Me…
5:07:58 PM [express] GET /api/sync-logs 304 in 76ms :: {"logs":[{"id":22,"integrationId":1,"startTim…
5:08:23 PM [express] GET /api/integrations 304 in 347ms :: {"integrations":[{"id":1,"name":"Google M…
OpenAI service initialized successfully
5:11:05 PM [express] POST /api/sync-now 200 in 3133ms :: {"message":"Sync started successfully","syn…
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
5:11:05 PM [express] GET /api/integrations 200 in 79ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Found 0 files in processed folder
After filtering: 3 files remaining to process
Extracting content from document: Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (ID: 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk)
Successfully extracted content (40694 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Technical Developments Meeting
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.'
}
Error creating transcript page: APIResponseError: Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:456:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:11:11 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '242',
    connection: 'keep-alive',
    'cf-ray': '943dee6a9a4a44ef-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"f2-CZYr8iLpOeN8CgWJ0mV8Z9biaLs"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '4e250e68-ce19-4555-9819-f40359d1ce04',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=WShAKmzKGX8z4npv7qRIfKWoA.xxcsiaHLals0FZNO0-1747933871-1.0.1.1-ZDC34YZHo7SvXc69Px1SKyTapnZy4uOmA38wgmCu6xK1IKW0lQpoXI6bDSgPxb4WPGE2tinU4lyG4PhE2vgbWYpGKQWbcvzxR0JxHX_Vf0M; path=/; expires=Thu, 22-May-25 17:41:11 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=Lfo0Bl519ZzUe9ijiRqBeqllK0c3fqU_VNCCvyS01kA-1747933871947-0.0.1.1-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.","request_id":"4e250e68-ce19-4555-9819-f40359d1ce04"}'
}
Error processing item Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk): APIResponseError: Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:456:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:11:11 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '242',
    connection: 'keep-alive',
    'cf-ray': '943dee6a9a4a44ef-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"f2-CZYr8iLpOeN8CgWJ0mV8Z9biaLs"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '4e250e68-ce19-4555-9819-f40359d1ce04',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=WShAKmzKGX8z4npv7qRIfKWoA.xxcsiaHLals0FZNO0-1747933871-1.0.1.1-ZDC34YZHo7SvXc69Px1SKyTapnZy4uOmA38wgmCu6xK1IKW0lQpoXI6bDSgPxb4WPGE2tinU4lyG4PhE2vgbWYpGKQWbcvzxR0JxHX_Vf0M; path=/; expires=Thu, 22-May-25 17:41:11 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=Lfo0Bl519ZzUe9ijiRqBeqllK0c3fqU_VNCCvyS01kA-1747933871947-0.0.1.1-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.","request_id":"4e250e68-ce19-4555-9819-f40359d1ce04"}'
}
File Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini will remain in source folder for retry in next sync
Extracting content from document: Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini (ID: 16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E)
Successfully extracted content (35496 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Development of an Integrated Meeting Transcription and Platform Automation Tool
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.'
}
Error creating transcript page: APIResponseError: Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:456:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:11:16 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '242',
    connection: 'keep-alive',
    'cf-ray': '943dee869ca7455a-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"f2-6DDop4H+pWFxLkwdhdY79ocl/Tg"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '867dbfcd-76fa-44fa-a77c-84c2cc5ed28f',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=s4mdhAXE5kL.NZVH78gRGJa6LL.NOdD2cGcJ_pYfme4-1747933876-1.0.1.1-AhoMaqitKNLaAprRr2v0uknDhDALEDg9zTNfpIvyZ0qn_97tfqkwxB8Ni_JDhnmhgMx2sMxLwX21GuKmtpTx6O3mScETVk29EJb9jjZz2KQ; path=/; expires=Thu, 22-May-25 17:41:16 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=fJkTzgu.Jd5UbSgwRyNjn0qFtPbVkv8zNOe9ADBKsZc-1747933876485-0.0.1.1-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.","request_id":"867dbfcd-76fa-44fa-a77c-84c2cc5ed28f"}'
}
Error processing item Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini (16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E): APIResponseError: Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:456:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:11:16 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '242',
    connection: 'keep-alive',
    'cf-ray': '943dee869ca7455a-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"f2-6DDop4H+pWFxLkwdhdY79ocl/Tg"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '867dbfcd-76fa-44fa-a77c-84c2cc5ed28f',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=s4mdhAXE5kL.NZVH78gRGJa6LL.NOdD2cGcJ_pYfme4-1747933876-1.0.1.1-AhoMaqitKNLaAprRr2v0uknDhDALEDg9zTNfpIvyZ0qn_97tfqkwxB8Ni_JDhnmhgMx2sMxLwX21GuKmtpTx6O3mScETVk29EJb9jjZz2KQ; path=/; expires=Thu, 22-May-25 17:41:16 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=fJkTzgu.Jd5UbSgwRyNjn0qFtPbVkv8zNOe9ADBKsZc-1747933876485-0.0.1.1-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.","request_id":"867dbfcd-76fa-44fa-a77c-84c2cc5ed28f"}'
}
File Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini will remain in source folder for retry in next sync
Extracting content from document: Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (ID: 16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ)
Successfully extracted content (24949 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Automating Meeting Notes and Integration Workflow
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.'
}
Error creating transcript page: APIResponseError: Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:456:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:11:20 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '242',
    connection: 'keep-alive',
    'cf-ray': '943deea27ddb6785-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"f2-Of1ugmidTy0YYtsy3QToNQltoiU"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '8feaaf0e-adf7-4273-947c-57cd85908a7b',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=6QovOjj5EiiqTHTVTGGK3UjTeUypetmHlMKq1OGAJug-1747933880-1.0.1.1-pxWGKq3byv4LX9eRXzWwI88Jj1YRhVFKoGF_XzpGyDoIBeAcDlw0H31OgNUHgukAj0nRVtho9Jy1iwAEYOHhm9SReyXWJok.2MrV9Zl4fZI; path=/; expires=Thu, 22-May-25 17:41:20 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=fzgX36jYXFTKsmIspYrVUT.xhgB1lzR5RwyTbb28MYw-1747933880929-0.0.1.1-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.","request_id":"8feaaf0e-adf7-4273-947c-57cd85908a7b"}'
}
Error processing item Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ): APIResponseError: Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:456:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:11:20 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '242',
    connection: 'keep-alive',
    'cf-ray': '943deea27ddb6785-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"f2-Of1ugmidTy0YYtsy3QToNQltoiU"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '8feaaf0e-adf7-4273-947c-57cd85908a7b',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=6QovOjj5EiiqTHTVTGGK3UjTeUypetmHlMKq1OGAJug-1747933880-1.0.1.1-pxWGKq3byv4LX9eRXzWwI88Jj1YRhVFKoGF_XzpGyDoIBeAcDlw0H31OgNUHgukAj0nRVtho9Jy1iwAEYOHhm9SReyXWJok.2MrV9Zl4fZI; path=/; expires=Thu, 22-May-25 17:41:20 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=fzgX36jYXFTKsmIspYrVUT.xhgB1lzR5RwyTbb28MYw-1747933880929-0.0.1.1-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Departments is not a property that exists. Tags is not a property that exists. SyncSource is not a property that exists.","request_id":"8feaaf0e-adf7-4273-947c-57cd85908a7b"}'
}
File Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini will remain in source folder for retry in next sync
Sync completed with status: failed
Items processed: 3, Success: 0, Failed: 3