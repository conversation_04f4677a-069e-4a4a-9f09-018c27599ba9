// DEPRECATED: This file is being modularized for better maintainability
// New imports should use the modular structure from ./schemas/ and ./types/
// This file is kept for backward compatibility during the migration

// Re-export everything from the new modular structure
export * from './schemas';
export * from './types';

// Legacy exports for backward compatibility
export {
  users,
  integrations,
  syncLogs,
  syncItems,
  files,
  fileChunks,
  chatSessions,
  chatMessages,
  projects,
  oauthTokens,
} from './schemas';

export {
  insertUserSchema,
  insertIntegrationSchema,
  insertSyncLogSchema,
  insertSyncItemSchema,
  insertFileSchema,
  insertFileChunkSchema,
  insertChatSessionSchema,
  insertChatMessageSchema,
  insertProjectSchema,
  insertOAuthTokenSchema,
} from './schemas';

// Legacy type exports
export type {
  User,
  InsertUser,
  Integration,
  InsertIntegration,
  SyncLog,
  InsertSyncLog,
  SyncItem,
  InsertSyncItem,
  File,
  InsertFile,
  FileChunk,
  InsertFileChunk,
  ChatSession,
  InsertChatSession,
  ChatMessage,
  InsertChatMessage,
  Project,
  InsertProject,
} from './types';

// OAuth types from schemas (avoiding conflict by explicit import)
export type {
  OAuthToken,
  NewOAuthToken,
} from './schemas/oauth.schema';

// Legacy validation schemas - use imports from ./types/ for new code
import { z } from "zod";

export const googleOAuthSchema = z.object({
  code: z.string(),
  redirectUri: z.string().url(),
});

export const scheduleUpdateSchema = z.object({
  integrationId: z.number(),
  schedule: z.string(),
  enabled: z.boolean().default(true),
});

export const syncNowSchema = z.object({
  integrationId: z.number(),
});

export const integrationConfigSchema = z.object({
  name: z.string().min(3),
  type: z.enum(["google-drive", "google_drive", "microsoft_teams", "slack", "gmail", "google_calendar", "google-calendar"]),
  config: z.record(z.any()).optional(),
  sourceConfig: z.record(z.any()).optional(),
  destinationConfig: z.record(z.any()).optional(),
  isLlmEnabled: z.boolean().default(true),
  syncFilters: z.record(z.any()).optional(),
  syncSchedule: z.union([z.string(), z.null()]).optional(),
  status: z.string().optional(),
});

export const createChatSessionSchema = z.object({
  title: z.string().optional(),
  enabledSources: z.array(z.string()).optional(),
  userId: z.string().optional(),
});

export const sendChatMessageSchema = z.object({
  content: z.string().min(1),
  enabledSources: z.array(z.string()).optional(),
});

export const createProjectSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  enabledSources: z.array(z.string()).optional(),
  userId: z.string().optional(),
});
