import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { getAuthUrl, updateIntegration } from "@/lib/api";
import { getIntegrationName } from "@/lib/utils/integrations";
import { IntegrationIcon } from "@/components/ui/integration-icons";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const integrationFormSchema = z.object({
  name: z.string().min(3, { message: "Name must be at least 3 characters" }),
  authMethod: z.enum(["oauth", "service_account"]),
  driveSource: z.string().optional(),
  enableTranscripts: z.boolean().default(true),
  enableChatLogs: z.boolean().default(true),
  enableLlm: z.boolean().default(true),
});

type IntegrationFormValues = z.infer<typeof integrationFormSchema>;

interface IntegrationWizardProps {
  step: number;
  integration: any;
  onNext: () => void;
  onCancel: () => void;
}

export default function IntegrationWizard({
  step,
  integration,
  onNext,
  onCancel,
}: IntegrationWizardProps) {
  const { toast } = useToast();
  const [location, navigate] = useLocation();
  
  const defaultValues: Partial<IntegrationFormValues> = {
    name: integration?.name || `${integration?.type === 'google_drive' || integration?.type === 'google-drive' ? 'Google Drive' : 'Google Workspace'} Integration`,
    authMethod: "oauth",
    driveSource: integration?.sourceConfig?.driveId || "Shared Documents Drive",
    enableTranscripts: true,
    enableChatLogs: true,
    enableLlm: integration?.isLlmEnabled ?? true,
  };

  const form = useForm<IntegrationFormValues>({
    resolver: zodResolver(integrationFormSchema),
    defaultValues,
  });

  const authUrlMutation = useMutation({
    mutationFn: (id: number) =>
      getAuthUrl(id, `${window.location.origin}/integrations/${id}/setup?step=2`),
    onSuccess: (data) => {
      window.location.href = data.authUrl;
    },
    onError: (error) => {
      toast({
        title: "Authentication failed",
        description: `Failed to generate authentication URL: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: any) => updateIntegration(integration.id, data),
    onSuccess: () => {
      toast({
        title: "Integration updated",
        description: "Integration configured successfully",
      });
      onNext();
    },
    onError: (error) => {
      toast({
        title: "Configuration failed",
        description: `Failed to update integration: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: IntegrationFormValues) => {
    if (step === 1) {
      // Step 1: Update basic configuration and start OAuth flow
      updateMutation.mutate({
        name: values.name,
        isLlmEnabled: values.enableLlm,
        sourceConfig: {
          driveId: values.driveSource,
        },
        config: {
          ...integration.config,
          enableTranscripts: values.enableTranscripts,
          enableChatLogs: values.enableChatLogs,
        }
      });
      
      // After saving, initialize OAuth flow
      if (values.authMethod === "oauth") {
        authUrlMutation.mutate(integration.id);
      }
    } else if (step === 2) {
      // Step 2: Additional configuration
      updateMutation.mutate({
        sourceConfig: {
          ...integration.sourceConfig,
          driveId: values.driveSource,
        },
        config: {
          ...integration.config,
          enableTranscripts: values.enableTranscripts,
          enableChatLogs: values.enableChatLogs,
        }
      });
    } else if (step === 3) {
      // Step 3: Schedule configuration
      onNext();
    } else if (step === 4) {
      // Step 4: Review and completion
      navigate("/integrations");
    }
  };

  // Using centralized logo and name functions from utilities

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="border-b border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center space-x-1">
          {[1, 2, 3, 4].map((num) => (
            <>
              <div
                className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${
                  num === step
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-700"
                }`}
              >
                {num}
              </div>
              {num < 4 && (
                <div
                  className={`h-px flex-grow ${
                    num < step ? "bg-primary" : "bg-gray-200"
                  }`}
                ></div>
              )}
            </>
          ))}
        </div>
        <div className="flex justify-between mt-2 text-xs text-gray-500">
          <span className={step >= 1 ? "font-medium text-primary" : ""}>
            Connect
          </span>
          <span className={step >= 2 ? "font-medium text-primary" : ""}>
            Configure
          </span>
          <span className={step >= 3 ? "font-medium text-primary" : ""}>
            Schedule
          </span>
          <span className={step >= 4 ? "font-medium text-primary" : ""}>
            Review
          </span>
        </div>
      </div>

      <div className="p-6">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 rounded bg-white border border-gray-100 flex items-center justify-center">
            <IntegrationIcon type={integration?.type || ''} size={32} />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900">{getIntegrationName(integration?.type)}</h3>
            <p className="text-sm text-gray-500">
              Connect to {getIntegrationName(integration?.type)} to sync all your files and documents for AI-powered search
            </p>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {step === 1 && (
              <>
                <FormField
                  control={form.control}
                  name="authMethod"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Authentication Method</FormLabel>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex space-x-4"
                      >
                        <FormControl>
                          <RadioGroupItem value="oauth" id="oauth" />
                        </FormControl>
                        <FormLabel htmlFor="oauth">OAuth 2.0</FormLabel>
                      </RadioGroup>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="border rounded-md p-4 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">OAuth 2.0 Connection</h4>
                  {integration?.type === 'microsoft_teams' ? (
                    <>
                      <p className="text-sm text-gray-600 mb-4">
                        Click the button below to authenticate with your Microsoft account. This will give the app permission to read your Microsoft Teams meetings and chat logs.
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        className="inline-flex items-center"
                        onClick={() => authUrlMutation.mutate(integration.id)}
                        disabled={authUrlMutation.isPending}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                        </svg>
                        {authUrlMutation.isPending ? "Connecting..." : "Connect Microsoft Teams"}
                      </Button>
                    </>
                  ) : (
                    <>
                      <p className="text-sm text-gray-600 mb-4">
                        We need permission to access your Google Drive files, documents, and meeting content for AI-powered search and analysis.
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        className="inline-flex items-center"
                        onClick={() => authUrlMutation.mutate(integration.id)}
                        disabled={authUrlMutation.isPending}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                        </svg>
                        {authUrlMutation.isPending ? "Connecting..." : "Connect with Google"}
                      </Button>
                    </>
                  )}
                </div>

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Integration Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormDescription className="text-xs text-gray-500">
                        This name will be displayed on the dashboard
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <FormLabel className="block text-sm font-medium text-gray-700 mb-1">Data Types</FormLabel>
                    <div className="space-y-2">
                      <FormField
                        control={form.control}
                        name="enableTranscripts"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Documents & Files</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="enableChatLogs"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Chat & Messages</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="driveSource"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Drive Source</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a drive" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Shared Documents Drive">Shared Documents Drive</SelectItem>
                            <SelectItem value="My Drive">My Drive</SelectItem>
                            <SelectItem value="Other Shared Drive">Other Shared Drive</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The Google Drive where your documents and files are stored
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="enableLlm"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1">LLM Metadata Extraction</FormLabel>
                      <div className="flex items-center mb-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            id="enable-llm"
                          />
                        </FormControl>
                        <label htmlFor="enable-llm" className="ml-2 block text-sm text-gray-700">
                          Enable AI-powered metadata extraction
                        </label>
                      </div>
                      <FormDescription className="text-xs text-gray-500">
                        Uses AI to extract metadata, topics, and key information from all document types
                      </FormDescription>
                    </FormItem>
                  )}
                />
              </>
            )}

            {step === 2 && (
              <>
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-gray-900">Configure Integration Settings</h3>
                  <p className="text-sm text-gray-600">
                    Configure how your {getIntegrationName(integration?.type)} integration works
                  </p>
                  
                  <FormField
                    control={form.control}
                    name="driveSource"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Source Drive</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a drive" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Shared Documents Drive">Shared Documents Drive</SelectItem>
                            <SelectItem value="My Drive">My Drive</SelectItem>
                            <SelectItem value="Other Shared Drive">Other Shared Drive</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The Google Drive where your documents and files are stored
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="enableLlm"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>AI Processing</FormLabel>
                        <div className="flex items-center">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              id="enable-llm-processing"
                            />
                          </FormControl>
                          <label htmlFor="enable-llm-processing" className="ml-2 block text-sm text-gray-700">
                            Enable OpenAI processing for metadata extraction
                          </label>
                        </div>
                        <FormDescription>
                          Extracts key information, topics, and metadata from all document types
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>
              </>
            )}

            {step === 3 && (
              <>
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-gray-900">Configure Sync Schedule</h3>
                  <p className="text-sm text-gray-600">
                    Set up when your {getIntegrationName(integration?.type)} integration should sync
                  </p>
                  
                  <div className="border p-4 rounded-md bg-gray-50">
                    <h4 className="text-sm font-medium mb-3">Sync Frequency</h4>
                    
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <input type="radio" id="hourly" name="schedule" className="h-4 w-4 text-primary focus:ring-primary border-gray-300" />
                        <label htmlFor="hourly" className="text-sm text-gray-700">Hourly</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input type="radio" id="daily" name="schedule" className="h-4 w-4 text-primary focus:ring-primary border-gray-300" checked />
                        <label htmlFor="daily" className="text-sm text-gray-700">Daily</label>
                        <Select defaultValue="09:00">
                          <SelectTrigger className="w-24 h-8 text-xs">
                            <SelectValue placeholder="Time" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="00:00">12:00 AM</SelectItem>
                            <SelectItem value="03:00">3:00 AM</SelectItem>
                            <SelectItem value="06:00">6:00 AM</SelectItem>
                            <SelectItem value="09:00">9:00 AM</SelectItem>
                            <SelectItem value="12:00">12:00 PM</SelectItem>
                            <SelectItem value="15:00">3:00 PM</SelectItem>
                            <SelectItem value="18:00">6:00 PM</SelectItem>
                            <SelectItem value="21:00">9:00 PM</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input type="radio" id="weekly" name="schedule" className="h-4 w-4 text-primary focus:ring-primary border-gray-300" />
                        <label htmlFor="weekly" className="text-sm text-gray-700">Weekly</label>
                        <Select defaultValue="1">
                          <SelectTrigger className="w-32 h-8 text-xs">
                            <SelectValue placeholder="Day" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">Monday</SelectItem>
                            <SelectItem value="2">Tuesday</SelectItem>
                            <SelectItem value="3">Wednesday</SelectItem>
                            <SelectItem value="4">Thursday</SelectItem>
                            <SelectItem value="5">Friday</SelectItem>
                            <SelectItem value="6">Saturday</SelectItem>
                            <SelectItem value="0">Sunday</SelectItem>
                          </SelectContent>
                        </Select>
                        <Select defaultValue="09:00">
                          <SelectTrigger className="w-24 h-8 text-xs">
                            <SelectValue placeholder="Time" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="00:00">12:00 AM</SelectItem>
                            <SelectItem value="03:00">3:00 AM</SelectItem>
                            <SelectItem value="06:00">6:00 AM</SelectItem>
                            <SelectItem value="09:00">9:00 AM</SelectItem>
                            <SelectItem value="12:00">12:00 PM</SelectItem>
                            <SelectItem value="15:00">3:00 PM</SelectItem>
                            <SelectItem value="18:00">6:00 PM</SelectItem>
                            <SelectItem value="21:00">9:00 PM</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input type="radio" id="custom" name="schedule" className="h-4 w-4 text-primary focus:ring-primary border-gray-300" />
                        <label htmlFor="custom" className="text-sm text-gray-700">Custom (Cron expression)</label>
                      </div>
                      
                      <div className="pl-6">
                        <Input placeholder="*/30 * * * *" className="text-xs" disabled />
                        <p className="text-xs text-gray-500 mt-1">Example: "*/30 * * * *" for every 30 minutes</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center mt-4">
                    <Checkbox id="enable-auto-sync" defaultChecked />
                    <label htmlFor="enable-auto-sync" className="ml-2 text-sm text-gray-700">Enable automatic synchronization</label>
                  </div>
                </div>
              </>
            )}

            {step === 4 && (
              <>
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-gray-900">Review Integration</h3>
                  <p className="text-sm text-gray-600">
                    Review your {getIntegrationName(integration?.type)} integration settings
                  </p>
                  
                  <div className="border rounded-md p-4 space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700">General Settings</h4>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <div className="text-sm text-gray-500">Integration Name</div>
                        <div className="text-sm font-medium">{form.watch("name")}</div>
                        
                        <div className="text-sm text-gray-500">Type</div>
                        <div className="text-sm font-medium">{getIntegrationName(integration?.type)}</div>
                        
                        <div className="text-sm text-gray-500">Authentication</div>
                        <div className="text-sm font-medium">OAuth 2.0</div>
                        
                        <div className="text-sm text-gray-500">Status</div>
                        <div className="text-sm font-medium text-green-600 flex items-center">
                          <span className="h-2 w-2 bg-green-500 rounded-full mr-1.5"></span>
                          Connected
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-700">Data Settings</h4>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <div className="text-sm text-gray-500">Source</div>
                        <div className="text-sm font-medium">{form.watch("driveSource")}</div>
                        
                        <div className="text-sm text-gray-500">Data Types</div>
                        <div className="text-sm font-medium">
                          {form.watch("enableTranscripts") && "Documents & Files"}
                          {form.watch("enableTranscripts") && form.watch("enableChatLogs") && ", "}
                          {form.watch("enableChatLogs") && "Chat & Messages"}
                        </div>
                        
                        <div className="text-sm text-gray-500">AI Processing</div>
                        <div className="text-sm font-medium">{form.watch("enableLlm") ? "Enabled" : "Disabled"}</div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-700">Schedule</h4>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <div className="text-sm text-gray-500">Frequency</div>
                        <div className="text-sm font-medium">Daily at 9:00 AM</div>
                        
                        <div className="text-sm text-gray-500">Auto-Sync</div>
                        <div className="text-sm font-medium">Enabled</div>
                        
                        <div className="text-sm text-gray-500">Next Sync</div>
                        <div className="text-sm font-medium">May 9, 2023 - 9:00 AM</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center py-2 px-4 bg-blue-50 text-primary text-sm rounded-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Your integration is ready to use. Click Finish to save and return to the integrations page.
                  </div>
                </div>
              </>
            )}

            <div className="mt-8 flex justify-end space-x-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {step === 4 ? "Cancel" : "Back"}
              </Button>
              <Button type="submit" disabled={step === 1 && authUrlMutation.isPending}>
                {step === 4 ? "Finish" : "Continue"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
