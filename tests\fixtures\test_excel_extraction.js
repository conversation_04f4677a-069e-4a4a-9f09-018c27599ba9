// Quick test to validate Excel extraction logic
import { unifiedContentExtractor } from './server/services/unified-content-extractor.service.js';

const testPlatformMetadata = {
  platform: 'test',
  fileName: 'test.xlsx',
  fileType: 'spreadsheet',
  mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
};

// Simple test with minimal Excel content
const simpleExcelBuffer = Buffer.from('test content for spreadsheet', 'utf-8');

async function testExcelExtraction() {
  console.log('Testing Excel extraction...');
  
  try {
    await unifiedContentExtractor.initialize();
    const result = await unifiedContentExtractor.extractContent(testPlatformMetadata, simpleExcelBuffer);
    
    console.log('Result:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testExcelExtraction(); 