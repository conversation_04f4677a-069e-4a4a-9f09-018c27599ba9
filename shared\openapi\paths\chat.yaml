ChatSessions:
  get:
    tags:
      - Chat
    summary: Get all chat sessions
    description: Retrieve a list of all chat sessions
    operationId: getChatSessions
    parameters:
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/OffsetParam'
      - $ref: '../components/parameters.yaml#/UserId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/ChatSessionsListResponse'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  post:
    tags:
      - Chat
    summary: Create chat session
    description: Create a new chat session
    operationId: createChatSession
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yaml#/CreateChatSession'
    responses:
      '201':
        $ref: '../components/responses.yaml#/ChatSessionResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ChatSessionById:
  get:
    tags:
      - Chat
    summary: Get chat session by ID
    description: Retrieve a specific chat session by its ID
    operationId: getChatSession
    parameters:
      - $ref: '../components/parameters.yaml#/SessionId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/ChatSessionResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  put:
    tags:
      - Chat
    summary: Update chat session
    description: Update an existing chat session
    operationId: updateChatSession
    parameters:
      - $ref: '../components/parameters.yaml#/SessionId'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yaml#/CreateChatSession'
    responses:
      '200':
        $ref: '../components/responses.yaml#/ChatSessionResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  delete:
    tags:
      - Chat
    summary: Delete chat session
    description: Delete a chat session and all associated messages
    operationId: deleteChatSession
    parameters:
      - $ref: '../components/parameters.yaml#/SessionId'
    responses:
      '204':
        $ref: '../components/responses.yaml#/NoContent'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ChatMessages:
  get:
    tags:
      - Chat
    summary: Get chat messages
    description: Retrieve messages for a specific chat session
    operationId: getChatMessages
    parameters:
      - $ref: '../components/parameters.yaml#/SessionId'
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/OffsetParam'
    responses:
      '200':
        $ref: '../components/responses.yaml#/ChatMessagesListResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  post:
    tags:
      - Chat
    summary: Send chat message
    description: Send a new message in a chat session
    operationId: sendChatMessage
    parameters:
      - $ref: '../components/parameters.yaml#/SessionId'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - content
            properties:
              content:
                type: string
                description: The message content
              role:
                type: string
                enum: [user, assistant]
                default: user
              enabledSources:
                type: array
                items:
                  type: string
                description: Sources to use for this message
    responses:
      '201':
        description: Message sent successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  $ref: '../components/schemas.yaml#/ChatMessage'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ChatSources:
  get:
    tags:
      - Chat
    summary: Get available sources
    description: Retrieve available sources for chat functionality
    operationId: getAvailableSources
    responses:
      '200':
        description: Available sources retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                sources:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      name:
                        type: string
                      type:
                        type: string
                      enabled:
                        type: boolean
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ChatSearch:
  post:
    tags:
      - Chat
    summary: Search similar content
    description: Search for similar content using semantic search
    operationId: searchSimilar
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yaml#/RagSearchRequest'
    responses:
      '200':
        $ref: '../components/responses.yaml#/RagSearchResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ChatSearchFiles:
  post:
    tags:
      - Chat
    summary: Search files for chat
    description: Search files specifically for chat context
    operationId: searchFilesForChat
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - query
            properties:
              query:
                type: string
              limit:
                type: integer
                default: 5
              enabledSources:
                type: array
                items:
                  type: string
              platforms:
                type: array
                items:
                  type: string
    responses:
      '200':
        description: File search results
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                results:
                  type: array
                  items:
                    type: object
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 