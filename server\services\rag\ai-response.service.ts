import OpenAI from "openai";
import { BaseService } from "../base/service.interface";
import { MCPManager } from "../mcp/mcp-manager.service";

/**
 * RAG AI Response Service - handles AI response generation with OpenAI
 */
export class RAGAIResponseService extends BaseService {
  private openai!: OpenAI;
  private mcpManager!: MCPManager;

  constructor() {
    super('RAGAIResponse', '1.0.0', 'AI response generation with OpenAI and function calling');
  }

  protected async onInitialize(): Promise<void> {
    this.validateEnvironment(['OPENAI_API_KEY']);
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.mcpManager = new MCPManager();

    this.log('info', 'RAG AI Response service initialized');
  }

  protected getDependencies(): string[] {
    return ['mcp-manager'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    try {
      // Test OpenAI connectivity with a simple request
      const models = await this.openai.models.list();
      return {
        openaiConnected: true,
        hasApiKey: !!process.env.OPENAI_API_KEY,
        modelCount: models.data.length,
        defaultModel: this.getDefaultModel(),
      };
    } catch (error) {
      return {
        openaiConnected: false,
        hasApiKey: !!process.env.OPENAI_API_KEY,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Generate AI response using ChatGPT with context and agentic actions
   * Inspired by LlamaIndex tutorial Part 3 - supports function calling
   */
  async generateAIResponse(
    userMessage: string,
    context: string,
    chatHistory: any[],
    enabledSources: string[],
    relevantChunks: any[]
  ): Promise<string> {
    this.ensureInitialized();
    this.validateString(userMessage, 'user message');

    try {
      this.log('info', `Generating AI response for: "${userMessage.substring(0, 100)}..."`);
      this.log('info', `Context length: ${context.length} characters`);
      this.log('info', `Chat history length: ${chatHistory.length} messages`);

      // Build initial conversation messages
      let messages = this.buildConversationMessages(
        userMessage,
        context,
        chatHistory,
        enabledSources,
        relevantChunks
      );

      // Get available function tools
      const tools = this.mcpManager.getAvailableTools();
      this.log('info', `Available function tools: ${tools.map(tool => tool.function.name).join(', ')}`);

      let finalResponse = '';
      let maxIterations = 5; // Prevent infinite loops
      let currentIteration = 0;

      while (currentIteration < maxIterations) {
        currentIteration++;
        this.log('info', `Function calling iteration ${currentIteration}`);

        const response = await this.openai.chat.completions.create({
          model: this.getDefaultModel(),
          messages,
          max_tokens: this.getMaxTokens(),
          temperature: this.getTemperature(),
          tools: tools.length > 0 ? tools : undefined,
          ...(tools.length > 0 ? { tool_choice: "auto" } : {}),
        });

        const message = response.choices[0].message;

        // If there are function calls, execute them and add results to the conversation
        if (message.tool_calls && message.tool_calls.length > 0) {
          this.log('info', `Executing ${message.tool_calls.length} function calls in iteration ${currentIteration}`);
          
          // Add assistant's message if it exists
          if (message.content) {
            finalResponse += message.content + '\n\n';
            messages.push({ role: 'assistant', content: message.content });
          }

          // Execute each function call
          for (const toolCall of message.tool_calls) {
            if (toolCall.type === "function") {
              const functionName = toolCall.function.name;
              const functionArgs = JSON.parse(toolCall.function.arguments);

              this.log('info', `Executing function: ${functionName}`);

              try {
                const result = await this.mcpManager.callTool(functionName, functionArgs);
                const resultText = `**Function Result (${functionName}):**\n${result}\n\n`;
                finalResponse += resultText;

                // Add function call and result to messages for context
                messages.push({
                  role: 'assistant',
                  content: null,
                  tool_calls: [toolCall]
                });
                messages.push({
                  role: 'tool',
                  tool_call_id: toolCall.id,
                  content: result,
                  name: functionName
                });
              } catch (error: any) {
                const errorText = `**Function Error (${functionName}):**\n${error.message}\n\n`;
                finalResponse += errorText;
                
                // Add error result to messages
                messages.push({
                  role: 'tool',
                  tool_call_id: toolCall.id,
                  content: `Error: ${error.message}`,
                  name: functionName
                });
              }
            }
          }

          // Continue to next iteration to let AI process function results
          continue;
        }

        // No function calls, this is the final response
        if (message.content) {
          finalResponse += message.content;
        }
        break;
      }

      if (currentIteration >= maxIterations) {
        this.log('warn', `Reached maximum iterations (${maxIterations}) for function calling`);
        finalResponse += '\n\n**Note:** Reached maximum number of function call iterations. Some operations may be incomplete.';
      }

      this.log('info', `Final response generated with ${currentIteration} iterations`);
      return finalResponse || "I apologize, but I couldn't generate a response.";

    } catch (error: any) {
      this.handleError(error, 'generating AI response');
    }
  }

  /**
   * Generate streaming AI response - LobeChat style
   */
  async generateStreamingAIResponse(
    userMessage: string,
    context: string,
    chatHistory: any[],
    enabledSources: string[],
    relevantChunks: any[],
    onChunk: (chunk: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    this.ensureInitialized();
    this.validateString(userMessage, 'user message');

    try {
      this.log('info', `Starting streaming AI response for: "${userMessage.substring(0, 100)}..."`);

      // Build initial conversation messages
      let messages = this.buildConversationMessages(
        userMessage,
        context,
        chatHistory,
        enabledSources,
        relevantChunks
      );

      // Get available function tools
      const tools = this.mcpManager.getAvailableTools();
      this.log('info', `Available function tools: ${tools.map(tool => tool.function.name).join(', ')}`);

      let fullResponse = '';
      let maxIterations = 5; // Prevent infinite loops
      let currentIteration = 0;

      while (currentIteration < maxIterations) {
        currentIteration++;
        this.log('info', `Function calling iteration ${currentIteration}`);

        const stream = await this.openai.chat.completions.create({
          model: this.getDefaultModel(),
          messages,
          max_tokens: this.getMaxTokens(),
          temperature: this.getTemperature(),
          tools: tools.length > 0 ? tools : undefined,
          ...(tools.length > 0 ? { tool_choice: "auto" } : {}),
          stream: true,
        });

        let currentMessage = '';
        let toolCalls: any[] = [];
        let hasToolCalls = false;

        for await (const chunk of stream) {
          const delta = chunk.choices[0]?.delta;

          if (delta?.content) {
            const content = delta.content;
            currentMessage += content;
            fullResponse += content;
            onChunk(content);
          }

          if (delta?.tool_calls) {
            hasToolCalls = true;
            for (const toolCall of delta.tool_calls) {
              const existingCall = toolCalls.find(t => t.id === toolCall.id);
              if (existingCall) {
                if (toolCall.function?.arguments) {
                  existingCall.function.arguments += toolCall.function.arguments;
                }
              } else {
                toolCalls.push({
                  id: toolCall.id,
                  type: toolCall.type,
                  function: {
                    name: toolCall.function?.name,
                    arguments: toolCall.function?.arguments || ''
                  }
                });
              }
            }
          }
        }

        // Add assistant's message if it exists
        if (currentMessage) {
          messages.push({ role: 'assistant', content: currentMessage });
        }

        // If there are tool calls, execute them and continue the conversation
        if (hasToolCalls && toolCalls.length > 0) {
          this.log('info', `Executing ${toolCalls.length} function calls in iteration ${currentIteration}`);

          // Add tool calls to messages
          if (toolCalls.length > 0) {
            messages.push({
              role: 'assistant',
              content: null,
              tool_calls: toolCalls
            });
          }

          // Execute each function call
          for (const toolCall of toolCalls) {
            if (toolCall.type === "function") {
              const functionName = toolCall.function.name;
              try {
                const functionArgs = JSON.parse(toolCall.function.arguments);
                
                onChunk(`\n\n**Executing Function: ${functionName}**\n`);
                this.log('info', `Executing function: ${functionName}`);

                const result = await this.mcpManager.callTool(functionName, functionArgs);
                const resultText = `**Function Result (${functionName}):**\n${result}\n\n`;
                
                fullResponse += resultText;
                onChunk(resultText);

                // Add function result to messages
                messages.push({
                  role: 'tool',
                  tool_call_id: toolCall.id,
                  content: result,
                  name: functionName
                });
              } catch (error: any) {
                const errorText = `**Function Error (${functionName}):**\n${error.message}\n\n`;
                fullResponse += errorText;
                onChunk(errorText);

                // Add error result to messages
                messages.push({
                  role: 'tool',
                  tool_call_id: toolCall.id,
                  content: `Error: ${error.message}`,
                  name: functionName
                });
              }
            }
          }

          // Continue to next iteration to let AI process function results
          continue;
        }

        // No function calls, we're done
        break;
      }

      if (currentIteration >= maxIterations) {
        const maxIterationsMessage = '\n\n**Note:** Reached maximum number of function call iterations. Some operations may be incomplete.';
        fullResponse += maxIterationsMessage;
        onChunk(maxIterationsMessage);
        this.log('warn', `Reached maximum iterations (${maxIterations}) for function calling`);
      }

      onComplete(fullResponse);
      this.log('info', `Streaming response completed: ${fullResponse.length} characters`);

    } catch (error: any) {
      this.log('error', 'Error in streaming AI response', error);
      onError(error);
    }
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    userMessage: string,
    context: string,
    chatHistory: any[],
    enabledSources: string[],
    relevantChunks: any[]
  ): any[] {
    const baseSystemPrompt = this.getSystemPrompt();

    const messages: any[] = [
      {
        role: "system",
        content: this.buildSystemPrompt(baseSystemPrompt, context, enabledSources, relevantChunks),
      },
    ];

    // Add recent chat history (excluding system messages)
    const recentHistory = chatHistory
      .filter(msg => msg.role !== "system")
      .slice(-8) // Last 8 messages for context
      .map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

    messages.push(...recentHistory);

    // Add current user message
    messages.push({
      role: "user",
      content: userMessage,
    });

    return messages;
  }

  /**
   * Build comprehensive system prompt
   */
  private buildSystemPrompt(
    basePrompt: string,
    context: string,
    enabledSources: string[],
    relevantChunks: any[]
  ): string {
    return `${basePrompt}

IMPORTANT IDENTITY INFORMATION:
- Your name is GPT Unify - an AI Assistant with both READ and WRITE capabilities
      - You have access to data from multiple integrated platforms through the GPT Unify platform
      - You can search through meeting transcripts, documents, and files from connected sources
      - You are part of the GPT Unify ecosystem that unifies and analyzes data across platforms
- You can access Google Drive files, meeting transcripts, and other connected data sources
- You can CREATE, SEARCH, and MANAGE files using your function tools

AGENTIC CAPABILITIES:
- You can create new files when users request it
- You can search for existing files by name, content, or description
- You can get detailed information about specific files
- You always follow the Human-in-the-Loop (HITL) pattern: Draft → Confirm → Execute
- For any write action, you MUST first create a draft and get user confirmation
- You have access to function tools for file operations

CURRENT SESSION CONTEXT:
- You have access to ${enabledSources.length} data source(s): ${enabledSources.join(", ")}
- Found ${relevantChunks.length} relevant document chunks for this query
- Available sources include Google Drive files, Microsoft Teams documents, uploaded files, and comprehensive document collections

${context ? `RELEVANT DOCUMENT CONTENT:

${context}

Use this information to provide accurate, helpful responses. Always reference which documents or sources you're using when providing information.` : `No specific document content found for this query. You can still help by explaining what data sources are available and how to access them.`}

RESPONSE GUIDELINES:
- NEVER say "give me a moment" or "let me search" - you already have the search results
- Provide direct, immediate answers based on available content
- When referencing information, mention the source file name and location when possible
- If asked about file locations, provide specific folder paths and file names from the metadata
- If you find relevant content, answer immediately with specific details
- If no relevant content is found, clearly explain what sources you searched and suggest alternatives
- Be conversational and helpful, not robotic
- Always remember you have real access to the user's files and documents
- When users describe files they're looking for, help identify them by name and location
- For file search queries, provide file names, locations, and brief descriptions of content
- If users ask "what files do you have access to", list the available sources and explain the types of files
- Always maintain context about what data sources are enabled and available`;
  }

  /**
   * Handle function calls from OpenAI
   */
  private async handleFunctionCalls(message: any): Promise<string> {
    this.log('info', `Function calls detected: ${message.tool_calls.length}`);

    let functionResults = "";

    for (const toolCall of message.tool_calls) {
      if (toolCall.type === "function") {
        const functionName = toolCall.function.name;
        const functionArgs = JSON.parse(toolCall.function.arguments);

        this.log('info', `Executing function: ${functionName}`);

        try {
          const result = await this.mcpManager.callTool(functionName, functionArgs);
          functionResults += `\n\n**Function Result (${functionName}):**\n${result}\n`;

        } catch (error: any) {
          this.log('error', `Function execution error: ${error.message}`);
          functionResults += `\n\n**Function Error (${functionName}):**\n${error.message}\n`;
        }
      }
    }

    return functionResults;
  }

  /**
   * Get default OpenAI model
   */
  private getDefaultModel(): string {
    return process.env.OPENAI_MODEL || "gpt-4-1106-preview";
  }

  /**
   * Get max tokens for response
   */
  private getMaxTokens(): number {
    return parseInt(process.env.OPENAI_MAX_TOKENS || "2000");
  }

  /**
   * Get temperature for response generation
   */
  private getTemperature(): number {
    return parseFloat(process.env.OPENAI_TEMPERATURE || "0.1");
  }

  /**
   * Get system prompt
   */
  private getSystemPrompt(): string {
    return `You are GPT Unify, a specialized AI assistant integrated with multiple platforms to help users access, search, and manage their data across Google Drive, Microsoft Teams, meeting transcripts, and uploaded documents.

YOUR CORE CAPABILITIES:
1. **Multi-Platform Data Access**: You can search and retrieve information from connected Google Drive files, Microsoft Teams documents, meeting transcripts, and uploaded files
2. **Intelligent Search**: You understand context and can find relevant information across different document types and sources
3. **Agentic Actions**: You have tools to create, search, and manage files when requested by users
4. **Real-Time Integration**: You work with live data from integrated platforms

YOUR OPERATIONAL GUIDELINES:
- Be direct and helpful - you already have access to search results and data
- Always reference specific sources when providing information
- Explain what data sources you're searching when users ask about availability
- For file operations, follow the Human-in-the-Loop pattern: Draft → Confirm → Execute
- Maintain awareness of enabled data sources and available content
- Provide actionable insights based on the user's integrated data

Remember: You're not just answering questions - you're helping users leverage their integrated data ecosystem effectively.`;
  }

  /**
   * Handle service errors with context
   */
  protected handleError(error: any, context: string): never {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const fullMessage = `Error ${context}: ${errorMessage}`;
    
    this.log('error', fullMessage, error);
    throw new Error(fullMessage);
  }

  /**
   * Estimate token count for a given text
   */
  estimateTokenCount(text: string): number {
    // Rough estimation: ~4 characters per token for English text
    // This is a simple approximation - for more accuracy, use tiktoken
    return Math.ceil(text.length / 4);
  }

  /**
   * Test OpenAI connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.openai.models.list();
      return true;
    } catch (error) {
      this.log('error', 'OpenAI connection test failed', error);
      return false;
    }
  }

  /**
   * Get available OpenAI models
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const models = await this.openai.models.list();
      return models.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      this.log('error', 'Failed to fetch available models', error);
      return [this.getDefaultModel()];
    }
  }

  /**
   * Convert MCP tools to OpenAI function format
   */
  private getMCPToolsForOpenAI(): any[] {
    try {
      const mcpTools = this.mcpManager.getAvailableTools();
      return mcpTools.map(tool => ({
        type: 'function',
        function: {
          name: tool.function.name,
          description: tool.function.description,
          parameters: tool.function.parameters
        }
      }));
    } catch (error) {
      this.log('warn', `Failed to get MCP tools: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  }

  /**
   * Check if a tool name is an MCP tool
   */
  private isMCPTool(toolName: string): boolean {
    try {
      const mcpTools = this.mcpManager.getAvailableTools();
      return mcpTools.some(tool => tool.function.name === toolName);
    } catch (error) {
      return false;
    }
  }

  /**
   * Execute an MCP tool and return formatted result
   */
  private async executeMCPTool(toolName: string, args: any): Promise<string> {
    try {
      const result = await this.mcpManager.callTool(toolName, args);

      // Format the result for display
      if (result && result.content && Array.isArray(result.content)) {
        return result.content.map((item: any) => item.text || JSON.stringify(item)).join('\n');
      } else if (typeof result === 'string') {
        return result;
      } else {
        return JSON.stringify(result, null, 2);
      }
    } catch (error) {
      this.log('error', `MCP tool execution error: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}
