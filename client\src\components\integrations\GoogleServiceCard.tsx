import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { useToast } from '../../hooks/use-toast';
import { 
  CheckCircleIcon, 
  AlertCircleIcon,
  RefreshCwIcon,
  UserIcon,
  CalendarIcon,
  Loader2Icon,
  ExternalLinkIcon
} from 'lucide-react';

interface ServiceStats {
  total?: number;
  recent?: number;
  unread?: number;
  starred?: number;
  lastSyncAt?: string;
  [key: string]: any; // Allow for service-specific stats
}

interface ServiceProfile {
  email?: string;
  displayName?: string;
  [key: string]: any; // Allow for service-specific profile data
}

interface GoogleServiceAction {
  label: string;
  action: () => Promise<void>;
  variant?: 'default' | 'outline' | 'secondary';
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
}

interface GoogleServiceCardProps {
  // Core props
  serviceName: string; // "Gmail", "Google Drive", "Google Calendar"
  serviceType: string; // "gmail", "google_drive", "google_calendar"
  icon: React.ReactNode;
  description: string;
  
  // Integration data
  integrationId: number;
  isConnected: boolean;
  
  // Handlers
  onStatusChange?: (connected: boolean) => void;
  onConnect?: () => Promise<void>;
  
  // Service-specific endpoints (relative to /api/integrations/:id/)
  endpoints: {
    profile: string;    // e.g., "gmail/profile", "drive/profile"
    stats: string;      // e.g., "gmail/stats", "drive/stats"  
    test: string;       // e.g., "gmail/test", "drive/test"
  };
  
  // Service-specific actions
  actions?: GoogleServiceAction[];
  
  // Custom stats renderer
  renderStats?: (stats: ServiceStats) => React.ReactNode;
  
  // Custom profile renderer  
  renderProfile?: (profile: ServiceProfile) => React.ReactNode;
}

export function GoogleServiceCard({
  serviceName,
  serviceType,
  icon,
  description,
  integrationId,
  isConnected,
  onStatusChange,
  onConnect,
  endpoints,
  actions = [],
  renderStats,
  renderProfile,
}: GoogleServiceCardProps) {
  const [profile, setProfile] = useState<ServiceProfile | null>(null);
  const [stats, setStats] = useState<ServiceStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [actionLoading, setActionLoading] = useState<Record<string, boolean>>({});
  const { toast } = useToast();

  // Load service data when connected
  useEffect(() => {
    if (isConnected) {
      loadServiceData();
    }
  }, [isConnected, integrationId]);

  const loadServiceData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadProfile(),
        loadStats()
      ]);
    } catch (error) {
      console.error(`Error loading ${serviceName} data:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadProfile = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/${endpoints.profile}`);
      if (response.ok) {
        const data = await response.json();
        setProfile(data.profile || data);
      }
    } catch (error) {
      console.error(`Error loading ${serviceName} profile:`, error);
    }
  };

  const loadStats = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/${endpoints.stats}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error(`Error loading ${serviceName} stats:`, error);
    }
  };

  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      const response = await fetch(`/api/integrations/${integrationId}/${endpoints.test}`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok && data.connected) {
        toast({
          title: `${serviceName} Connection Successful`,
          description: `Connected to ${data.profile?.email || data.profile?.displayName || serviceName}`,
        });
        if (data.profile) setProfile(data.profile);
        onStatusChange?.(true);
      } else {
        toast({
          title: `${serviceName} Connection Failed`,
          description: data.message || `Failed to connect to ${serviceName}`,
          variant: "destructive",
        });
        onStatusChange?.(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to test ${serviceName} connection`,
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleAction = async (action: GoogleServiceAction, index: number) => {
    const actionKey = `action_${index}`;
    setActionLoading(prev => ({ ...prev, [actionKey]: true }));
    
    try {
      await action.action();
    } catch (error) {
      console.error(`Error executing ${action.label}:`, error);
    } finally {
      setActionLoading(prev => ({ ...prev, [actionKey]: false }));
    }
  };

  const defaultStatsRenderer = (stats: ServiceStats) => (
    <div className="grid grid-cols-2 gap-4 text-sm">
      {stats.total !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.total.toLocaleString()}</div>
          <div className="text-gray-500">Total Items</div>
        </div>
      )}
      {stats.recent !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.recent.toLocaleString()}</div>
          <div className="text-gray-500">Recent</div>
        </div>
      )}
      {stats.unread !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.unread.toLocaleString()}</div>
          <div className="text-gray-500">Unread</div>
        </div>
      )}
      {stats.starred !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.starred.toLocaleString()}</div>
          <div className="text-gray-500">Starred</div>
        </div>
      )}
    </div>
  );

  const defaultProfileRenderer = (profile: ServiceProfile) => (
    <div className="flex items-center space-x-3">
      <UserIcon className="h-8 w-8 text-gray-400" />
      <div>
        <div className="font-medium">{profile.displayName || profile.email}</div>
        {profile.displayName && profile.email && (
          <div className="text-sm text-gray-500">{profile.email}</div>
        )}
      </div>
    </div>
  );

  if (!isConnected) {
    return (
      <Card className="bg-white hover:shadow-lg transition-all duration-300 border border-gray-200">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            {icon}
            <div>
              <CardTitle className="text-lg">{serviceName}</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                {description}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-gray-500 mb-4">Connect your Google account to sync {serviceName.toLowerCase()} data</p>
            <Button 
              onClick={onConnect} 
              className="w-full"
              disabled={!onConnect}
            >
              Connect {serviceName}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white hover:shadow-lg transition-all duration-300 border border-gray-200">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {icon}
            <div>
              <CardTitle className="text-lg flex items-center space-x-2">
                <span>{serviceName}</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                  Connected
                </Badge>
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                {description}
              </CardDescription>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={testConnection}
            disabled={isTestingConnection}
          >
            {isTestingConnection ? (
              <Loader2Icon className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCwIcon className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Profile Section */}
        {profile && (
          <div className="border rounded-lg p-3 bg-gray-50">
            <h4 className="text-sm font-medium mb-2">Account</h4>
            {renderProfile ? renderProfile(profile) : defaultProfileRenderer(profile)}
          </div>
        )}

        {/* Stats Section */}
        {stats && (
          <div className="border rounded-lg p-3">
            <h4 className="text-sm font-medium mb-3">Statistics</h4>
            {renderStats ? renderStats(stats) : defaultStatsRenderer(stats)}
            {stats.lastSyncAt && (
              <div className="mt-3 pt-3 border-t text-xs text-gray-500">
                Last synced: {new Date(stats.lastSyncAt).toLocaleString()}
              </div>
            )}
          </div>
        )}

        {/* Actions Section */}
        {actions.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Actions</h4>
            <div className="flex flex-wrap gap-2">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  size="sm"
                  onClick={() => handleAction(action, index)}
                  disabled={action.disabled || actionLoading[`action_${index}`] || action.loading}
                  className="flex items-center space-x-1"
                >
                  {(actionLoading[`action_${index}`] || action.loading) ? (
                    <Loader2Icon className="h-3 w-3 animate-spin" />
                  ) : action.icon ? (
                    action.icon
                  ) : null}
                  <span>{action.label}</span>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-4">
            <Loader2Icon className="h-5 w-5 animate-spin mr-2" />
            <span className="text-sm text-gray-500">Loading {serviceName.toLowerCase()} data...</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 