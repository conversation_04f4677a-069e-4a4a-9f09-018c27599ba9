Update
Great news:

The Google OAuth flow works perfectly when opened in an external browser tab.

I was able to sign in using my organization’s Google account and advanced to step 2 of the integration setup (“Select a Google Drive folder”).

Current Issue
Problem:

On the “Select a Google Drive folder” step, no folders are showing up in the dropdown/search, even if I type the exact folder name.

It’s as if the app isn’t retrieving or displaying my Drive folders at all.

What to Check and Fix Next
1. Backend: Confirm Drive API Permissions
Double-check that you are requesting these scopes in the Google OAuth flow:

https://www.googleapis.com/auth/drive.readonly

https://www.googleapis.com/auth/drive.metadata.readonly

Make sure the backend is using the returned tokens to call the Google Drive API and list folders for the authenticated user.

2. Verify API Endpoint and Credentials
Ensure the backend API endpoint that fetches the folder list:

Is being called from the frontend after OAuth completes

Receives the correct integration ID and user credentials/tokens

Returns a list of folders (not just files)

Add backend logs to output what’s being requested from Google Drive and what (if anything) is coming back.

3. Debug the Frontend Folder Fetch
Make sure the frontend:

Triggers the API call after a successful Google OAuth connection

Properly displays any returned folder list

Shows a loading indicator or a helpful error if nothing is returned

4. Surface and Handle Errors
If the backend gets a 403, 401, or any error from Google, log the full error and show it in the UI.

Show a helpful message if no folders are found or if access is restricted.

5. Test Google Drive API Directly
Try running a direct Drive API query with the authenticated tokens to confirm you can list folders for this account.

If the Drive is a Shared Drive (not “My Drive”), make sure your code sets includeItemsFromAllDrives and supportsAllDrives to true.

6. Permissions
Ensure the Google account you authenticated with has:

Access to at least one folder in Drive (especially in “My Drive” or “Shared Drives”)

Sufficient permissions (Viewer or higher)

If you intend to access a Shared Drive, confirm the service/app/user account has been explicitly added.

Summary Instructions for Replit
Please debug and fix the “Select Google Drive folder” step:

Ensure Drive API is being called with the right permissions and tokens.

Add logs on the backend to show the folder listing request and response.

Check if the frontend is properly requesting and rendering folders.

If there’s an error or the folder list is empty, surface that in the UI so I know what’s happening.

If special handling is needed for Shared Drives, please implement or document that.

Let me know if you need more details (account IDs, folder IDs, etc.) and tell me when I can test again.

Thanks! Let’s keep up the momentum.