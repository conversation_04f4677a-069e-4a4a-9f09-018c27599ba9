import React from 'react';
import { GoogleServiceCard } from './GoogleServiceCard';
import { 
  RefreshCwIcon, 
  DownloadIcon,
  InboxIcon,
  StarIcon,
  ArchiveIcon
} from 'lucide-react';
import { IntegrationIcon } from '@/components/ui/integration-icons';
import { useToast } from '../../hooks/use-toast';

interface GmailCardProps {
  integrationId: number;
  isConnected: boolean;
  onStatusChange?: (connected: boolean) => void;
  onConnect?: () => Promise<void>;
}

export function GmailCard({
  integrationId,
  isConnected,
  onStatusChange,
  onConnect,
}: GmailCardProps) {
  const { toast } = useToast();

  const syncRecentEmails = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/gmail/sync-recent`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Email Sync Started",
          description: `Syncing recent emails (${data.daysBack} days)`,
        });
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to sync emails",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sync recent emails",
        variant: "destructive",
      });
    }
  };

  const syncAllEmails = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/gmail/sync-all`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Full Email Sync Started",
          description: "This may take several minutes for large inboxes",
        });
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to start full sync",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start full email sync",
        variant: "destructive",
      });
    }
  };

  const manageLabels = async () => {
    // Navigate to label management
    window.location.href = `/integrations/${integrationId}/gmail/manage`;
  };

  const actions = [
    {
      label: "Sync Recent",
      action: syncRecentEmails,
      variant: "default" as const,
      icon: <RefreshCwIcon className="h-3 w-3" />,
    },
    {
      label: "Sync All",
      action: syncAllEmails,
      variant: "outline" as const,
      icon: <DownloadIcon className="h-3 w-3" />,
    },
    {
      label: "Manage Labels",
      action: manageLabels,
      variant: "outline" as const,
      icon: <ArchiveIcon className="h-3 w-3" />,
    },
  ];

  const renderStats = (stats: any) => (
    <div className="grid grid-cols-2 gap-4 text-sm">
      {stats.totalEmails !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.totalEmails.toLocaleString()}</div>
          <div className="text-gray-500">Total Emails</div>
        </div>
      )}
      {stats.recentEmails !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.recentEmails.toLocaleString()}</div>
          <div className="text-gray-500">Recent</div>
        </div>
      )}
      {stats.unreadEmails !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg flex items-center justify-center space-x-1">
            <InboxIcon className="h-4 w-4 text-blue-500" />
            <span>{stats.unreadEmails.toLocaleString()}</span>
          </div>
          <div className="text-gray-500">Unread</div>
        </div>
      )}
      {stats.starredEmails !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg flex items-center justify-center space-x-1">
            <StarIcon className="h-4 w-4 text-yellow-500" />
            <span>{stats.starredEmails.toLocaleString()}</span>
          </div>
          <div className="text-gray-500">Starred</div>
        </div>
      )}
      {stats.threadsTotal !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.threadsTotal.toLocaleString()}</div>
          <div className="text-gray-500">Threads</div>
        </div>
      )}
      {stats.messagesTotal !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.messagesTotal.toLocaleString()}</div>
          <div className="text-gray-500">Messages</div>
        </div>
      )}
    </div>
  );

  const renderProfile = (profile: any) => (
    <div className="flex items-center space-x-3">
      <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
        <IntegrationIcon type="gmail" size={16} />
      </div>
      <div>
        <div className="font-medium">{profile.emailAddress}</div>
        {profile.historyId && (
          <div className="text-xs text-gray-400">
            History ID: {profile.historyId}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <GoogleServiceCard
      serviceName="Gmail"
      serviceType="gmail"
      icon={
        <div className="h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center">
          <IntegrationIcon type="gmail" size={24} />
        </div>
      }
      description="Sync and search your Gmail emails"
      integrationId={integrationId}
      isConnected={isConnected}
      onStatusChange={onStatusChange}
      onConnect={onConnect}
      endpoints={{
        profile: "gmail/profile",
        stats: "gmail/stats",
        test: "gmail/test",
      }}
      actions={actions}
      renderStats={renderStats}
      renderProfile={renderProfile}
    />
  );
} 