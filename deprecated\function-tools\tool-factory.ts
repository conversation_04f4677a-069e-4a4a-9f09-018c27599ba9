import { 
  FunctionTool,
  ToolCategory,
  IToolCategory,
  ToolRegistration
} from './base/function-tool.interface';
import { ToolRegistry } from './base/tool-registry';

// Import all tool categories
import { FileToolsCategory } from './categories/file-tools';
import { GoogleDriveToolsCategory } from './platforms/google-drive-tools';
import { DynamicToolGeneratorCategory } from './dynamic/dynamic-tool-generator';
import { HITLPatternsCategory } from './hitl/hitl-patterns';

/**
 * Tool factory for creating and managing function tools
 * Provides a centralized way to initialize and access all tool categories
 */
export class ToolFactory {
  private static instance: ToolFactory;
  private registry: ToolRegistry;
  private categories: Map<ToolCategory, IToolCategory> = new Map();
  private isInitialized: boolean = false;

  private constructor() {
    this.registry = ToolRegistry.getInstance();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ToolFactory {
    if (!ToolFactory.instance) {
      ToolFactory.instance = new ToolFactory();
    }
    return ToolFactory.instance;
  }

  /**
   * Initialize all tool categories
   */
  initialize(): void {
    if (this.isInitialized) {
      console.warn('[ToolFactory] Already initialized, skipping...');
      return;
    }

    console.log('[ToolFactory] Initializing tool factory...');

    // Create all tool categories
    const categories = this.createAllCategories();
    
    // Initialize registry with categories
    this.registry.initialize(categories);

    // Register tools from categories to the registry
    this.registerCategoryTools();

    this.isInitialized = true;
    console.log(`[ToolFactory] Initialized with ${categories.length} categories and ${this.registry.getStats().totalTools} tools`);
  }

  /**
   * Create all tool categories
   */
  private createAllCategories(): IToolCategory[] {
    const categories: IToolCategory[] = [];

    // File management tools
    const fileTools = new FileToolsCategory();
    this.categories.set(ToolCategory.FILE_MANAGEMENT, fileTools);
    categories.push(fileTools);

    // Platform-specific tools
    const googleDriveTools = new GoogleDriveToolsCategory();
    this.categories.set(ToolCategory.PLATFORM_SPECIFIC, googleDriveTools);
    categories.push(googleDriveTools);

    // Dynamic tools
    const dynamicTools = new DynamicToolGeneratorCategory();
    this.categories.set(ToolCategory.DYNAMIC, dynamicTools);
    categories.push(dynamicTools);

    // HITL patterns
    const hitlPatterns = new HITLPatternsCategory();
    this.categories.set(ToolCategory.HITL, hitlPatterns);
    categories.push(hitlPatterns);

    console.log(`[ToolFactory] Created ${categories.length} tool categories`);
    return categories;
  }

  /**
   * Register tools from categories to the registry
   */
  private registerCategoryTools(): void {
    Array.from(this.categories.entries()).forEach(([categoryType, category]) => {
      const tools = category.getTools();
      
      tools.forEach((tool: FunctionTool) => {
        const registration: ToolRegistration = {
          tool,
          category: categoryType,
          isDynamic: categoryType === ToolCategory.DYNAMIC,
          priority: this.getToolPriority(tool.name, categoryType)
        };

        this.registry.registerTool(registration);
      });

      console.log(`[ToolFactory] Registered ${tools.length} tools from category ${categoryType}`);
    });
  }

  /**
   * Get tool priority based on name and category
   */
  private getToolPriority(toolName: string, category: ToolCategory): number {
    // Higher priority for core file operations
    if (category === ToolCategory.FILE_MANAGEMENT) {
      if (toolName.includes('draft') || toolName.includes('confirm')) {
        return 10; // High priority for HITL patterns
      }
      return 8; // Medium-high priority for file operations
    }

    // Medium priority for HITL patterns
    if (category === ToolCategory.HITL) {
      return 6;
    }

    // Lower priority for dynamic and platform-specific tools
    if (category === ToolCategory.DYNAMIC || category === ToolCategory.PLATFORM_SPECIFIC) {
      return 4;
    }

    return 5; // Default priority
  }

  /**
   * Get a tool category by type
   */
  getCategory(categoryType: ToolCategory): IToolCategory | undefined {
    return this.categories.get(categoryType);
  }

  /**
   * Get all available categories
   */
  getCategories(): ToolCategory[] {
    return Array.from(this.categories.keys());
  }

  /**
   * Create a new tool and register it
   */
  createTool(
    name: string,
    description: string,
    parameters: any,
    handler: (params: any) => Promise<string> | string,
    category: ToolCategory = ToolCategory.DYNAMIC
  ): FunctionTool {
    const tool: FunctionTool = {
      name,
      description,
      parameters,
      handler
    };

    // Register with the registry
    const registration: ToolRegistration = {
      tool,
      category,
      isDynamic: true,
      priority: 5
    };

    this.registry.registerTool(registration);

    console.log(`[ToolFactory] Created and registered tool: ${name}`);
    return tool;
  }

  /**
   * Get tools for OpenAI function calling
   */
  getToolsForOpenAI(): any[] {
    return this.registry.getToolsForOpenAI();
  }

  /**
   * Execute a tool by name
   */
  async executeTool(name: string, parameters: any): Promise<string> {
    return this.registry.executeTool(name, parameters);
  }

  /**
   * Check if a tool exists
   */
  hasTool(name: string): boolean {
    return this.registry.hasTool(name);
  }

  /**
   * Get all tool names
   */
  getToolNames(): string[] {
    return this.registry.getToolNames();
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category: ToolCategory): FunctionTool[] {
    return this.registry.getToolsByCategory(category);
  }

  /**
   * Get tool categories with their tools (for reporting)
   */
  getToolCategoriesReport(): Record<string, string[]> {
    return this.registry.getToolCategoriesReport();
  }

  /**
   * Add a platform-specific tool
   */
  addPlatformTool(platform: string, toolConfig: {
    name: string;
    description: string;
    parameters: any;
    handler: (params: any) => Promise<string> | string;
  }): FunctionTool {
    const tool: FunctionTool = {
      name: toolConfig.name,
      description: toolConfig.description,
      parameters: toolConfig.parameters,
      handler: toolConfig.handler
    };

    const registration: ToolRegistration = {
      tool,
      category: ToolCategory.PLATFORM_SPECIFIC,
      isDynamic: true,
      platform,
      priority: 4
    };

    this.registry.registerTool(registration);

    console.log(`[ToolFactory] Added platform tool '${tool.name}' for platform '${platform}'`);
    return tool;
  }

  /**
   * Remove a tool
   */
  removeTool(name: string): boolean {
    const removed = this.registry.removeTool(name);
    if (removed) {
      console.log(`[ToolFactory] Removed tool: ${name}`);
    }
    return removed;
  }

  /**
   * Clear all tools (useful for testing)
   */
  clear(): void {
    this.registry.clear();
    this.categories.clear();
    this.isInitialized = false;
    console.log('[ToolFactory] Factory cleared');
  }

  /**
   * Get factory statistics
   */
  getStats(): {
    categories: number;
    totalTools: number;
    toolsByCategory: Record<string, number>;
    isInitialized: boolean;
  } {
    const registryStats = this.registry.getStats();
    
    return {
      categories: this.categories.size,
      totalTools: registryStats.totalTools,
      toolsByCategory: registryStats.toolsByCategory,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Validate the factory state
   */
  validate(): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    if (!this.isInitialized) {
      issues.push('Factory not initialized');
    }

    if (this.categories.size === 0) {
      issues.push('No categories registered');
    }

    const registryStats = this.registry.getStats();
    if (registryStats.totalTools === 0) {
      issues.push('No tools registered');
    }

    // Check for duplicate tool names
    const toolNames = this.registry.getToolNames();
    const uniqueNames = new Set(toolNames);
    if (toolNames.length !== uniqueNames.size) {
      issues.push('Duplicate tool names detected');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Get the registry instance (for advanced usage)
   */
  getRegistry(): ToolRegistry {
    return this.registry;
  }
} 