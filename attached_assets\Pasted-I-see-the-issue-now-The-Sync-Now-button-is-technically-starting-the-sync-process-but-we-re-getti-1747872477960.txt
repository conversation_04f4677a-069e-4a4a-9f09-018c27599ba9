I see the issue now! The "Sync Now" button is technically starting the sync process, but we're getting the error "Sync not implemented for integration type: google-meet". This means our application structure is working correctly, but we're missing the actual implementation for Google Meet sync in the controller.

Let's fix this by implementing the Google Meet sync functionality. Let me take a look at the controller and add the missing code:


Opened server/controllers/sync.ts
Now I'll implement the Google Meet sync functionality. Let's see the runSync method and the structure we need to modify:


Opened server/controllers/sync.ts
I see the problem now! Looking at the sync controller, we have two issues to fix:

The current code checks for "google_meet" but your integration type is actually "google-meet" (note the hyphen)
We need to update the check in the runSync method to handle your integration type correctly
Let me fix these issues:


Edited server/controllers/sync.ts

Restarted Start application
Let me check the Google Drive integration now that we've fixed the integration type issue:


Executed grep -A5 "sourceFolderId =" server/controllers/sync.ts
I'm still working on fixing the Google Meet sync functionality. Let's check the folder ID issue I noticed in the logs:


Opened server/controllers/sync.ts
I need to fix the issue where we're looking for the wrong field name in the sourceConfig. Let me check how the integration's sourceConfig is structured:


Executed grep -n "sourceConfig" server/controllers/integration.ts | head -10
Now I understand the issue! I'll fix the sync functionality by updating how we retrieve the source folder ID from the integration:


Edited server/controllers/sync.ts

Restarted Start application

Took a screenshot
Checkpoint made
3 minutes ago
•
Enable support for Google Meet transcript synchronization to Notion
Screenshot
Load preview
Great progress! We've identified the issue and fixed the Google Meet sync functionality:

✓ Added support for the "google-meet" integration type (the code was only looking for "google_meet")
✓ Fixed how the source folder ID is accessed (now uses sourceConfig.driveId instead of folderId)
✓ Added better logging to track the sync process

Let's test the sync process now! Try clicking the "Sync Now" button again on your Google Meet integration. The system should now properly access your Google Drive folder and process any meeting transcripts found there.

Is the sync working correctly now? Are you seeing any new errors when you click "Sync Now"?



> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Scheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
12:03:26 AM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
12:03:29 AM [express] GET /api/sync-logs 200 in 81ms :: {"logs":[{"id":7,"integrationId":1,"startTime…
12:03:29 AM [express] GET /api/integrations 200 in 312ms :: {"integrations":[{"id":1,"name":"Google M…
12:04:10 AM [express] GET /api/integrations 200 in 314ms :: {"integrations":[{"id":1,"name":"Google M…
OpenAI service initialized successfully
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
12:04:14 AM [express] POST /api/sync-now 200 in 260ms :: {"message":"Sync started successfully","sync…
12:04:14 AM [express] GET /api/integrations 200 in 78ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Error listing files in folder 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2: GaxiosError: Invalid Value
    at Gaxios._request (/home/<USER>/workspace/node_modules/gaxios/src/gaxios.ts:146:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async OAuth2Client.requestAsync (/home/<USER>/workspace/node_modules/google-auth-library/build/src/auth/oauth2client.js:429:18)
    at async GoogleService.listFiles (/home/<USER>/workspace/server/services/google-service.ts:218:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:132:23) {
  config: {
    url: 'https://www.googleapis.com/drive/v3/files?q=%271pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2%27%20in%20parents%20and%20trashed%20%3D%20false%20and%20mimeType%20%3D%20%27application%2Fvnd.google-apps.document%27%20and%20%271ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0%27%20not%20in%20parents&fields=files%28id%2C%20name%2C%20mimeType%2C%20modifiedTime%29&orderBy=modifiedTime%20desc',
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/20.18.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)',
      Authorization: '<<REDACTED> - See errorRedactor option in gaxios for configuration>.'
    },
    params: {
      q: "'1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and mimeType = 'application/vnd.google-apps.document' and '1ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0' not in parents",
      fields: 'files(id, name, mimeType, modifiedTime)',
      orderBy: 'modifiedTime desc'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1747872255134,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: {
    config: {
      url: 'https://www.googleapis.com/drive/v3/files?q=%271pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2%27%20in%20parents%20and%20trashed%20%3D%20false%20and%20mimeType%20%3D%20%27application%2Fvnd.google-apps.document%27%20and%20%271ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0%27%20not%20in%20parents&fields=files%28id%2C%20name%2C%20mimeType%2C%20modifiedTime%29&orderBy=modifiedTime%20desc',
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: [Object],
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: { error: [Object] },
    headers: {
      'cache-control': 'private, max-age=0',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Thu, 22 May 2025 00:04:15 GMT',
      expires: 'Thu, 22 May 2025 00:04:15 GMT',
      server: 'ESF',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://www.googleapis.com/drive/v3/files?q=%271pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2%27%20in%20parents%20and%20trashed%20%3D%20false%20and%20mimeType%20%3D%20%27application%2Fvnd.google-apps.document%27%20and%20%271ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0%27%20not%20in%20parents&fields=files%28id%2C%20name%2C%20mimeType%2C%20modifiedTime%29&orderBy=modifiedTime%20desc'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: 'Invalid Value',
      domain: 'global',
      reason: 'invalid',
      location: 'q',
      locationType: 'parameter'
    }
  ],
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
Error in sync job 8: GaxiosError: Invalid Value
    at Gaxios._request (/home/<USER>/workspace/node_modules/gaxios/src/gaxios.ts:146:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async OAuth2Client.requestAsync (/home/<USER>/workspace/node_modules/google-auth-library/build/src/auth/oauth2client.js:429:18)
    at async GoogleService.listFiles (/home/<USER>/workspace/server/services/google-service.ts:218:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:132:23) {
  config: {
    url: 'https://www.googleapis.com/drive/v3/files?q=%271pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2%27%20in%20parents%20and%20trashed%20%3D%20false%20and%20mimeType%20%3D%20%27application%2Fvnd.google-apps.document%27%20and%20%271ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0%27%20not%20in%20parents&fields=files%28id%2C%20name%2C%20mimeType%2C%20modifiedTime%29&orderBy=modifiedTime%20desc',
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/20.18.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)',
      Authorization: '<<REDACTED> - See errorRedactor option in gaxios for configuration>.'
    },
    params: {
      q: "'1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and mimeType = 'application/vnd.google-apps.document' and '1ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0' not in parents",
      fields: 'files(id, name, mimeType, modifiedTime)',
      orderBy: 'modifiedTime desc'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1747872255134,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: {
    config: {
      url: 'https://www.googleapis.com/drive/v3/files?q=%271pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2%27%20in%20parents%20and%20trashed%20%3D%20false%20and%20mimeType%20%3D%20%27application%2Fvnd.google-apps.document%27%20and%20%271ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0%27%20not%20in%20parents&fields=files%28id%2C%20name%2C%20mimeType%2C%20modifiedTime%29&orderBy=modifiedTime%20desc',
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: [Object],
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: { error: [Object] },
    headers: {
      'cache-control': 'private, max-age=0',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Thu, 22 May 2025 00:04:15 GMT',
      expires: 'Thu, 22 May 2025 00:04:15 GMT',
      server: 'ESF',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://www.googleapis.com/drive/v3/files?q=%271pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2%27%20in%20parents%20and%20trashed%20%3D%20false%20and%20mimeType%20%3D%20%27application%2Fvnd.google-apps.document%27%20and%20%271ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0%27%20not%20in%20parents&fields=files%28id%2C%20name%2C%20mimeType%2C%20modifiedTime%29&orderBy=modifiedTime%20desc'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: 'Invalid Value',
      domain: 'global',
      reason: 'invalid',
      location: 'q',
      locationType: 'parameter'
    }
  ],
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}

it said the sync started but i still dont see the transcript in my notion database