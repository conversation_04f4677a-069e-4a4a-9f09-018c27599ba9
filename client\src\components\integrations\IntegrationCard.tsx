import { format } from "date-fns";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { startSync, deleteIntegration } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useRef, useState } from "react";
import { getIntegrationLogo, getIntegrationName, getSourceName, getDestinationName } from "@/lib/utils/integrations";
import IntegrationDetails from "./IntegrationDetails";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MoreVertical, Setting<PERSON>, Trash2, <PERSON> } from "lucide-react";

interface IntegrationCardProps {
  integration: {
    id: number;
    name: string;
    type: string;
    status: string;
    lastSyncAt: string | null;
    nextSyncAt: string | null;
    sourceConfig: any;
    destinationConfig: any;
  };
  onConfigure: (id: number) => void;
}

export default function IntegrationCard({ integration, onConfigure }: IntegrationCardProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Logo function now imported from utilities - supports multiple formats
  // Using centralized utility functions for consistent integration handling

  const syncMutation = useMutation({
    mutationFn: () => {
      return startSync(integration.id);
    },
    onSuccess: () => {
      // Only proceed with normal success handling for Google Meet
      if (integration.type !== 'microsoft_teams' && integration.type !== 'slack') {
        queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
        toast({
          title: "Sync started",
          description: `Synchronization for ${integration.name} has been started.`,
        });

        // Start polling for status updates
        startPollingForSyncCompletion();
      }
    },
    onError: (error) => {
      toast({
        title: "Sync failed",
        description: `Failed to start sync: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: () => deleteIntegration(integration.id),
    onMutate: async () => {
      // Cancel any outgoing refetches so they don't overwrite our optimistic update
      await queryClient.cancelQueries({ queryKey: ['/api/integrations'] });

      // Snapshot the previous value
      const previousIntegrations = queryClient.getQueryData(['/api/integrations']);

      // Optimistically update to remove the integration immediately
      queryClient.setQueryData(['/api/integrations'], (old: any) => {
        if (!old?.integrations) return old;
        return {
          ...old,
          integrations: old.integrations.filter((int: any) => int.id !== integration.id)
        };
      });

      // Return a context object with the snapshotted value
      return { previousIntegrations };
    },
    onSuccess: () => {
      toast({
        title: "Integration deleted",
        description: `${integration.name} integration has been removed. Your synced files have been preserved.`,
      });
      setShowDeleteDialog(false);
      
      // Refetch to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
    },
    onError: (error: any, variables, context) => {
      // Rollback the optimistic update on error
      if (context?.previousIntegrations) {
        queryClient.setQueryData(['/api/integrations'], context.previousIntegrations);
      }
      
      toast({
        title: "Failed to delete integration",
        description: error.message || "An error occurred while deleting the integration.",
        variant: "destructive",
      });
      setShowDeleteDialog(false);
    },
    onSettled: () => {
      // Always refetch after mutation settles to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
    },
  });

  // Function to start polling for sync completion
  const startPollingForSyncCompletion = () => {
    // Clear any existing polling
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
    }

    let pollCount = 0;
    const maxPolls = 60; // Poll for up to 5 minutes (60 * 5 seconds)

    pollIntervalRef.current = setInterval(() => {
      pollCount++;

      // Refetch integration data to check status
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });

      // Stop polling after max attempts or if integration is no longer syncing
      if (pollCount >= maxPolls) {
        if (pollIntervalRef.current) {
          clearInterval(pollIntervalRef.current);
          pollIntervalRef.current = null;
        }
      }
    }, 5000); // Poll every 5 seconds
  };

  // Stop polling when integration status changes from "syncing"
  useEffect(() => {
    if (integration.status !== 'syncing' && pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;

      // Show completion toast if sync finished
      if (integration.status === 'connected') {
        toast({
          title: "Sync completed",
          description: `Synchronization for ${integration.name} has completed successfully.`,
        });
      } else if (integration.status === 'error') {
        toast({
          title: "Sync failed",
          description: `Synchronization for ${integration.name} encountered an error.`,
          variant: "destructive",
        });
      }
    }
  }, [integration.status, integration.name, toast]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };
  }, []);

  // Consider "configured", "connected", and "syncing" status as connected since they mean the integration is set up
  const isConnected = integration.status === 'connected' || integration.status === 'configured' || integration.status === 'syncing';
  const isSyncing = integration.status === 'syncing';

  if (!isConnected) {
    return (
      <>
        <div className={`
          bg-white rounded-xl shadow-sm border border-gray-100 
          hover:shadow-lg hover:border-gray-200 
          transition-all duration-300 ease-in-out
          transform hover:-translate-y-1
          ${deleteMutation.isPending ? 'opacity-50 pointer-events-none' : 'opacity-70'}
        `}>
          <div className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex items-center">
                <img
                  src={getIntegrationLogo(integration.type)}
                  alt={`${getIntegrationName(integration.type)} Logo`}
                  className="w-12 h-12 rounded-lg shadow-sm"
                />
                <div className="ml-4">
                  <h4 className="font-semibold text-gray-900 text-lg">{getIntegrationName(integration.type)}</h4>
                  <div className="flex items-center mt-1">
                    <div className="w-3 h-3 bg-gray-300 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      Not Connected
                    </span>
                  </div>
                </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0 hover:bg-gray-100 transition-colors"
                    disabled={deleteMutation.isPending}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="rounded-lg border-gray-200">
                  <DropdownMenuItem 
                    onClick={() => setShowDetails(true)}
                    disabled={deleteMutation.isPending}
                    className="focus:bg-blue-50"
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onConfigure(integration.id)}
                    disabled={deleteMutation.isPending}
                    className="focus:bg-blue-50"
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Configure
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-red-600 focus:text-red-600 focus:bg-red-50"
                    disabled={deleteMutation.isPending}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Integration
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="mt-6 text-center py-8">
              <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 
                              rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
                        d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <p className="text-sm text-gray-600 mb-6 max-w-sm mx-auto leading-relaxed">
                Connect {getIntegrationName(integration.type)} to sync all your files and documents for AI-powered search and analysis
              </p>
              <button
                className="group relative px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 
                           text-white rounded-lg font-medium shadow-md hover:shadow-lg 
                           hover:from-blue-600 hover:to-blue-700 
                           transform transition-all duration-200 hover:scale-105
                           focus:ring-4 focus:ring-blue-200 focus:outline-none"
                onClick={() => onConfigure(integration.id)}
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" 
                       fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  Connect {getIntegrationName(integration.type)}
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Dialog - Shared for disconnected cards */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent className="rounded-xl">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-xl">Delete Integration</AlertDialogTitle>
              <AlertDialogDescription className="text-gray-600 leading-relaxed">
                Are you sure you want to delete the <strong>{integration.name}</strong> integration?
                <br /><br />
                This will remove the integration and clear its configuration, but <strong>all synced files and their content will be preserved</strong>. You can still access your documents through the Files page and use them in chat conversations.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={deleteMutation.isPending} className="rounded-lg">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => deleteMutation.mutate()}
                disabled={deleteMutation.isPending}
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600 rounded-lg"
              >
                {deleteMutation.isPending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deleting...
                  </>
                ) : (
                  'Delete Integration'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }

  return (
    <>
    <div className={`
      bg-white rounded-xl shadow-sm border border-gray-100 
      hover:shadow-lg hover:border-gray-200 
      transition-all duration-300 ease-in-out
      transform hover:-translate-y-1
      ring-2 ring-green-50
      ${deleteMutation.isPending ? 'opacity-50 pointer-events-none' : ''}
    `}>
      <div className="p-6">
        <div className="flex justify-between items-start">
          <div className="flex items-center">
            <img
              src={getIntegrationLogo(integration.type)}
              alt={`${getIntegrationName(integration.type)} Logo`}
              className="w-12 h-12 rounded-lg shadow-sm"
            />
            <div className="ml-4">
              <h4 className="font-semibold text-gray-900 text-lg">{integration.name}</h4>
              <div className="flex items-center mt-1">
                {isSyncing ? (
                  <div className="flex items-center">
                    <div className="relative mr-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                      <div className="absolute top-0 left-0 w-3 h-3 bg-blue-400 rounded-full animate-ping opacity-75"></div>
                    </div>
                    <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                      Syncing...
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2 ring-2 ring-green-100"></div>
                    <span className="text-sm font-medium text-green-600 bg-green-50 px-3 py-1 rounded-full">
                      Connected
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0 hover:bg-gray-100 transition-colors"
                disabled={deleteMutation.isPending}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="rounded-lg border-gray-200">
              <DropdownMenuItem 
                onClick={() => setShowDetails(true)}
                disabled={deleteMutation.isPending}
                className="focus:bg-blue-50"
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onConfigure(integration.id)}
                disabled={deleteMutation.isPending}
                className="focus:bg-blue-50"
              >
                <Settings className="mr-2 h-4 w-4" />
                Configure
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => setShowDeleteDialog(true)}
                className="text-red-600 focus:text-red-600 focus:bg-red-50"
                disabled={deleteMutation.isPending}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Integration
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="mt-6 space-y-3 bg-gray-50/50 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-600">Last sync</span>
            <span className="text-sm text-gray-900 font-medium">
              {integration.lastSyncAt
                ? format(new Date(integration.lastSyncAt), "MMM d, yyyy - h:mm a")
                : "Never"}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-600">Next sync</span>
            <span className="text-sm text-gray-900 font-medium">
              {integration.nextSyncAt
                ? format(new Date(integration.nextSyncAt), "MMM d, yyyy - h:mm a")
                : "Not scheduled"}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-600">Source</span>
            <span className="text-sm text-gray-900 font-medium">{getSourceName(integration.type, integration.sourceConfig)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-600">Destination</span>
            <span className="text-sm text-gray-900 font-medium">{getDestinationName(integration.destinationConfig)}</span>
          </div>
        </div>
      </div>

      <div className="border-t border-gray-100 bg-gray-50/30">
        <button
          className={`w-full px-6 py-4 text-sm font-medium transition-all duration-200 
                      flex items-center justify-center space-x-2 rounded-b-xl
                      ${syncMutation.isPending || isSyncing
                        ? 'text-blue-600 bg-blue-50 cursor-not-allowed' 
                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600 group'
                      }`}
          onClick={() => syncMutation.mutate()}
          disabled={syncMutation.isPending || isSyncing}
        >
          {(syncMutation.isPending || isSyncing) ? (
            <>
              <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Syncing...</span>
            </>
          ) : (
            <>
              <svg className="w-5 h-5 group-hover:rotate-180 transition-transform duration-300" 
                   fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Sync Now</span>
            </>
          )}
        </button>
      </div>
    </div>

    {/* Delete Confirmation Dialog */}
    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
      <AlertDialogContent className="rounded-xl">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-xl">Delete Integration</AlertDialogTitle>
          <AlertDialogDescription className="text-gray-600 leading-relaxed">
            Are you sure you want to delete the <strong>{integration.name}</strong> integration?
            <br /><br />
            This will remove the integration and clear its configuration, but <strong>all synced files and their content will be preserved</strong>. You can still access your documents through the Files page and use them in chat conversations.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteMutation.isPending} className="rounded-lg">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={() => deleteMutation.mutate()}
            disabled={deleteMutation.isPending}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600 rounded-lg"
          >
            {deleteMutation.isPending ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Deleting...
              </>
            ) : (
              'Delete Integration'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    {/* Integration Details Modal */}
    <IntegrationDetails
      isOpen={showDetails}
      onClose={() => setShowDetails(false)}
      integration={integration}
      onConfigure={onConfigure}
      onSync={(id) => syncMutation.mutate()}
    />
    </>
  );
}
