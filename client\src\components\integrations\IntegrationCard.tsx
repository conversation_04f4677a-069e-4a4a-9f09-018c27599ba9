import { format } from "date-fns";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { startSync, deleteIntegration } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useRef, useState } from "react";
import { getIntegrationName, getSourceName, getDestinationName } from "@/lib/utils/integrations";
import { IntegrationIcon } from "@/components/ui/integration-icons";
import IntegrationDetails from "./IntegrationDetails";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ting<PERSON>, Trash2, <PERSON>, RefreshCw } from "lucide-react";

interface IntegrationCardProps {
  integration: {
    id: number;
    name: string;
    type: string;
    status: string;
    lastSyncAt: string | null;
    nextSyncAt: string | null;
    sourceConfig: any;
    destinationConfig: any;
  };
  onConfigure: (id: number) => void;
}

export default function IntegrationCard({ integration, onConfigure }: IntegrationCardProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showDetails, setShowDetails] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  const syncMutation = useMutation({
    mutationFn: () => startSync(integration.id),
    onSuccess: () => {
      toast({
        title: 'Sync started',
        description: `${getIntegrationName(integration.type)} sync has been initiated`,
      });
      // Force immediate refresh to show syncing state
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations', 'polling'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Sync failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: () => deleteIntegration(integration.id),
    onSuccess: () => {
      toast({
        title: 'Integration deleted',
        description: `${integration.name} has been removed`,
      });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      setShowDeleteDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to delete integration',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const isConnected = integration.status === 'connected' || integration.status === 'configured' || integration.status === 'syncing';
  const isSyncing = integration.status === 'syncing';

  const formatLastSync = (lastSyncAt: string | null) => {
    if (!lastSyncAt) return "Never";
    const date = new Date(lastSyncAt);
    return format(date, "MMM d, yyyy 'at' h:mm a");
  };

  if (!isConnected) {
    return (
      <>
        <div 
          className="group bg-white rounded-lg border border-gray-200 hover:border-gray-300 
                     transition-all duration-200 cursor-pointer overflow-hidden"
          onClick={() => onConfigure(integration.id)}
        >
          {/* Header */}
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-lg bg-white flex items-center justify-center">
                  <IntegrationIcon type={integration.type} size={40} />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 text-base">{getIntegrationName(integration.type)}</h4>
                  <div className="flex items-center mt-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-500 whitespace-nowrap">Not Connected</span>
                  </div>
                </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    disabled={deleteMutation.isPending}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setShowDetails(true)}>
                    <Eye className="mr-2 h-3 w-3" />
                    Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowDeleteDialog(true)} className="text-red-600">
                    <Trash2 className="mr-2 h-3 w-3" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Spacer to match connected card height */}
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Status</span>
                <span className="text-sm text-gray-700 font-medium">Ready to connect</span>
              </div>
            </div>
          </div>

          {/* Connect Button */}
          <div className="px-6 pb-6">
            <div className="w-full text-center py-2.5 bg-blue-50 text-blue-600 rounded-md text-sm font-medium
                            group-hover:bg-blue-100 transition-colors duration-200 border border-blue-200">
              Connect Now
            </div>
          </div>
        </div>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Integration</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this integration? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={deleteMutation.isPending}>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={() => deleteMutation.mutate()}
                disabled={deleteMutation.isPending}
                className="bg-red-600 hover:bg-red-700"
              >
                {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <IntegrationDetails
          isOpen={showDetails}
          onClose={() => setShowDetails(false)}
          integration={integration}
          onConfigure={onConfigure}
          onSync={(id) => syncMutation.mutate()}
        />
      </>
    );
  }

  return (
    <>
      <div className="group bg-white rounded-lg border border-green-200 hover:border-green-300 
                      transition-all duration-200 overflow-hidden">
        {/* Header */}
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-lg bg-white flex items-center justify-center">
                <IntegrationIcon type={integration.type} size={40} />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 text-base">{getIntegrationName(integration.type)}</h4>
                <div className="flex items-center mt-1">
                  {isSyncing ? (
                    <>
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                      <span className="text-sm text-blue-600 whitespace-nowrap">Syncing...</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-sm text-green-600 whitespace-nowrap">Connected</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  disabled={deleteMutation.isPending}
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setShowDetails(true)}>
                  <Eye className="mr-2 h-3 w-3" />
                  Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onConfigure(integration.id)}>
                  <Settings className="mr-2 h-3 w-3" />
                  Configure
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => syncMutation.mutate()}
                  disabled={syncMutation.isPending || isSyncing}
                >
                  <RefreshCw className={`mr-2 h-3 w-3 ${(syncMutation.isPending || isSyncing) ? 'animate-spin' : ''}`} />
                  Sync Now
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setShowDeleteDialog(true)} className="text-red-600">
                  <Trash2 className="mr-2 h-3 w-3" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Last Sync Info */}
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Last sync</span>
              <span className="text-sm text-gray-700 font-medium">
                {formatLastSync(integration.lastSyncAt)}
              </span>
            </div>
          </div>
        </div>

        {/* Sync Button */}
        <div className="border-t border-gray-100 bg-gray-50">
          <button
            className={`w-full px-6 py-3 text-sm font-medium transition-all duration-200 
                        flex items-center justify-center space-x-2 rounded-b-lg
                        ${syncMutation.isPending || isSyncing
                          ? 'text-blue-600 bg-blue-50 cursor-not-allowed' 
                          : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600 group'
                        }`}
            onClick={() => syncMutation.mutate()}
            disabled={syncMutation.isPending || isSyncing}
          >
            {(syncMutation.isPending || isSyncing) ? (
              <>
                <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Syncing...</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4 group-hover:rotate-180 transition-transform duration-200" 
                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Sync Now</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Integration</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this integration? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteMutation.isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => deleteMutation.mutate()}
              disabled={deleteMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <IntegrationDetails
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        integration={integration}
        onConfigure={onConfigure}
        onSync={(id) => syncMutation.mutate()}
      />
    </>
  );
}
