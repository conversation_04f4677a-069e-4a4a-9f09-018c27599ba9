import { Request, Response } from 'express';
import { BaseIntegrationController } from '../base/base-integration.controller.js';
import { microsoftService } from '../../../services/platform-integrations/microsoft/index.js';
import { teamsService } from '../../../services/platform-integrations/teams-service.js';
import { cryptoService } from '../../../services/core/crypto-service.js';

/**
 * Controller for Microsoft Teams integration operations
 * Handles Teams channels, sources, folders, and connection testing
 */
export class TeamsIntegrationController extends BaseIntegrationController {
  constructor() {
    super();
    // Bind all methods to preserve 'this' context
    this.getTeamsSources = this.getTeamsSources.bind(this);
    this.getTeamsChannels = this.getTeamsChannels.bind(this);
    this.getTeamsFolders = this.getTeamsFolders.bind(this);
    this.testTeamsConnection = this.testTeamsConnection.bind(this);
  }

  /**
   * Get available Teams sources for an integration
   */
  async getTeamsSources(req: Request, res: Response) {
    try {
      const { integration, success, response } = await this.validateIntegrationCredentials(req, res);
      if (!success) {
        return response;
      }

      // Validate this is a Teams integration
      if (!this.validateIntegrationType(integration!, ['microsoft_teams', 'microsoft-teams'])) {
        return res.status(400).json({ message: 'This endpoint is only for Microsoft Teams integrations.' });
      }

      try {
        // Decrypt credentials
        const credentialsJson = await cryptoService.decrypt(integration!.credentials);
        const credentials = JSON.parse(credentialsJson);
        
        this.logAction('Fetching Teams sources for integration:', integration!.id);
        const sources = await microsoftService.getAvailableSources(credentials);
        
        return this.successResponse(res, { sources });
      } catch (error: any) {
        return this.handleError(res, error, 'Failed to fetch Teams sources');
      }
    } catch (error: any) {
      return this.handleError(res, error, `Failed to get Teams sources for integration ${req.params.id}`);
    }
  }

  /**
   * Get Teams channels for a specific team
   */
  async getTeamsChannels(req: Request, res: Response) {
    try {
      const { integration, success, response } = await this.validateIntegrationCredentials(req, res);
      if (!success) {
        return response;
      }

      // Validate this is a Teams integration
      if (!this.validateIntegrationType(integration!, ['microsoft_teams', 'microsoft-teams'])) {
        return res.status(400).json({ message: 'This endpoint is only for Microsoft Teams integrations.' });
      }

      const teamId = req.params.teamId;
      if (!teamId) {
        return res.status(400).json({ message: 'Team ID is required' });
      }

      try {
        // Decrypt credentials
        const credentialsJson = await cryptoService.decrypt(integration!.credentials);
        const credentials = JSON.parse(credentialsJson);
        
        this.logAction('Fetching Teams channels for team:', teamId);
        const channels = await microsoftService.getTeamsChannels(credentials, teamId);
        
        return this.successResponse(res, { channels });
      } catch (error: any) {
        return this.handleError(res, error, 'Failed to fetch Teams channels');
      }
    } catch (error: any) {
      return this.handleError(res, error, `Failed to get Teams channels for integration ${req.params.id}`);
    }
  }

  /**
   * Get Teams folders for file selection
   */
  async getTeamsFolders(req: Request, res: Response) {
    try {
      const { integration, success, response } = await this.validateIntegrationCredentials(req, res);
      if (!success) {
        return response;
      }

      // Validate this is a Teams integration
      if (!this.validateIntegrationType(integration!, ['microsoft_teams', 'microsoft-teams'])) {
        return res.status(400).json({ message: 'This endpoint is only for Microsoft Teams integrations.' });
      }

      try {
        // Decrypt credentials
        const credentialsJson = await cryptoService.decrypt(integration!.credentials);
        const credentials = JSON.parse(credentialsJson);
        
        this.logAction('Fetching Teams folders for integration:', integration!.id);
        const foldersData = await microsoftService.getFoldersForSelection(credentials);
        
        return this.successResponse(res, foldersData);
      } catch (error: any) {
        return this.handleError(res, error, 'Failed to fetch Teams folders');
      }
    } catch (error: any) {
      return this.handleError(res, error, `Failed to get Teams folders for integration ${req.params.id}`);
    }
  }

  /**
   * Test Microsoft Teams connection
   */
  async testTeamsConnection(req: Request, res: Response) {
    try {
      const { integration, success, response } = await this.validateIntegrationCredentials(req, res);
      if (!success) {
        return response;
      }

      // Validate this is a Teams integration
      if (!this.validateIntegrationType(integration!, ['microsoft_teams', 'microsoft-teams'])) {
        return res.status(400).json({ message: 'This endpoint is only for Microsoft Teams integrations.' });
      }

      try {
        this.logAction('Testing Microsoft Teams connection for integration:', {
          id: integration!.id,
          type: integration!.type,
          hasCredentials: !!integration!.credentials
        });

        // Decrypt credentials
        const credentialsJson = await cryptoService.decrypt(integration!.credentials);
        const credentials = JSON.parse(credentialsJson);
        
        const result = await microsoftService.testConnection(credentials);
        
        // If the test was successful, update the integration status to "connected"
        if (result.success) {
          await this.updateIntegrationStatus(integration!.id, 'connected');
        }

        return this.successResponse(res, result);
      } catch (error: any) {
        return this.handleError(res, error, 'Failed to test Teams connection');
      }
    } catch (error: any) {
      return this.handleError(res, error, `Failed to test Teams connection for integration ${req.params.id}`);
    }
  }
}

export const teamsIntegrationController = new TeamsIntegrationController(); 