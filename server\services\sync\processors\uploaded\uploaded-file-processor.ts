import { BaseFileProcessor, type ProcessResult } from '../base/file-processor.interface';

/**
 * Uploaded files processor
 * Handles processing files uploaded directly to the system
 */
export class UploadedFileProcessor extends BaseFileProcessor {

  getPlatform(): string {
    return 'uploaded_files';
  }

  canProcess(platform: string, fileType?: string): boolean {
    return platform === 'uploaded_files';
  }

  /**
   * Process an uploaded file
   */
  async processFile(
    file: any,
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    const startTime = Date.now();
    
    try {
      this.log('info', `Processing uploaded file: ${file.fileName}`);

      // For uploaded files, we typically don't need to check for changes
      // since they are processed immediately upon upload
      
      const processingTime = Date.now() - startTime;
      this.log('info', `Successfully processed uploaded file ${file.fileName} in ${processingTime}ms`);

      return this.createResult(true, false, file, undefined, {
        contentExtracted: !!file.fileContent,
        embeddingsGenerated: false, // Embeddings are handled separately for uploaded files
        aiMetadataExtracted: false,
        processingTime
      });

    } catch (error: any) {
      this.log('error', `Error processing uploaded file ${file.fileName}:`, error);
      return this.createResult(false, false, undefined, error.message);
    }
  }
}
