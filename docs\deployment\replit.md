# GPT Unify

## Overview

This project is a full-stack application for managing integrations with various file storage platforms (Google Drive, Microsoft Teams, Slack, etc.) to synchronize and store all types of business files. The application aggregates content from different sources and can process them using OpenAI's language models to enhance discoverability and analysis through AI-powered search.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

The project follows a modern web application architecture with clearly separated client and server components:

1. **Frontend**: React-based single-page application with client-side routing
2. **Backend**: Express.js server providing REST API endpoints
3. **Database**: PostgreSQL database (via Neon Serverless) with Drizzle ORM for database interactions
4. **External Services**: Integrations with various third-party APIs (Google, Microsoft, Zoom, Slack, Notion)

The application uses a monorepo structure where both client and server code exist in the same repository but are built and deployed separately. Shared types and schemas are stored in a dedicated directory to ensure type safety across the codebase.

## Key Components

### Backend Components

1. **Express Server**: Main HTTP server handling API requests, static file serving, and routing
   - Located in `server/index.ts` and `server/routes.ts`
   - Uses middleware for logging, error handling, and request parsing

2. **Controllers**: Handle business logic for API endpoints
   - Integration controller: Manages connections to external services
   - Sync controller: Handles synchronization processes

3. **Services**: Encapsulate specific functionality
   - Crypto service: Handles encryption/decryption of API credentials
   - Google service: Manages OAuth flow and Google API interactions
   - Notion service: Interfaces with Notion API
   - OpenAI service: Provides AI augmentation capabilities
   - Scheduler service: Manages recurring sync tasks

4. **Database Layer**: Uses Drizzle ORM to interact with PostgreSQL
   - Located in `server/db.ts`
   - Schema defined in `shared/schema.ts`

### Frontend Components

1. **React Application**: Main frontend application
   - Built with Vite and React
   - Uses Wouter for client-side routing
   - Uses TanStack Query for data fetching and caching

2. **UI Components**:
   - Uses shadcn/ui component library (based on Radix UI primitives)
   - Organized in a component-based architecture
   - Theme support (light/dark mode)

3. **Pages**: Main application views
   - Dashboard: Overview and summary statistics
   - Integrations: Manage external service connections
   - Logs: View synchronization logs and results
   - Settings: Application configuration

4. **API Client**: Functions to interact with the backend API
   - Located in `client/src/lib/api.ts`
   - Uses fetch API with error handling

## Data Flow

1. **Authentication Flow**:
   - User sets up integration (Google, Microsoft, etc.)
   - Backend generates OAuth URL and state token
   - User authenticates with third-party service
   - Service redirects to callback with authorization code
   - Backend exchanges authorization code for access/refresh tokens
   - Tokens are encrypted and stored in the database

2. **Synchronization Flow**:
   - Triggered manually or via scheduled job
   - Backend retrieves encrypted credentials and decrypts them
   - Connects to third-party API to fetch new content
   - Processes content (optional LLM enhancement)
   - Stores results in the database
   - Updates logs with results

3. **Frontend Data Flow**:
   - React components make API requests via TanStack Query
   - API responses are cached in memory
   - UI is updated with fetched data
   - Forms submit data to API endpoints

## External Dependencies

### Backend Dependencies
- Express: Web server framework
- Drizzle ORM: Database toolkit
- @neondatabase/serverless: PostgreSQL database client
- @notionhq/client: Notion API client
- OpenAI: AI services
- Google APIs: For Google Meet and Chat integrations
- node-cron: Scheduling for recurring tasks
- Zod: Runtime type validation

### Frontend Dependencies
- React: UI library
- Wouter: Routing library
- TanStack Query: Data fetching and caching
- shadcn/ui + Radix UI: Component library
- Tailwind CSS: Utility-first CSS framework
- date-fns: Date formatting utility
- Recharts: Charting library

## Deployment Strategy

The application is set up to deploy on Replit with the following configuration:

1. **Build Process**:
   - Client-side code is built with Vite
   - Server-side code is bundled with esbuild
   - Output is placed in the `dist` directory

2. **Runtime Environment**:
   - Node.js server running the bundled application
   - Serves static files from the `dist/public` directory
   - Handles API requests via Express endpoints

3. **Database**:
   - Uses PostgreSQL module from Replit
   - Connected via DATABASE_URL environment variable

4. **Environment Variables Required**:
   - DATABASE_URL: PostgreSQL connection string
   - OPENAI_API_KEY: API key for OpenAI services
   - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET: For Google OAuth
   - ENCRYPTION_KEY: For encrypting API credentials (optional in development)

The deployment is configured through the `.replit` file, which defines the build and run commands, as well as port mapping (port 5000 is mapped to 80 for external access).