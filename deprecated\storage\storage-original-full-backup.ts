// DEPRECATED: This file is being modularized for better maintainability
// New imports should use the modular structure from ./storage/
// This file is kept for backward compatibility during the migration

import { integrations, syncLogs, syncItems, files, users, fileChunks, chatSessions, chatMessages, projects, type User, type InsertUser, type Integration, type InsertIntegration, type SyncLog, type InsertSyncLog, type SyncItem, type InsertSyncItem, type File, type InsertFile, type FileChunk, type InsertFileChunk, type ChatSession, type InsertChatSession, type ChatMessage, type InsertChatMessage, type Project, type InsertProject } from "@shared/schema";
import { cryptoService } from "./services/crypto-service";
import { drizzle } from "drizzle-orm/postgres-js";
import { eq, desc, and, sql, inArray } from "drizzle-orm";
import postgres from "postgres";

// Import the new modular storage system
import { StorageFacade } from "./storage/storage.facade";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Integration methods
  getIntegrations(): Promise<Integration[]>;
  getIntegration(id: number): Promise<Integration | undefined>;
  getIntegrationsByType(type: string): Promise<Integration[]>;
  createIntegration(integration: InsertIntegration): Promise<Integration>;
  updateIntegration(id: number, data: Partial<Integration>): Promise<Integration | undefined>;
  deleteIntegration(id: number): Promise<boolean>;
  updateIntegrationStatus(id: number, status: string): Promise<Integration | undefined>;

  // Sync Log methods
  getSyncLogs(integrationId?: number, limit?: number): Promise<SyncLog[]>;
  getSyncLog(id: number): Promise<SyncLog | undefined>;
  createSyncLog(log: InsertSyncLog): Promise<SyncLog>;
  updateSyncLog(id: number, data: Partial<SyncLog>): Promise<SyncLog | undefined>;

  // Sync Item methods
  getSyncItems(syncLogId?: number, status?: string): Promise<SyncItem[]>;
  getSyncItem(id: number): Promise<SyncItem | undefined>;
  getSyncItemByExternalId(externalId: string, integrationId: number): Promise<SyncItem | undefined>;
  createSyncItem(item: InsertSyncItem): Promise<SyncItem>;
  updateSyncItem(id: number, data: Partial<SyncItem>): Promise<SyncItem | undefined>;

  // File methods
  getFiles(platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string): Promise<File[]>;
  getFile(id: number): Promise<File | undefined>;
  getFileByExternalId(externalId: string, platform: string): Promise<File | undefined>;
  createFile(file: InsertFile): Promise<File>;
  updateFile(id: number, data: Partial<File>): Promise<File | undefined>;
  deleteFile(id: number): Promise<boolean>;
  markFileAsDeleted(id: number): Promise<File | undefined>;
  searchFiles(query: string, platform?: string, fileType?: string, folderId?: string): Promise<File[]>;

  // Incremental sync methods
  getFilesForIntegration(integrationId: number, platform: string): Promise<File[]>;
  markFilesAsDeleted(externalIds: string[], platform: string): Promise<void>;
  updateFileStatus(id: number, status: string): Promise<File | undefined>;

  // RAG and Chat methods
  // File Chunks
  getFileChunks(fileId: number): Promise<FileChunk[]>;
  createFileChunk(chunk: InsertFileChunk): Promise<FileChunk>;
  deleteFileChunks(fileId: number): Promise<void>;
  searchSimilarChunks(queryEmbedding: number[], enabledSources: string[], limit: number): Promise<any[]>;

  // Chat Sessions
  getChatSessions(userId?: string, limit?: number): Promise<ChatSession[]>;
  getChatSession(id: number): Promise<ChatSession | undefined>;
  createChatSession(session: InsertChatSession): Promise<ChatSession>;
  updateChatSession(id: number, data: Partial<ChatSession>): Promise<ChatSession | undefined>;
  deleteChatSession(id: number): Promise<boolean>;

  // Chat Messages
  getChatMessages(sessionId: number, limit?: number): Promise<ChatMessage[]>;
  createChatMessage(message: InsertChatMessage): Promise<ChatMessage>;

  // Projects
  getProjects(userId?: string): Promise<Project[]>;
  getProject(id: number): Promise<Project | undefined>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: number, data: Partial<Project>): Promise<Project | undefined>;
  deleteProject(id: number): Promise<boolean>;

  // Development utility methods
  clearAllData?(): Promise<void>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private integrations: Map<number, Integration>;
  private syncLogs: Map<number, SyncLog>;
  private syncItems: Map<number, SyncItem>;
  private files: Map<number, File>;
  private fileChunks: Map<number, FileChunk>;
  private chatSessions: Map<number, ChatSession>;
  private chatMessages: Map<number, ChatMessage>;
  private projects: Map<number, Project>;
  private userIdCounter: number;
  private integrationIdCounter: number;
  private syncLogIdCounter: number;
  private syncItemIdCounter: number;
  private fileIdCounter: number;
  private fileChunkIdCounter: number;
  private chatSessionIdCounter: number;
  private chatMessageIdCounter: number;
  private projectIdCounter: number;

  constructor() {
    this.users = new Map();
    this.integrations = new Map();
    this.syncLogs = new Map();
    this.syncItems = new Map();
    this.files = new Map();
    this.fileChunks = new Map();
    this.chatSessions = new Map();
    this.chatMessages = new Map();
    this.projects = new Map();
    this.userIdCounter = 1;
    this.integrationIdCounter = 1;
    this.syncLogIdCounter = 1;
    this.syncItemIdCounter = 1;
    this.fileIdCounter = 1;
    this.fileChunkIdCounter = 1;
    this.chatSessionIdCounter = 1;
    this.chatMessageIdCounter = 1;
    this.projectIdCounter = 1;
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Integration methods
  async getIntegrations(): Promise<Integration[]> {
    return Array.from(this.integrations.values());
  }

  async getIntegration(id: number): Promise<Integration | undefined> {
    return this.integrations.get(id);
  }

  async getIntegrationsByType(type: string): Promise<Integration[]> {
    return Array.from(this.integrations.values()).filter(
      (integration) => integration.type === type
    );
  }

  async createIntegration(integration: InsertIntegration): Promise<Integration> {
    const id = this.integrationIdCounter++;
    const now = new Date();

    // Encrypt credentials if provided
    let credentials: string | null = integration.credentials || null;
    if (credentials) {
      credentials = await cryptoService.encrypt(credentials);
    }

    const newIntegration: Integration = {
      ...integration,
      id,
      credentials,
      status: integration.status || "active",
      createdAt: now,
      updatedAt: now,
      lastSyncAt: null,
      nextSyncAt: null,
      config: integration.config || null,
      sourceConfig: integration.sourceConfig || null,
      destinationConfig: integration.destinationConfig || null,
      syncFilters: integration.syncFilters || null,
      syncSchedule: integration.syncSchedule || null,
      syncStatus: integration.syncStatus || 'idle',
      isLlmEnabled: integration.isLlmEnabled || null,
    };

    this.integrations.set(id, newIntegration);
    return newIntegration;
  }

  async updateIntegration(id: number, data: Partial<Integration>): Promise<Integration | undefined> {
    const integration = this.integrations.get(id);
    if (!integration) return undefined;

    // Encrypt credentials if they've been updated and are not already encrypted
    let credentials = data.credentials;
    if (credentials && credentials !== integration.credentials) {
      // Check if credentials are already encrypted (contain the iv:data format)
      if (!cryptoService.validateEncryptedFormat(credentials)) {
        console.log('[STORAGE] Encrypting new credentials');
        credentials = await cryptoService.encrypt(credentials);
        data.credentials = credentials;
      } else {
        console.log('[STORAGE] Credentials already encrypted, skipping encryption');
      }
    }

    const updated: Integration = {
      ...integration,
      ...data,
      updatedAt: new Date(),
    };

    this.integrations.set(id, updated);
    return updated;
  }

  async deleteIntegration(id: number): Promise<boolean> {
    return this.integrations.delete(id);
  }

  async updateIntegrationStatus(id: number, status: string): Promise<Integration | undefined> {
    return this.updateIntegration(id, { status });
  }

  // Sync Log methods
  async getSyncLogs(integrationId?: number, limit?: number): Promise<SyncLog[]> {
    let logs = Array.from(this.syncLogs.values());

    if (integrationId) {
      logs = logs.filter(log => log.integrationId === integrationId);
    }

    // Sort by start time descending (newest first)
    logs.sort((a, b) =>
      new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    );

    if (limit) {
      logs = logs.slice(0, limit);
    }

    return logs;
  }

  async getSyncLog(id: number): Promise<SyncLog | undefined> {
    return this.syncLogs.get(id);
  }

  async createSyncLog(log: InsertSyncLog): Promise<SyncLog> {
    const id = this.syncLogIdCounter++;
    const syncLog: SyncLog = { 
      ...log, 
      id, 
      endTime: null,
      startTime: log.startTime || new Date(),
      itemsProcessed: log.itemsProcessed || 0,
      itemsSuccess: log.itemsSuccess || 0,
      itemsFailed: log.itemsFailed || 0,
      details: log.details || null,
      error: log.error || null,
    };
    this.syncLogs.set(id, syncLog);
    return syncLog;
  }

  async updateSyncLog(id: number, data: Partial<SyncLog>): Promise<SyncLog | undefined> {
    const log = this.syncLogs.get(id);
    if (!log) return undefined;

    const updated: SyncLog = {
      ...log,
      ...data,
    };

    this.syncLogs.set(id, updated);
    return updated;
  }

  // Sync Item methods
  async getSyncItems(syncLogId?: number, status?: string): Promise<SyncItem[]> {
    let items = Array.from(this.syncItems.values());

    if (syncLogId) {
      items = items.filter(item => item.syncLogId === syncLogId);
    }

    if (status) {
      items = items.filter(item => item.status === status);
    }

    // Sort by created date descending
    items.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    return items;
  }

  async getSyncItem(id: number): Promise<SyncItem | undefined> {
    return this.syncItems.get(id);
  }

  async getSyncItemByExternalId(externalId: string, integrationId: number): Promise<SyncItem | undefined> {
    return Array.from(this.syncItems.values()).find(
      (item) => item.externalId === externalId && item.integrationId === integrationId
    );
  }

  async createSyncItem(item: InsertSyncItem): Promise<SyncItem> {
    const id = this.syncItemIdCounter++;
    const syncItem: SyncItem = {
      ...item,
      id,
      processedAt: null,
      createdAt: new Date(),
      error: item.error || null,
      syncLogId: item.syncLogId || null,
      sourceUrl: item.sourceUrl || null,
      destinationUrl: item.destinationUrl || null,
      metadata: item.metadata || null,
    };

    this.syncItems.set(id, syncItem);
    return syncItem;
  }

  async updateSyncItem(id: number, data: Partial<SyncItem>): Promise<SyncItem | undefined> {
    const item = this.syncItems.get(id);
    if (!item) return undefined;

    const updated: SyncItem = {
      ...item,
      ...data,
    };

    this.syncItems.set(id, updated);
    return updated;
  }

  // File methods
  async getFiles(platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string): Promise<File[]> {
    let allFiles = Array.from(this.files.values());

    // Filter by platform if specified
    if (platform) {
      allFiles = allFiles.filter(file => file.platform === platform);
    }

    // Filter by userId if specified
    if (userId) {
      allFiles = allFiles.filter(file => file.userId === userId);
    }

    // Filter by folderId if specified (check _sourceFolderId in extractedMetadata)
    if (folderId) {
      allFiles = allFiles.filter(file => {
        const metadata = file.extractedMetadata as any;
        return metadata?._sourceFolderId === folderId;
      });
    }

    // Sort by creation date (newest first)
    allFiles.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    // Apply offset and limit
    if (offset) {
      allFiles = allFiles.slice(offset);
    }

    if (limit) {
      allFiles = allFiles.slice(0, limit);
    }

    return allFiles;
  }

  async getFile(id: number): Promise<File | undefined> {
    return this.files.get(id);
  }

  async getFileByExternalId(externalId: string, platform: string): Promise<File | undefined> {
    return Array.from(this.files.values()).find(
      (file) => file.externalId === externalId && file.platform === platform
    );
  }

  async createFile(file: InsertFile): Promise<File> {
    const id = this.fileIdCounter++;
    const newFile: File = {
      ...file,
      id,
      status: file.status || "active",
      fileContent: file.fileContent || null,
      sourceUrl: file.sourceUrl || null,
      mimeType: file.mimeType || null,
      fileSize: file.fileSize || null,
      fileUrl: file.fileUrl || null,
      downloadUrl: file.downloadUrl || null,
      thumbnailUrl: file.thumbnailUrl || null,
      parentFolder: file.parentFolder || null,
      tags: file.tags || [],
      extractedMetadata: file.extractedMetadata || {},
      userId: file.userId || null,
      organizationId: file.organizationId || null,
      syncItemId: file.syncItemId || null,
      notionPageId: file.notionPageId || null,
      lastModified: file.lastModified || null,
      isShared: file.isShared || false,
      sharedWith: file.sharedWith || [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.files.set(id, newFile);
    return newFile;
  }

  async updateFile(id: number, data: Partial<File>): Promise<File | undefined> {
    const file = this.files.get(id);
    if (!file) return undefined;

    const updated: File = {
      ...file,
      ...data,
      updatedAt: new Date(),
    };

    this.files.set(id, updated);
    return updated;
  }

  async deleteFile(id: number): Promise<boolean> {
    return this.files.delete(id);
  }

  async markFileAsDeleted(id: number): Promise<File | undefined> {
    const file = this.files.get(id);
    if (!file) return undefined;

    // Update status to deleted instead of removing the file
    const updated: File = {
      ...file,
      status: "deleted",
      updatedAt: new Date(),
    };

    this.files.set(id, updated);
    return updated;
  }

  async searchFiles(query: string, platform?: string, fileType?: string, folderId?: string): Promise<File[]> {
    return Array.from(this.files.values()).filter(
      (file) => {
        // Search in file name
        const nameMatch = file.fileName && file.fileName.toLowerCase().includes(query.toLowerCase());

        // Search in owner field (from extracted metadata) - handle type safety
        const metadata = file.extractedMetadata as Record<string, any>;
        const ownerMatch = metadata?.owners?.[0]?.displayName?.toLowerCase().includes(query.toLowerCase()) ||
                          metadata?.owners?.[0]?.emailAddress?.toLowerCase().includes(query.toLowerCase());

        // Search in userId field
        const userIdMatch = file.userId && file.userId.toLowerCase().includes(query.toLowerCase());

        // At least one search field should match
        const searchMatch = nameMatch || ownerMatch || userIdMatch;

        // Filter by platform if specified
        const platformMatch = !platform || file.platform === platform;

        // Filter by file type if specified
        const typeMatch = !fileType || file.fileType === fileType;

        // Filter by folderId if specified (check _sourceFolderId in extractedMetadata)
        const folderMatch = !folderId || (() => {
          const metadata = file.extractedMetadata as any;
          return metadata?._sourceFolderId === folderId;
        })();

        return searchMatch && platformMatch && typeMatch && folderMatch;
      }
    );
  }

  // Incremental sync methods
  async getFilesForIntegration(integrationId: number, platform: string): Promise<File[]> {
    // Since we don't have integrationId directly on files, we'll filter by platform
    // In a real implementation, we'd join with sync items or have integrationId on files
    return Array.from(this.files.values()).filter(
      (file) => file.platform === platform
    );
  }

  async markFilesAsDeleted(externalIds: string[], platform: string): Promise<void> {
    // Convert iterator to array to avoid downlevelIteration issue
    const allFiles = Array.from(this.files.values());
    for (const file of allFiles) {
      if (file.platform === platform && externalIds.includes(file.externalId)) {
        const updated: File = {
          ...file,
          status: "deleted",
          updatedAt: new Date(),
        };
        this.files.set(file.id, updated);
      }
    }
  }

  async updateFileStatus(id: number, status: string): Promise<File | undefined> {
    const file = this.files.get(id);
    if (!file) return undefined;

    const updated: File = {
      ...file,
      status,
      updatedAt: new Date(),
    };

    this.files.set(id, updated);
    return updated;
  }

  // RAG and Chat methods implementation
  // File Chunks
  async getFileChunks(fileId: number): Promise<FileChunk[]> {
    return Array.from(this.fileChunks.values()).filter(
      chunk => chunk.fileId === fileId
    ).sort((a, b) => a.chunkIndex - b.chunkIndex);
  }

  async createFileChunk(chunk: InsertFileChunk): Promise<FileChunk> {
    const id = this.fileChunkIdCounter++;
    const newChunk: FileChunk = {
      fileId: chunk.fileId,
      chunkIndex: chunk.chunkIndex,
      content: chunk.content,
      id,
      metadata: chunk.metadata || {},
      embedding: chunk.embedding || null,
      tokenCount: chunk.tokenCount || null,
      createdAt: new Date(),
    };
    this.fileChunks.set(id, newChunk);
    return newChunk;
  }

  async deleteFileChunks(fileId: number): Promise<void> {
    const chunksToDelete = Array.from(this.fileChunks.entries()).filter(
      ([_, chunk]) => chunk.fileId === fileId
    );

    for (const [id, _] of chunksToDelete) {
      this.fileChunks.delete(id);
    }
  }

  async searchSimilarChunks(queryEmbedding: number[], enabledSources: string[], limit: number): Promise<any[]> {
    try {
      // Validate input parameters
      if (!queryEmbedding || !Array.isArray(queryEmbedding) || queryEmbedding.length === 0) {
        console.log("Invalid or empty queryEmbedding provided:", queryEmbedding);
        return [];
      }

      if (!limit || limit <= 0) {
        limit = 10; // Default limit
      }

      console.log(`Searching for similar chunks with embedding length: ${queryEmbedding.length}, sources: ${enabledSources}, limit: ${limit}`);

      // Convert the embedding array to pgvector format [1,2,3]
      const embeddingVector = `[${queryEmbedding.join(',')}]`;

      let queryText = `
        SELECT
          fc.id,
          fc.file_id as "fileId",
          fc.content,
          fc.chunk_index as "chunkIndex",
          fc.metadata,
          f.file_name as "fileName",
          f.platform,
          1 - (fc.embedding <=> $1::vector) as similarity
        FROM file_chunks fc
        JOIN files f ON fc.file_id = f.id
        WHERE fc.embedding IS NOT NULL
          AND f.status = 'active'
          AND 1 - (fc.embedding <=> $1::vector) > 0.3
      `;

      const queryParams: any[] = [embeddingVector];

      // Add platform filtering if sources are specified
      if (enabledSources.length > 0) {
        // Map source IDs to platform names
        const platformNames: string[] = [];
        for (const sourceId of enabledSources) {
          try {
            // Handle special virtual sources
            if (sourceId === 'uploaded-files') {
              platformNames.push('uploaded_files');
              console.log(`Mapped virtual source ${sourceId} -> platform: uploaded_files`);
              continue;
            }

            const integrationId = parseInt(sourceId);
            if (!isNaN(integrationId)) {
              const integration = await this.getIntegration(integrationId);
              if (integration) {
                // Map integration type to platform name
                let platformName: string;
                switch(integration.type) {
                  case 'google-drive':
                  case 'google_drive':
                    platformName = 'google_drive';
                    break;
                  case 'microsoft-teams':
                  case 'microsoft_teams':
                    platformName = 'microsoft_teams';
                    break;
                  case 'slack':
                    platformName = 'slack';
                    break;
                  case 'uploaded-files':
                    platformName = 'uploaded_files';
                    break;
                  default:
                    platformName = integration.type.replace('-', '_');
                }
                platformNames.push(platformName);
                console.log(`Mapped integration ${integrationId} (type: ${integration.type}) -> platform: ${platformName}`);
              }
            } else {
              platformNames.push(sourceId);
            }
          } catch (e) {
            platformNames.push(sourceId);
          }
        }

        if (platformNames.length > 0) {
          queryText += ` AND f.platform = ANY($2)`;
          queryParams.push(platformNames);
        }
      }

      queryText += ` ORDER BY similarity DESC LIMIT $${queryParams.length + 1}`;
      queryParams.push(limit);

      console.log(`Executing vector query with params:`, queryParams.map((p, i) => `$${i + 1}: ${Array.isArray(p) ? `[${p.join(',')}]` : p}`));

      // Execute raw SQL query to avoid Drizzle array formatting issues
      const client = postgres(process.env.DATABASE_URL!);
      const result = await client.unsafe(queryText, queryParams);
      await client.end();

      console.log(`Found ${result.length} similar chunks`);
      return result as any[];

    } catch (error: any) {
      console.error("Error in searchSimilarChunks:", error);
      // If there's a vector format error, try alternative approach
      if (error.message?.includes('Vector contents must start') || error.message?.includes('malformed array literal')) {
        console.log("Attempting fallback vector search without filtering...");
        try {
          const client = postgres(process.env.DATABASE_URL!);
          const fallbackResult = await client.unsafe(`
            SELECT
              fc.id,
              fc.file_id as "fileId",
              fc.content,
              fc.chunk_index as "chunkIndex",
              fc.metadata,
              f.file_name as "fileName",
              f.platform,
              0.5 as similarity
            FROM file_chunks fc
            JOIN files f ON fc.file_id = f.id
            WHERE fc.embedding IS NOT NULL
              AND f.status = 'active'
            ORDER BY fc.id DESC
            LIMIT $1
          `, [limit]);
          await client.end();
          console.log(`Fallback search found ${fallbackResult.length} chunks`);
          return fallbackResult as any[];
        } catch (fallbackError) {
          console.error("Fallback search also failed:", fallbackError);
        }
      }
      return [];
    }
  }

  // Chat Sessions
  async getChatSessions(userId?: string, limit?: number): Promise<ChatSession[]> {
    let sessions = Array.from(this.chatSessions.values());

    if (userId) {
      sessions = sessions.filter(session => session.userId === userId);
    }

    sessions.sort((a, b) =>
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );

    if (limit) {
      sessions = sessions.slice(0, limit);
    }

    return sessions;
  }

  async getChatSession(id: number): Promise<ChatSession | undefined> {
    return this.chatSessions.get(id);
  }

  async createChatSession(session: InsertChatSession): Promise<ChatSession> {
    const id = this.chatSessionIdCounter++;
    const now = new Date();
    const newSession: ChatSession = {
      id,
      title: session.title ?? null,
      userId: session.userId ?? null,
      enabledSources: session.enabledSources || {},
      createdAt: now,
      updatedAt: now,
    };
    this.chatSessions.set(id, newSession);
    return newSession;
  }

  async updateChatSession(id: number, data: Partial<ChatSession>): Promise<ChatSession | undefined> {
    const session = this.chatSessions.get(id);
    if (!session) return undefined;

    const updated: ChatSession = {
      ...session,
      ...data,
      updatedAt: new Date(),
    };

    this.chatSessions.set(id, updated);
    return updated;
  }

  async deleteChatSession(id: number): Promise<boolean> {
    // Also delete all messages in this session
    const messagesToDelete = Array.from(this.chatMessages.entries()).filter(
      ([_, message]) => message.sessionId === id
    );

    for (const [messageId, _] of messagesToDelete) {
      this.chatMessages.delete(messageId);
    }

    return this.chatSessions.delete(id);
  }

  // Chat Messages
  async getChatMessages(sessionId: number, limit?: number): Promise<ChatMessage[]> {
    let messages = Array.from(this.chatMessages.values()).filter(
      message => message.sessionId === sessionId
    );

    messages.sort((a, b) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    if (limit) {
      messages = messages.slice(-limit); // Get last N messages
    }

    return messages;
  }

  async createChatMessage(message: InsertChatMessage): Promise<ChatMessage> {
    const id = this.chatMessageIdCounter++;
    const newMessage: ChatMessage = {
      id,
      sessionId: message.sessionId,
      role: message.role,
      content: message.content,
      tokenCount: message.tokenCount ?? null,
      sourcesUsed: message.sourcesUsed || {},
      relevantChunks: message.relevantChunks || {},
      model: message.model ?? null,
      createdAt: new Date(),
    };
    this.chatMessages.set(id, newMessage);
    return newMessage;
  }

  // Projects
  async getProjects(userId?: string): Promise<Project[]> {
    let projects = Array.from(this.projects.values());

    if (userId) {
      projects = projects.filter(project => project.userId === userId);
    }

    projects.sort((a, b) =>
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );

    return projects;
  }

  async getProject(id: number): Promise<Project | undefined> {
    return this.projects.get(id);
  }

  async createProject(project: InsertProject): Promise<Project> {
    const id = this.projectIdCounter++;
    const now = new Date();
    const newProject: Project = {
      ...project,
      id,
      description: project.description || null,
      userId: project.userId || null,
      enabledSources: project.enabledSources || [],
      createdAt: now,
      updatedAt: now,
    };
    this.projects.set(id, newProject);
    return newProject;
  }

  async updateProject(id: number, data: Partial<Project>): Promise<Project | undefined> {
    const project = this.projects.get(id);
    if (!project) return undefined;

    const updated: Project = {
      ...project,
      ...data,
      updatedAt: new Date(),
    };

    this.projects.set(id, updated);
    return updated;
  }

  async deleteProject(id: number): Promise<boolean> {
    return this.projects.delete(id);
  }

  // Development utility method to clear all data
  async clearAllData(): Promise<void> {
    console.log('🧹 Clearing all in-memory data for fresh start...');

    try {
      // Clear all maps and reset counters
      console.log('  - Clearing chat messages...');
      this.chatMessages.clear();

      console.log('  - Clearing chat sessions...');
      this.chatSessions.clear();

      console.log('  - Clearing file chunks...');
      this.fileChunks.clear();

      console.log('  - Clearing files...');
      this.files.clear();

      console.log('  - Clearing sync items...');
      this.syncItems.clear();

      console.log('  - Clearing sync logs...');
      this.syncLogs.clear();

      console.log('  - Clearing integrations...');
      this.integrations.clear();

      console.log('  - Clearing projects...');
      this.projects.clear();

      console.log('  - Clearing users...');
      this.users.clear();

      // Reset all ID counters
      this.userIdCounter = 1;
      this.integrationIdCounter = 1;
      this.syncLogIdCounter = 1;
      this.syncItemIdCounter = 1;
      this.fileIdCounter = 1;
      this.fileChunkIdCounter = 1;
      this.chatSessionIdCounter = 1;
      this.chatMessageIdCounter = 1;
      this.projectIdCounter = 1;

      console.log('✅ Successfully cleared all in-memory data');
    } catch (error) {
      console.error('❌ Error clearing in-memory data:', error);
      throw error;
    }
  }
}

// DatabaseStorage implementation using PostgreSQL
export class DatabaseStorage implements IStorage {
  private db: ReturnType<typeof drizzle>;

  constructor() {
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL is required for DatabaseStorage');
    }

    const client = postgres(process.env.DATABASE_URL);
    this.db = drizzle(client);
  }

  // Development utility method to clear all data
  async clearAllData(): Promise<void> {
    console.log('🧹 Clearing all database data for fresh start...');

    try {
      // Clear in reverse dependency order to avoid foreign key constraints
      console.log('  - Clearing chat messages...');
      await this.db.delete(chatMessages);

      console.log('  - Clearing chat sessions...');
      await this.db.delete(chatSessions);

      console.log('  - Clearing file chunks...');
      await this.db.delete(fileChunks);

      console.log('  - Clearing files...');
      await this.db.delete(files);

      console.log('  - Clearing sync items...');
      await this.db.delete(syncItems);

      console.log('  - Clearing sync logs...');
      await this.db.delete(syncLogs);

      console.log('  - Clearing integrations...');
      await this.db.delete(integrations);

      console.log('  - Clearing projects...');
      await this.db.delete(projects);

      console.log('  - Clearing users...');
      await this.db.delete(users);

      console.log('✅ Successfully cleared all database data');
    } catch (error) {
      console.error('❌ Error clearing database data:', error);
      throw error;
    }
  }

  private throwNotImplemented(): never {
    throw new Error('Method not implemented in DatabaseStorage');
  }

  async getUser(id: number): Promise<User | undefined> { this.throwNotImplemented(); }
  async getUserByUsername(username: string): Promise<User | undefined> { this.throwNotImplemented(); }
  async createUser(user: InsertUser): Promise<User> { this.throwNotImplemented(); }
  async getIntegrations(): Promise<Integration[]> {
    const result = await this.db.select().from(integrations).orderBy(desc(integrations.createdAt));
    return result.map(integration => ({
      ...integration,
      config: integration.config as Record<string, any> | null,
      sourceConfig: integration.sourceConfig as Record<string, any> | null,
      destinationConfig: integration.destinationConfig as Record<string, any> | null,
      syncFilters: integration.syncFilters as Record<string, any> | null,
      syncStatus: integration.syncStatus || 'idle',
    }));
  }

  async getIntegration(id: number): Promise<Integration | undefined> {
    const result = await this.db.select().from(integrations).where(eq(integrations.id, id));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      config: result[0].config as Record<string, any> | null,
      sourceConfig: result[0].sourceConfig as Record<string, any> | null,
      destinationConfig: result[0].destinationConfig as Record<string, any> | null,
      syncFilters: result[0].syncFilters as Record<string, any> | null,
      syncStatus: result[0].syncStatus || 'idle',
    };
  }

  async getIntegrationsByType(type: string): Promise<Integration[]> {
    const result = await this.db.select().from(integrations).where(eq(integrations.type, type));
    return result.map(integration => ({
      ...integration,
      config: integration.config as Record<string, any> | null,
      sourceConfig: integration.sourceConfig as Record<string, any> | null,
      destinationConfig: integration.destinationConfig as Record<string, any> | null,
      syncFilters: integration.syncFilters as Record<string, any> | null,
      syncStatus: integration.syncStatus || 'idle',
    }));
  }

  async createIntegration(integration: InsertIntegration): Promise<Integration> {
    // Encrypt credentials if provided
    let encryptedCredentials = integration.credentials;
    if (encryptedCredentials) {
      encryptedCredentials = await cryptoService.encrypt(encryptedCredentials);
    }

    const integrationData = {
      ...integration,
      credentials: encryptedCredentials,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastSyncAt: null,
      nextSyncAt: null,
    };

    const result = await this.db.insert(integrations).values(integrationData).returning();
    return {
      ...result[0],
      config: result[0].config as Record<string, any> | null,
      sourceConfig: result[0].sourceConfig as Record<string, any> | null,
      destinationConfig: result[0].destinationConfig as Record<string, any> | null,
      syncFilters: result[0].syncFilters as Record<string, any> | null,
      syncStatus: result[0].syncStatus || 'idle',
    };
  }

  async updateIntegration(id: number, data: Partial<Integration>): Promise<Integration | undefined> {
    const result = await this.db.update(integrations)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(integrations.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      config: result[0].config as Record<string, any> | null,
      sourceConfig: result[0].sourceConfig as Record<string, any> | null,
      destinationConfig: result[0].destinationConfig as Record<string, any> | null,
      syncFilters: result[0].syncFilters as Record<string, any> | null,
      syncStatus: result[0].syncStatus || 'idle',
    };
  }

  async deleteIntegration(id: number): Promise<boolean> {
    try {
      await this.db.delete(integrations).where(eq(integrations.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting integration:", error);
      return false;
    }
  }

  async updateIntegrationStatus(id: number, status: string): Promise<Integration | undefined> {
    const result = await this.db.update(integrations)
      .set({ status, updatedAt: new Date() })
      .where(eq(integrations.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      config: result[0].config as Record<string, any> | null,
      sourceConfig: result[0].sourceConfig as Record<string, any> | null,
      destinationConfig: result[0].destinationConfig as Record<string, any> | null,
      syncFilters: result[0].syncFilters as Record<string, any> | null,
      syncStatus: result[0].syncStatus || 'idle',
    };
  }
  async getSyncLogs(integrationId?: number, limit?: number): Promise<SyncLog[]> {
    let baseQuery = this.db.select().from(syncLogs);

    if (integrationId) {
      baseQuery = baseQuery.where(eq(syncLogs.integrationId, integrationId)) as any;
    }

    baseQuery = baseQuery.orderBy(desc(syncLogs.startTime)) as any;

    if (limit) {
      baseQuery = baseQuery.limit(limit) as any;
    }

    const result = await baseQuery;
    return result.map(log => ({
      ...log,
      details: log.details as Record<string, any> | null,
    }));
  }

  async getSyncLog(id: number): Promise<SyncLog | undefined> {
    const result = await this.db.select().from(syncLogs).where(eq(syncLogs.id, id));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      details: result[0].details as Record<string, any> | null,
    };
  }

  async createSyncLog(log: InsertSyncLog): Promise<SyncLog> {
    const result = await this.db.insert(syncLogs).values(log).returning();
    return {
      ...result[0],
      details: result[0].details as Record<string, any> | null,
    };
  }

  async updateSyncLog(id: number, data: Partial<SyncLog>): Promise<SyncLog | undefined> {
    const result = await this.db.update(syncLogs)
      .set(data)
      .where(eq(syncLogs.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      details: result[0].details as Record<string, any> | null,
    };
  }
  async getSyncItems(syncLogId?: number, status?: string): Promise<SyncItem[]> {
    let baseQuery = this.db.select().from(syncItems);

    const conditions = [];
    if (syncLogId) conditions.push(eq(syncItems.syncLogId, syncLogId));
    if (status) conditions.push(eq(syncItems.status, status));

    if (conditions.length > 0) {
      baseQuery = baseQuery.where(and(...conditions)) as any;
    }

    return await (baseQuery.orderBy(desc(syncItems.createdAt)) as any);
  }

  async getSyncItem(id: number): Promise<SyncItem | undefined> {
    const result = await this.db.select().from(syncItems).where(eq(syncItems.id, id));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      metadata: result[0].metadata as Record<string, any> | null,
    };
  }

  async getSyncItemByExternalId(externalId: string, integrationId: number): Promise<SyncItem | undefined> {
    const result = await this.db.select().from(syncItems)
      .where(and(eq(syncItems.externalId, externalId), eq(syncItems.integrationId, integrationId)));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      metadata: result[0].metadata as Record<string, any> | null,
    };
  }

  async createSyncItem(item: InsertSyncItem): Promise<SyncItem> {
    const result = await this.db.insert(syncItems).values(item).returning();
    return {
      ...result[0],
      metadata: result[0].metadata as Record<string, any> | null,
    };
  }

  async updateSyncItem(id: number, data: Partial<SyncItem>): Promise<SyncItem | undefined> {
    const result = await this.db.update(syncItems)
      .set(data)
      .where(eq(syncItems.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      metadata: result[0].metadata as Record<string, any> | null,
    };
  }
  async getFiles(platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string): Promise<File[]> {
    let baseQuery = this.db.select().from(files);
    
    const conditions = [];
    if (platform) conditions.push(eq(files.platform, platform));
    if (userId) conditions.push(eq(files.userId, userId));
    if (folderId) conditions.push(eq(files.parentFolder, folderId));

    if (conditions.length > 0) {
      baseQuery = baseQuery.where(and(...conditions)) as any;
    }

    baseQuery = baseQuery.orderBy(desc(files.createdAt)) as any;

    if (limit) baseQuery = baseQuery.limit(limit) as any;
    if (offset) baseQuery = baseQuery.offset(offset) as any;

    const result = await baseQuery;
    return result.map(file => ({
      ...file,
      extractedMetadata: file.extractedMetadata as Record<string, any> | null,
    }));
  }

  async getFile(id: number): Promise<File | undefined> {
    const result = await this.db.select().from(files).where(eq(files.id, id));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      extractedMetadata: result[0].extractedMetadata as Record<string, any> | null,
    };
  }

  async getFileByExternalId(externalId: string, platform: string): Promise<File | undefined> {
    const result = await this.db.select().from(files)
      .where(and(eq(files.externalId, externalId), eq(files.platform, platform)));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      extractedMetadata: result[0].extractedMetadata as Record<string, any> | null,
    };
  }

  async createFile(file: InsertFile): Promise<File> {
    const result = await this.db.insert(files).values(file).returning();
    return {
      ...result[0],
      extractedMetadata: result[0].extractedMetadata as Record<string, any> | null,
    };
  }

  async updateFile(id: number, data: Partial<File>): Promise<File | undefined> {
    const result = await this.db.update(files)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(files.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      extractedMetadata: result[0].extractedMetadata as Record<string, any> | null,
    };
  }

  async deleteFile(id: number): Promise<boolean> {
    try {
      await this.db.delete(files).where(eq(files.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting file:", error);
      return false;
    }
  }

  async markFileAsDeleted(id: number): Promise<File | undefined> {
    const result = await this.db.update(files)
      .set({ status: 'deleted', updatedAt: new Date() })
      .where(eq(files.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      extractedMetadata: result[0].extractedMetadata as Record<string, any> | null,
    };
  }

  async searchFiles(query: string, platform?: string, fileType?: string, folderId?: string): Promise<File[]> {
    let baseQuery = this.db.select().from(files);

    const conditions = [];
    if (platform) conditions.push(eq(files.platform, platform));
    if (fileType) conditions.push(eq(files.fileType, fileType));
    if (folderId) conditions.push(eq(files.parentFolder, folderId));

    // Simple text search in filename
    if (query) {
      conditions.push(sql`${files.fileName} ILIKE ${'%' + query + '%'}`);
    }

    if (conditions.length > 0) {
      baseQuery = baseQuery.where(and(...conditions)) as any;
    }

    const result = await (baseQuery.orderBy(desc(files.createdAt)) as any);
    return result.map((file: any) => ({
      ...file,
      extractedMetadata: file.extractedMetadata as Record<string, any> | null,
    }));
  }

  async getFilesForIntegration(integrationId: number, platform: string): Promise<File[]> {
    try {
      // For now, let's get all files for this platform since we don't have a direct relationship
      // This is a temporary solution until we can properly establish the relationship
      const result = await this.db.select().from(files)
        .where(and(
          eq(files.platform, platform),
          eq(files.status, 'active')
        ))
        .orderBy(desc(files.createdAt));

      return result.map(file => ({
        ...file,
        extractedMetadata: file.extractedMetadata as Record<string, any> | null,
      }));
    } catch (error) {
      console.error("Error in getFilesForIntegration:", error);
      return [];
    }
  }

  async markFilesAsDeleted(externalIds: string[], platform: string): Promise<void> {
    if (externalIds.length === 0) return;

    // Use a simple loop approach to avoid SQL array issues
    for (const externalId of externalIds) {
      await this.db.update(files)
        .set({ status: 'deleted', updatedAt: new Date() })
        .where(and(
          eq(files.externalId, externalId),
          eq(files.platform, platform)
        ));
    }
  }

  async updateFileStatus(id: number, status: string): Promise<File | undefined> {
    const result = await this.db.update(files)
      .set({ status, updatedAt: new Date() })
      .where(eq(files.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      extractedMetadata: result[0].extractedMetadata as Record<string, any> | null,
    };
  }

  // RAG and Chat methods implementations
  async getFileChunks(fileId: number): Promise<FileChunk[]> {
    const result = await this.db.select().from(fileChunks).where(eq(fileChunks.fileId, fileId));
    return result.map(chunk => ({
      ...chunk,
      metadata: chunk.metadata as Record<string, any> | null,
    }));
  }

  async createFileChunk(chunk: InsertFileChunk): Promise<FileChunk> {
    const result = await this.db.insert(fileChunks).values(chunk).returning();
    return {
      ...result[0],
      metadata: result[0].metadata as Record<string, any> | null,
    };
  }

  async deleteFileChunks(fileId: number): Promise<void> {
    await this.db.delete(fileChunks).where(eq(fileChunks.fileId, fileId));
  }

  async deleteChunk(chunkId: number): Promise<void> {
    await this.db.delete(fileChunks).where(eq(fileChunks.id, chunkId));
  }

  async searchSimilarChunks(queryEmbedding: number[], enabledSources: string[], limit: number): Promise<any[]> {
    try {
      // Validate input parameters
      if (!queryEmbedding || !Array.isArray(queryEmbedding) || queryEmbedding.length === 0) {
        console.log("Invalid or empty queryEmbedding provided:", queryEmbedding);
        return [];
      }

      if (!limit || limit <= 0) {
        limit = 10; // Default limit
      }

      console.log(`Searching for similar chunks with embedding length: ${queryEmbedding.length}, sources: ${enabledSources}, limit: ${limit}`);

      // Convert the embedding array to pgvector format [1,2,3]
      const embeddingVector = `[${queryEmbedding.join(',')}]`;

      let queryText = `
        SELECT
          fc.id,
          fc.file_id as "fileId",
          fc.content,
          fc.chunk_index as "chunkIndex",
          fc.metadata,
          f.file_name as "fileName",
          f.platform,
          1 - (fc.embedding <=> $1::vector) as similarity
        FROM file_chunks fc
        JOIN files f ON fc.file_id = f.id
        WHERE fc.embedding IS NOT NULL
          AND f.status = 'active'
          AND 1 - (fc.embedding <=> $1::vector) > 0.3
      `;

      const queryParams: any[] = [embeddingVector];

      // Add platform filtering if sources are specified
      if (enabledSources.length > 0) {
        // Map source IDs to platform names
        const platformNames: string[] = [];
        for (const sourceId of enabledSources) {
          try {
            // Handle special virtual sources
            if (sourceId === 'uploaded-files') {
              platformNames.push('uploaded_files');
              console.log(`Mapped virtual source ${sourceId} -> platform: uploaded_files`);
              continue;
            }

            const integrationId = parseInt(sourceId);
            if (!isNaN(integrationId)) {
              const integration = await this.getIntegration(integrationId);
              if (integration) {
                // Map integration type to platform name
                let platformName: string;
                switch(integration.type) {
                  case 'google-drive':
                  case 'google_drive':
                    platformName = 'google_drive';
                    break;
                  case 'microsoft-teams':
                  case 'microsoft_teams':
                    platformName = 'microsoft_teams';
                    break;
                  case 'slack':
                    platformName = 'slack';
                    break;
                  case 'uploaded-files':
                    platformName = 'uploaded_files';
                    break;
                  default:
                    platformName = integration.type.replace('-', '_');
                }
                platformNames.push(platformName);
                console.log(`Mapped integration ${integrationId} (type: ${integration.type}) -> platform: ${platformName}`);
              }
            } else {
              platformNames.push(sourceId);
            }
          } catch (e) {
            platformNames.push(sourceId);
          }
        }

        if (platformNames.length > 0) {
          queryText += ` AND f.platform = ANY($2)`;
          queryParams.push(platformNames);
        }
      }

      queryText += ` ORDER BY similarity DESC LIMIT $${queryParams.length + 1}`;
      queryParams.push(limit);

      console.log(`Executing vector query with params:`, queryParams.map((p, i) => `$${i + 1}: ${Array.isArray(p) ? `[${p.join(',')}]` : p}`));

      // Execute raw SQL query to avoid Drizzle array formatting issues
      const client = postgres(process.env.DATABASE_URL!);
      const result = await client.unsafe(queryText, queryParams);
      await client.end();

      console.log(`Found ${result.length} similar chunks`);
      return result as any[];

    } catch (error: any) {
      console.error("Error in searchSimilarChunks:", error);
      // If there's a vector format error, try alternative approach
      if (error.message?.includes('Vector contents must start') || error.message?.includes('malformed array literal')) {
        console.log("Attempting fallback vector search without filtering...");
        try {
          const client = postgres(process.env.DATABASE_URL!);
          const fallbackResult = await client.unsafe(`
            SELECT
              fc.id,
              fc.file_id as "fileId",
              fc.content,
              fc.chunk_index as "chunkIndex",
              fc.metadata,
              f.file_name as "fileName",
              f.platform,
              0.5 as similarity
            FROM file_chunks fc
            JOIN files f ON fc.file_id = f.id
            WHERE fc.embedding IS NOT NULL
              AND f.status = 'active'
            ORDER BY fc.id DESC
            LIMIT $1
          `, [limit]);
          await client.end();
          console.log(`Fallback search found ${fallbackResult.length} chunks`);
          return fallbackResult as any[];
        } catch (fallbackError) {
          console.error("Fallback search also failed:", fallbackError);
        }
      }
      return [];
    }
  }
  async getChatSessions(userId?: string, limit?: number): Promise<ChatSession[]> {
    let baseQuery = this.db.select().from(chatSessions);

    if (userId) {
      baseQuery = baseQuery.where(eq(chatSessions.userId, userId)) as any;
    }

    baseQuery = baseQuery.orderBy(desc(chatSessions.updatedAt)) as any;

    if (limit) {
      baseQuery = baseQuery.limit(limit) as any;
    }

    const result = await baseQuery;
    return result.map(session => ({
      ...session,
      enabledSources: session.enabledSources as Record<string, any> | null,
    }));
  }

  async getChatSession(id: number): Promise<ChatSession | undefined> {
    const result = await this.db.select().from(chatSessions).where(eq(chatSessions.id, id));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      enabledSources: result[0].enabledSources as Record<string, any> | null,
    };
  }

  async createChatSession(session: InsertChatSession): Promise<ChatSession> {
    const result = await this.db.insert(chatSessions).values(session).returning();
    return {
      ...result[0],
      enabledSources: result[0].enabledSources as Record<string, any> | null,
    };
  }

  async updateChatSession(id: number, data: Partial<ChatSession>): Promise<ChatSession | undefined> {
    const result = await this.db.update(chatSessions)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(chatSessions.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      enabledSources: result[0].enabledSources as Record<string, any> | null,
    };
  }

  async deleteChatSession(id: number): Promise<boolean> {
    try {
      // Delete messages first (cascade should handle this, but being explicit)
      await this.db.delete(chatMessages).where(eq(chatMessages.sessionId, id));

      // Delete session
      const result = await this.db.delete(chatSessions).where(eq(chatSessions.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting chat session:", error);
      return false;
    }
  }

  async getChatMessages(sessionId: number, limit?: number): Promise<ChatMessage[]> {
    let baseQuery = this.db.select().from(chatMessages)
      .where(eq(chatMessages.sessionId, sessionId));

    baseQuery = baseQuery.orderBy(chatMessages.createdAt) as any;

    if (limit) {
      baseQuery = baseQuery.limit(limit) as any;
    }

    const result = await baseQuery;
    return result.map(message => ({
      ...message,
      sourcesUsed: message.sourcesUsed as Record<string, any> | null,
      relevantChunks: message.relevantChunks as Record<string, any> | null,
    }));
  }

  async createChatMessage(message: InsertChatMessage): Promise<ChatMessage> {
    const result = await this.db.insert(chatMessages).values(message).returning();
    return {
      ...result[0],
      sourcesUsed: result[0].sourcesUsed as Record<string, any> | null,
      relevantChunks: result[0].relevantChunks as Record<string, any> | null,
    };
  }
  async getProjects(userId?: string): Promise<Project[]> {
    let baseQuery = this.db.select().from(projects);

    if (userId) {
      baseQuery = baseQuery.where(eq(projects.userId, userId)) as any;
    }

    const result = await (baseQuery.orderBy(desc(projects.createdAt)) as any);
    return result.map((project: any) => ({
      ...project,
      enabledSources: project.enabledSources as Record<string, any> | null,
    }));
  }

  async getProject(id: number): Promise<Project | undefined> {
    const result = await this.db.select().from(projects).where(eq(projects.id, id));
    if (!result[0]) return undefined;
    return {
      ...result[0],
      enabledSources: result[0].enabledSources as Record<string, any> | null,
    };
  }

  async createProject(project: InsertProject): Promise<Project> {
    const result = await this.db.insert(projects).values(project).returning();
    return {
      ...result[0],
      enabledSources: result[0].enabledSources as Record<string, any> | null,
    };
  }

  async updateProject(id: number, data: Partial<Project>): Promise<Project | undefined> {
    const result = await this.db.update(projects)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(projects.id, id))
      .returning();
    if (!result[0]) return undefined;
    return {
      ...result[0],
      enabledSources: result[0].enabledSources as Record<string, any> | null,
    };
  }

  async deleteProject(id: number): Promise<boolean> {
    try {
      await this.db.delete(projects).where(eq(projects.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting project:", error);
      return false;
    }
  }
}

// DEPRECATED: Use the new modular storage system instead
// This is kept for backward compatibility during migration

// Use the new modular storage facade
export const storage = new StorageFacade(!!process.env.DATABASE_URL);

// Legacy storage classes (kept for reference during migration)
// Use DatabaseStorage when DATABASE_URL is available, otherwise fallback to MemStorage
// This ensures RAG functionality works when PostgreSQL is configured
export const legacyStorage = process.env.DATABASE_URL
  ? new DatabaseStorage()
  : new MemStorage();

console.log(`Storage initialized: ${process.env.DATABASE_URL ? 'PostgreSQL (DatabaseStorage)' : 'In-Memory (MemStorage)'}`);
if (process.env.DATABASE_URL) {
  console.log('✅ RAG functionality enabled with PostgreSQL backend');
} else {
  console.log('⚠️ Using in-memory storage - RAG functionality limited');
}