// Re-export all RAG service modules
export * from './chat.service';
export * from './ai-response.service';
export * from './context.service';
export * from './batch.service';
export * from './rag.facade';

// Convenience exports for commonly used classes
export { RAGChatService } from './chat.service';
export { RAGAIResponseService } from './ai-response.service';
export { RAGContextService } from './context.service';
export { RAGBatchService } from './batch.service';
export { RAGServiceFacade } from './rag.facade';

// Create and export the default RAG service instance
import { RAGServiceFacade } from './rag.facade';

// Initialize the RAG service facade
export const ragService = new RAGServiceFacade();

// Export for backward compatibility
export default ragService;
