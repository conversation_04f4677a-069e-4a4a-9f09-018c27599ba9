# Modular Routes Structure

This directory contains the modularized route definitions for the MeetSync Web App API server, organized to match the OpenAPI specification structure.

## Route Modules

### Core API Routes

- **`system.ts`** - System & Health endpoints
  - `/api/info` - API information
  - `/api/health/*` - Health check endpoints
  - `/api/readiness`, `/api/liveness` - Kubernetes-style health checks
  - `/api/debug/database-state` - Database diagnostic
  - `/api/diagnostic/*` - Basic diagnostic endpoints

- **`integrations.ts`** - Integration Management
  - `/api/integrations` - CRUD operations for integrations
  - `/api/integrations/:id/auth-url` - OAuth authorization URLs
  - `/api/integrations/:id/oauth/callback` - OAuth callbacks
  - `/api/integrations/:id/folders` - Google Drive folder access
  - `/api/integrations/:id/teams-*` - Microsoft Teams endpoints
  - `/api/integrations/oauth/callback` - Global OAuth callback handler

- **`sync.ts`** - Synchronization & Scheduling
  - `/api/schedules` - Sync schedule management
  - `/api/sync-logs` - Sync operation logs
  - `/api/sync-items/:syncLogId` - Individual sync items
  - `/api/sync-now` - Manual sync trigger
  - `/api/sync/re-vectorize` - Re-vectorization endpoint

- **`chat.ts`** - Chat System
  - `/api/chat/sessions` - Chat session management
  - `/api/chat/sessions/:id/messages` - Message handling
  - `/api/chat/sources` - Available chat sources
  - `/api/chat/search` - Similar content search
  - `/api/chat/search-files` - File description search

- **`files.ts`** - File Management
  - `/api/files` - File listing with filtering/pagination
  - `/api/files/search` - File search functionality
  - `/api/files/:id` - Individual file operations
  - `/api/files/upload` - File upload endpoints
  - `/api/files/uploaded` - Uploaded file management
  - `/api/files/:fileId/embeddings` - Embedding processing
  - `/api/files/reprocess-google-drive` - Reprocessing utility

### Supporting API Routes

- **`rag.ts`** - RAG (Retrieval Augmented Generation)
  - `/api/rag/search` - RAG search functionality
  - `/api/rag/status` - RAG system status

- **`upload.ts`** - Upload Service
  - `/api/upload/status` - Upload service status

- **`legacy.ts`** - Legacy Compatibility
  - `/api/chat-sessions` - Legacy chat sessions endpoint
  - `/api/projects` - Legacy projects endpoint

### Development & Debug Routes

- **`test.ts`** - Test Endpoints
  - `/api/test` - Basic test endpoint
  - `/api/test/storage` - Storage connectivity test

- **`debug.ts`** - Debug & Diagnostic
  - `/api/diagnostic/health` - Enhanced health diagnostics
  - `/api/diagnostic/integrations` - Integration testing
  - `/api/diagnostic/google-drive` - Google Drive diagnostics
  - `/api/diagnostic/teams` - Microsoft Teams diagnostics
  - Various other diagnostic endpoints

## Architecture

Each route module follows this pattern:

```typescript
import type { Express } from "express";
import { /* required dependencies */ } from "../path/to/dependency";

/**
 * Register [module name] routes
 */
export function register[ModuleName]Routes(app: Express) {
  // Route definitions here
}
```

## Integration with Main Router

All route modules are registered in `../routes.ts`:

```typescript
import { register[ModuleName]Routes } from "./routes/[module]";

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize services
  await initializeServices();

  // Register all route modules
  register[ModuleName]Routes(app);
  
  // ... other modules
  
  const httpServer = createServer(app);
  return httpServer;
}
```

## Benefits

1. **Modularity**: Routes are organized by functional area
2. **Maintainability**: Easier to find and modify specific route groups
3. **OpenAPI Alignment**: Structure matches the OpenAPI specification
4. **Scalability**: New route modules can be added easily
5. **Testing**: Individual route modules can be tested in isolation
6. **Documentation**: Each module has a clear purpose and scope

## File Size Constraint

Each route module is kept under 500 lines of code to maintain readability and follow the project's code structure guidelines. 