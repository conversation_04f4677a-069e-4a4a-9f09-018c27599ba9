import React, { useEffect, useRef, useState } from 'react';
import { useChatStore, chatSelectors } from '../../store/chat/store';
import { ChatInput } from './ChatInput';
import { MessageList } from './MessageList';
import { SessionSidebar } from './SessionSidebar';
import { SourceSelector } from './SourceSelector';
import { ChatWelcome } from './ChatWelcome';
import { ActiveToolIndicator } from '../../components/chat/ActiveToolIndicator';
import { MCPErrorNotification, MCPError } from '../../components/chat/MCPErrorNotification';
import { MockToolUsageGenerator } from '../../lib/mock-tool-usage';
import { MCPErrorManager } from '../../lib/mcp-error-manager';
import { MCPStatusPanel, MCPToolUsage } from '../../components/mcp';
import { AlertTriangle } from 'lucide-react';
import { cn } from '../../lib/utils';

// API functions for fetching sources and statistics
const fetchSources = async () => {
  try {
    const response = await fetch('/api/chat/sources');
    if (!response.ok) throw new Error('Failed to fetch chat sources');
    const data = await response.json();

    // Return sources directly from the chat sources endpoint
    return (data.sources || []).map((source: any) => ({
      id: source.id,
      name: source.name,
      type: source.type,
      platform: source.platform,
      connected: source.connected || source.status === 'connected',
      status: source.status,
      fileCount: source.fileCount || 0,
      lastSyncAt: source.lastSyncAt,
      config: source.config,
      driveInfo: source.driveInfo
    }));
  } catch (error) {
    console.error('Error fetching sources:', error);
    return [];
  }
};

const fetchFileStats = async () => {
  try {
    // Try multiple approaches to get file statistics
    
    // Approach 1: Try the search endpoint with a broad query
    try {
      const searchResponse = await fetch('/api/chat/search-files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: '',  // Empty query to get all files
          limit: 100
        })
      });
      
      if (searchResponse.ok) {
        const searchData = await searchResponse.json();
        if (searchData.count > 0) {
          return {
            totalFiles: searchData.count,
            indexedFiles: searchData.count
          };
        }
      }
    } catch (error) {
      console.log('Search-files endpoint not available, trying alternative...');
    }

    // Approach 2: Try the general search endpoint
    try {
      const searchResponse = await fetch('/api/chat/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: 'document file content',
          limit: 50
        })
      });
      
      if (searchResponse.ok) {
        const searchData = await searchResponse.json();
        return {
          totalFiles: searchData.count || searchData.results?.length || 0,
          indexedFiles: searchData.results?.length || 0
        };
      }
    } catch (error) {
      console.log('Search endpoint not available, using fallback...');
    }

    // Approach 3: Fallback based on integration status
    // From logs, we know Google Drive has ~7 files when working
    const integrationsResponse = await fetch('/api/integrations');
    if (integrationsResponse.ok) {
      const data = await integrationsResponse.json();
      const connectedIntegrations = (data.integrations || []).filter((integration: any) => 
        integration.sourceConfig && Object.keys(integration.sourceConfig).length > 0
      );
      
      if (connectedIntegrations.length > 0) {
        // Estimate based on connected integrations
        return {
          totalFiles: connectedIntegrations.length * 7, // Rough estimate
          indexedFiles: connectedIntegrations.length * 7
        };
      }
    }
    
    return { totalFiles: 0, indexedFiles: 0 };
  } catch (error) {
    console.error('Error fetching file stats:', error);
    return { totalFiles: 0, indexedFiles: 0 };
  }
};

interface ChatInterfaceProps {
  className?: string;
  mode?: 'fullpage' | 'widget';
  onSessionChange?: (sessionId: string) => void;
}

export function ChatInterface({ 
  className = "", 
  mode = 'fullpage',
  onSessionChange 
}: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [sources, setSources] = useState<any[]>([]);
  const [fileStats, setFileStats] = useState({ totalFiles: 0, indexedFiles: 0 });
  const [isThinking, setIsThinking] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [newChatClicked, setNewChatClicked] = useState(false);
  const [clickedPrompt, setClickedPrompt] = useState<string | null>(null);
  const [activeTools, setActiveTools] = useState<string[]>([]);
  const [mcpErrors, setMcpErrors] = useState<MCPError[]>([]);
  const [toolUsages, setToolUsages] = useState<MCPToolUsage[]>([]);
  const [showMCPStatus, setShowMCPStatus] = useState(false);
  
  // Initialize showWelcome based on current sessions state
  const [showWelcome, setShowWelcome] = useState(() => {
    const { sessions, currentSessionId } = useChatStore.getState();
    return sessions.length === 0 && !currentSessionId;
  });
  
  // Store state
  const {
    messages,
    inputMessage,
    isGenerating,
    currentSessionId,
    sessions,
    enabledSources,
    availableSources,
    uploadingFiles,
    isUploading,
    openaiConfig,
  } = useChatStore();

  // Store actions
  const {
    updateInputMessage,
    sendMessage,
    deleteMessage,
    createSession,
    switchSession,
    uploadFiles,
    removeFile,
    toggleSource,
    updateOpenAIConfig,
  } = useChatStore();

  // Selectors
  const currentSession = useChatStore(chatSelectors.currentSession);
  const currentMessages = useChatStore(chatSelectors.currentMessages);
  const canSend = useChatStore(chatSelectors.canSend);

  // Fetch sources and file stats on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Fetch both sources and file statistics
        const [sourcesData, statsData] = await Promise.all([
          fetchSources(),
          fetchFileStats()
        ]);
        
        // Update sources with file counts if available
        const updatedSources = sourcesData.map((source: any) => ({
          ...source,
          fileCount: source.connected ? Math.floor(statsData.totalFiles / Math.max(sourcesData.filter((s: any) => s.connected).length, 1)) : 0
        }));
        
        setSources(updatedSources);
        setFileStats(statsData);
        
        console.log('Loaded sources:', updatedSources);
        console.log('Loaded file stats:', statsData);
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };
    
    loadData();
  }, []);

  // Show welcome screen logic
  useEffect(() => {
    const shouldShowWelcome = sessions.length === 0 && !currentSessionId;
    setShowWelcome(shouldShowWelcome);
    console.log('Welcome screen logic:', { sessions: sessions.length, currentSessionId, shouldShowWelcome });
  }, [sessions.length, currentSessionId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [currentMessages]);

  // Handle session switch
  const handleSessionSwitch = (sessionId: string) => {
    console.log('Switching to session:', sessionId);
    switchSession(sessionId);
    setShowWelcome(false); // Explicitly hide welcome screen
    onSessionChange?.(sessionId);
  };

  // Handle file upload
  const handleFileUpload = async (files: File[]) => {
    if (files.length > 0) {
      await uploadFiles(files);
      if (!enabledSources.includes('uploaded-files')) {
        toggleSource('uploaded-files');
      }
    }
  };

  // Enhanced message send with thinking visualization
  const handleSendMessage = async () => {
    if (!canSend) return;

    try {
      setIsThinking(true);

      // Simulate active tool usage during message generation
      const mockActiveTools = MockToolUsageGenerator.generateActiveTools();
      setActiveTools(mockActiveTools);

      // Simulate potential MCP errors during tool usage
      setTimeout(() => {
        const mockError = MCPErrorManager.generateMockError();
        if (mockError) {
          setMcpErrors(prev => [...prev, mockError]);
        }
      }, 2000 + Math.random() * 3000); // Random delay between 2-5 seconds

      await sendMessage();
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsThinking(false);
      setActiveTools([]); // Clear active tools when done
    }
  };

  // MCP Error handling functions
  const handleRetryError = async (errorId: string) => {
    const error = mcpErrors.find(e => e.id === errorId);
    if (!error) return;

    try {
      const success = await MCPErrorManager.retryOperation(errorId);
      if (success) {
        // Remove error on successful retry
        setMcpErrors(prev => prev.filter(e => e.id !== errorId));
      } else {
        // Update error message to indicate retry failed
        setMcpErrors(prev => prev.map(e =>
          e.id === errorId
            ? { ...e, message: `${e.message} (Retry failed - please try again)` }
            : e
        ));
      }
    } catch (err) {
      console.error('Error during retry:', err);
    }
  };

  const handleDismissError = (errorId: string) => {
    setMcpErrors(prev => prev.filter(e => e.id !== errorId));
  };

  const handleOpenSettings = () => {
    setShowSettings(true);
  };

  // Handle key press in input
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const layoutClasses = mode === 'fullpage' 
    ? "h-full flex bg-gray-50 overflow-hidden" 
    : "h-[100vh] flex rounded-lg border border-gray-200 shadow-lg overflow-hidden bg-white";

  // Handle welcome screen actions
  const handleStartChat = () => {
    const sessionId = createSession('New Chat');
    handleSessionSwitch(sessionId);
  };

  const handleQuickAction = (prompt: string) => {
    setClickedPrompt(prompt);
    const sessionId = createSession();
    handleSessionSwitch(sessionId);
    updateInputMessage(prompt);
    setTimeout(() => {
      handleSendMessage();
      setClickedPrompt(null);
    }, 100);
  };

  // Show welcome screen if conditions are met
  if (showWelcome) {
    return (
      <div className={cn(layoutClasses, className)}>
        <ChatWelcome
          onStartChat={handleStartChat}
          onQuickAction={handleQuickAction}
          sources={sources}
          fileStats={fileStats}
        />
      </div>
    );
  }

  return (
    <div className={cn(layoutClasses, className)}>
      {/* Sidebar - Professional Design */}
      <div className="w-80 bg-gradient-to-b from-slate-100 via-blue-50 to-indigo-100 border-r border-slate-200 flex flex-col h-full">
        {/* Header with logo */}
        <div className="p-6 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-blue-50">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg">
              <span className="text-white text-xl font-bold">🤖</span>
            </div>
            <h1 className="text-xl font-bold text-slate-800">GPT Unify</h1>
          </div>
          
          <button
            onClick={() => {
              setNewChatClicked(true);
            const sessionId = createSession();
            handleSessionSwitch(sessionId);
              setTimeout(() => setNewChatClicked(false), 1000);
            }}
            className={cn(
              "text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 flex items-center space-x-2 shadow-md hover:shadow-lg transform hover:scale-105",
              newChatClicked 
                ? "bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 scale-110" 
                : "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600"
            )}
            title="Start new chat"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            <span>{newChatClicked ? "🎉 Created!" : "✨ New Chat"}</span>
          </button>
        </div>

        {/* Chat History Section */}
        <div className="flex-1 min-h-0 bg-gradient-to-b from-slate-50 to-blue-50">
          <div className="p-4">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-lg">💬</span>
              <h3 className="text-sm font-medium text-slate-700">Your Conversations</h3>
            </div>
            <div className="space-y-2">
              {sessions.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-md">
                    <span className="text-lg">🎯</span>
                  </div>
                  <p className="text-sm text-slate-600 font-medium">No conversations yet</p>
                  <p className="text-xs text-slate-500 mt-1">Start a chat above!</p>
                </div>
              ) : (
                sessions.map((session) => (
                  <button
                    key={session.id}
                    onClick={() => handleSessionSwitch(session.id)}
                    className={cn(
                      "w-full text-left p-3 rounded-xl transition-all duration-300 relative group",
                      session.id === currentSessionId
                        ? "bg-gradient-to-r from-blue-100 to-indigo-100 border border-blue-200 shadow-md" 
                        : "hover:bg-gradient-to-r hover:from-slate-100 hover:to-blue-100 border border-transparent hover:border-slate-200 hover:shadow-sm"
                    )}
                  >
                    {/* Active indicator */}
                    {session.id === currentSessionId && (
                      <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-r"></div>
                    )}
                    
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0 ml-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm">🎨</span>
                          <h4 className={cn(
                            "text-sm font-medium truncate",
                            session.id === currentSessionId ? "text-slate-800" : "text-slate-600"
                          )}>
                            {session.title}
                          </h4>
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <span className="text-xs">💫</span>
                          <p className="text-xs text-slate-500">
                            {session.messageCount || 0} messages
                          </p>
                        </div>
                      </div>
                    </div>
                  </button>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Data Sources Section */}
        <div className="p-4 border-t border-slate-200 bg-gradient-to-r from-slate-50 to-blue-50">
          <div className="flex items-center space-x-2 mb-3">
            <span className="text-lg">🔌</span>
            <h3 className="text-sm font-medium text-slate-700">Data Sources</h3>
          </div>
          <div className="space-y-2">
            {availableSources.map((source) => {
              const isEnabled = enabledSources.includes(source);
              const sourceConfig = {
                'uploaded-files': {
                  label: 'Files',
                  emoji: '📁',
                  color: 'from-green-400 to-emerald-500',
                  icon: (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5L12 5H5a2 2 0 00-2 2z" />
                    </svg>
                  )
                },
                google_drive: {
                  label: 'Google Drive',
                  emoji: '🟡',
                  color: 'from-yellow-400 to-orange-500',
                  icon: (
                    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M6.19 2.76L11.88 1.27C12.19 1.18 12.53 1.27 12.76 1.5L22.12 10.87C22.35 11.1 22.44 11.44 22.35 11.75L20.86 17.44C20.77 17.75 20.5 18 20.19 18.09L14.5 19.58C14.19 19.67 13.85 19.58 13.62 19.35L4.26 10C4.03 9.77 3.94 9.43 4.03 9.12L5.52 3.43C5.61 3.12 5.88 2.87 6.19 2.76ZM8.5 11.94L11.94 8.5L15.38 11.94L11.94 15.38L8.5 11.94Z"/>
                    </svg>
                  )
                },
                microsoft_teams: {
                  label: 'Microsoft Teams',
                  emoji: '🟣',
                  color: 'from-purple-400 to-blue-500',
                  icon: (
                    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M20.17 7.83C20.72 8.38 21 9.17 21 10V16C21 17.1 20.1 18 19 18H13V14H17V10H13V8H19C19.83 8 20.17 7.83 20.17 7.83ZM11 6H7V2H11V6ZM3 8H11V18H3V8ZM5 10V16H9V10H5Z"/>
                    </svg>
                  )
                },
                emails: {
                  label: 'Emails',
                  emoji: '📧',
                  color: 'from-red-400 to-pink-500',
                  icon: (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  )
                }
              };
              
              const config = sourceConfig[source as keyof typeof sourceConfig];
              if (!config) return null;
              
              return (
                <button
                  key={source}
                  onClick={() => toggleSource(source)}
                  className={cn(
                    "w-full flex items-center justify-between px-3 py-3 rounded-xl text-sm transition-all duration-300 shadow-sm",
                    isEnabled 
                      ? `bg-gradient-to-r ${config.color} text-white border border-white/20 hover:shadow-md` 
                      : "bg-gradient-to-r from-slate-200 to-slate-300 text-slate-600 border border-slate-300 hover:from-slate-300 hover:to-slate-400 hover:text-slate-700"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-base">{config.emoji}</span>
                    <div className="flex items-center space-x-2">
                      <div className={cn(
                        "flex items-center justify-center",
                        isEnabled ? "text-white" : "text-slate-500"
                      )}>
                        {config.icon}
                      </div>
                      <span className="font-medium">{config.label}</span>
                    </div>
                  </div>
                  
                  {/* Toggle Switch */}
                  <div className={cn(
                    "w-10 h-5 rounded-full relative transition-all duration-300",
                    isEnabled ? "bg-white/30" : "bg-slate-400/50"
                  )}>
                    <div className={cn(
                      "w-4 h-4 bg-white rounded-full absolute top-0.5 transition-transform duration-300 shadow-sm",
                      isEnabled ? "translate-x-5" : "translate-x-0.5"
                    )} />
                  </div>
                </button>
              );
            })}
          </div>
        </div>
        
        {/* Settings Button */}
        <div className="p-4 border-t border-slate-200 bg-gradient-to-r from-slate-100 to-blue-100">
          <button 
            onClick={() => setShowSettings(true)}
            className="w-full flex items-center space-x-3 text-slate-600 hover:text-slate-800 text-sm transition-all duration-300 p-3 rounded-xl hover:bg-gradient-to-r hover:from-slate-200 hover:to-blue-200"
          >
            <span className="text-base">⚙️</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span className="font-medium">Model Settings</span>
            <span className="text-xs bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-2 py-1 rounded-full shadow-sm">GPT-4</span>
          </button>
        </div>
      </div>

      {/* Main Chat Panel - Stunning Design */}
      <div className="flex-1 flex flex-col bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 scrollbar-hide min-w-0 h-full">
        {currentMessages.length === 0 ? (
          /* Enhanced Empty State - Professional */
          <div className="flex-1 overflow-y-auto scrollbar-hide bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
            <div className="max-w-2xl mx-auto text-center space-y-6 py-4 px-4" style={{ paddingBottom: '20px' }}>
              {/* Welcome Header - Professional */}
              <div className="space-y-3 animate-fadeIn">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-700 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  GPT Unify
                </h1>
                <div className="text-lg font-medium">
                  {enabledSources.length > 0 ? (
                    <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                      🎯 Active Source{enabledSources.length > 1 ? 's' : ''}: {' '}
                      <span className="font-bold">
                        {enabledSources.map(source => {
                          const labels = { uploaded_files: '📁 Files', google_drive: '🟡 Google Drive', microsoft_teams: '🟣 Teams', emails: '📧 Emails' };
                          return labels[source as keyof typeof labels] || source;
                        }).join(', ')}
                      </span>
                    </span>
                  ) : (
                    <span className="bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">
                      🔗 Enable data sources to get started
                    </span>
                  )}
                </div>
              </div>
              
              {/* Prompt Starters - Professional Cards */}
              <div className="space-y-3">
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-lg">🎨</span>
                  <h2 className="text-lg font-medium bg-gradient-to-r from-slate-600 to-blue-600 bg-clip-text text-transparent">
                    Get started with these prompts
                  </h2>
                  <span className="text-lg">✨</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {[
                    { title: "Explain this code snippet", icon: "💻", emoji: "🔍", className: "bg-gradient-to-br from-blue-400 to-blue-600", description: "Upload code and get detailed explanations" },
                    { title: "Draft an email", icon: "📧", emoji: "✍️", className: "bg-gradient-to-br from-green-400 to-green-600", description: "Create professional emails from your notes" },
                    { title: "Brainstorm ideas", icon: "💡", emoji: "🧠", className: "bg-gradient-to-br from-orange-400 to-orange-600", description: "Generate creative solutions and concepts" },
                    { title: "Analyze documents", icon: "📄", emoji: "📊", className: "bg-gradient-to-br from-purple-400 to-purple-600", description: "Extract insights from your uploaded files" }
                  ].map((prompt, index) => (
                    <button 
                      key={index}
                      onClick={() => handleQuickAction(prompt.title)}
                      disabled={clickedPrompt === prompt.title}
                      className={cn(
                        "p-4 text-left text-white rounded-lg hover:shadow-lg transition-all duration-300 group transform hover:scale-102 border border-white/20",
                        clickedPrompt === prompt.title 
                          ? "bg-gradient-to-br from-green-500 to-emerald-600 scale-102 shadow-lg" 
                          : prompt.className
                      )}
                    >
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-2xl">{prompt.emoji}</span>
                        <span className="text-xl">{prompt.icon}</span>
                      </div>
                      <h3 className="font-semibold text-base group-hover:text-yellow-100 transition-colors mb-2">
                        {clickedPrompt === prompt.title ? "🚀 Starting..." : prompt.title}
                      </h3>
                      <p className="text-sm text-white/90 group-hover:text-white transition-colors">{prompt.description}</p>
                    </button>
                  ))}
                </div>
              </div>
              
              {/* File Upload Area - Professional */}
              <div className="space-y-3">
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-lg">📁</span>
                  <h2 className="text-lg font-medium bg-gradient-to-r from-slate-600 to-blue-600 bg-clip-text text-transparent">
                    Or upload files to analyze
                  </h2>
                  <span className="text-lg">📤</span>
                </div>
                <div 
                  className="border-2 border-dashed border-blue-300 rounded-lg p-4 text-center hover:border-solid transition-all duration-300 bg-gradient-to-br from-slate-50 to-blue-50 shadow-sm hover:shadow-md transform hover:scale-101 cursor-pointer"
                  onDrop={(e) => {
                    e.preventDefault();
                    const files = Array.from(e.dataTransfer.files);
                    handleFileUpload(files);
                  }}
                  onDragOver={(e) => e.preventDefault()}
                  onClick={() => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.multiple = true;
                    input.accept = '.pdf,.doc,.docx,.txt,.md,.jpg,.jpeg,.png,.gif,.csv,.json,.xml';
                    input.onchange = (e) => {
                      const files = Array.from((e.target as HTMLInputElement).files || []);
                      if (files.length > 0) {
                        handleFileUpload(files);
                      }
                    };
                    input.click();
                  }}
                >
                  <div className="space-y-2">
                    <div className="w-12 h-12 mx-auto bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-md">
                      <span className="text-xl">☁️</span>
                    </div>
                    <div>
                      <p className="text-base font-medium bg-gradient-to-r from-slate-700 to-blue-600 bg-clip-text text-transparent mb-1">
                        {isUploading ? "📤 Uploading files..." : "Drop files here or click to upload"}
                      </p>
                      <p className="text-sm text-slate-600">
                        Supports documents, images, and more
                      </p>
                      {uploadingFiles.length > 0 && (
                        <p className="text-sm text-blue-600 font-medium mt-2">
                          📎 {uploadingFiles.length} file{uploadingFiles.length !== 1 ? 's' : ''} ready
                        </p>
                      )}
                    </div>
                  </div>
                </div>
                  </div>
                </div>
              </div>
            ) : (
          /* Chat Messages View - Enhanced */
          <>
            {/* Compact Header for Active Chat - Professional */}
            <div className="px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-white via-slate-50 to-blue-50 flex-shrink-0 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-xl">💬</span>
                  <div>
                    <h1 className="text-lg font-semibold bg-gradient-to-r from-slate-700 to-blue-600 bg-clip-text text-transparent">
                      {currentSession?.title || 'New Chat'}
                    </h1>
                    <p className="text-sm text-slate-600 font-medium">
                      {currentMessages.length} messages • {enabledSources.length} sources active
                    </p>
                  </div>
                </div>

                {/* Status and MCP Panel - Professional */}
                <div className="flex items-center space-x-3">
                  {/* MCP Status Compact */}
                  <MCPStatusPanel compact={true} showToolsList={false} />

                  {isGenerating && (
                    <div className="flex items-center space-x-2 text-blue-600 bg-blue-100 px-3 py-1 rounded-full">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <span className="text-sm font-medium">Generating...</span>
                    </div>
                  )}

                  {isThinking && (
                    <div className="flex items-center space-x-2 text-indigo-600 bg-indigo-100 px-3 py-1 rounded-full">
                      <div className="w-2 h-2 bg-indigo-600 rounded-full"></div>
                      <span className="text-sm font-medium">Processing...</span>
                    </div>
                  )}

                  {/* Active Tool Indicator */}
                  {activeTools.length > 0 && (
                    <ActiveToolIndicator activeTools={activeTools} />
                  )}

                  {/* MCP Error Indicator */}
                  {mcpErrors.length > 0 && (
                    <div className="flex items-center space-x-2 text-red-600 bg-red-100 px-3 py-1 rounded-full border border-red-200">
                      <AlertTriangle className="w-3 h-3" />
                      <span className="text-sm font-medium">
                        {mcpErrors.length} MCP Error{mcpErrors.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto scrollbar-hide bg-gradient-to-b from-slate-50 to-blue-50 min-h-0">
              <MessageList
                messages={currentMessages}
                isGenerating={isGenerating}
                onDeleteMessage={deleteMessage}
              />

              {/* MCP Error Notifications */}
              {mcpErrors.length > 0 && (
                <div className="px-4 pb-4 space-y-3">
                  {mcpErrors.map((error) => (
                    <MCPErrorNotification
                      key={error.id}
                      error={error}
                      onRetry={() => handleRetryError(error.id)}
                      onDismiss={() => handleDismissError(error.id)}
                      onOpenSettings={handleOpenSettings}
                    />
                  ))}
                </div>
              )}

            <div ref={messagesEndRef} />
          </div>
          </>
        )}
          
        {/* Enhanced Chat Input - Professional Design */}
        <div className="bg-gradient-to-r from-white via-blue-50 to-purple-50 border-t border-slate-200 flex-shrink-0 p-4 shadow-lg">
          <div className="max-w-4xl mx-auto">
          <ChatInput
            value={inputMessage}
            onChange={updateInputMessage}
            onSend={handleSendMessage}
            onKeyPress={handleKeyPress}
            onFileUpload={handleFileUpload}
            uploadingFiles={uploadingFiles}
            onRemoveFile={removeFile}
              isLoading={isGenerating || isThinking}
            isUploading={isUploading}
            canSend={canSend as boolean}
              placeholder="✨ Type your magical message... (Shift+Enter for new line) 🚀"
          />
          </div>
        </div>
      </div>

      {/* Settings Modal - Spectacular */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-3xl shadow-2xl border border-purple-200/50 w-full max-w-md mx-4 animate-bounceIn">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-xl">⚙️</span>
                  </div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Settings
                  </h2>
                </div>
                <button
                  onClick={() => setShowSettings(false)}
                  className="p-2 hover:bg-red-100 rounded-xl transition-all duration-200 text-gray-500 hover:text-red-600 transform hover:scale-110"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Settings Content */}
              <div className="space-y-6">
                {/* Model Configuration */}
                <div className="space-y-3">
                  <h3 className="text-lg font-bold text-gray-800 flex items-center space-x-2">
                    <span className="text-xl">🧠</span>
                    <span>AI Model</span>
                  </h3>
                  <div className="bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl p-4 border border-blue-200/50">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-bold text-gray-800">GPT-4 Turbo</p>
                        <p className="text-sm text-gray-600">Advanced reasoning and creativity</p>
                      </div>
                      <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                        ACTIVE ✨
                      </div>
                    </div>
                  </div>
                </div>

                {/* Theme Settings */}
                <div className="space-y-3">
                  <h3 className="text-lg font-bold text-gray-800 flex items-center space-x-2">
                    <span className="text-xl">🎨</span>
                    <span>Theme</span>
                  </h3>
                  <div className="bg-gradient-to-r from-pink-100 to-purple-100 rounded-xl p-4 border border-pink-200/50">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-bold text-gray-800">Vibrant Mode</p>
                        <p className="text-sm text-gray-600">Colorful and impactful design</p>
                      </div>
                      <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                        ENABLED 🚀
                      </div>
                    </div>
                  </div>
                </div>

                {/* Data Sources Summary */}
                <div className="space-y-3">
                  <h3 className="text-lg font-bold text-gray-800 flex items-center space-x-2">
                    <span className="text-xl">📊</span>
                    <span>Data Sources</span>
                  </h3>
                  <div className="space-y-2">
                    {availableSources.map((source) => {
                      const isEnabled = enabledSources.includes(source);
                      const sourceLabels = { 
                        uploaded_files: '📁 Files', 
                        google_drive: '🟡 Google Drive', 
                        microsoft_teams: '🟣 Teams',
                        emails: '📧 Emails'
                      };
                      return (
                        <div key={source} className="flex items-center justify-between p-3 bg-white/80 rounded-lg border border-gray-200/50">
                          <span className="font-medium text-gray-700">
                            {sourceLabels[source as keyof typeof sourceLabels] || source}
                          </span>
                          <div className={cn(
                            "px-2 py-1 rounded-full text-xs font-bold",
                            isEnabled ? "bg-green-100 text-green-700" : "bg-gray-100 text-gray-500"
                          )}>
                            {isEnabled ? "ACTIVE" : "DISABLED"}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="mt-8 pt-6 border-t border-purple-200/50">
                <button
                  onClick={() => setShowSettings(false)}
                  className="w-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 hover:from-purple-600 hover:via-pink-600 hover:to-blue-600 text-white py-3 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  ✨ Close Settings ✨
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 