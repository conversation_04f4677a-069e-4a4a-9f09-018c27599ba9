# 🗄️ Modular Storage System

This directory contains the new modular storage system that replaces the monolithic `storage.ts` file. The system is designed for better maintainability, team collaboration, and feature isolation.

## 📁 Directory Structure

```
server/storage/
├── base/
│   ├── storage.interface.ts    # Base storage interface and abstract class
│   └── index.ts               # Base exports
├── features/
│   ├── user.storage.ts        # User management operations
│   ├── integration.storage.ts # Integration management operations
│   ├── file.storage.ts        # File management operations
│   ├── chat.storage.ts        # Chat sessions and messages
│   ├── rag.storage.ts         # RAG and vector search operations
│   ├── sync.storage.ts        # Sync logs and items
│   ├── project.storage.ts     # Project management operations
│   └── index.ts              # Feature exports
├── storage.facade.ts          # Facade for backward compatibility
├── index.ts                   # Main storage exports
└── README.md                  # This documentation
```

## 🎯 Key Benefits

### **1. Modular Architecture**
- **Single Responsibility**: Each storage class handles one domain
- **Feature Isolation**: Teams can work on different features independently
- **Reduced Conflicts**: Smaller files mean fewer merge conflicts

### **2. Backward Compatibility**
- **Facade Pattern**: `StorageFacade` maintains the original `IStorage` interface
- **Drop-in Replacement**: Existing code continues to work without changes
- **Gradual Migration**: Teams can migrate to direct module usage over time

### **3. Better Testing**
- **Unit Testing**: Each storage class can be tested independently
- **Mocking**: Easy to mock individual storage modules
- **Isolation**: Test failures are isolated to specific domains

### **4. Improved Maintainability**
- **Clear Boundaries**: Each file has a clear purpose and scope
- **Easy Navigation**: Developers can quickly find relevant code
- **Consistent Patterns**: All storage classes follow the same structure

## 🚀 Usage Examples

### **Using the Facade (Recommended for existing code)**
```typescript
import { storage } from './storage';

// All existing code continues to work
const integrations = await storage.getIntegrations();
const files = await storage.getFiles('google_drive');
```

### **Using Individual Modules (Recommended for new code)**
```typescript
import { IntegrationStorage, FileStorage } from './storage/features';

const integrationStorage = new IntegrationStorage();
const fileStorage = new FileStorage();

const integrations = await integrationStorage.getIntegrations();
const files = await fileStorage.getFiles('google_drive');
```

### **Using Specific Storage Types**
```typescript
// Database-only storage
const dbIntegrationStorage = new IntegrationStorage(true);

// Memory-only storage (for testing)
const memoryIntegrationStorage = new IntegrationStorage(false);
```

## 🏗️ Architecture Patterns

### **1. Interface Segregation**
Each storage class implements only the methods relevant to its domain:
- `UserStorage`: User management
- `IntegrationStorage`: Integration management
- `FileStorage`: File operations
- `ChatStorage`: Chat sessions and messages
- `RAGStorage`: Vector search and chunks
- `SyncStorage`: Sync logs and items
- `ProjectStorage`: Project management

### **2. Dependency Injection**
Storage classes accept a `useDatabase` parameter to control storage backend:
```typescript
// Use database if available, fallback to memory
const storage = new FileStorage(true);

// Force memory storage (useful for testing)
const testStorage = new FileStorage(false);
```

### **3. Facade Pattern**
`StorageFacade` provides a unified interface while delegating to specialized storage classes:
```typescript
class StorageFacade implements IStorage {
  private fileStorage: FileStorage;
  private chatStorage: ChatStorage;
  // ... other storage modules

  async getFiles(...args) {
    return this.fileStorage.getFiles(...args);
  }
}
```

## 🔧 Configuration

### **Database Mode**
When `DATABASE_URL` is available:
- Uses PostgreSQL for persistent storage
- Enables vector similarity search for RAG
- Supports full ACID transactions

### **Memory Mode**
When `DATABASE_URL` is not available:
- Uses in-memory Maps for storage
- Suitable for development and testing
- Data is lost on server restart

## 🧪 Testing

### **Unit Testing Individual Modules**
```typescript
import { FileStorage } from './storage/features';

describe('FileStorage', () => {
  let storage: FileStorage;

  beforeEach(() => {
    storage = new FileStorage(false); // Use memory for testing
  });

  it('should create and retrieve files', async () => {
    const file = await storage.createFile({
      externalId: 'test-123',
      fileName: 'test.txt',
      platform: 'test',
      fileType: 'text',
    });

    const retrieved = await storage.getFile(file.id);
    expect(retrieved).toEqual(file);
  });
});
```

### **Integration Testing with Facade**
```typescript
import { StorageFacade } from './storage';

describe('StorageFacade', () => {
  let storage: StorageFacade;

  beforeEach(() => {
    storage = new StorageFacade(false); // Use memory for testing
  });

  it('should maintain backward compatibility', async () => {
    // Test that all original IStorage methods work
    const integrations = await storage.getIntegrations();
    expect(Array.isArray(integrations)).toBe(true);
  });
});
```

## 🔄 Migration Guide

### **Phase 1: Backward Compatibility (Current)**
- ✅ All existing code continues to work
- ✅ `storage` export uses `StorageFacade`
- ✅ Original `storage.ts` kept for reference

### **Phase 2: Gradual Migration (Recommended)**
```typescript
// Old way (still works)
import { storage } from './storage';

// New way (recommended for new code)
import { FileStorage, ChatStorage } from './storage/features';
```

### **Phase 3: Direct Module Usage (Future)**
```typescript
// Eventually, teams can use modules directly
import { IntegrationStorage } from './storage/features/integration.storage';

class IntegrationService {
  constructor(private storage = new IntegrationStorage()) {}
}
```

## 📊 Performance Considerations

### **Memory Usage**
- Each storage class maintains its own connection/state
- Facade pattern adds minimal overhead
- Memory storage uses efficient Map structures

### **Database Connections**
- Each storage class reuses the same database connection
- Connection pooling handled by the database client
- No connection leaks or excessive connections

### **Query Optimization**
- Each storage class optimizes queries for its domain
- Vector search operations isolated to `RAGStorage`
- Bulk operations available where appropriate

## 🛠️ Development Guidelines

### **Adding New Storage Operations**
1. Identify the appropriate storage class
2. Add the method to the relevant class
3. Update the facade if needed for backward compatibility
4. Add tests for the new functionality

### **Creating New Storage Modules**
1. Create a new file in `features/`
2. Follow the existing pattern and naming conventions
3. Export from `features/index.ts`
4. Update facade if the operations should be part of the main interface

### **Best Practices**
- Keep storage classes focused on their domain
- Use consistent error handling patterns
- Validate inputs at the storage layer
- Provide both database and memory implementations
- Write comprehensive tests for all operations

## 🔍 Debugging

### **Storage Information**
```typescript
// Get information about all storage modules
const info = storage.getStorageInfo();
console.log(info);
```

### **Individual Module Information**
```typescript
const fileStorage = new FileStorage();
const info = fileStorage.getStorageInfo();
console.log('File storage:', info);
```

This modular storage system provides a solid foundation for the application's data layer while maintaining backward compatibility and enabling future growth.
