-- =============================================================================
-- MeetSync PostgreSQL Initialization Script
-- =============================================================================

-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Enable uuid extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pg_trgm extension for fuzzy text search
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Enable unaccent extension for text normalization
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Create custom indexes and functions for better performance
-- This will be executed after Drizzle migrations

-- Function to calculate cosine similarity (for vector search)
CREATE OR REPLACE FUNCTION cosine_similarity(a vector, b vector)
RETURNS float AS $$
BEGIN
    RETURN 1 - (a <=> b);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to normalize text for search
CREATE OR REPLACE FUNCTION normalize_text(text_input text)
RETURNS text AS $$
BEGIN
    RETURN lower(unaccent(trim(text_input)));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'MeetSync PostgreSQL initialization completed';
    RAISE NOTICE 'Extensions enabled: vector, uuid-ossp, pg_trgm, unaccent';
    RAISE NOTICE 'Custom functions created: cosine_similarity, normalize_text';
END $$; 