import { pgTable, text, serial, integer, timestamp, jsonb, varchar } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";

// Chat sessions table schema
export const chatSessions = pgTable("chat_sessions", {
  id: varchar("id", { length: 255 }).primaryKey(),
  userId: varchar("user_id", { length: 255 }),
  title: varchar("title", { length: 500 }),
  enabledSources: jsonb("enabled_sources"), // Array of integration IDs or platform names
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Chat messages table schema
export const chatMessages = pgTable("chat_messages", {
  id: serial("id").primaryKey(),
  sessionId: varchar("session_id", { length: 255 }).notNull().references(() => chatSessions.id, { onDelete: "cascade" }),
  role: varchar("role", { length: 20 }).notNull(), // 'user' | 'assistant'
  content: text("content").notNull(),
  sourcesUsed: jsonb("sources_used"), // Which integrations/files were used for this response
  relevantChunks: jsonb("relevant_chunks"), // IDs of file chunks used for context
  tokenCount: integer("token_count"),
  model: varchar("model", { length: 100 }).default("gpt-4.1-nano"), // Track which model was used
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Insert schemas
export const insertChatSessionSchema = createInsertSchema(chatSessions).omit({
  createdAt: true,
  updatedAt: true,
});

export const insertChatMessageSchema = createInsertSchema(chatMessages).omit({
  id: true,
  createdAt: true,
});

// Export types
export type ChatSession = typeof chatSessions.$inferSelect;
export type InsertChatSession = typeof insertChatSessionSchema._type;

export type ChatMessage = typeof chatMessages.$inferSelect;
export type InsertChatMessage = typeof insertChatMessageSchema._type;
