import json
from notion_client import Client
from metadata_utils import extract_metadata
from llm_utils import get_meeting_name, get_attendees_csv
from google_drive_utils import extract_summary_paragraph

def get_database_structure(notion, database_id):
    db = notion.databases.retrieve(database_id)
    return db['properties']

def upload_to_notion(title, transcript_text, notes_text, NOTION_TOKEN, DATABASE_ID, notes_structural_elements=None):
    notion = Client(auth=NOTION_TOKEN)
    print(f"\nContent lengths:")
    print(f"Title ({len(title)} chars):", title[:100])
    print(f"Transcript ({len(transcript_text)} chars):", transcript_text[:100])
    print(f"Notes ({len(notes_text)} chars):", notes_text[:100])

    # Extract metadata from filename (for date/time only)
    metadata = extract_metadata(title)

    # Extract summary paragraph from Notes tab structural elements
    summary_para = ''
    if notes_structural_elements:
        summary_para = extract_summary_paragraph(notes_structural_elements)
    if not summary_para:
        summary_para = notes_text[:300]  # fallback: first 300 chars

    # Use LLM to get meeting name and attendees
    try:
        meeting_name = get_meeting_name(summary_para)
        attendees = get_attendees_csv(summary_para)
    except Exception as e:
        print(f"LLM extraction failed: {e}")
        meeting_name = metadata['meeting_name']
        attendees = 'Not specified'

    # Create the main page with metadata and initial content
    main_blocks = [
        {
            'object': 'block',
            'type': 'heading_1',
            'heading_1': {
                'rich_text': [{'type': 'text', 'text': {'content': meeting_name}}]
            }
        },
        {
            'object': 'block',
            'type': 'paragraph',
            'paragraph': {
                'rich_text': [
                    {'type': 'text', 'text': {'content': '📅 ' + metadata['date']}},
                    {'type': 'text', 'text': {'content': ' | '}},
                    {'type': 'text', 'text': {'content': '🕒 ' + metadata['time']}},
                    {'type': 'text', 'text': {'content': ' | '}},
                    {'type': 'text', 'text': {'content': '👥 ' + attendees}}
                ]
            }
        },
        {
            'object': 'block',
            'type': 'divider',
            'divider': {}
        }
    ]

    # Create the main page first
    properties = {
        'Name': {
            'title': [{'type': 'text', 'text': {'content': meeting_name}}]
        },
        'Date': {
            'date': {
                'start': metadata['date_iso']
            }
        },
        'Time': {
            'rich_text': [{'type': 'text', 'text': {'content': metadata['time']}}]
        },
        'Attendees': {
            'rich_text': [{'type': 'text', 'text': {'content': attendees}}]
        }
    }

    print("\nCreating main Notion page...")
    main_page = notion.pages.create(
        parent={'database_id': DATABASE_ID},
        properties=properties,
        children=main_blocks
    )
    print(f"✅ Created main page: {meeting_name}")

    # Create Notes pages
    notes_chunks = [notes_text[i:i+2000] for i in range(0, len(notes_text), 2000)]
    notes_pages = []
    
    # Calculate how many pages we need (90 blocks per page to be safe)
    num_notes_pages = (len(notes_chunks) + 89) // 90
    
    for page_num in range(num_notes_pages):
        start_idx = page_num * 90
        end_idx = min((page_num + 1) * 90, len(notes_chunks))
        
        notes_blocks = [
            {
                'object': 'block',
                'type': 'heading_1',
                'heading_1': {
                    'rich_text': [{'type': 'text', 'text': {'content': f"Notes {page_num + 1}/{num_notes_pages}"}}]
                }
            }
        ]
        
        # Add chunks for this page
        for chunk in notes_chunks[start_idx:end_idx]:
            notes_blocks.append({
                'object': 'block',
                'type': 'paragraph',
                'paragraph': {
                    'rich_text': [{'type': 'text', 'text': {'content': chunk}}]
                }
            })
        
        # Create Notes page
        notes_page = notion.pages.create(
            parent={'page_id': main_page['id']},
            properties={
                'title': [{'type': 'text', 'text': {'content': f"Notes {page_num + 1}/{num_notes_pages}"}}]
            },
            children=notes_blocks
        )
        notes_pages.append(notes_page)
        print(f"✅ Created Notes page {page_num + 1}/{num_notes_pages}")

    # Create Transcript pages
    transcript_chunks = [transcript_text[i:i+2000] for i in range(0, len(transcript_text), 2000)]
    transcript_pages = []
    
    # Calculate how many pages we need (90 blocks per page to be safe)
    num_transcript_pages = (len(transcript_chunks) + 89) // 90
    
    for page_num in range(num_transcript_pages):
        start_idx = page_num * 90
        end_idx = min((page_num + 1) * 90, len(transcript_chunks))
        
        transcript_blocks = [
            {
                'object': 'block',
                'type': 'heading_1',
                'heading_1': {
                    'rich_text': [{'type': 'text', 'text': {'content': f"Transcript {page_num + 1}/{num_transcript_pages}"}}]
                }
            }
        ]
        
        # Add chunks for this page
        for chunk in transcript_chunks[start_idx:end_idx]:
            transcript_blocks.append({
                'object': 'block',
                'type': 'paragraph',
                'paragraph': {
                    'rich_text': [{'type': 'text', 'text': {'content': chunk}}]
                }
            })
        
        # Create Transcript page
        transcript_page = notion.pages.create(
            parent={'page_id': main_page['id']},
            properties={
                'title': [{'type': 'text', 'text': {'content': f"Transcript {page_num + 1}/{num_transcript_pages}"}}]
            },
            children=transcript_blocks
        )
        transcript_pages.append(transcript_page)
        print(f"✅ Created Transcript page {page_num + 1}/{num_transcript_pages}")

    return main_page 

def get_source_folders_from_notion(notion_token, database_id):
    """
    Fetch all rows from the Notion database and return a list of dicts with 'name' and 'folder_id'.
    Skips rows missing either property.
    """
    notion = Client(auth=notion_token)
    results = []
    next_cursor = None
    while True:
        query = notion.databases.query(
            database_id=database_id,
            start_cursor=next_cursor
        )
        for row in query['results']:
            props = row.get('properties', {})
            name_prop = props.get('Name', {})
            folder_id_prop = props.get('Folder ID', {})
            name = name_prop.get('title', [])
            folder_id = folder_id_prop.get('rich_text', [])
            if name and folder_id:
                results.append({
                    'name': name[0]['plain_text'],
                    'folder_id': folder_id[0]['plain_text']
                })
        if not query.get('has_more'):
            break
        next_cursor = query.get('next_cursor')
    return results 