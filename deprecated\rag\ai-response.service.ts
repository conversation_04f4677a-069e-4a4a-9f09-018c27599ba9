import OpenAI from "openai";
import { BaseService } from "../base/service.interface";
import { functionToolsService } from "../function-tools/";

/**
 * RAG AI Response Service - handles AI response generation with OpenAI
 */
export class RAGAIResponseService extends BaseService {
  private openai!: OpenAI;

  constructor() {
    super('RAGAIResponse', '1.0.0', 'AI response generation with OpenAI and function calling');
  }

  protected async onInitialize(): Promise<void> {
    this.validateEnvironment(['OPENAI_API_KEY']);
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.log('info', 'RAG AI Response service initialized');
  }

  protected getDependencies(): string[] {
    return ['function-tools'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    try {
      // Test OpenAI connectivity with a simple request
      const models = await this.openai.models.list();
      return {
        openaiConnected: true,
        hasApiKey: !!process.env.OPENAI_API_KEY,
        modelCount: models.data.length,
        defaultModel: this.getDefaultModel(),
      };
    } catch (error) {
      return {
        openaiConnected: false,
        hasApiKey: !!process.env.OPENAI_API_KEY,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Generate AI response using ChatGPT with context and agentic actions
   * Inspired by LlamaIndex tutorial Part 3 - supports function calling
   */
  async generateAIResponse(
    userMessage: string,
    context: string,
    chatHistory: any[],
    enabledSources: string[],
    relevantChunks: any[]
  ): Promise<string> {
    this.ensureInitialized();
    this.validateString(userMessage, 'user message');

    try {
      this.log('info', `Generating AI response for: "${userMessage.substring(0, 100)}..."`);
      this.log('info', `Context length: ${context.length} characters`);
      this.log('info', `Chat history length: ${chatHistory.length} messages`);

      // Build conversation messages
      const messages = this.buildConversationMessages(
        userMessage,
        context,
        chatHistory,
        enabledSources,
        relevantChunks
      );

      this.log('info', `Calling OpenAI with model: ${this.getDefaultModel()}`);
      this.log('info', `Message count: ${messages.length}`);

      // Get available function tools
      const tools = functionToolsService.getToolsForOpenAI();
      this.log('info', `Available function tools: ${functionToolsService.getToolNames().join(', ')}`);

      const response = await this.openai.chat.completions.create({
        model: this.getDefaultModel(),
        messages,
        max_tokens: this.getMaxTokens(),
        temperature: this.getTemperature(),
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: "auto", // Let the model decide when to use tools
      });

      this.log('info', 'OpenAI response received');

      const message = response.choices[0].message;

      // Handle function calls if present
      if (message.tool_calls && message.tool_calls.length > 0) {
        return await this.handleFunctionCalls(message);
      }

      const responseContent = message.content || "I apologize, but I couldn't generate a response.";
      this.log('info', `Response length: ${responseContent.length} characters`);

      return responseContent;

    } catch (error: any) {
      this.handleError(error, 'generating AI response');
    }
  }

  /**
   * Generate streaming AI response - LobeChat style
   */
  async generateStreamingAIResponse(
    userMessage: string,
    context: string,
    chatHistory: any[],
    enabledSources: string[],
    relevantChunks: any[],
    onChunk: (chunk: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    this.ensureInitialized();
    this.validateString(userMessage, 'user message');

    try {
      this.log('info', `Starting streaming AI response for: "${userMessage.substring(0, 100)}..."`);

      // Build conversation messages
      const messages = this.buildConversationMessages(
        userMessage,
        context,
        chatHistory,
        enabledSources,
        relevantChunks
      );

      // Get available function tools
      const tools = functionToolsService.getToolsForOpenAI();

      const stream = await this.openai.chat.completions.create({
        model: this.getDefaultModel(),
        messages,
        max_tokens: this.getMaxTokens(),
        temperature: this.getTemperature(),
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: "auto",
        stream: true, // Enable streaming
      });

      let fullResponse = '';
      let functionCalls: any[] = [];

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;

        if (delta?.content) {
          const content = delta.content;
          fullResponse += content;
          onChunk(content);
        }

        // Handle function calls in streaming
        if (delta?.tool_calls) {
          functionCalls.push(...delta.tool_calls);
        }
      }

      // Handle function calls after streaming completes
      if (functionCalls.length > 0) {
        const functionResults = await this.handleStreamingFunctionCalls(functionCalls, onChunk);
        fullResponse += functionResults;
      }

      onComplete(fullResponse);
      this.log('info', `Streaming response completed: ${fullResponse.length} characters`);

    } catch (error: any) {
      this.log('error', 'Error in streaming AI response', error);
      onError(error);
    }
  }

  /**
   * Handle function calls during streaming
   */
  private async handleStreamingFunctionCalls(functionCalls: any[], onChunk: (chunk: string) => void): Promise<string> {
    let functionResults = "";

    // Group function calls by function name and merge arguments
    const mergedCalls = new Map();
    
    for (const call of functionCalls) {
      if (call.function) {
        const key = call.id || call.function.name;
        if (!mergedCalls.has(key)) {
          mergedCalls.set(key, {
            id: call.id,
            function: { name: call.function.name, arguments: '' }
          });
        }
        mergedCalls.get(key).function.arguments += call.function.arguments || '';
      }
    }

    // Execute each function call
    for (const [, toolCall] of Array.from(mergedCalls.entries())) {
      if (toolCall.function) {
        const functionName = toolCall.function.name;
        
        try {
          const functionArgs = JSON.parse(toolCall.function.arguments);
          
          onChunk(`\n\n**Executing Function: ${functionName}**\n`);
          this.log('info', `Executing streaming function: ${functionName}`);

          const result = await functionToolsService.executeTool(functionName, functionArgs);
          const resultText = `**Function Result (${functionName}):**\n${result}\n`;
          
          functionResults += `\n\n${resultText}`;
          onChunk(resultText);
          
        } catch (error: any) {
          this.log('error', `Streaming function execution error: ${error.message}`);
          const errorText = `**Function Error (${functionName}):**\n${error.message}\n`;
          functionResults += `\n\n${errorText}`;
          onChunk(errorText);
        }
      }
    }

    return functionResults;
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    userMessage: string,
    context: string,
    chatHistory: any[],
    enabledSources: string[],
    relevantChunks: any[]
  ): any[] {
    const baseSystemPrompt = this.getSystemPrompt();

    const messages: any[] = [
      {
        role: "system",
        content: this.buildSystemPrompt(baseSystemPrompt, context, enabledSources, relevantChunks),
      },
    ];

    // Add recent chat history (excluding system messages)
    const recentHistory = chatHistory
      .filter(msg => msg.role !== "system")
      .slice(-8) // Last 8 messages for context
      .map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

    messages.push(...recentHistory);

    // Add current user message
    messages.push({
      role: "user",
      content: userMessage,
    });

    return messages;
  }

  /**
   * Build comprehensive system prompt
   */
  private buildSystemPrompt(
    basePrompt: string,
    context: string,
    enabledSources: string[],
    relevantChunks: any[]
  ): string {
    return `${basePrompt}

IMPORTANT IDENTITY INFORMATION:
- Your name is GPT Unify - an AI Assistant with both READ and WRITE capabilities
      - You have access to data from multiple integrated platforms through the GPT Unify platform
      - You can search through meeting transcripts, documents, and files from connected sources
      - You are part of the GPT Unify ecosystem that unifies and analyzes data across platforms
- You can access Google Drive files, meeting transcripts, and other connected data sources
- You can CREATE, SEARCH, and MANAGE files using your function tools

AGENTIC CAPABILITIES:
- You can create new files when users request it
- You can search for existing files by name, content, or description
- You can get detailed information about specific files
- You always follow the Human-in-the-Loop (HITL) pattern: Draft → Confirm → Execute
- For any write action, you MUST first create a draft and get user confirmation
- You have access to function tools for file operations

CURRENT SESSION CONTEXT:
- You have access to ${enabledSources.length} data source(s): ${enabledSources.join(", ")}
- Found ${relevantChunks.length} relevant document chunks for this query
- Available sources include Google Drive files, Microsoft Teams documents, uploaded files, and comprehensive document collections

${context ? `RELEVANT DOCUMENT CONTENT:

${context}

Use this information to provide accurate, helpful responses. Always reference which documents or sources you're using when providing information.` : `No specific document content found for this query. You can still help by explaining what data sources are available and how to access them.`}

RESPONSE GUIDELINES:
- NEVER say "give me a moment" or "let me search" - you already have the search results
- Provide direct, immediate answers based on available content
- When referencing information, mention the source file name and location when possible
- If asked about file locations, provide specific folder paths and file names from the metadata
- If you find relevant content, answer immediately with specific details
- If no relevant content is found, clearly explain what sources you searched and suggest alternatives
- Be conversational and helpful, not robotic
- Always remember you have real access to the user's files and documents
- When users describe files they're looking for, help identify them by name and location
- For file search queries, provide file names, locations, and brief descriptions of content
- If users ask "what files do you have access to", list the available sources and explain the types of files
- Always maintain context about what data sources are enabled and available`;
  }

  /**
   * Handle function calls from OpenAI
   */
  private async handleFunctionCalls(message: any): Promise<string> {
    this.log('info', `Function calls detected: ${message.tool_calls.length}`);

    let functionResults = "";

    for (const toolCall of message.tool_calls) {
      if (toolCall.type === "function") {
        const functionName = toolCall.function.name;
        const functionArgs = JSON.parse(toolCall.function.arguments);

        this.log('info', `Executing function: ${functionName}`);

        try {
          const result = await functionToolsService.executeTool(functionName, functionArgs);
          functionResults += `\n\n**Function Result (${functionName}):**\n${result}\n`;
          
        } catch (error: any) {
          this.log('error', `Function execution error: ${error.message}`);
          functionResults += `\n\n**Function Error (${functionName}):**\n${error.message}\n`;
        }
      }
    }

    return functionResults;
  }

  /**
   * Get default OpenAI model
   */
  private getDefaultModel(): string {
    return process.env.OPENAI_MODEL || "gpt-4-1106-preview";
  }

  /**
   * Get max tokens for response
   */
  private getMaxTokens(): number {
    return parseInt(process.env.OPENAI_MAX_TOKENS || "2000");
  }

  /**
   * Get temperature for response generation
   */
  private getTemperature(): number {
    return parseFloat(process.env.OPENAI_TEMPERATURE || "0.1");
  }

  /**
   * Get system prompt
   */
  private getSystemPrompt(): string {
    return `You are GPT Unify, a specialized AI assistant integrated with multiple platforms to help users access, search, and manage their data across Google Drive, Microsoft Teams, meeting transcripts, and uploaded documents.

YOUR CORE CAPABILITIES:
1. **Multi-Platform Data Access**: You can search and retrieve information from connected Google Drive files, Microsoft Teams documents, meeting transcripts, and uploaded files
2. **Intelligent Search**: You understand context and can find relevant information across different document types and sources
3. **Agentic Actions**: You have tools to create, search, and manage files when requested by users
4. **Real-Time Integration**: You work with live data from integrated platforms

YOUR OPERATIONAL GUIDELINES:
- Be direct and helpful - you already have access to search results and data
- Always reference specific sources when providing information
- Explain what data sources you're searching when users ask about availability
- For file operations, follow the Human-in-the-Loop pattern: Draft → Confirm → Execute
- Maintain awareness of enabled data sources and available content
- Provide actionable insights based on the user's integrated data

Remember: You're not just answering questions - you're helping users leverage their integrated data ecosystem effectively.`;
  }

  /**
   * Handle service errors with context
   */
  protected handleError(error: any, context: string): never {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const fullMessage = `Error ${context}: ${errorMessage}`;
    
    this.log('error', fullMessage, error);
    throw new Error(fullMessage);
  }

  /**
   * Estimate token count for a given text
   */
  estimateTokenCount(text: string): number {
    // Rough estimation: ~4 characters per token for English text
    // This is a simple approximation - for more accuracy, use tiktoken
    return Math.ceil(text.length / 4);
  }

  /**
   * Test OpenAI connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.openai.models.list();
      return true;
    } catch (error) {
      this.log('error', 'OpenAI connection test failed', error);
      return false;
    }
  }

  /**
   * Get available OpenAI models
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const models = await this.openai.models.list();
      return models.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      this.log('error', 'Failed to fetch available models', error);
      return [this.getDefaultModel()];
    }
  }
}
