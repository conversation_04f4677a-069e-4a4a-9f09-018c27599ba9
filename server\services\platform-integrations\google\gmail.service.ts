import { google, gmail_v1 } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { BaseService } from "../../base/service.interface";
import { EmailStorage } from "../../../storage/features/email.storage.js";
import { storage } from "../../../storage/index.js";

/**
 * Gmail Service - handles email fetching and processing from Gmail API
 */
export class GmailService extends BaseService {
  private emailStorage: EmailStorage;

  constructor() {
    super('Gmail', '1.0.0', 'Gmail email fetching and processing');
    this.emailStorage = new EmailStorage();
  }

  /**
   * Get Gmail API client
   */
  private getGmailClient(auth: OAuth2Client): gmail_v1.Gmail {
    return google.gmail({ version: 'v1', auth });
  }

  /**
   * Fetch all emails from Gmail
   */
  async fetchAllEmails(auth: OAuth2Client, userId: string, organizationId: string): Promise<void> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(userId, 'user ID');

    try {
      const gmail = this.getGmailClient(auth);
      
      this.log('info', 'Starting Gmail email fetch process');

      // Get list of all messages
      let pageToken: string | undefined;
      let totalProcessed = 0;
      const maxResults = 100; // Process emails in batches

      do {
        const listResponse = await gmail.users.messages.list({
          userId: 'me',
          maxResults,
          pageToken,
          q: '-in:chat', // Exclude chat messages, only get emails
        });

        const messages = listResponse.data.messages || [];
        
        if (messages.length === 0) {
          this.log('info', 'No more messages to process');
          break;
        }

        this.log('info', `Processing batch of ${messages.length} emails`);

        // Process emails in parallel batches
        const batchSize = 10;
        for (let i = 0; i < messages.length; i += batchSize) {
          const batch = messages.slice(i, i + batchSize);
          
          await Promise.all(
            batch.map(async (message) => {
              try {
                await this.processGmailMessage(gmail, message.id!, userId, organizationId);
                totalProcessed++;
              } catch (error) {
                this.log('error', `Error processing message ${message.id}`, error);
              }
            })
          );
        }

        pageToken = listResponse.data.nextPageToken || undefined;
        
        this.log('info', `Processed ${totalProcessed} emails so far`);
        
        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } while (pageToken);

      this.log('info', `Completed Gmail sync. Total emails processed: ${totalProcessed}`);
    } catch (error: any) {
      this.log('error', 'Error fetching Gmail emails', error);
      throw error;
    }
  }

  /**
   * Process a single Gmail message
   */
  private async processGmailMessage(
    gmail: gmail_v1.Gmail, 
    messageId: string, 
    userId: string, 
    organizationId: string
  ): Promise<void> {
    try {
      // Check if email already exists
      const existingEmails = await this.emailStorage.getEmails('gmail', userId);
      const exists = existingEmails.some(email => email.externalId === messageId);
      
      if (exists) {
        this.log('info', `Email ${messageId} already exists, skipping`);
        return;
      }

      // Fetch full message details
      const messageResponse = await gmail.users.messages.get({
        userId: 'me',
        id: messageId,
        format: 'full',
      });

      const message = messageResponse.data;
      
      if (!message.payload) {
        this.log('warn', `Message ${messageId} has no payload, skipping`);
        return;
      }

      // Extract email data
      const emailData = this.extractEmailData(message, userId, organizationId);
      
      if (!emailData) {
        this.log('warn', `Could not extract data from message ${messageId}, skipping`);
        return;
      }

      // Save to database
      await this.emailStorage.createEmail(emailData);
      
      this.log('info', `Successfully processed email: ${emailData.subject}`);
    } catch (error: any) {
      this.log('error', `Error processing Gmail message ${messageId}`, error);
      throw error;
    }
  }

  /**
   * Extract email data from Gmail message
   */
  private extractEmailData(message: gmail_v1.Schema$Message, userId: string, organizationId: string): any {
    try {
      const headers = message.payload?.headers || [];
      const headerMap = new Map(headers.map(h => [h.name?.toLowerCase(), h.value]));

      const subject = headerMap.get('subject') || 'No Subject';
      const from = headerMap.get('from') || 'Unknown Sender';
      const to = headerMap.get('to') || '';
      const cc = headerMap.get('cc') || '';
      const date = headerMap.get('date');
      const messageId = headerMap.get('message-id');
      const inReplyTo = headerMap.get('in-reply-to');
      const references = headerMap.get('references');

      // Extract email content
      if (!message.payload) {
        this.log('warn', `Message ${message.id} has no payload, skipping`);
        return null;
      }
      
      const content = this.extractEmailContent(message.payload);
      
      if (!content || content.trim().length === 0) {
        this.log('warn', `Empty content for message ${message.id}, skipping`);
        return null;
      }

      // Parse recipients
      const recipients = this.parseEmailAddresses(to);
      const ccList = this.parseEmailAddresses(cc);

      // Extract labels
      const labels = message.labelIds || [];
      const emailLabels = this.mapGmailLabels(labels);

      // Determine read status
      const isRead = !labels.includes('UNREAD');
      const isStarred = labels.includes('STARRED');
      const isImportant = labels.includes('IMPORTANT');

      // Parse date
      let receivedAt = new Date();
      if (date) {
        try {
          receivedAt = new Date(date);
        } catch (error) {
          this.log('warn', `Could not parse date: ${date}`);
        }
      }

      return {
        externalId: message.id!,
        platform: 'gmail',
        subject,
        content,
        sender: this.extractEmailAddress(from),
        recipients,
        cc: ccList,
        bcc: [], // Gmail API doesn't provide BCC for privacy
        threadId: message.threadId || null,
        status: 'active',
        metadata: {
          messageId,
          inReplyTo,
          references: references ? references.split(' ') : [],
          originalHeaders: Object.fromEntries(headerMap),
          gmailLabels: labels,
        },
        extractedMetadata: {
          sentiment: null, // Will be computed later if needed
          topics: [], // Will be computed later if needed
          hasAttachments: message.payload ? this.hasAttachments(message.payload) : false,
          wordCount: content.split(/\s+/).length,
        },
        userId,
        organizationId,
        isRead,
        isStarred,
        isArchived: labels.includes('TRASH'),
        labels: emailLabels,
        receivedAt,
      };
    } catch (error: any) {
      this.log('error', 'Error extracting email data', error);
      return null;
    }
  }

  /**
   * Extract email content from message payload
   */
  private extractEmailContent(payload: gmail_v1.Schema$MessagePart): string {
    let content = '';

    // Handle multipart messages
    if (payload.parts && payload.parts.length > 0) {
      for (const part of payload.parts) {
        content += this.extractEmailContent(part);
      }
      return content;
    }

    // Handle single part messages
    if (payload.mimeType === 'text/plain' || payload.mimeType === 'text/html') {
      if (payload.body?.data) {
        try {
          const decoded = Buffer.from(payload.body.data, 'base64url').toString('utf8');
          
          // If it's HTML, strip tags for plain text content
          if (payload.mimeType === 'text/html') {
            content += this.stripHtmlTags(decoded);
          } else {
            content += decoded;
          }
        } catch (error) {
          this.log('warn', 'Error decoding email content', error);
        }
      }
    }

    return content;
  }

  /**
   * Strip HTML tags from content
   */
  private stripHtmlTags(html: string): string {
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\n\s*\n/g, '\n') // Remove extra newlines
      .trim();
  }

  /**
   * Parse email addresses from header string
   */
  private parseEmailAddresses(emailString: string): string[] {
    if (!emailString) return [];
    
    return emailString
      .split(',')
      .map(email => this.extractEmailAddress(email.trim()))
      .filter(email => email.length > 0);
  }

  /**
   * Extract email address from "Name <<EMAIL>>" format
   */
  private extractEmailAddress(emailString: string): string {
    const match = emailString.match(/<(.+)>/);
    return match ? match[1] : emailString.trim();
  }

  /**
   * Map Gmail labels to human-readable labels
   */
  private mapGmailLabels(gmailLabels: string[]): string[] {
    const labelMap: Record<string, string> = {
      'INBOX': 'Inbox',
      'SENT': 'Sent',
      'DRAFT': 'Draft',
      'SPAM': 'Spam',
      'TRASH': 'Trash',
      'STARRED': 'Starred',
      'IMPORTANT': 'Important',
      'UNREAD': 'Unread',
    };

    return gmailLabels
      .map(label => labelMap[label] || label)
      .filter(label => !['UNREAD', 'INBOX'].includes(label)); // Filter out system labels
  }

  /**
   * Check if message has attachments
   */
  private hasAttachments(payload: gmail_v1.Schema$MessagePart): boolean {
    if (payload.parts) {
      return payload.parts.some(part => 
        part.filename && part.filename.length > 0 && 
        part.body?.attachmentId
      );
    }
    return payload.filename ? payload.filename.length > 0 : false;
  }

  /**
   * Sync recent emails (for incremental updates)
   */
  async syncRecentEmails(auth: OAuth2Client, userId: string, organizationId: string, daysBack: number = 7): Promise<void> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(userId, 'user ID');

    try {
      const gmail = this.getGmailClient(auth);
      
      // Calculate date for query
      const date = new Date();
      date.setDate(date.getDate() - daysBack);
      const dateString = date.toISOString().split('T')[0].replace(/-/g, '/');
      
      this.log('info', `Syncing emails from the last ${daysBack} days (since ${dateString})`);

      const listResponse = await gmail.users.messages.list({
        userId: 'me',
        q: `after:${dateString} -in:chat`,
        maxResults: 100,
      });

      const messages = listResponse.data.messages || [];
      
      if (messages.length === 0) {
        this.log('info', 'No recent messages to sync');
        return;
      }

      this.log('info', `Found ${messages.length} recent messages to sync`);

      // Process messages in batches
      const batchSize = 5;
      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        
        await Promise.all(
          batch.map(async (message) => {
            try {
              await this.processGmailMessage(gmail, message.id!, userId, organizationId);
            } catch (error) {
              this.log('error', `Error processing recent message ${message.id}`, error);
            }
          })
        );
      }

      this.log('info', `Completed recent email sync`);
    } catch (error: any) {
      this.log('error', 'Error syncing recent Gmail emails', error);
      throw error;
    }
  }

  /**
   * Get user's Gmail profile information
   */
  async getGmailProfile(auth: OAuth2Client): Promise<any> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');

    try {
      const gmail = this.getGmailClient(auth);
      
      const profileResponse = await gmail.users.getProfile({
        userId: 'me',
      });

      return {
        emailAddress: profileResponse.data.emailAddress,
        messagesTotal: profileResponse.data.messagesTotal,
        threadsTotal: profileResponse.data.threadsTotal,
        historyId: profileResponse.data.historyId,
      };
    } catch (error: any) {
      this.log('error', 'Error getting Gmail profile', error);
      throw error;
    }
  }
}

// Export singleton instance
export const gmailService = new GmailService(); 