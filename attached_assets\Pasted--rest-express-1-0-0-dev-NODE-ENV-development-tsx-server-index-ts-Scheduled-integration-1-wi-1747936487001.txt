
> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Scheduled integration 1 with cron: 0 9 * * * (next run: Fri May 23 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
Re-optimizing dependencies because lockfile has changed
5:48:45 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
5:48:48 PM [express] GET /api/integrations 200 in 77ms :: {"integrations":[{"id":1,"name":"Google Me…
5:48:49 PM [express] GET /api/sync-logs 200 in 383ms :: {"logs":[{"id":30,"integrationId":1,"startTi…
5:49:42 PM [express] GET /api/integrations 200 in 315ms :: {"integrations":[{"id":1,"name":"Google M…
OpenAI service initialized successfully
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
5:53:56 PM [express] POST /api/sync-now 200 in 1107ms :: {"message":"Sync started successfully","syn…
5:53:56 PM [express] GET /api/integrations 200 in 79ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Found 0 files in processed folder
After filtering: 3 files remaining to process
Extracting content from document: Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (ID: 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk)
Successfully extracted content (40694 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Technical Developments Meeting
Created Notion page for "Strategic Initiatives and Technical Developments Meeting": 1fbd68e5-c2ad-8156-936a-ceb203ddf1da
Splitting summary of 341 chars into 1 chunks
Created chunk with 1950 characters (max: 2000)
Created chunk with 1943 characters (max: 2000)
Created chunk with 1880 characters (max: 2000)
Created chunk with 1815 characters (max: 2000)
Created chunk with 1826 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1892 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1876 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1866 characters (max: 2000)
Created chunk with 1871 characters (max: 2000)
Created chunk with 1873 characters (max: 2000)
Created chunk with 1835 characters (max: 2000)
Created chunk with 1791 characters (max: 2000)
Created chunk with 1894 characters (max: 2000)
Created chunk with 1763 characters (max: 2000)
Created chunk with 1935 characters (max: 2000)
Created chunk with 1931 characters (max: 2000)
Created chunk with 928 characters (max: 2000)
Created chunk with 75 characters (max: 2000)
Added content blocks to page 1fbd68e5-c2ad-8156-936a-ceb203ddf1da
Moved file 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Successfully processed Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini and added to Notion database
Moved file 16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Verified and skipped already processed file Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini - exists in Notion
Extracting content from document: Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (ID: 16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ)
Successfully extracted content (24949 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Automation and Integration of Meeting Notes and Recordings
Created Notion page for "Automation and Integration of Meeting Notes and Recordings": 1fbd68e5-c2ad-8157-9e6c-f87f0e496122
Splitting summary of 436 chars into 1 chunks
Created chunk with 1831 characters (max: 2000)
Created chunk with 1892 characters (max: 2000)
Created chunk with 1861 characters (max: 2000)
Created chunk with 1883 characters (max: 2000)
Created chunk with 1946 characters (max: 2000)
Created chunk with 1868 characters (max: 2000)
Created chunk with 1906 characters (max: 2000)
Created chunk with 1903 characters (max: 2000)
Created chunk with 1766 characters (max: 2000)
Created chunk with 1830 characters (max: 2000)
Created chunk with 1946 characters (max: 2000)
Created chunk with 1852 characters (max: 2000)
Created chunk with 1765 characters (max: 2000)
Created chunk with 625 characters (max: 2000)
Created chunk with 75 characters (max: 2000)
Added content blocks to page 1fbd68e5-c2ad-8157-9e6c-f87f0e496122
Moved file 16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Successfully processed Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini and added to Notion database
Sync completed with status: success
Items processed: 3, Success: 2, Failed: 0
