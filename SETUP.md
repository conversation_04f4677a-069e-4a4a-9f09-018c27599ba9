# MeetSync Web App Setup Guide

## 🚀 Quick Start for New Team Members

### Prerequisites
- **Node.js** (v18 or higher)
- **PostgreSQL** (v14 or higher)
- **Git**

### 1. Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd MeetSync-Web-App

# Install dependencies
npm install
```

### 2. Database Setup

#### Install and Start PostgreSQL

**Windows:**
```bash
# Download PostgreSQL from https://www.postgresql.org/download/windows/
# Install with default settings (user: postgres, port: 5432)
# Remember your postgres user password
```

**macOS:**
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Create postgres user if needed
createuser -s postgres
```

**Linux:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Create Database
```bash
# Connect to PostgreSQL
psql -U postgres -h localhost -p 5432

# Create the database
CREATE DATABASE meetsync_db;

# Exit PostgreSQL
\q
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
```

**Required .env Configuration:**
```env
# Database (LOCAL DEVELOPMENT)
DATABASE_URL=postgresql://postgres:YOUR_POSTGRES_PASSWORD@localhost:5432/meetsync_db
POSTGRES_DB=meetsync_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=YOUR_POSTGRES_PASSWORD
POSTGRES_PORT=5432

# Application
NODE_ENV=development
PORT=8080
SERVER_URL=http://localhost:8080

# Security
SESSION_SECRET=your-session-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Google OAuth (get from Google Cloud Console)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Microsoft OAuth (optional)
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_TENANT_ID=your-microsoft-tenant-id
```

### 4. Database Migration

```bash
# Run database migrations to create all tables
npm run migrate

# Verify database setup
npm run db:status
```

### 5. Start Development

```bash
# Start both frontend and backend
npm run dev

# Or start them separately:
npm run dev:server  # Backend on port 8080
npm run dev:client  # Frontend on port 5173
```

### 6. Verify Setup

1. **Backend Health Check:**
   ```bash
   curl http://localhost:8080/health
   ```
   Should return: `{"status":"healthy","checks":{"database":{"status":"healthy"}}}`

2. **Frontend:** Open http://localhost:5173

3. **Test Google OAuth:** Try connecting a Google integration

---

## 🔄 Update Instructions for Existing Team Members

If you're an existing team member who needs to apply recent database changes:

### Step 1: Pull Latest Changes
```bash
git pull origin main
npm install  # Install any new dependencies
```

### Step 2: Database Migration Required

**Important:** We recently migrated from Neon Serverless to local PostgreSQL and added new database constraints.

#### Option A: Fresh Database Setup (Recommended)
```bash
# 1. Drop and recreate database
psql -U postgres -h localhost -p 5432 -c "DROP DATABASE IF EXISTS meetsync_db;"
psql -U postgres -h localhost -p 5432 -c "CREATE DATABASE meetsync_db;"

# 2. Run migrations
npm run migrate

# 3. Restart application
npm run dev
```

#### Option B: Apply Incremental Changes
```bash
# 1. Add missing unique constraint
psql -U postgres -h localhost -p 5432 -d meetsync_db -c "
ALTER TABLE oauth_tokens 
ADD CONSTRAINT oauth_tokens_user_platform_unique 
UNIQUE (user_id, platform);"

# 2. Update database configuration is already done in code
npm run dev
```

### Step 3: Environment File Updates

Update your `.env` file to ensure you're using local PostgreSQL:

```env
# Make sure these are uncommented and Docker sections are commented:
DATABASE_URL=postgresql://postgres:YOUR_PASSWORD@localhost:5432/meetsync_db
POSTGRES_DB=meetsync_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=YOUR_PASSWORD
POSTGRES_PORT=5432

# Comment out Docker settings:
# DATABASE_URL=********************************************/meetsync_dev
```

### Step 4: Verify Everything Works

```bash
# 1. Check health
curl http://localhost:8080/health

# 2. Test Google OAuth (should now work end-to-end)
# 3. Test chat functionality (session creation issues are fixed)
```

---

## 🗃️ Database Schema Overview

### Current Tables
- `integrations` - OAuth integrations (Google, Microsoft, etc.)
- `oauth_tokens` - Encrypted OAuth tokens with unique constraint on (user_id, platform)
- `chat_sessions` - Chat sessions with string IDs
- `chat_messages` - Chat messages linked to sessions
- `files` - Uploaded and integrated files
- `file_chunks` - Vector embeddings for RAG
- `sync_operations` - Platform sync tracking

### Key Changes Made
1. **OAuth Tokens Table:** Added unique constraint on `(user_id, platform)`
2. **Database Driver:** Switched from Neon Serverless to standard PostgreSQL
3. **Session Handling:** Fixed string session ID support
4. **Integration Status:** Fixed OAuth callback to properly set 'connected' status

---

## 🔧 Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
# Windows:
services.msc  # Look for postgresql service

# macOS/Linux:
brew services list | grep postgresql
sudo systemctl status postgresql
```

### OAuth Issues
1. **"User not authenticated"** - Fixed in latest code
2. **"Integration is not connected"** - Fixed in latest code  
3. **Token storage errors** - Fixed with database constraints

### Chat Session Issues
1. **"Session does not exist"** - Fixed with improved session creation logic
2. **String vs Number ID issues** - Fixed to support frontend string IDs

### Port Conflicts
```bash
# Kill processes using ports
npm run kill-server  # Kills port 8080 processes
```

---

## 📚 Additional Resources

### Useful Commands
```bash
# Database operations
npm run migrate        # Run database migrations
npm run db:status      # Check database connection
npm run kill-server    # Kill server processes

# Development
npm run dev           # Start both frontend/backend
npm run dev:server    # Backend only
npm run dev:client    # Frontend only
npm run check         # TypeScript type checking

# Database direct access
psql -U postgres -h localhost -p 5432 -d meetsync_db
```

### Database Management
```sql
-- View all tables
\dt

-- Check oauth_tokens table structure
\d oauth_tokens

-- View integrations
SELECT id, name, platform, status FROM integrations;

-- View sessions
SELECT id, title, "userId", "createdAt" FROM chat_sessions;
```

---

## 🚨 Important Notes

1. **No Docker Required:** We're now using local development setup
2. **PostgreSQL Required:** Local PostgreSQL instance is mandatory
3. **Environment Variables:** Ensure `.env` uses local PostgreSQL settings
4. **Google OAuth:** Works end-to-end after our fixes
5. **Chat Sessions:** Now properly handle frontend-generated session IDs

For any issues, check the troubleshooting section or reach out to the team! 