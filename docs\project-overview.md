# Project Overview

Generated on: 2025-05-26T19:05:00.163Z

## Key Files

### package.json
Size: 3.95 KB

```json
{
  "name": "rest-express",
  "version": "1.0.0",
  "type": "module",
  "license": "MIT",
  "scripts": {
    "dev": "cross-env NODE_ENV=development tsx server/index.ts",
    "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist",
    "start": "NODE_ENV=production node dist/index.js",
    "check": "tsc",
    "db:push": "drizzle-kit push",
    "migrate": "cross-env NODE_ENV=development drizzle-kit push"
  },
  "dependencies": {
    "@hookform/resolvers": "^3.10.0",
    "@jridgewell/trace-mapping": "^0.3.25",
    "@neondatabase/serverless": "^0.10.4",
    "@notionhq/client": "^3.1.1",
    "@radix-ui/react-accordion": "^1.2.4",
    "@radix-ui/react-alert-dialog": "^1.1.7",
    "@radix-ui/react-aspect-ratio": "^1.1.3",
    "@radix-ui/react-avatar": "^1.1.4",
    "@radix-ui/react-checkbox": "^1.1.5",
    "@radix-ui/react-collapsible": "^1.1.4",
    "@radix-ui/react-context-menu": "^2.2.7",
    "@radix-ui/react-dialog": "^1.1.7",
    "@radix-ui/react-dropdown-menu": "^2.1.7",
    "@radix-ui/react-hover-card": "^1.1.7",
    "@radix-ui/react-label": "^2.1.3",
    "@radix-ui/react-menubar": "^1.1.7",
    "@radix-ui/react-navigation-menu": "^1.2.6",
    "@radix-ui/react-popover": "^1.1.7",
    "@radix-ui/react-progress": "^1.1.3",
    "@radix-ui/react-radio-group": "^1.2.4",
    "@radix-ui/react-scroll-area": "^1.2.4",
    "@radix-ui/react-select": "^2.1.7",
    "@radix-ui/react-separator": "^1.1.3",
    "@radix-ui/react-slider": "^1.2.4",
    "@radix-ui/react-slot": "^1.2.0",
    "@radix-ui/react-switch": "^1.1.4",
    "@radix-ui/react-tabs": "^1.1.4",
    "@radix-ui/react-toast": "^1.2.7",
    "@radix-ui/react-toggle": "^1.1.3",
    "@radix-ui/react-toggle-group": "^1.1.3",
    "@radix-ui/react-tooltip": "^1.2.0",
    "@tanstack/react-query": "^5.60.5",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.1.1",
    "connect-pg-simple": "^10.0.0",
    "cron": "^4.3.0",
    "cron-parser": "^5.2.0",
    "date-fns": "^3.6.0",
    "dotenv": "^16.5.0",
    "drizzle-orm": "^0.39.1",
    "drizzle-zod": "^0.7.0",
    "embla-carousel-react": "^8.6.0",
    "express": "^4.21.2",
    "express-session": "^1.18.1",
    "framer-motion": "^11.13.1",
    "google-auth-library": "^9.15.1",
    "googleapis": "^149.0.0",
    "input-otp": "^1.4.2",
    "lucide-react": "^0.453.0",
    "memorystore": "^1.6.7",
    "next-themes": "^0.4.6",
    "node-cron": "^4.0.6",
    "node-schedule": "^2.1.1",
    "openai": "^4.102.0",
    "passport": "^0.7.0",
    "passport-local": "^1.0.0",
    "react": "^18.3.1",
    "react-day-picker": "^8.10.1",
    "react-dom": "^18.3.1",
    "react-hook-form": "^7.55.0",
    "react-icons": "^5.4.0",
    "react-resizable-panels": "^2.1.7",
    "recharts": "^2.15.2",
    "tailwind-merge": "^2.6.0",
    "tailwindcss-animate": "^1.0.7",
    "tw-animate-css": "^1.2.5",
    "vaul": "^1.1.2",
    "wouter": "^3.3.5",
    "ws": "^8.18.0",
    "zod": "^3.24.2",
    "zod-validation-error": "^3.4.0"
  },
  "devDependencies": {
    "@replit/vite-plugin-cartographer": "^0.2.3",
    "@replit/vite-plugin-runtime-error-modal": "^0.0.3",
    "@tailwindcss/typography": "^0.5.15",
    "@tailwindcss/vite": "^4.1.3",
    "@types/connect-pg-simple": "^7.0.3",
    "@types/dotenv": "^6.1.1",
    "@types/express": "4.17.21",
    "@types/express-session": "^1.18.0",
    "@types/node": "20.16.11",
    "@types/passport": "^1.0.16",
    "@types/passport-local": "^1.0.38",
    "@types/react": "^18.3.11",
    "@types/react-dom": "^18.3.1",
    "@types/ws": "^8.5.13",
    "@vitejs/plugin-react": "^4.3.2",
    "autoprefixer": "^10.4.20",
    "cross-env": "^7.0.3",
    "drizzle-kit": "^0.30.6",
    "esbuild": "^0.25.0",
    "postcss": "^8.4.47",
    "tailwindcss": "^3.4.17",
    "tsx": "^4.19.1",
    "typescript": "5.6.3",
    "vite": "^5.4.14"
  },
  "optionalDependencies": {
    "bufferutil": "^4.0.8"
  }
}

```

### README.md
Size: 83.66 KB

```md
# MeetSync-Web-App
Web App that transfers meeting transcripts to a desired repository (Notion, Drive, etc.)

Initial PRD prompt given to replit:
https://chatgpt.com/s/dr_682de4bbac5c81919fda9202f655b0a7

Unified Meeting Transcript Repository (Single-Tenant) - Product Requirements Document
Executive Summary
This document outlines the Product Requirements for the Unified Meeting Transcript Repository, a single-tenant web application designed for IT Administrators. The application consolidates meeting transcripts and chat logs from multiple communication platforms (Google Meet, Google Chat, Microsoft Teams meetings and chats, Zoom, Slack) into a centralized repository. IT admins can configure integrations to these platforms, schedule automatic transcript synchronization, and store the data in a structured format. The repository can either push content to an external knowledge base (like Notion) or maintain a hosted internal repository. The primary audience for this PRD is the engineering team tasked with building the solution, with input from IT administrators as key stakeholders. The purpose of this product is to reduce reliance on complex low-code tools (e.g. Zapier, Power Automate) that organizations currently use to collect and archive communication data. By providing a dedicated solution, we aim to improve reliability, security, and searchability of meeting knowledge while simplifying configuration and maintenance for IT administrators.
Goals & Problems to Solve
Goals:
Centralize Communication Records: Provide a unified archive of meeting transcripts and chat logs across popular platforms. This ensures all valuable discussions and decisions are stored in one place for easy access and auditing.
Streamline IT Admin Workflows: Offer a simple interface for configuring data integrations and schedules. The product replaces custom scripts or multiple automation workflows with one cohesive system.
Enable Knowledge Reuse: By aggregating transcripts, the system allows search across meetings by date, participant, or platform, enabling employees and admins to find historical discussion points or decisions quickly.
Ensure Security & Compliance: Maintain strong encryption and access control so that sensitive conversation data is protected. The solution should support organizational compliance needs (data retention policies, audit trails).
Problems being solved:
Fragmentation of Data: Currently, transcripts and chat histories are scattered across different platforms (Zoom cloud recordings, Slack channels, Teams chat logs, etc.). It's difficult for an IT admin or compliance officer to collect and review all of them without using multiple tools or exporting data manually.
Complex Automation: Many organizations resort to low-code platforms or custom scripts to periodically export and store communications data. These approaches are error-prone, hard to maintain, and may pose security risks (e.g., storing credentials in scripts).
Lack of Unified Search: Without a central repository, searching for a topic discussed in past meetings/chats requires checking each platform individually. This is time-consuming and often impossible if one doesn't know where the conversation happened.
Inconsistent Formats & Storage: Each platform provides data in different formats (JSON via APIs, SRT caption files, text, etc.). Storing and converting these into a human-readable, consistent format is burdensome for IT staff. Our product standardizes this process.
By addressing these issues, the Unified Meeting Transcript Repository will save IT administrators time, reduce the chance of missed information, and improve an organization's ability to retain and leverage institutional knowledge from meetings and communications.
User Roles
IT Administrator (Primary User): The IT Admin is responsible for configuring the application. They set up integrations with each platform (e.g., connecting the company's Zoom account or Slack workspace via OAuth), define synchronization schedules, and manage storage/destinations. They also use the dashboard to monitor system health and use search for compliance or information retrieval. Initially, the IT Admin is the sole user role with full access to all features.
(Future Roles – not in MVP): In future iterations, we might introduce additional roles such as Compliance Officer (with read-only access to search transcripts and audit logs) or Team Lead/User (with limited access to search or view transcripts relevant to them). However, the MVP focuses only on the IT Admin role to reduce complexity.
Functional Requirements
The application will provide the following core functionalities, each described with required features, acceptance criteria, and user story breakdown.
Integrations & Authentication
The system must integrate with external communication platforms to fetch transcripts and chat logs. Each integration uses secure authentication (typically OAuth 2.0) to authorize access on behalf of the organization or user. Key Features:
OAuth 2.0 authentication for Google Workspace (for Google Meet and Google Chat data), Microsoft 365 (for Teams meetings and chats), Zoom, and Slack.
Support storing multiple integration credentials (e.g., an organization may connect both Google and Microsoft accounts if they use both, or multiple Slack workspaces).
All tokens/credentials must be encrypted at rest in the application database.
A plugin architecture for integrations: each platform's data retrieval logic is modular, allowing future addition of new services (e.g., Webex or others) without major overhaul.
The integration should fetch transcripts (for meetings) and chat logs (for group chats or channels) via official APIs. For example, use Zoom's API for meeting transcripts, Slack's API for channel history, Microsoft Graph API for Teams chats and meeting transcripts, and Google Meet transcripts via Google's API (if available, or via stored recordings).
Handle pagination and rate limiting gracefully when pulling large volumes of data from APIs.
Acceptance Criteria:
The IT Admin can add an integration via a wizard. They select the platform, click "Connect", and complete OAuth login. On success, the integration is saved with an encrypted token.
Only valid credentials are accepted. If OAuth fails or a token is revoked, the system notifies the admin and marks that integration as needing attention (e.g., "Disconnected" status).
The system can successfully fetch a sample transcript or message after connecting (e.g., a test retrieval to verify permissions).
Credentials (OAuth tokens, refresh tokens, API keys if any) are stored using AES-256 encryption in the database. No sensitive token data is logged or exposed in the UI.
New integration modules can be added without impacting existing ones (e.g., adding a Webex integration in future should not require changes to core code, just plugging in a new module).
User Stories & Effort Estimates:
Story: Connect to Google Meet & Chat via OAuth – As an IT Admin, I want to connect the application to Google Workspace to pull Google Meet transcripts and Google Chat messages (from spaces or group chats), so that these are archived in the repository. (Size: M)
AC: Given valid Google OAuth credentials with appropriate scopes, the system obtains an access token and can list recent meetings and chat messages. If the token expires, the system can refresh it using a refresh token without user intervention.
Story: Connect to Microsoft Teams via OAuth – As an IT Admin, I want to authorize Microsoft 365 (Azure AD) so the app can collect Teams meeting transcripts and chat logs. (Size: M)
AC: The system uses Microsoft's OAuth to get Graph API access. It can retrieve Teams channel messages and any available meeting transcripts (assuming they're saved in MS Stream or OneDrive via Graph API). Proper permissions (Graph API scopes) are documented and required.
Story: Connect to Zoom via OAuth – Allow the admin to connect a Zoom account (or enterprise Zoom) to fetch meeting transcripts and associated chat logs. (Size: M)
AC: After OAuth, the system can call Zoom APIs to get past meeting records (transcripts if available, or recording files that include transcripts). It handles Zoom's rate limits and stores necessary Zoom identifiers (meeting IDs, etc.) for incremental sync.
Story: Connect to Slack via OAuth – Integrate with Slack to pull channel chat logs (and possibly Slack call transcripts if available). (Size: M)
AC: Using Slack OAuth (installing a Slack App with appropriate scopes), the system retrieves messages from selected channels or all channels (if using an admin token). It respects Slack's pagination (batching through history) and rate limits.
Story: Encrypted Credential Storage – The system encrypts and securely stores all integration tokens/credentials in the database. (Size: S)
AC: Tokens are encrypted with AES-256 (using a server-held key or secrets manager). Direct database access does not reveal plaintext tokens. Only the application (server) can decrypt them at runtime for API calls.
Story: Integration Plugin Framework – Structure the integration code as plugins. Each new platform integration implements a standard interface (e.g., methods for authentication setup, data fetch, data normalization) to plug into the main app. (Size: L)
AC: Developers can add a new integration by creating a module that adheres to the interface. For testing, a dummy plugin can be added easily without affecting others, proving the modularity.
Scheduling & Sync
Once integrations are connected, the IT Admin should be able to schedule automatic synchronization of data. The system will periodically fetch new transcripts and logs based on the schedule, and also allow manual sync on demand. Key Features:
Configurable schedule per integration (e.g., Google data sync every hour, Slack sync daily at midnight). Use a cron-style expression or simple presets (hourly, daily, weekly) to set frequency.
A "Sync Now" button for each integration (and/or a global sync all) which triggers an immediate fetch of any new data since last sync.
Backend scheduler service that triggers jobs at the configured times. If multiple integration syncs overlap, the system queues or runs them in parallel threads/workers as appropriate without overloading the system.
The scheduler should be robust (able to handle missed runs; e.g., if the system was down at a scheduled time, it should catch up or run on restart) and should log the outcome of each run (e.g., how many items fetched, any errors).
Option for the admin to temporarily pause an integration's sync (e.g., for maintenance or if an API quota is exceeded, so the admin can pause to avoid hitting limits).
Acceptance Criteria:
IT Admin can set a schedule during integration setup or edit it later. For example: "Every day at 01:00 AM" or "Every 6 hours". The UI provides either a cron expression input or a user-friendly scheduling form.
The "Sync Now" feature allows immediate data retrieval. When clicked, it will bypass the schedule and run the sync job for that integration immediately, providing feedback (e.g., a progress indicator and success/failure notification).
Scheduled jobs run at the correct times (verified via timestamps in logs or test with short intervals). If a scheduled run is missed due to downtime, the system triggers it soon after the app is back up or flags that it was missed.
Only new transcripts or messages since the last sync are retrieved, to avoid duplicates. The system tracks the last synced timestamp or message ID per integration.
Sync operations are reliable and do not conflict. For example, if Slack and Zoom sync run concurrently, each operates in isolation and uses separate API calls/threads so neither blocks the other.
The admin can see the status of the scheduler (next run times, last run results) and any sync errors on the dashboard or integration status page.
User Stories & Effort Estimates:
Story: Schedule Integration Sync – As an IT Admin, I can configure the synchronization frequency for each connected platform (using cron syntax or preset intervals) so that data is updated automatically. (Size: M)
AC: Saving a schedule updates the scheduler configuration. The schedule is stored persistently (in DB) and is applied even after a restart. Verify by setting an hourly schedule and seeing jobs trigger on the hour.
Story: Manual Sync Trigger – As an IT Admin, I want to manually trigger a sync for an integration at any time, so I can ensure data is up-to-date when needed (e.g., right before a meeting or compliance audit). (Size: S)
AC: Clicking "Sync Now" starts the job promptly and provides user feedback (e.g., a spinner or log output). The UI is responsive during sync, and once done, the last sync time updates and new data is available.
Story: Incremental Data Fetch – The system remembers the last sync point (timestamp or ID) per integration and only fetches data after that point to avoid duplicates. (Size: M)
AC: If the last Slack message saved was timestamp X, the next sync uses X as a cutoff to fetch messages after X. If data is re-fetched (due to edits or API quirks), duplicates are recognized and not double-stored.
Story: Scheduler Service Implementation – Implement a backend scheduler (e.g., using APScheduler or Celery beat) that reads schedules and executes sync jobs on time. (Size: L)
AC: The scheduler can handle multiple jobs and is reliable. It logs each run's start and end, and records summary info (e.g., "Zoom sync completed, 5 transcripts downloaded"). If a job fails, the error is caught and logged without crashing the scheduler.
Story: Pause/Resume Sync – Admin can disable or pause an integration's automatic sync without removing the integration entirely. (Size: S)
AC: A toggle or setting in the integration settings can mark it as "paused." The scheduler will skip paused integrations. The admin can later resume it, and the next scheduled time (or manual trigger) will pick up from the last sync point.
Filtering & Preprocessing
Not all transcripts or messages may be relevant or desired in the archive. The app should allow filtering of data during retrieval, as well as record key metadata for each entry and perform basic preprocessing of content. Key Features:
Ability to exclude certain data based on criteria:
By Participants: e.g., exclude meetings that only involve certain users (like internal test accounts) or exclude chat channels by name or user.
By Keywords: e.g., ignore any meeting whose title or transcript contains certain keywords (like "private" or "do not archive").
By Duration/Size: e.g., ignore meeting transcripts shorter than N minutes, or chat threads with fewer than X messages.
Option to include only certain departments or teams (if metadata about participants' department is available – possibly via integration with a directory or a mapping the admin provides).
Preprocessing steps such as:
Removing or marking system-generated messages (like "Meeting recording started..." notifications in transcripts).
Normalizing date/time formats in transcripts.
Optionally, splitting or summarizing very large transcripts (not for MVP, but consider if needed to handle extremely long meetings).
Annotating transcripts with metadata headers (e.g., adding a header line in a text file with meeting title, date, participants).
Each archived transcript or chat record should include metadata fields stored in the index: timestamp of the meeting or message, list of participants (or sender for chats), platform/source, and any tag like department or project.
Acceptance Criteria:
During integration setup or in a global settings section, the IT Admin can define filters. For example: select users to exclude (from a list of known users or by entering identifiers), specify keywords to exclude, set a minimum meeting length, etc.
When a sync runs, the filters are applied before or during saving data. Ineligible transcripts or messages are skipped (and ideally logged as skipped with a reason for transparency).
The system correctly records metadata for each saved transcript/log:
Date/Time of the meeting or chat.
Participants (for a meeting, all attendees; for a chat, sender and chat name or participants in that chat).
Platform (Slack, Zoom, etc.).
Department/Team if available (e.g., if user emails are mapped to departments via a provided list or an API).
A transcript or chat that does not meet filter criteria is not stored in the repository nor pushed to Notion. For instance, a 2-minute Zoom call with only one participant would be skipped if a 5-minute minimum is set.
Preprocessing rules are correctly applied. E.g., if "remove system messages" is enabled, those lines do not appear in the stored transcript. If adding a header with metadata to each transcript file, the header is present and consistently formatted.
The admin can update filter settings, and those apply to subsequent syncs (already stored data remains unless manually cleaned up).
User Stories & Effort Estimates:
Story: Configure User Exclusion Filter – As an IT Admin, I can specify a list of users (emails or IDs) whose meetings or messages should be excluded from archiving. (Size: S)
AC: The UI allows selection or input of user identifiers to exclude. During sync, if a meeting's organizer or all participants are in the exclude list, that transcript is skipped. If a Slack message's sender is excluded, that message (or entire thread, depending on implementation) is skipped.
AC: The UI allows selection or input of user identifiers to exclude. During sync, if a meeting’s organizer or all participants are in the exclude list, that transcript is skipped. If a Slack message’s sender is excluded, that message (or entire thread, depending on implementation) is skipped.
Story: Keyword/Pattern Filter – As an IT Admin, I can provide keywords or regex patterns such that if a transcript’s content or title contains them, it will be skipped. (Size: M)
AC: The filter is case-insensitive by default. For example, if keyword "Confidential" is set, any meeting transcript containing that word (perhaps in captions or chat) is not saved. A chat message containing a banned keyword is dropped from the archive.
Story: Duration/Length Filter – Admin can set a minimum meeting length (in minutes) or minimum number of messages for a chat to be archived. (Size: S)
AC: The system obtains meeting duration from metadata (or calculates from transcript timestamps) and compares to the threshold. Shorter meetings are skipped. Similarly, chat threads with fewer messages than the threshold are ignored.
Story: Department Tagging – The system can tag transcripts by department if it knows participant departments. (Size: M)
AC: If integrated with a company directory or if the admin uploads a CSV mapping users to departments, the system adds a metadata field “Department” (e.g., Marketing, Engineering) to each transcript record. This field is stored in the index and can be used in search filters.
Story: Transcript Preprocessing – Implement data cleaning steps like removing system messages and normalizing text. (Size: M)
AC: Known system messages (like Teams "Meeting recording started..." or Zoom "Transcription: ...") are filtered out or flagged. All timestamps in transcripts, if present in text, are converted to a standard format (e.g., ISO 8601 or a uniform timezone). Each stored text file begins with a header containing meeting title, date, participants.
Story: Metadata Indexing – Ensure that for each saved transcript or chat, the metadata (date, participants, platform, etc.) is recorded in the database index. (Size: S)
AC: After sync, the database entry for a transcript includes fields for date/time, participants, platform, department (if available), etc. A quick check can be done via a DB query or through the search UI to confirm metadata presence.
Format & Storage
The application should store the transcripts and chat logs in a structured and accessible manner. We will use an S3-compatible object storage for storing transcript files, and allow multiple file formats for export. There will be an organized folder structure for ease of management. Key Features:
S3-compatible Storage: Use AWS S3 or any S3 API compatible service (e.g., MinIO for self-hosted deployments) to store the transcript and chat files. This provides scalable and durable storage for potentially large volumes of text data.
Folder Organization: Establish a clear hierarchy for stored files. For example:
Top-level by Platform (/GoogleMeet, /Zoom, /Slack, etc.).
Within each, organize by date or by user. A possible scheme: /Platform/Year/Month/... or /Platform/User/.... For MVP, simplest is by date. e.g., Zoom/2025/05/Project Kickoff Meeting_2025-05-20.txt.
Configurable File Formats: Allow admin to choose the format of saved transcripts:
Plain text (.txt) for simplicity.
Markdown (.md) for formatted text (useful if transcripts are to be read or further processed).
JSON (.json) for structured content (keyed by timestamp, speaker, message, etc.).
Microsoft Word (.docx) for a portable rich-text format (some organizations may prefer .docx for distribution).
(Admin might set a global default format and optionally override per integration type.)
File Naming Convention: Use a clear naming scheme that includes date and meeting title or chat identifier to avoid collisions and make content recognizable from the file name.
Database Index: Use PostgreSQL to store references (metadata and paths) to each file. The DB index allows quick searching and avoids having to scan S3 for listing content. The actual transcript text is primarily kept in files (not loaded into the DB, which keeps the DB size manageable and allows storing large transcripts without performance hits).
Acceptance Criteria:
The application can connect to the configured S3 storage (credentials for S3 provided in app configuration or via the admin settings). If the bucket is not accessible (incorrect creds or wrong name), the admin is alerted upon setup.
When transcripts/chats are fetched, the system saves each as a file in the S3 bucket under the correct path. The path structure follows the defined convention. Example: a Zoom meeting on 2025-05-01 titled "Project Kickoff" might be stored at Zoom/2025/05/Project Kickoff_2025-05-01.txt (or .md/.json depending on format).
If the chosen format is JSON, the content of the file is a JSON structure (e.g., an array of message objects with fields for timestamp, speaker, text). If Markdown, the file contains formatted text (e.g., using bold for speaker names or bullet points for separate messages). If DOCX, the file is a well-formed Word document containing the transcript text and basic formatting (e.g., heading for title, bold for speaker names).
The admin can set a default format globally and override format per integration if needed. For instance, Slack chat logs might be more useful in JSON, while meeting transcripts might be fine as Markdown or text.
All text-based files use UTF-8 encoding to preserve special characters and international text.
The database index is updated for each file saved: it stores at least the file path (or S3 key), platform, date, title (meeting name or chat name), participants, and any tags (department, etc.). This index is used by the search functionality to retrieve results without scanning the entire storage.
User Stories & Effort Estimates:
Story: Connect to S3 Storage – As an IT Admin, I want to configure the app with my S3 (or compatible) storage credentials and bucket name, so transcripts can be stored in my controlled repository. (Size: S)
AC: The system provides a settings form for S3 details (endpoint, bucket, access key, secret). A "Test Connection" button verifies that the bucket is reachable (e.g., tries to list or create a test object). On success, the setting is saved.
Story: Save Transcript in Chosen Format – The system saves each fetched transcript or chat log as a file in the specified format and location. (Size: M)
AC: If the admin chose Markdown for Zoom transcripts, after a sync the .md file is present in S3 for each new transcript. If they switch the preference to JSON, subsequent transcripts use .json. Past files remain in their original format (unless reprocessed).
Story: Organize Files by Platform/Date – Implement the directory structure convention in S3 for stored files. (Size: S)
AC: Files are organized predictably. For example, Slack messages from July 2025 end up under Slack/2025/07/..., and Zoom transcripts under Zoom/2025/07/.... Meeting titles or chat names in file names have spaces and special chars safely handled (e.g., spaces replaced with underscores or URL encoding if needed).
Story: PostgreSQL Index of Files – Each time a file is saved to S3, create or update a corresponding record in Postgres with metadata for search. (Size: M)
AC: A table (e.g., transcripts_index) stores: an ID, platform, title/name, date/time, participants, department (if any), and S3 key or URL. Querying this table by date or other fields returns the expected records. Verify that after a sync, entries appear in this table matching the files.
Story: Multiple Format Support – Build utility functions or use libraries to output transcript data in different formats (text, MD, JSON, DOCX). (Size: M)
AC: Given a standardized transcript data object (with fields like title, date, participants, and an array of messages), the system can output it as plain text (e.g., "Speaker: text"), as Markdown (with basic formatting), as JSON (structured fields), or as a .docx file. Verify each format by opening a sample output (e.g., load the DOCX in Word, open JSON in a viewer to confirm structure).
Destination (Notion or Hosted Repository)
The product provides two main options for where the consolidated transcripts are accessible:
Push to Notion: Use the Notion API to create pages or database entries with the transcript content.
Hosted Repository: Use the application’s own storage and interface as the repository where transcripts can be searched and viewed (within the app itself).
Key Features:
Notion Integration: If the admin chooses Notion as a destination, they will connect a Notion workspace via API token or OAuth. The app will then create a structure in Notion (either a database or pages in a designated page) to store transcripts.
Each transcript becomes a new Notion page or a row in a Notion database. Metadata like date, participants, platform can be properties, and the transcript text can be the page content or an attached file.
Support basic rich text formatting in Notion pages (e.g., bold for speaker names, using Notion blocks for different parts of the transcript).
If pushing to Notion fails (due to API errors or rate limits), the system logs the error and retries or alerts the admin.
Hosted Repository (Internal): If Notion is not used, the application itself serves as the repository:
The transcripts are stored in S3 as above, and the app’s UI provides a way to list and view them.
The IT Admin (and any future authorized users) can search and view transcript content via the app’s interface. Viewing a transcript would pull the file from S3 and display it (rendering Markdown or showing plain text, etc.).
Access control: since it’s single-tenant and only the admin uses it in MVP, the main control is that the app is behind login. In future, if multiple users, we will implement proper view permissions.
The admin should be able to choose the destination mode either globally or per integration. Possibly:
A global toggle like “Push all data to Notion (if connected)” vs “Store data internally only”.
Or a per-integration choice (though mixing might complicate search, so likely global for simplicity).
Note: Even if Notion is chosen, the system may still store files in S3 as a backup and for search indexing, treating Notion as a secondary copy. (Alternatively, we could store only metadata and rely on Notion as storage, but having our own copy ensures independence and full search capability).
Acceptance Criteria:
Notion Setup: The admin can connect to Notion by entering an integration token or following an OAuth flow. On success, the app either creates a default "Meeting Transcripts" database in Notion or allows the admin to select an existing target in Notion.
After each sync, new transcripts are pushed to Notion as pages or database entries. The content in Notion matches the transcript file content. For example, a transcript stored in Markdown is converted to Notion blocks (via the Notion API) preserving basic formatting.
Each Notion page/entry includes metadata: e.g., a title (meeting name - date), properties for date, platform, participants (perhaps as multi-select or text property), etc., making it easy to browse in Notion as well.
If the app cannot reach Notion (network or auth error), it queues a retry and/or logs the failure in the dashboard. The transcript should still be saved internally (so data is not lost) and can later be re-pushed via a retry mechanism or manual trigger.
Hosted Repository: The web application provides a "Transcripts" or "Archive" page where the admin can see all stored transcripts in a list or table (with columns like Date, Title, Platform, maybe length).
Clicking on a transcript in the list opens a detailed view within the app: showing the transcript content (loaded from S3). If the file is markdown or text, it’s displayed nicely (monospaced or formatted text). If it's a .docx, the app could offer a download or use a converter to display (for MVP, even a download link might suffice for docx).
The admin can configure the destination mode. For MVP, if Notion is connected, we assume push to Notion is enabled; if not connected, data stays internal. (Optionally, a setting "Enable Notion sync" can control it).
Data consistency: transcripts stored internally vs in Notion should be identical in content. The system does not need to sync back changes from Notion (one-way push only in MVP). This is documented to the admin: if they edit a Notion page, that change is not reflected in the app’s copy unless they also re-upload it, which is not supported in MVP.
User Stories & Effort Estimates:
Story: Notion API Integration – As an IT Admin, I can connect a Notion workspace so that transcripts can be automatically created as pages in Notion for wider access. (Size: M)
AC: Using the Notion API, the system obtains authorization and can create a test page. The admin can specify or accept a default location (e.g., a database or parent page). The connection status is shown in settings (e.g., "Connected to Notion workspace XYZ").
Story: Create Notion Pages for Transcripts – The system creates a new Notion page (or database entry) for each transcript after sync. (Size: M)
AC: After a Zoom meeting transcript is fetched and saved internally, a page titled with the meeting name and date is created in Notion under the chosen section. The page contains the transcript text and key metadata. Verification: see the page appear in Notion with correct content after a sync.
Story: Internal Repository Viewing – As an IT Admin, I want to view transcripts directly in the app (especially if I am not using Notion), so I don’t have to leave the platform to read a transcript. (Size: M)
AC: The UI has a "Transcripts" page listing available records. Clicking one opens the transcript within the app. For example, a markdown transcript is rendered to HTML for display (line breaks, bold text preserved). There’s also an option to download the raw file if needed.
Story: Destination Mode Toggle – Provide a setting to choose whether to push data to Notion, internal, or both. (Size: S)
AC: In settings, admin can check/uncheck “Sync to Notion”. If unchecked, the app will not push to Notion even if connected (keeping data only internal). If checked (and Notion is connected), the app pushes new data to Notion. Changing this setting affects subsequent syncs only.
Story: Error Handling & Retry for Notion – Implement robust error handling for pushing to Notion. (Size: M)
AC: If a Notion API call fails (due to rate limit or other error), the system logs it and retries after a delay (e.g., exponential backoff or next sync cycle). The dashboard shows an alert like “5 transcripts pending to sync to Notion due to errors” if applicable. Admin can attempt a manual retry (perhaps by clicking "Sync Now" again or a specific retry action).
Search & Dashboard
This section covers the features for searching stored transcripts and monitoring the system via an admin dashboard. These functionalities allow the IT Admin to quickly find specific records and to ensure the system is operating correctly.
Search (Metadata-Only for MVP)
The application will provide a search interface to query the transcripts repository. In the initial version, search will be based on metadata (not full transcript text):
Filter by Date/Date Range: e.g., find all meetings between Jan 1 and Jan 31, 2025.
Filter by Participant: e.g., find transcripts where a certain user (email or name) was a participant.
Filter by Platform: e.g., show only Slack chat logs or only Zoom meetings.
Possibly filter by Department/Team: if that metadata is captured, allow narrowing results to a department.
Search results can be displayed as a list of matching transcripts with key info (title, date, platform, participants). Key Features:
A search UI with fields or filters for the above criteria (date pickers, dropdown for platform, autocomplete for participants).
Query implementation that runs against the Postgres metadata database for efficiency.
Support basic logical queries (e.g., date range AND participant = X).
For MVP, we do not implement full-text search of transcript content (that is a roadmap item). However, we might include a simple keyword search on the transcript title or meeting name since those are usually short.
The results link to viewing the transcript (either in Notion or in the app’s viewer depending on configuration).
Acceptance Criteria:
The IT Admin can open a "Search" page and see options to filter by date, participant, platform. Leaving all fields blank and hitting search shows all transcripts available.
Setting a date range limits results accordingly. For example, if the admin selects March 2025, only transcripts from that month are listed.
Participant filter allows typing a name or email; the system suggests matches (based on participants present in metadata). Selecting one shows only transcripts where that person was involved.
Platform filter is a dropdown of connected platforms. Selecting "Slack" yields only Slack chat log entries in results, etc.
Search results show at least: Title or Meeting name, Date/Time, Platform, and perhaps a snippet or first line of the transcript for context. If department info is available, that can show as a column or badge.
Clicking a result opens the transcript (either within the app viewer or navigates to Notion if applicable).
The search operation should be fast (metadata queries on indexed fields). For example, retrieving results among 10,000 transcripts with a filter should take only a couple of seconds at most.
User Stories & Effort Estimates:
Story: Search by Metadata – As an IT Admin, I want to search transcripts by date, participant, or platform so that I can quickly find relevant meetings or chats. (Size: M)
AC: The search form allows combining filters. After executing a search, the list updates with matching items. Verified by creating dummy data and ensuring queries return expected results (e.g., known meeting on a date is found).
Story: Participant Autocomplete – The search interface provides suggestions when typing a participant’s name or email, improving usability. (Size: S)
AC: If the letters "Ali" are typed, the system might suggest "Alice Johnson (<EMAIL>)" if that participant exists in metadata. Selecting it populates the filter and updates results.
Story: Result Navigation – From the search results, the admin can click to view the full transcript. (Size: S)
AC: If the app is hosting the repository, clicking opens the transcript within the app's viewer page. If using Notion as destination, clicking could open the Notion page (in a new tab). Ensure that works and the user has access to that Notion page.
Story: Basic Keyword Filter (Title) – (Optional for MVP) Allow a simple text search on the meeting title or chat name. (Size: S)
AC: If the admin enters a keyword "Budget" into a title search field, any transcripts with "Budget" in the title or meeting name are returned. (This is a shallow text search on a metadata field, not the full transcript content.)
Story: Index Optimization – Create database indexes on key metadata fields (date, participants, platform) to ensure search queries are performant. (Size: S)
AC: On a dataset of thousands of records, queries filtering by these fields return quickly (< 2 seconds). This may be verified through performance testing or explaining the index usage in query plans.
Admin Dashboard
The admin dashboard provides an overview of the system’s status and usage. It's the landing page for the IT Admin after login, showing metrics and any important alerts. Key Features:
Key Metrics: e.g., number of transcripts archived in the last week/month, total transcripts archived, number of integrations active, timestamp of last sync per integration.
Activity Timeline or Visualization: a simple chart or graph showing the number of transcripts collected over time (e.g., a line chart by day for the past month, or bar chart by week).
Error Log Summary: display recent errors or warnings (e.g., failed sync attempts, failed Notion pushes, authentication errors). Possibly show the last few error messages or a count of errors with a link to a detailed log page.
License Status (if applicable): show if the instance’s license or subscription is active and when it expires, to avoid unexpected service interruption.
Possibly a System Health indicator: e.g., show if the scheduler is running, storage utilization (how much of the S3 bucket used), or other diagnostics (not critical for MVP, but good for enterprise trust).
Acceptance Criteria:
Upon logging in, the IT Admin sees a dashboard page with at least:
A tile/card for each configured integration showing its status (e.g., "Google Meet: last sync 2 hours ago; 120 transcripts archived; status ✅" or "Slack: ❌ Token expired, re-authenticate").
A summary count of transcripts in the repository (and possibly breakdown by platform in smaller text).
A chart or graph indicating recent activity (for MVP, even a simple histogram of transcripts per day for last 7 days).
The dashboard clearly highlights if any integration needs attention: for example, an integration card could turn red or show an error icon if the last sync failed or if auth is invalid.
There is a section for "Recent Activity" or "Logs" where the admin can see entries like "Zoom sync at 10:00 AM – 3 transcripts fetched" or "Slack sync at 11:00 AM – Error: Invalid credentials".
The license information is displayed (especially for self-hosted deployments). For example: "License: Enterprise - valid until 2026-12-31" or "Subscription: Pro Plan (5 users) - Active".
The UI is clean and uncluttered, focusing on high-level info. Charts are simple and do not overwhelm the page.
All data on the dashboard updates after relevant events (e.g., after a sync finishes, the last sync time and counts update, perhaps on next page refresh or via websockets/polling in future).
User Stories & Effort Estimates:
Story: Integration Status Overview – Show a card for each configured integration with key info (platform name, last sync time, next scheduled sync, total items archived, error state if any). (Size: M)
AC: If an integration is active and healthy, its card shows a green status. If it’s paused or has an error, that is indicated (grey or red status, with a message like “Paused” or “Error: <details>”). Last sync time and total count display correctly from the database.
Story: Transcripts Count Metrics – The dashboard displays total transcripts archived and recent counts (e.g., how many added this week vs last week). (Size: S)
AC: These metrics update as new transcripts come in. For example, after a new sync, the total count increases accordingly. The calculation for "last week vs this week" is correct (ensuring date ranges align).
Story: Recent Activity Log – Show the last N events (sync operations, user actions, errors) in a list for quick monitoring. (Size: M)
AC: Each event entry has a timestamp and a short description. For example, "2025-05-20 10:00 – Google Meet sync completed, 5 new transcripts" or "2025-05-20 09:00 – ⚠️ Slack sync failed (token revoked)". New events appear in the list as they occur (or on refresh).
Story: Activity Visualization – Include a simple chart (e.g., bar chart or line chart) showing number of transcripts added per day/week over a recent period. (Size: M)
AC: The chart displays data (if available). For instance, bars for each day of the past 14 days showing how many transcripts were archived each day. Verify the numbers match actual data (from the index).
Story: License Info Display – As an admin, I can see my subscription/license status on the dashboard or settings page so I know when renewal is needed. (Size: S)
AC: If using a license key, show license type and expiry date. If on a SaaS plan, show plan name and a generic "Active" if current. Maybe include a link or instruction for renewal if expiry is near.
UI/UX
The user interface should be modern, clean, and intuitive. Even though initially only IT Admins use the app, a good UX is important to reduce configuration errors and make the system pleasant to use. The design should be responsive (usable on various screen sizes, though primary use will be desktop for admin tasks) and minimalistic. General Design Principles:
Integration Setup Wizard: Setting up a new integration is done through a step-by-step wizard to guide the admin through OAuth, scheduling, filters, and destination. This prevents overwhelm and ensures all required info is collected in order.
Global Navigation: Use a simple sidebar or header menu with clear sections: Dashboard, Integrations, Transcripts (Search), Settings, Support. The currently active section should be highlighted.
Forms & Feedback: Provide clear forms for configurations (with labels and help text explaining options like filter criteria or cron syntax). After any action (save settings, trigger sync), give immediate feedback via toast notifications or visible status indicators.
Consistency: Use a consistent visual style (colors, typography, spacing) and consistent terminology (e.g., consistently use "transcript" vs "record" to avoid confusion). Icons for known platforms (Slack, Zoom, etc.) can be used for quick visual identification.
Responsive Design: Ensure the layout adapts to different screen sizes. On a smaller screen, the sidebar might collapse to a hamburger menu; cards may stack vertically; tables might scroll horizontally within their container.
Modern Minimalist Aesthetic: Likely use a light background, with accent colors for highlights (possibly configurable or at least a default that matches an enterprise feel). Avoid clutter – show only necessary information, with advanced details hidden behind toggles or info modals.
Key UI Elements & Flow:
Integration Wizard Flow: (for adding a new integration)
Select Platform: Admin chooses which service to integrate (display logos/names for Google, Microsoft, Slack, Zoom, etc.).
Authenticate: Click "Connect" which initiates OAuth flow. Upon return, indicate success or failure.
Schedule: UI for setting sync schedule (could be a dropdown of common options or a cron expression input with validation).
Filters: Show options for filters (user exclusion, keyword, etc.). This step can be optional or skipped with defaults (no filters) if admin doesn’t need them.
Destination: Choose Notion vs Internal (if Notion is available) and choose format for this integration’s files if desired (or use global default).
Review & Save: Show a summary of settings and a "Finish" button.
The wizard should allow "Back" to previous steps and "Cancel" to abort.
Dashboard Page: Tiles for each integration (maybe laid out in a grid), a summary section for totals, and an activity log section. Possibly split the page into an upper portion (metrics/tiles) and lower portion (activity feed, chart).
Integrations Page: List existing integrations with basic info (platform, status, next sync, last sync). Each entry with actions like "Edit" (to change schedule/filters), "Pause/Resume", or "Delete". Also an "Add Integration" button to launch the wizard.
Transcripts/Search Page: A set of filter controls at top (date pickers, dropdowns, text input for participant or keyword), with a "Search" button. Below, a results table or list that updates after search. Each result clickable. Possibly allow sorting by date or platform.
Transcript View Page/Modal: If viewing within app, display the transcript content. Could be a full page or a modal dialog. Include metadata at top (title, date, participants, maybe a link to open in Notion if applicable) and the content below. If content is long, make it scrollable. If possible, preserve basic formatting (line breaks, bold, etc).
Settings Page: Contains global settings like S3 configuration, default format, license key upload, Notion connection status (with option to connect/disconnect), and other global toggles (like enabling Notion sync, enabling email notifications for errors, etc., if any).
Support Page: Provides information on how to get support. Possibly dynamic based on license tier (e.g., Basic tier: "Email <NAME_EMAIL>"; Enterprise tier: also list phone number or dedicated rep contact). Could also include a link to documentation or an FAQ.
Wireframe Description (ASCII Outline):
[Sidebar Navigation] -- [Main Content Area]
Sidebar:
 - Dashboard
 - Integrations
 - Transcripts/Search
 - Settings
 - Support

Main Content Area (examples):
 Dashboard:
  -----------------------------------------
  | [Integration: Google Meet] [Integration: Slack]  |  (cards with status)
  | Last sync: 2h ago        Error: None             |
  -----------------------------------------
  | Total Transcripts: 345    (chart: transcripts/day for 2 weeks) |
  | Slack Error yesterday (see logs)                              |
  -----------------------------------------
  | Recent Activity:                                          |
  | 2025-05-20 10:00 Zoom sync completed (5 items)            |
  | 2025-05-20 09:00 Slack sync failed (auth error)           |
  -----------------------------------------

 Integrations:
  -----------------------------------------
  | + Add Integration                      |
  | Google Meet - Active - Sync hourly - Last sync 2h ago [Edit][Pause] |
  | Slack - Error (token) - Sync daily - Last sync failed [Reconnect]   |
  -----------------------------------------

 Search:
  -----------------------------------------
  | Date Range [___ to ___]  Participant [____] Platform [v] [Search] |
  | Results: (show X results)                                       |
  |  - 2025-05-20 | Project Kickoff Meeting | Zoom | Alice, Bob, ... |
  |  - 2025-05-18 | Design Discussion       | Slack| (Channel: #design) |
  -----------------------------------------

 Transcript View (Internal):
  -----------------------------------------
  | Title: Project Kickoff Meeting (Zoom)           Date: 2025-05-20 |
  | Participants: Alice, Bob, Carol, Dave (Engineering)              |
  |------------------------------------------------------------------|
  | Alice (10:00 AM): Hi team, let's start the project kickoff...    |
  | Bob (10:05 AM): I have updated the project plan.                 |
  | ... (transcript continues) ...                                   |
  |------------------------------------------------------------------|
  | [Download .txt]                                      [Close]     |
  -----------------------------------------
(The above is a textual representation for illustration; the actual UI will be visually designed.) Acceptance Criteria (UI/UX):
The application UI works on modern browsers (Chrome, Firefox, Edge) and adapts to different screen sizes without major usability issues. Critical flows (like adding an integration or viewing a transcript) are usable on a laptop and a tablet screen at minimum.
The integration wizard prevents configuration mistakes: e.g., it won’t proceed if OAuth isn’t completed or if required fields are empty. It provides useful error messages (e.g., "Schedule format invalid" or "Authentication failed, please retry").
Visual style is consistent and professional. For example, use a consistent color for primary actions, and platform-specific colors/logos subtly for identification (e.g., Slack integration card might have Slack logo).
All interactive elements (buttons, inputs) have appropriate labels and/or tooltips. For example, a pause icon has a tooltip "Pause sync".
The UI provides feedback for background operations: if a sync is running, there could be an indicator (like a spinner on the integration card, or a status message "Sync in progress...").
No major UI bugs: e.g., text should not overflow badly, modals should close properly, forms should validate input.
The UI/UX has been reviewed ideally with an IT admin user for clarity: terms like "Integration", "Sync", "Transcript" are understood, and help text is provided for any non-obvious settings (e.g., next to the cron expression input, an info icon explains the format or offers examples).
User Stories & Effort Estimates:
Story: Integration Wizard UI – Implement the multi-step form for adding an integration, including all required steps and validations, following the design flow. (Size: L)
AC: The wizard guides the user through connecting a platform and configuring options. It retains state between steps, and cannot finish unless all required info is provided. On completion, the new integration appears in the list.
Story: Dashboard UI Implementation – Build the dashboard page with integration cards, metrics, and activity log. (Size: M)
AC: The dashboard matches the described layout and updates dynamically or on refresh to show current data. It highlights issues (e.g., uses red text or icons for errors). Test by simulating an error and seeing the UI reflect it.
Story: Search & Results UI – Create the search interface with filter controls and results list/table. (Size: M)
AC: The admin can input filter criteria and click Search, then see a list of results. If there are many results, they might be paginated or scrollable. Each result entry is clickable and shows key info. The layout remains clear even if fields are empty or if some metadata (like participants list) is long.
Story: Transcript Viewing UI – Provide an interface to read transcript content in-app. (Size: M)
AC: For internally stored transcripts, clicking a result opens a page/modal with the transcript text and metadata. The text is formatted for readability (line breaks, etc.). If the transcript is very long, ensure the page is still responsive (maybe limit height with scroll). If Notion is used, clicking can redirect to Notion – ensure a notice if the user doesn’t have permission or if page is not found.
Story: Settings & Support Pages – Implement pages for global settings (S3 config, license key, Notion connect/disconnect) and a static page for support contact info. (Size: S)
AC: The admin can update and save settings like S3 credentials on the Settings page (with proper validation for required fields). The Support page displays contact information appropriate to the license tier (this may be done by a simple conditional or config file). Both pages should be protected (only accessible to logged-in admin).
System Architecture
The system will be designed with a modular architecture to ensure scalability, maintainability, and alignment with the single-tenant deployment model. Architecture Overview:
Frontend: A React (JavaScript) single-page application for the admin UI. It communicates with the backend via RESTful APIs (JSON) or GraphQL endpoints. The UI is decoupled from the backend to allow independent deployment and updates.
Backend: A Python-based application server. We can use a lightweight web framework (Flask/FastAPI or Django) to expose APIs for the front end and to handle webhooks/callbacks (like OAuth redirects). This backend also contains the core business logic for managing integrations, scheduling jobs, processing transcripts, etc.
Background Worker(s): For handling scheduled sync tasks and any long-running processes, we will have either a background worker process (using something like Celery with a message broker, or simply a separate thread with a scheduler if simpler) or leverage serverless cron jobs (if deployed on a platform that supports it). The key is that heavy data fetching is done asynchronously, not blocking the main API thread.
Database: PostgreSQL is used for persistent data: configurations (integrations, schedules, user account), transcript metadata index, audit logs, etc. Each customer/instance has their own database (for single-tenancy). Use migrations to manage schema changes.
Object Storage: S3 (or compatible) for storing transcript files as described. Each instance might use a separate bucket or a separate key prefix to keep data isolated if using a shared S3.
Integration Modules: Each external integration (Slack, Zoom, etc.) is implemented as a separate module or service. For example, we might have integration_slack.py, integration_zoom.py, each providing functions like authenticate() (if needed) and fetch_data(since_timestamp). A common interface or abstract base class defines how the main system calls these modules. This plugin approach will make it easier to add or update integrations without touching unrelated code.
APIs and Internal Communication: The front end calls the backend API for all operations (e.g., create integration, trigger sync, search transcripts). The backend, when needing to perform a sync, may put a job in a queue or trigger an async task. For simplicity, MVP might use an in-process scheduler thread if load is low.
Single-Tenant Consideration: Each deployed instance of the app serves one organization. In a SaaS scenario, that might mean separate containers or at least logically separate contexts for each org (separate DB and storage space). No runtime comingling of data in memory.
Security Components: Use an authentication mechanism for the admin user (e.g., JWT or session cookies). Protect all API endpoints with authentication checks. Use HTTPS for all communications. Possibly include a simple rate-limit on the API to prevent misuse (though single-user, low risk).
Scalability: For MVP, one instance should handle the typical load for one organization (which might be syncing a few hundred transcripts a day max). We design so that if needed, the backend and worker can be scaled (multiple processes) and storage/DB are external to allow horizontal scaling. For example, if one org is very large, we could run multiple workers for parallel API calls if needed.
Diagram (simplified text diagram):
[ React Frontend (SPA) ]  <----->  [ Python Backend API ]  <----->  [ Postgres DB ]
          |                              |------->  [ S3 Storage ] (for files)
          |                              |------->  [ Integration Plugins/Services ]
          |                                          (calls external APIs: Slack, Zoom, etc.)
          |                              |------->  [ Scheduler/Worker Process ]
[ Admin’s Browser ]              (executes cron jobs, fetches data via plugins, stores results)
The React app runs in the admin’s browser, making XHR/fetch calls to the backend.
The Python backend handles those requests, uses DB for state, and enqueues or triggers background tasks for heavy lifting.
The integration plugins in the worker or backend handle communication with external services.
S3 stores the large content (transcripts), while Postgres stores small data (metadata, config).
Acceptance Criteria (Architecture/Technology):
The application can be deployed on a single server or VM with the above components, but also can be containerized (each component in a container) for flexibility. E.g., we can run the web app and worker in one container (monolith) or separate containers – design allows either.
Adding a new integration does not require significant rework of the system. Prove this by hypothetically adding a stub integration plugin and showing it can be called by the scheduler with minimal changes.
The system should be resilient: one failing integration or a bug in one plugin should not crash the entire system. Use isolation where possible (a try/catch around plugin calls, possibly separate process for workers so a crash doesn’t bring down the API).
The database design is normalized and indexed appropriately. For example, ensure tables for transcripts metadata have indexes on date and other searchable fields.
Basic performance considerations: The system should handle, say, 1000 transcripts and 100k chat messages without issues in the DB and storage. We assume transcripts are mostly text; for chat messages, if volume is huge, might need to batch into transcripts per day or channel.
Ensure that the architecture supports the single-tenant model: e.g., configuration for DB connection and S3 bucket are per instance (not global). In a SaaS environment, deploying a new instance for a new customer is straightforward (scripted or via automation).
User Stories & Effort Estimates (Technical):
Story: Backend Project Setup – Initialize the Python backend with chosen frameworks (API and async job handling), and ensure it can run a simple health-check endpoint. (Size: M)
AC: We have a running backend that connects to Postgres (verify by a test endpoint reading/writing a test value) and is structured to add further routes and tasks.
Story: Frontend Project Setup – Initialize the React app (create-react-app or equivalent), set up routing for the main pages, and integrate a UI library if using one (for example, Material UI or Ant Design). (Size: M)
AC: The React app loads, can navigate between empty placeholder pages for Dashboard, Integrations, etc., and can call a dummy API endpoint successfully (testing CORS, etc.).
Story: Database Schema Definition – Design and create the database schema for the core entities: User (admin), Integration, Transcript (metadata), AuditLog, etc. Use a migration tool. (Size: S)
AC: The schema is applied to the dev database, tables exist with correct columns and constraints (e.g., foreign keys if any, not null where applicable). Example tables: user, integration, transcript, audit_log, license (for license info if needed).
Story: Integration Plugin Interface – Define a standard interface/class for integration plugins. Implement at least one (e.g., Slack) following this pattern to validate it. (Size: M)
AC: There is a base class or template that specifies functions like authenticate(config), fetch_since(last_sync_timestamp) -> [transcripts]. The Slack integration class implements these. The scheduler calls the same interface for Slack, and future integrations can follow this.
Story: Scheduler & Worker Implementation – Set up an asynchronous job runner (could be Celery with Redis, or a simpler approach like threading with schedule/cron library) to execute sync jobs in the background. (Size: L)
AC: The system can schedule a job (e.g., via APScheduler cron) and run the integration fetch in a separate thread or worker. It can handle multiple jobs (though sequentially or limited parallelism for MVP). Validate by scheduling two jobs close together and see they both execute (maybe sequentially).
Story: API Endpoint Development – Implement all necessary API endpoints: login, CRUD for integrations (create, read/list, update, delete), trigger sync, retrieve search results, fetch transcript content (if needed for internal viewer), update settings, etc. (Size: L)
AC: The API adheres to REST principles, with proper authentication. For example: POST /api/integrations to add one, GET /api/integrations to list, POST /api/sync/{id} to trigger sync, GET /api/search?params for search, etc. Each endpoint returns appropriate HTTP status and JSON data. Test each endpoint with sample data.
Deployment & Hosting
We offer flexibility in how the product is deployed, catering to both SaaS (hosted by the vendor) and self-hosted scenarios. In all cases, each customer’s instance is isolated (single-tenant per instance deployment). Deployment Options:
Vendor-Hosted (SaaS): We (the vendor) run the application in our cloud infrastructure. This could mean each customer gets a dedicated VM/container (for strong isolation) or a separate namespace in a multi-tenant cluster. Each instance has its own database and its own S3 bucket or storage namespace. We handle maintenance, updates, and scaling. Customers subscribe and access via a web URL.
Self-Hosted (Customer-Managed): We provide a Docker distribution (or Helm chart for Kubernetes) that the customer can deploy on their own servers or cloud. The self-hosted package includes the application and perhaps optional bundled dependencies (or expects the customer to provide Postgres and an S3 endpoint). This mode uses a license key to enable the software and possibly feature flags.
In both models, a License Verification mechanism exists:
For self-hosted: the admin will upload a license key (e.g., a file or string) into the app. The app will validate it (could be a signed JWT or use a license server for online verification). The license encodes the expiration date and tier of the subscription.
For SaaS: license verification is handled on our end (we ensure the instance is active as long as subscription payments are current). The app might still have an internal license token to enable certain features per tier.
The app should periodically re-check the license (daily or on startup) to see if it’s still valid (especially for self-hosted, to enforce annual renewals). For offline environments, we might allow a grace period and then require manual reactivation.
Support Tiers Integration: The application should be aware of the support tier mostly in terms of information display:
Basic: likely no special in-app features; support is via email.
Business: similarly handled outside app, but we might indicate “Priority email support” somewhere.
Enterprise: we might provide an in-app chat support widget or at least list a phone number for support. Possibly, enterprise license could unlock integration to a support system or a dedicated account manager contact info.
These differences can be configured via the license or a config file loaded per instance.
Hosting and Maintenance Considerations:
One instance per customer means upgrades have to be rolled out to each instance. The system should be dockerized to make this as seamless as possible.
For SaaS, we may automate deployment (CI/CD) so new versions deploy to all customer instances or allow rolling upgrades.
For self-hosted, provide clear documentation for installation and upgrade (e.g., run new Docker image, apply DB migrations).
The app should have a health-check endpoint for monitoring (so that in SaaS our orchestration can check if an instance is up).
Logging and monitoring in SaaS: centralize logs for our support team. For self-hosted: allow logs to be written to stdout or file so customers can integrate with their logging.
Acceptance Criteria:
Isolation: Deploy two instances of the app (simulate two customers) on one server using Docker with different ports/databases. They should operate completely independently (no shared data). Test by configuring different S3 buckets and ensuring each instance only writes to its own.
Docker Deployment Simplicity: A customer following provided instructions (e.g., using a docker-compose.yml) can bring up the application. They set environment variables for DB and S3 (and provide the license key), run docker-compose up, and the app is running accessible on a port. We consider it passed if a non-developer admin can do this by following our guide.
License Key Check: If the license is expired (we can simulate by using an expired date in the license), the system should display a warning on the UI (e.g., banner saying "License expired, please renew"). It should continue to allow login, but perhaps disable syncing and new data fetch (read-only mode) after a grace period.
License Enforcement: The app does not allow usage without a valid license (except a trial period if provided). E.g., if no license or an invalid license is present on startup (and trial period lapsed), the app should restrict access (maybe only show a screen to upload license, or show everything but not allow adding integrations).
Support Contacts Vary by Tier: Based on a configuration (license tier), the Support page shows the correct info. For example, for Enterprise tier, it shows "Contact your account <NAME_EMAIL> or call +1-234-...". For Basic, it just shows "Email <NAME_EMAIL>". We can simulate this by loading different license types and checking the page.
Multi-tenant SaaS (if applicable): Though initially single-tenant, if we ever had to run multiple customers in one app instance, ensure tenant separation in the code (all queries scoped by tenant). However, since we choose one-instance-per-tenant, we enforce isolation at deploy level rather than code level.
User Stories & Effort Estimates:
Story: Docker/Container Packaging – Package the app and its components into Docker images, and provide a Docker Compose file or Helm chart for easy deployment. (Size: M)
AC: Running the provided Docker setup launches the app, database, (and optionally a dummy S3 service for testing like MinIO). The app comes up and is usable. Documentation covers how to set env variables (like for external DB or real S3 in production).
Story: License Key Validation Mechanism – Implement reading a license file/key on startup and validating it (could be a cryptographic signature check or a call to a license server). (Size: M)
AC: The app successfully reads a license key. If the key is valid and not expired, the app enables normal operation. If invalid/expired, it logs the issue and the UI shows the appropriate message. Use a test key to verify both flows.
Story: In-App License Management – As an admin, I can view the current license info and update the license key in the Settings page. (Size: S)
AC: The Settings page has a section to display license status (tier, expiry) and an input to upload a new license key. On uploading, the key is validated and saved (likely stored in the DB or a file). If invalid, show error.
Story: Tier-Based Support Info – The support page or section shows contact info based on support tier from the license. (Size: S)
AC: If license tier is Enterprise, show enterprise support contacts; if Basic, show basic contacts. This can be implemented via simple conditionals. Test by switching a config or license and reloading the page.
Story: Trial Mode – Allow the app to run without a license for a limited trial period (e.g., 14 days) for evaluation. (Size: S)
AC: If no license is provided on first run, the app generates a trial start timestamp. The UI should display "Trial mode - X days remaining". After the trial period, the app locks out or requires a license. Test by simulating time passage or overriding the trial period in config.
Compliance & Security
Given the sensitive nature of meeting transcripts and chat logs, security and compliance are paramount. The system must implement strong security controls and consider common compliance requirements (though specific certifications or legal compliance would be handled at the organizational level, the tool should facilitate it). Security Requirements:
Encryption at Rest:
All sensitive data in the database (like OAuth tokens, user passwords, license keys) must be encrypted or hashed. Use AES-256 for tokens, and bcrypt or similar for passwords.
For S3 storage, enable server-side encryption for all objects (SSE-S3 or SSE-KMS). The app, when uploading files, should set the flag to encrypt (if using AWS this can also be enforced at bucket level).
Encryption in Transit: Use TLS (HTTPS) for all client-server communication. Also ensure any calls from the backend to external APIs are over HTTPS. If self-hosted, provide instructions to set up SSL (maybe via a reverse proxy like Nginx or by configuring the app to serve HTTPS).
Access Control & Authentication:
The app will have at least one user (the IT Admin). Enforce strong password policy (for example, minimum length, or encourage using an SSO in future). Support a single admin login for MVP, but allow that credentials can be rotated.
All API endpoints require authentication (except maybe a health check). Use a secure session mechanism (httpOnly cookies or JWT with secure storage) to prevent XSS from stealing tokens.
Prepare for role-based access control: though MVP has one role, design the user model such that roles can be added (e.g., a role field in user table). This will ease adding read-only or multi-user support later.
Audit Logging: Maintain audit logs of key events:
Admin actions: login, logout, adding/removing integrations, changing settings, manual sync triggers.
System events: scheduled syncs (success/failure), errors (with error type), automatic retries.
The logs should include timestamp, user (if applicable), event description. These logs could be stored in a separate table or file. Consider making them append-only or at least not easily deletable via UI to prevent tampering.
Secure Coding & Dependencies:
Validate all external inputs (especially any web form inputs). Use parameterized queries or ORM to avoid SQL injection.
Sanitize any data that might be displayed in the UI to prevent XSS (particularly if transcripts contain special characters or HTML - though transcripts are mostly plain text, but one should escape any HTML just in case).
Keep third-party libraries updated and check for known vulnerabilities (use something like pip-audit or OWASP dependency check).
Data Retention & Deletion:
Provide means to comply with data deletion requests. For MVP, an admin can manually delete an integration which could optionally delete all its transcripts (we should confirm how to do this or at least document the manual process).
Ensure if a user is removed or requests their data removed, the admin can locate transcripts involving that user (via search) and delete those files/records manually. Future versions may have a more automated way.
Compliance Standards Consideration: While not implemented fully in MVP, design with common standards in mind:
GDPR: ability to delete personal data, honoring retention (maybe the filtering by date could help implement retention policies in future).
HIPAA (if applicable): if any health info might be discussed, ensure high security, possibly audit trail for access (not in MVP).
SOC 2: logging, access control, encryption, backup – many of which we cover.
Provide documentation for customers on what the app does for security (encryption, access control) to help them in their compliance reports.
Acceptance Criteria:
Verification that database entries for sensitive fields are encrypted. For example, inspect the database after adding an integration: the token stored should be encrypted (unreadable). Password fields for users should be hashed, not plaintext.
Verify that all web requests require authentication: try accessing an API endpoint (like list transcripts) without logging in or without a valid token, and confirm it returns 401/403.
Check that the audit log is recording events properly. For example, after a successful login and adding an integration, query the audit log and see entries for "login" and "added integration (Slack)".
Test password security: ensure that an extremely weak password (like "123") is either disallowed or at least warned. (If not enforcing via code in MVP, at least document recommended complexity).
Ensure the web UI does not expose sensitive data. E.g., open browser dev tools and check that responses don’t contain things like plaintext tokens or passwords. Also ensure that error messages are generic enough not to leak info (e.g., on login failure say "invalid credentials" rather than specifying if user or password was wrong).
Confirm that files in S3 are encrypted: this can be done by checking bucket settings or S3 object metadata for the "Server Side Encryption" field. For MinIO or others, ensure we have an equivalent setup.
Conduct a basic penetration test on a dev instance (even manually): try SQL injection in search fields (should be mitigated by using only allowed filters, but ensure any free text input is handled safely). Try XSS by inserting a script tag into a meeting title (simulate via an API test) and see if the UI escapes it when displaying.
User Stories & Effort Estimates:
Story: Database Encryption for Secrets – Implement encryption for sensitive fields (integration tokens, etc.) in the database. (Size: S)
AC: Introduce a secure encryption utility. When saving a token, use e.g. Fernet/AES with a key from environment. Ensure decryption works when needed. Test by checking DB content and by performing a fetch that uses a decrypted token.
Story: Secure Admin Authentication – Implement admin login with secure password storage and session management. (Size: M)
AC: Passwords stored with strong hashing (bcrypt). Login endpoint issues a secure session (JWT in httpOnly cookie or similar). Test that one cannot use the API without logging in. Include logout functionality that invalidates session.
Story: Audit Logging Mechanism – Create an audit log for user actions and key events. (Size: M)
AC: Functions that perform important actions call an audit log writer. The audit entries (at least type, user, timestamp, detail) are saved. Demonstrate by triggering a few actions and querying the log.
Story: Input Validation & Sanitization – Go through all forms and APIs to ensure inputs are validated and sanitized. (Size: M)
AC: For example, the schedule cron expression is validated for correct format, text fields are limited in length and escaped on output. Use a web security scanner on the UI to catch any XSS or SQLi vulnerability (if any).
Story: Data Deletion (Manual) – Provide a way (even if manual) for the admin to delete stored data if needed. (Size: S)
AC: At minimum, the admin can remove an integration which stops future data collection. We also give instructions that to delete existing data, they can remove it via the S3 bucket or perhaps we implement a simple "Delete transcripts older than X" function. If implementing, test that it removes the files and DB entries as expected.
Monetization
We plan to monetize the product via subscription-based licensing, with different tiers for features and support. The model will accommodate both self-hosted and SaaS customers. Subscription Tiers:
Basic (Self-Hosted License or Basic SaaS): Core functionality for one organization, with community or email support. This might be suited for small businesses or pilots. Priced lower, possibly with some limits (e.g., only 2 platform integrations or limited data volume, to encourage upgrades).
Business (SaaS or Advanced Self-Hosted): All core features plus priority support (email + chat), and possibly some advanced features (if any differentiators, e.g., more integrations, or inclusion of future AI summary feature when available). Priced at a mid-tier rate.
Enterprise: Includes all features, with premium support (24/7 support, dedicated rep, SLA) and the ability to request custom integrations or features. Pricing likely custom or high-end.
For Self-Hosted licenses, pricing is typically annual per instance (could be based on organization size or number of users in the org). For SaaS, pricing could be monthly per user or per integration:
E.g., $X per month for up to Y users (or transcripts volume), additional charge for more.
Or a flat fee per month for the service, possibly tiered by company size.
Billing Implementation:
Self-Hosted: Likely handled via direct sales or online purchase for a license key. The license key encodes what tier and expiration. The app enforcement (as described in Deployment & Hosting) will ensure they can’t use beyond expiry.
SaaS: Use an online payment system (Stripe) for recurring billing. This could be integrated into a separate billing portal or minimal integration in-app:
Possibly provide an "Upgrade/Purchase" button in the app (especially during trial) that redirects to our website or Stripe checkout.
Use Stripe subscriptions for monthly/annual plans. Webhooks from Stripe can inform our system of activation, cancellation, payment failures.
Free Trial: Offer a 14 or 30-day trial for SaaS with full functionality so customers can evaluate. No credit card required for trial (to reduce friction). After trial, they must choose a plan.
We will not implement complex in-app billing management in MVP (like changing credit cards, etc., likely handled via Stripe’s portal or by contacting support). For the license key model, it’s manual (upload new key when renewed).
Monetization-related Features in App:
UI should show the license/subscription status as mentioned.
Possibly an upgrade prompt if trial or nearing expiration.
Possibly disable certain features if they are tier-specific (though for MVP we have all features to all tiers, but in future, e.g., AI summary might be Enterprise only).
Ensure that in self-hosted mode, the app cannot be run without a valid license beyond trial. In SaaS mode, ensure only paying customers have an instance running (that’s more on ops side).
Acceptance Criteria:
A Basic tier license should be tested to ensure it enables all base features. If any feature is meant to be tier-limited, verify that with a Basic license that feature is disabled or the user is informed it's an Enterprise feature (for MVP likely all features are available, support level is main difference).
Subscription payment flow (for SaaS) is largely external, but test that when a subscription is active, the corresponding instance is running and license is valid. If a subscription is cancelled or payment fails, after grace period the instance should show as suspended (perhaps by not renewing their license or flag in DB).
Verify that entering a new license key (upgrading tier or extending date) immediately updates the app's understanding of the tier/expiry (e.g., the dashboard no longer shows expiry warning).
Ensure the app does not inadvertently send any usage data or transcripts to us (the vendor) except license checks, as customers will be sensitive about data. Monetization checks should be lightweight (e.g., verify license signature without sending details externally, or if calling home, only send license ID, not any PII).
Documentation for how to purchase/renew is clear to the admin (maybe a note in the UI like "Contact <EMAIL> to upgrade" or a link to a website for self-service).
User Stories & Effort Estimates:
Story: Implement License Tier Logic – Use the license info to toggle features/support info in the app. (Size: M)
AC: The app reads the tier from the license. For Basic tier, maybe the Support page only shows email. For Enterprise, shows phone. If in future features are tiered, those checks are in place. Test by using dummy licenses of different tiers.
Story: Trial Expiry Notification – As a prospective customer, if I’m in a trial, I should see how many days are left and what to do next. (Size: S)
AC: If trial mode and < 10 days left, display a banner like "Trial ends in X days – please add a license to continue using the product uninterrupted." No banner if fully licensed.
Story: Stripe Integration Webhook – For SaaS, set up a webhook listener (could be part of backend) for Stripe events like invoice.payment_failed or customer.subscription.deleted. (Size: M)
AC: On receiving such an event for a given customer, mark that instance or license as inactive (maybe set a flag in DB to not allow syncs and show a suspension message). Test with sample webhook payloads.
Story: Upgrade Process – Provide an in-app prompt or link for upgrading. (Size: S)
AC: For example, a "Upgrade" button during trial or on the license warning banner that takes the admin to a pricing page or contact form. Not fully automated in-app purchase for MVP, but at least a call-to-action.
Story: Usage Tracking (internal) – (Optional) Gather some basic usage metrics that could feed into billing if needed, e.g., number of transcripts per month (if we ever consider a usage-based component). (Size: S)
AC: Not exposed to user, but ensure we can query number of transcripts or data volume easily. This might just be a matter of having those metrics on the dashboard as well, and logging them for ourselves.
Future Roadmap
While the above describes the MVP scope, we have several enhancements planned for future releases to add more value to the product:
Full-Text Search Indexing: Implement full-text search across transcript contents. This could involve integrating Elasticsearch or using Postgres Full Text Search. It would allow admins or authorized users to search for keywords within meeting discussions, not just metadata. This is a significant feature for quickly finding specific discussions.
AI-Powered Summaries & Insights: Integrate AI services to automatically summarize transcripts, extract action items, or highlight important moments. Summaries could be stored alongside transcripts (in Notion pages or in the repository) for quick reading. Could use OpenAI GPT models or similar, with an option to run on-prem for privacy if needed.
Additional Integrations: Add support for other platforms:
Cisco Webex Meetings (transcripts and chat).
Webex Teams (now Webex app) or other chat services.
Google Workspace Emails or Calendar (to archive related communications, broadening beyond transcripts).
Other enterprise chat like Mattermost or RocketChat if demand exists.
Possibly unified communications systems or PBX call logs if relevant.
User Portal & Access Control: Introduce the ability for non-admin users to access the repository. For example, a team leader might search transcripts of their team’s meetings. This requires robust access control, perhaps linking with SSO/LDAP to allow login and then filtering data based on user identity (e.g., they can only see transcripts where their name is in participants, or that belong to their department).
Compliance & eDiscovery Features: Provide tools needed for legal compliance:
Legal hold: mark certain transcripts as preserved (not deletable).
Data retention rules: auto-delete or archive data older than X years.
Export functionality: export a set of transcripts (e.g., all transcripts in date range or involving a person) as a zip or PDF bundle for legal review.
Audit trail on data access: log which user viewed which transcript (important when more users have access).
Mobile App or Notifications: Possibly develop a mobile-friendly app or PWA specifically for admins to get alerts on the go (like if a sync fails or if certain keywords appear in transcripts, e.g., an alert if "urgent" was mentioned in any transcript today).
Improved Visualization & Analytics: Provide insights such as:
Which departments meet the most (based on number of transcripts).
Communication patterns (maybe network graph of which teams talk to which).
Sentiment analysis of chats (to gauge morale or client satisfaction if meetings with clients).
White-Labeling/Multi-tenant SaaS: If we pivot to offering a multi-tenant version (one app serving many orgs securely), re-architecting for multi-tenancy and perhaps offering a white-labeled UI for partners could be considered.
Plugin Marketplace: If the integration plugin system is robust, we could allow third parties or an open-source community to build custom plugins (for niche platforms) that can be plugged into the app.
Each of these roadmap items can be expanded into detailed epics and user stories when prioritized. They are noted here to guide architecture decisions in the MVP (to not design ourselves into a corner that blocks these features later).
Acceptance Criteria for PRD Completion: The PRD above thoroughly describes the MVP feature set and constraints. The engineering team will consider the product ready for initial release when all MVP user stories and acceptance criteria are met through development and testing. An IT admin should be able to install the system (or access our SaaS), configure at least one integration (e.g., Slack or Zoom), have transcripts flow into the repository, and successfully search and view a transcript – all with proper security and stability. Future enhancements will be scheduled based on customer feedback and strategic priorities once the MVP is delivered.

```

### tsconfig.json
Size: 0.64 KB

```json
{
  "include": ["client/src/**/*", "shared/**/*", "server/**/*"],
  "exclude": ["node_modules", "build", "dist", "**/*.test.ts"],
  "compilerOptions": {
    "incremental": true,
    "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo",
    "noEmit": true,
    "module": "ESNext",
    "strict": true,
    "lib": ["esnext", "dom", "dom.iterable"],
    "jsx": "preserve",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowImportingTsExtensions": true,
    "moduleResolution": "bundler",
    "baseUrl": ".",
    "types": ["node", "vite/client"],
    "paths": {
      "@/*": ["./client/src/*"],
      "@shared/*": ["./shared/*"]
    }
  }
}

```

### vite.config.ts
Size: 0.87 KB

```ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";

export default defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...(process.env.NODE_ENV !== "production" &&
    process.env.REPL_ID !== undefined
      ? [
          await import("@replit/vite-plugin-cartographer").then((m) =>
            m.cartographer(),
          ),
        ]
      : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets"),
    },
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true,
  },
});

```

### tailwind.config.ts
Size: 2.72 KB

```ts
import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: ["./client/index.html", "./client/src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
} satisfies Config;

```

### drizzle.config.ts
Size: 0.32 KB

```ts
import { defineConfig } from "drizzle-kit";

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL, ensure the database is provisioned");
}

export default defineConfig({
  out: "./migrations",
  schema: "./shared/schema.ts",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
});

```

### client/src/App.tsx
Size: 1.60 KB

```tsx
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";

// Import Pages
import Dashboard from "@/pages/dashboard";
import Integrations from "@/pages/integrations";
import IntegrationSetupPage from "@/pages/integrations/setup";
import IntegrationSetupAlt from "@/pages/integration-setup";
import Schedules from "@/pages/schedules";
import Logs from "@/pages/logs";
import Settings from "@/pages/settings";
import DebugPage from "@/pages/debug";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Dashboard} />
      <Route path="/dashboard" component={Dashboard} />
      <Route path="/integrations" component={Integrations} />
      <Route path="/integrations/:id/setup" component={IntegrationSetupPage} />
      {/* Alternative route for compatibility */}
      <Route path="/integration-setup" component={IntegrationSetupAlt} />
      <Route path="/schedules" component={Schedules} />
      <Route path="/logs" component={Logs} />
      <Route path="/settings" component={Settings} />
      <Route path="/debug" component={DebugPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;

```

### client/src/main.tsx
Size: 0.15 KB

```tsx
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

createRoot(document.getElementById("root")!).render(<App />);

```

### server/index.ts
Size: 2.22 KB

```ts
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  // Seed test data in development mode
  if (app.get("env") === "development") {
    try {
      const { seedTestData } = await import("./seed-data");
      await seedTestData();
    } catch (error) {
      console.error('Failed to seed test data:', error);
    }
  }

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();

```

### server/routes.ts
Size: 2.88 KB

```ts
import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { integrationController } from "./controllers/integration/index.js";
import { syncController } from "./controllers/sync";
import { schedulerService } from "./services/scheduler-service";
import { registerTestRoutes } from "./routes/test";
import { registerDebugRoutes } from "./routes/diagnostic";

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize the scheduler
  try {
    await schedulerService.initialize();
  } catch (error) {
    console.error('Failed to initialize scheduler:', error);
    console.log('Continuing without scheduler - manual sync will still work');
  }

  // Integration Routes
  app.get('/api/integrations', integrationController.getIntegrations);
  app.get('/api/integrations/:id', integrationController.getIntegration);
  app.post('/api/integrations', integrationController.createIntegration);
  app.put('/api/integrations/:id', integrationController.updateIntegration);
  app.delete('/api/integrations/:id', integrationController.deleteIntegration);
  
  // OAuth Routes
  app.get('/api/integrations/:id/auth-url', integrationController.getAuthUrl);
  app.get('/api/integrations/:id/oauth/callback', integrationController.handleOAuthCallback);
  
  // Global OAuth callback endpoint (for Google Cloud Console redirect URI configuration)
  app.get('/api/integrations/oauth/callback', (req, res) => {
    // Extract integration ID from state parameter
    const state = req.query.state as string;
    const stateData = state ? state.split('_') : [];
    const integrationId = stateData.length > 0 ? stateData[0] : null;
    
    if (integrationId) {
      // Redirect to the integration-specific callback
      const url = `/api/integrations/${integrationId}/oauth/callback${req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : ''}`;
      res.redirect(url);
    } else {
      res.status(400).json({ message: 'Invalid state parameter' });
    }
  });
  app.post('/api/integrations/:id/test-connection', integrationController.testConnection);
  app.get('/api/integrations/:id/folders', integrationController.getGoogleDriveFolders);
  
  // Schedule Routes
  app.post('/api/schedules', integrationController.updateSchedule);
  
  // Sync Routes
  app.get('/api/sync-logs', (req, res) => syncController.getSyncLogs(req, res));
  app.get('/api/sync-logs/:id', (req, res) => syncController.getSyncLog(req, res));
  app.post('/api/sync-now', (req, res) => syncController.syncNow(req, res));
  app.get('/api/sync-items', (req, res) => syncController.getSyncItems(req, res));

  // Test Routes (for debugging)
  registerTestRoutes(app);
  
  // Diagnostic Routes (for comprehensive debugging)
  registerDebugRoutes(app);

  const httpServer = createServer(app);

  return httpServer;
}

```

### server/db.ts
Size: 0.69 KB

```ts
import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";
import dotenv from "dotenv";
dotenv.config();

// Only configure Neon and create database connection if DATABASE_URL is provided
let pool: Pool | null = null;
let db: any = null;

if (process.env.DATABASE_URL) {
  neonConfig.webSocketConstructor = ws;
  pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = drizzle(pool, { schema });
  console.log('Database connection initialized with Neon');
} else {
  console.log('DATABASE_URL not set - using in-memory storage for development');
}

export { pool, db };
```

## Directory Structure

### client\src\pages/
- dashboard.tsx (22.96 KB)
- debug.tsx (0.44 KB)
- integration-setup.tsx (13.24 KB)
- integrations.tsx (6.06 KB)
- logs.tsx (9.19 KB)
- not-found.tsx (0.69 KB)
- schedules.tsx (9.54 KB)
- settings.tsx (16.58 KB)

### client\src\pages\integrations/
- setup.tsx (27.27 KB)

### client\src\components\debug/
- ApiTester.tsx (6.76 KB)
- SystemStatus.tsx (5.42 KB)

### client\src\components\integrations/
- IntegrationCard.tsx (12.55 KB)
- IntegrationWizard.tsx (26.36 KB)
- SyncPreview.tsx (5.25 KB)

### client\src\components\layout/
- AppLayout.tsx (0.89 KB)
- Header.tsx (2.45 KB)
- Sidebar.tsx (6.35 KB)

### client\src\components\ui/
- accordion.tsx (1.93 KB)
- alert-dialog.tsx (4.32 KB)
- alert.tsx (1.55 KB)
- aspect-ratio.tsx (0.14 KB)
- avatar.tsx (1.39 KB)
- badge.tsx (1.10 KB)
- breadcrumb.tsx (2.65 KB)
- button.tsx (1.86 KB)
- calendar.tsx (2.63 KB)
- card.tsx (1.81 KB)
- carousel.tsx (6.06 KB)
- chart.tsx (10.24 KB)
- checkbox.tsx (1.03 KB)
- collapsible.tsx (0.32 KB)
- command.tsx (4.77 KB)
- context-menu.tsx (7.25 KB)
- dialog.tsx (3.76 KB)
- drawer.tsx (2.95 KB)
- dropdown-menu.tsx (7.43 KB)
- form.tsx (4.02 KB)
- hover-card.tsx (1.22 KB)
- input-otp.tsx (2.10 KB)
- input.tsx (0.77 KB)
- label.tsx (0.69 KB)
- menubar.tsx (8.40 KB)
- navigation-menu.tsx (5.01 KB)
- pagination.tsx (2.69 KB)
- popover.tsx (1.25 KB)
- progress.tsx (0.77 KB)
- radio-group.tsx (1.43 KB)
- resizable.tsx (1.68 KB)
- scroll-area.tsx (1.60 KB)
- select.tsx (5.61 KB)
- separator.tsx (0.74 KB)
- sheet.tsx (4.18 KB)
- sidebar.tsx (23.01 KB)
- skeleton.tsx (0.25 KB)
- slider.tsx (1.05 KB)
- switch.tsx (1.11 KB)
- table.tsx (2.70 KB)
- tabs.tsx (1.84 KB)
- textarea.tsx (0.67 KB)
- toast.tsx (4.73 KB)
- toaster.tsx (0.75 KB)
- toggle-group.tsx (1.71 KB)
- toggle.tsx (1.49 KB)
- tooltip.tsx (1.18 KB)

### server\controllers/
- integration.ts (30.12 KB)
- sync.ts (19.02 KB)

### server/
- db.ts (0.69 KB)
- index.ts (2.22 KB)
- routes.ts (2.88 KB)
- seed-data.ts (2.63 KB)
- setup-notion.ts (14.35 KB)
- storage.ts (12.90 KB)
- utils.ts (4.10 KB)
- vite.ts (2.33 KB)

### server/routes/
- system.ts (System health and diagnostic routes)
- integrations.ts (OAuth and integration management)
- sync.ts (Sync schedules and logs)
- chat.ts (Chat sessions and messages)
- files.ts (File management and search)
- rag.ts (RAG search functionality)
- upload.ts (File upload services)
- legacy.ts (Legacy compatibility routes)
- test.ts (Test routes for debugging)
- diagnostic.ts (Comprehensive diagnostic routes)
- index.ts (Route registration functions)
- README.md (Route organization documentation)

### server\scripts/
- setup-google-integration.ts (2.58 KB)

### server\services/
- crypto-service.ts (4.58 KB)
- google-service.ts (27.55 KB)
- notion-service.ts (23.94 KB)
- openai-service.ts (8.12 KB)
- scheduler-service.ts (3.48 KB)
- websocket-service.ts (5.00 KB)

## File Contents

### client\src\pages\debug.tsx

```tsx
import ApiTester from '@/components/debug/ApiTester';
import SystemStatus from '@/components/debug/SystemStatus';

export default function DebugPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        <h1 className="text-3xl font-bold">System Diagnostics & Debugging</h1>
        
        <SystemStatus />
        
        <ApiTester />
      </div>
    </div>
  );
}
```

### client\src\pages\integrations.tsx

```tsx
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { getIntegrations } from "@/lib/api";
import AppLayout from "@/components/layout/AppLayout";
import IntegrationCard from "@/components/integrations/IntegrationCard";

export default function Integrations() {
  const [location, navigate] = useLocation();
  const [currentTab, setCurrentTab] = useState("All Integrations");
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: getIntegrations,
  });
  
  const integrations = data?.integrations || [];
  
  const filteredIntegrations = integrations.filter(integration => {
    if (currentTab === "All Integrations") return true;
    if (currentTab === "Connected") return integration.status === "connected" || integration.status === "configured" || integration.status === "syncing";
    if (currentTab === "Disconnected") return integration.status === "disconnected" || integration.status === "error";
    return true;
  });
  
  const handleAddIntegration = () => {
    // Create a new integration and redirect to setup
    navigate("/integrations/new/setup");
  };
  
  const handleConfigureIntegration = (id: number) => {
    navigate(`/integrations/${id}/setup`);
  };

  return (
    <AppLayout 
      title="Integrations" 
      onAddIntegration={handleAddIntegration}
      tabs={["All Integrations", "Connected", "Disconnected"]}
      currentTab={currentTab}
      onTabChange={setCurrentTab}
    >
      <section className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Active Integrations</h3>
          <div className="flex space-x-2">
            <button className="px-3 py-1.5 bg-white border border-gray-300 rounded text-sm text-gray-700 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-500" fill="none" viewBox="0 
... (truncated)
```

### client\src\pages\logs.tsx

```tsx
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getSyncLogs, getSyncLog, getSyncItems } from "@/lib/api";
import { formatDate, formatRelativeTime } from "@/lib/utils/date";
import { getIntegrationName } from "@/lib/utils/integrations";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

export default function Logs() {
  const [selectedLogId, setSelectedLogId] = useState<number | null>(null);
  
  const { data: logsData, isLoading: isLoadingLogs } = useQuery({
    queryKey: ['/api/sync-logs'],
    queryFn: () => getSyncLogs({ limit: 50 }),
  });
  
  const { data: logDetailData, isLoading: isLoadingDetail } = useQuery({
    queryKey: [`/api/sync-logs/${selectedLogId}`],
    queryFn: () => getSyncLog(selectedLogId!),
    enabled: selectedLogId !== null,
  });
  
  const logs = logsData?.logs || [];
  const selectedLog = logDetailData?.log;
  const logItems = logDetailData?.items || [];
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-500">Success</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-500">Partial</Badge>;
      case 'failed':
        return <Badge className="bg-red-500">Failed</Badge>;
      case 'running':
        return <Badge className="bg-blue-500">Running</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };
  
  return (
    <AppLayout title="Sync Logs">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Logs List */}
        <div className="lg:w-2/5">
          <Card>
            <CardHeader>
              <CardTitle>Sync History</CardTitle>
            </CardHeader>
            <CardContent className="p
... (truncated)
```

### client\src\pages\not-found.tsx

```tsx
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="pt-6">
          <div className="flex mb-4 gap-2">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <h1 className="text-2xl font-bold text-gray-900">404 Page Not Found</h1>
          </div>

          <p className="mt-4 text-sm text-gray-600">
            Did you forget to add the page to the router?
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

```

### client\src\pages\schedules.tsx

```tsx
import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getIntegrations, updateSchedule } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import AppLayout from "@/components/layout/AppLayout";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDate } from "@/lib/utils/date";
import { getIntegrationName, getIntegrationLogo } from "@/lib/utils/integrations";

export default function Schedules() {
  const { toast } = useToast();
  
  const { data, isLoading } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: getIntegrations,
  });
  
  const integrations = data?.integrations || [];
  
  const updateScheduleMutation = useMutation({
    mutationFn: ({ integrationId, schedule, enabled }: { integrationId: number, schedule: string, enabled: boolean }) => 
      updateSchedule(integrationId, schedule, enabled),
    onSuccess: () => {
      toast({
        title: "Schedule updated",
        description: "The sync schedule has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: `Failed to update schedule: ${error.message}`,
        variant: "destructive",
      });
    },
  });
  
  return (
    <AppLayout title="Schedules">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Sync Schedules</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <svg className="animate-spin h-8 w-8 text-primary mx-auto" xmlns="http://www.w3
... (truncated)
```

### client\src\components\debug\ApiTester.tsx

```tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ApiResponse {
  status: number;
  data: any;
  error?: string;
  timestamp: string;
}

export default function ApiTester() {
  const [responses, setResponses] = useState<{ [key: string]: ApiResponse }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});

  const makeRequest = async (endpoint: string, method: string = 'GET', body?: any) => {
    const key = `${method} ${endpoint}`;
    setLoading(prev => ({ ...prev, [key]: true }));

    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (body) {
        options.body = JSON.stringify(body);
      }

      const response = await fetch(endpoint, options);
      const data = await response.json();

      setResponses(prev => ({
        ...prev,
        [key]: {
          status: response.status,
          data,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (error: any) {
      setResponses(prev => ({
        ...prev,
        [key]: {
          status: 0,
          data: null,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [key]: false }));
    }
  };

  const testEndpoints = [
    {
      name: 'Health Check',
      endpoint: '/api/diagnostic/health',
      method: 'GET'
    },
    {
      name: 'Test Encryption',
      endpoint: '/api/diagnostic/test-encryption',
      method: 'POST',
      body: { testData: 'Hello, encryption test!' }
    },
    {
      name: 'Test Credential Flow',
      endpoint: '/api/diagnostic/test-credential-flow',
      method: 'POST'
... (truncated)
```

### client\src\components\debug\SystemStatus.tsx

```tsx
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface SystemHealth {
  api: 'healthy' | 'error' | 'unknown';
  storage: 'working' | 'error' | 'unknown';
  integrations: number;
  lastChecked: string;
  details?: any;
}

export default function SystemStatus() {
  const [health, setHealth] = useState<SystemHealth>({
    api: 'unknown',
    storage: 'unknown',
    integrations: 0,
    lastChecked: 'Never'
  });
  const [checking, setChecking] = useState(false);

  const checkSystemHealth = async () => {
    setChecking(true);
    try {
      // Test API health
      const healthResponse = await fetch('/api/diagnostic/health');
      const healthData = await healthResponse.json();

      // Test integrations
      const integrationsResponse = await fetch('/api/integrations');
      const integrationsData = await integrationsResponse.json();

      setHealth({
        api: healthResponse.ok ? 'healthy' : 'error',
        storage: healthData.services?.storage || 'unknown',
        integrations: integrationsData.integrations?.length || 0,
        lastChecked: new Date().toLocaleTimeString(),
        details: {
          health: healthData,
          integrations: integrationsData
        }
      });

    } catch (error: any) {
      setHealth(prev => ({
        ...prev,
        api: 'error',
        storage: 'error',
        lastChecked: new Date().toLocaleTimeString(),
        details: { error: error.message }
      }));
    } finally {
      setChecking(false);
    }
  };

  useEffect(() => {
    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);
... (truncated)
```

### client\src\components\integrations\SyncPreview.tsx

```tsx
import { Button } from "@/components/ui/button";

interface SyncPreviewProps {
  syncItem: {
    title: string;
    sourceUrl: string;
    metadata: {
      date: string;
      time: string;
      meetingName: string;
      attendees: string;
    };
    content?: {
      notes: string;
      transcript: string;
    };
  };
  onClose: () => void;
  onConfirm: () => void;
  onSkip: () => void;
}

export default function SyncPreview({
  syncItem,
  onClose,
  onConfirm,
  onSkip,
}: SyncPreviewProps) {
  const { title, sourceUrl, metadata, content } = syncItem;
  const [activeTab, setActiveTab] = React.useState("Notes");

  return (
    <section>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Sync Preview</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onClose}>
            Close Preview
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6">
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h4 className="text-base font-medium text-gray-900 mb-1">Transcript Details</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Original filename</p>
                <p className="text-sm font-medium">{title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Source</p>
                <p className="text-sm font-medium">Shared Meeting Transcripts Drive</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Extracted date</p>
                <p className="text-sm font-medium">{metadata.date}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Extracted time</p>
                <p className="text-sm font-medium">{metadata.time}</p>
          
... (truncated)
```

### client\src\components\layout\AppLayout.tsx

```tsx
import { ReactNode } from "react";
import Sidebar from "./Sidebar";
import Header from "./Header";

interface AppLayoutProps {
  children: ReactNode;
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function AppLayout({
  children,
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: AppLayoutProps) {
  return (
    <div className="bg-gray-100 h-screen flex overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          title={title} 
          onAddIntegration={onAddIntegration}
          tabs={tabs}
          currentTab={currentTab}
          onTabChange={onTabChange}
        />
        
        <main className="flex-1 overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}

```

### client\src\components\layout\Header.tsx

```tsx
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

interface HeaderProps {
  title: string;
  onAddIntegration?: () => void;
  tabs?: string[];
  currentTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function Header({
  title,
  onAddIntegration,
  tabs,
  currentTab,
  onTabChange
}: HeaderProps) {
  const [location] = useLocation();
  
  return (
    <header className="bg-white shadow-sm z-10">
      <div className="flex items-center justify-between px-6 py-3">
        <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
        <div className="flex items-center space-x-3">
          {onAddIntegration && location.includes("integrations") && (
            <button 
              onClick={onAddIntegration}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 flex items-center font-medium"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Integration
            </button>
          )}
          <button className="p-2 rounded-full hover:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
          </button>
        </div>
      </div>
      
      {tabs && tabs.length > 0 && (
        <div className="px-6 py-2 border-b border-gray-200 bg-gray-50">
          <div className="flex space-x-5">
            {tabs.map((tab) => (
        
... (truncated)
```

