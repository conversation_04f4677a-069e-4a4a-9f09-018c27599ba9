import { BaseMCPServer } from '../base-mcp.server';
import { uploadFile<PERSON><PERSON><PERSON><PERSON><PERSON>, listUploadedFiles<PERSON>ool<PERSON>and<PERSON>, getFileContentToolHandler } from './file-upload-tools';

export class FileUploadServer extends BaseMCPServer {
  constructor() {
    super('file-upload-mcp', 'File Upload MCP', '1.0.0');
  }

  protected async initializeServer(): Promise<void> {
    // Any file-upload specific initialization can go here
  }

  protected async registerTools(): Promise<void> {
    this.setupHandlers();
  }

  protected async registerResources(): Promise<void> {
    // No resources for file upload MCP (for now)
  }

  protected async registerPrompts(): Promise<void> {
    // No prompts for file upload MCP (for now)
  }

  private setupHandlers(): void {
    this.addTool(
      {
        name: 'upload_file',
        description: 'Upload and process a new file through the unified content extractor',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for file ownership' },
            fileName: { type: 'string', description: 'Name of the file' },
            fileContent: { type: 'string', description: 'Base64 encoded file content' },
            mimeType: { type: 'string', description: 'MIME type of the file' },
            processContent: { type: 'boolean', description: 'Whether to process content for AI (default: true)' }
          },
          required: ['userId', 'fileName', 'fileContent', 'mimeType']
        },
      },
      uploadFileToolHandler,
      'file-management'
    );

    this.addTool(
      {
        name: 'list_uploaded_files',
        description: 'List all uploaded files for a user with optional filtering',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID to list files for' },
            fileType: { type: 'string', description: 'Filter by file type (pdf, doc, image, etc.)' },
            maxResults: { type: 'number', description: 'Maximum number of results (default: 50)' },
            sortBy: { type: 'string', description: 'Sort by field (uploadedAt, fileName, fileSize)' }
          },
          required: ['userId']
        },
      },
      listUploadedFilesToolHandler,
      'file-management'
    );

    this.addTool(
      {
        name: 'get_file_content',
        description: 'Get the extracted content of an uploaded file',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for file ownership verification' },
            fileId: { type: 'string', description: 'File ID to retrieve content for' },
            includeMetadata: { type: 'boolean', description: 'Include file metadata (default: true)' }
          },
          required: ['userId', 'fileId']
        },
      },
      getFileContentToolHandler,
      'file-management'
    );
  }

  async start(): Promise<void> {
    await this.connect();
    // Optionally, add any startup logging here
    console.log('File Upload MCP Server started');
  }
} 