# =============================================================================
# MeetSync Microservices Architecture (Future Implementation)
# =============================================================================
# This file demonstrates how the current monolith will be split into microservices
# Each service will have its own container, database, and scaling configuration

version: '3.8'

services:
  # =============================================================================
  # API Gateway & Load Balancer
  # =============================================================================
  api-gateway:
    image: nginx:alpine
    container_name: meetsync-gateway
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/upstream.conf:/etc/nginx/conf.d/upstream.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - auth-service
      - chat-service
      - integration-service
      - file-service
    networks:
      - meetsync-mesh
    labels:
      - "service=api-gateway"
      - "tier=infrastructure"

  # =============================================================================
  # Authentication & User Management Service
  # =============================================================================
  auth-service:
    build:
      context: .
      dockerfile: docker/services/Dockerfile.auth
    container_name: meetsync-auth-service
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      NODE_ENV: production
      PORT: 8080
      SERVICE_NAME: auth-service
      DATABASE_URL: *********************************************/auth_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      OAUTH_GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      OAUTH_GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      OAUTH_MICROSOFT_CLIENT_ID: ${MICROSOFT_CLIENT_ID}
      OAUTH_MICROSOFT_CLIENT_SECRET: ${MICROSOFT_CLIENT_SECRET}
    volumes:
      - auth-logs:/app/logs
    depends_on:
      auth-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - meetsync-mesh
      - auth-internal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    labels:
      - "service=auth-service"
      - "tier=application"

  auth-db:
    image: postgres:16-alpine
    container_name: meetsync-auth-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: auth_pass
    volumes:
      - auth-db-data:/var/lib/postgresql/data
    networks:
      - auth-internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user -d auth_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Chat & RAG Service
  # =============================================================================
  chat-service:
    build:
      context: .
      dockerfile: docker/services/Dockerfile.chat
    container_name: meetsync-chat-service
    restart: unless-stopped
    ports:
      - "8082:8080"
    environment:
      NODE_ENV: production
      PORT: 8080
      SERVICE_NAME: chat-service
      DATABASE_URL: *********************************************/chat_db
      VECTOR_DB_URL: ***********************************************/vector_db
      REDIS_URL: redis://redis:6379
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      AUTH_SERVICE_URL: http://auth-service:8080
    volumes:
      - chat-logs:/app/logs
    depends_on:
      chat-db:
        condition: service_healthy
      vector-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - meetsync-mesh
      - chat-internal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    labels:
      - "service=chat-service"
      - "tier=application"

  chat-db:
    image: postgres:16-alpine
    container_name: meetsync-chat-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: chat_db
      POSTGRES_USER: chat_user
      POSTGRES_PASSWORD: chat_pass
    volumes:
      - chat-db-data:/var/lib/postgresql/data
    networks:
      - chat-internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chat_user -d chat_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  vector-db:
    image: pgvector/pgvector:pg16
    container_name: meetsync-vector-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: vector_db
      POSTGRES_USER: chat_user
      POSTGRES_PASSWORD: chat_pass
    volumes:
      - vector-db-data:/var/lib/postgresql/data
      - ./docker/postgres/init-vector.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    networks:
      - chat-internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chat_user -d vector_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Integration Service (Google Drive, Microsoft Teams, Notion)
  # =============================================================================
  integration-service:
    build:
      context: .
      dockerfile: docker/services/Dockerfile.integration
    container_name: meetsync-integration-service
    restart: unless-stopped
    ports:
      - "8083:8080"
    environment:
      NODE_ENV: production
      PORT: 8080
      SERVICE_NAME: integration-service
      DATABASE_URL: ******************************************************************/integration_db
      REDIS_URL: redis://redis:6379
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      MICROSOFT_CLIENT_ID: ${MICROSOFT_CLIENT_ID}
      MICROSOFT_CLIENT_SECRET: ${MICROSOFT_CLIENT_SECRET}
      MICROSOFT_TENANT_ID: ${MICROSOFT_TENANT_ID}
      NOTION_API_KEY: ${NOTION_API_KEY}
      AUTH_SERVICE_URL: http://auth-service:8080
      FILE_SERVICE_URL: http://file-service:8080
    volumes:
      - integration-logs:/app/logs
    depends_on:
      integration-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - meetsync-mesh
      - integration-internal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.75'
        reservations:
          memory: 512M
          cpus: '0.5'
    labels:
      - "service=integration-service"
      - "tier=application"

  integration-db:
    image: postgres:16-alpine
    container_name: meetsync-integration-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: integration_db
      POSTGRES_USER: integration_user
      POSTGRES_PASSWORD: integration_pass
    volumes:
      - integration-db-data:/var/lib/postgresql/data
    networks:
      - integration-internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U integration_user -d integration_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # File Processing Service
  # =============================================================================
  file-service:
    build:
      context: .
      dockerfile: docker/services/Dockerfile.file
    container_name: meetsync-file-service
    restart: unless-stopped
    ports:
      - "8084:8080"
    environment:
      NODE_ENV: production
      PORT: 8080
      SERVICE_NAME: file-service
      DATABASE_URL: *********************************************/file_db
      REDIS_URL: redis://redis:6379
      S3_BUCKET: ${S3_BUCKET}
      S3_ACCESS_KEY: ${S3_ACCESS_KEY}
      S3_SECRET_KEY: ${S3_SECRET_KEY}
      AUTH_SERVICE_URL: http://auth-service:8080
    volumes:
      - file-storage:/app/uploads
      - file-logs:/app/logs
    depends_on:
      file-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - meetsync-mesh
      - file-internal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.75'
        reservations:
          memory: 512M
          cpus: '0.5'
    labels:
      - "service=file-service"
      - "tier=application"

  file-db:
    image: postgres:16-alpine
    container_name: meetsync-file-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: file_db
      POSTGRES_USER: file_user
      POSTGRES_PASSWORD: file_pass
    volumes:
      - file-db-data:/var/lib/postgresql/data
    networks:
      - file-internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U file_user -d file_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Frontend Service (React App)
  # =============================================================================
  frontend-service:
    build:
      context: .
      dockerfile: docker/services/Dockerfile.frontend
    container_name: meetsync-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      REACT_APP_API_URL: http://localhost/api
      REACT_APP_WS_URL: ws://localhost/ws
    volumes:
      - frontend-logs:/app/logs
    networks:
      - meetsync-mesh
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    labels:
      - "service=frontend"
      - "tier=presentation"

  # =============================================================================
  # Shared Infrastructure
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: meetsync-redis-cluster
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 1gb --maxmemory-policy allkeys-lru
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis-cluster-data:/data
    networks:
      - meetsync-mesh
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # =============================================================================
  # Service Discovery & Configuration
  # =============================================================================
  consul:
    image: consul:latest
    container_name: meetsync-consul
    restart: unless-stopped
    ports:
      - "8500:8500"
    command: consul agent -server -bootstrap-expect=1 -data-dir=/consul/data -config-dir=/consul/config -ui -bind=0.0.0.0 -client=0.0.0.0
    volumes:
      - consul-data:/consul/data
      - ./docker/consul:/consul/config:ro
    networks:
      - meetsync-mesh
    labels:
      - "service=service-discovery"
      - "tier=infrastructure"

  # =============================================================================
  # Monitoring & Observability
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: meetsync-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus-microservices.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/monitoring/prometheus-microservices.yml:/etc/prometheus/prometheus-microservices.yml:ro
      - prometheus-data:/prometheus
    networks:
      - meetsync-mesh
    labels:
      - "service=monitoring"
      - "tier=infrastructure"

  grafana:
    image: grafana/grafana:latest
    container_name: meetsync-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/monitoring/grafana/microservices:/etc/grafana/provisioning:ro
    networks:
      - meetsync-mesh
    depends_on:
      - prometheus
    labels:
      - "service=monitoring"
      - "tier=infrastructure"

  # =============================================================================
  # Message Queue (for async processing)
  # =============================================================================
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: meetsync-rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-admin}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS:-admin123}
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    networks:
      - meetsync-mesh
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    labels:
      - "service=message-queue"
      - "tier=infrastructure"

# =============================================================================
# Networks
# =============================================================================
networks:
  # Main service mesh network
  meetsync-mesh:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  # Service-specific internal networks
  auth-internal:
    driver: bridge
    internal: true
  
  chat-internal:
    driver: bridge
    internal: true
  
  integration-internal:
    driver: bridge
    internal: true
  
  file-internal:
    driver: bridge
    internal: true

# =============================================================================
# Volumes
# =============================================================================
volumes:
  # Database volumes
  auth-db-data:
  chat-db-data:
  vector-db-data:
  integration-db-data:
  file-db-data:
  
  # Application data volumes
  file-storage:
  redis-cluster-data:
  consul-data:
  prometheus-data:
  grafana-data:
  rabbitmq-data:
  
  # Log volumes
  auth-logs:
  chat-logs:
  integration-logs:
  file-logs:
  frontend-logs: