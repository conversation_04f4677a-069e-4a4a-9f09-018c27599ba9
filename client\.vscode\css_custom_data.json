{"version": 1.1, "atDirectives": [{"name": "@tailwind", "description": "Use the @tailwind directive to insert Tailwind's base, components, utilities and variants styles into your CSS."}, {"name": "@apply", "description": "Use @apply to inline any existing utility classes into your own custom CSS."}, {"name": "@layer", "description": "Use the @layer directive to tell Tailwind which \"bucket\" a set of custom styles belong to."}, {"name": "@variants", "description": "You can generate variants of your own utilities by wrapping their definitions in the @variants directive"}, {"name": "@responsive", "description": "You can generate responsive variants of your own classes by wrapping their definitions in the @responsive directive"}, {"name": "@screen", "description": "The @screen directive allows you to create media queries that reference your breakpoints by name"}]}