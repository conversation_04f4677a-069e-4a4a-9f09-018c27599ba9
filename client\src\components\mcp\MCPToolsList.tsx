import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Zap, 
  Clock, 
  TrendingUp,
  Filter,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { MCPTool, MCPServer } from './types';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface MCPToolsListProps {
  servers: MCPServer[];
  className?: string;
}

export function MCPToolsList({ servers, className = "" }: MCPToolsListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedServer, setSelectedServer] = useState<string>('all');
  const [expandedServers, setExpandedServers] = useState<Set<string>>(new Set());

  // Get all tools from all servers
  const allTools = servers.flatMap(server => 
    server.tools.map(tool => ({
      ...tool,
      serverId: server.id,
      serverName: server.name,
      serverType: server.type,
      serverConnected: server.status.connected
    }))
  );

  // Filter tools based on search and server selection
  const filteredTools = allTools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesServer = selectedServer === 'all' || tool.serverId === selectedServer;
    return matchesSearch && matchesServer;
  });

  // Group tools by server
  const toolsByServer = servers.reduce((acc, server) => {
    const serverTools = server.tools.filter(tool => {
      const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           tool.description.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesSearch;
    });
    
    if (serverTools.length > 0) {
      acc[server.id] = {
        server,
        tools: serverTools
      };
    }
    return acc;
  }, {} as Record<string, { server: MCPServer; tools: MCPTool[] }>);

  const toggleServerExpansion = (serverId: string) => {
    const newExpanded = new Set(expandedServers);
    if (newExpanded.has(serverId)) {
      newExpanded.delete(serverId);
    } else {
      newExpanded.add(serverId);
    }
    setExpandedServers(newExpanded);
  };

  const getServerIcon = (type: string) => {
    switch (type) {
      case 'google-drive': return '📁';
      case 'microsoft-teams': return '👥';
      case 'file-upload': return '📤';
      default: return '🔧';
    }
  };

  const getToolIcon = (toolName: string) => {
    if (toolName.includes('search')) return '🔍';
    if (toolName.includes('list')) return '📋';
    if (toolName.includes('get') || toolName.includes('read')) return '📖';
    if (toolName.includes('create') || toolName.includes('upload')) return '✨';
    if (toolName.includes('delete')) return '🗑️';
    return '⚡';
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-blue-500" />
            <span>Available Tools</span>
            <Badge variant="secondary" className="ml-2">
              {filteredTools.length}
            </Badge>
          </CardTitle>
        </div>
        
        {/* Search and Filter Controls */}
        <div className="flex space-x-2 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search tools..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={selectedServer}
            onChange={(e) => setSelectedServer(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Servers</option>
            {servers.map(server => (
              <option key={server.id} value={server.id}>
                {server.name}
              </option>
            ))}
          </select>
        </div>
      </CardHeader>
      
      <CardContent>
        {Object.keys(toolsByServer).length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Zap className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <div className="font-medium">No tools found</div>
            <div className="text-sm">Try adjusting your search or check server connections</div>
          </div>
        ) : (
          <div className="space-y-4">
            {Object.entries(toolsByServer).map(([serverId, { server, tools }]) => (
              <div key={serverId} className="border border-gray-200 rounded-lg">
                {/* Server Header */}
                <button
                  onClick={() => toggleServerExpansion(serverId)}
                  className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-lg">{getServerIcon(server.type)}</div>
                    <div className="text-left">
                      <div className="font-medium">{server.name}</div>
                      <div className="text-sm text-gray-500">
                        {tools.length} tool{tools.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {server.status.connected ? (
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      ) : (
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      )}
                    </div>
                  </div>
                  
                  {expandedServers.has(serverId) ? (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                
                {/* Tools List */}
                {expandedServers.has(serverId) && (
                  <div className="border-t border-gray-200 bg-gray-50">
                    {tools.map((tool, index) => (
                      <div key={`${serverId}-${tool.name}`} className="px-4 py-3 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <div className="text-sm mt-0.5">{getToolIcon(tool.name)}</div>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-sm">{tool.name}</div>
                              <div className="text-xs text-gray-600 mt-1 line-clamp-2">
                                {tool.description}
                              </div>
                              
                              {/* Tool Stats */}
                              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                {tool.usageCount !== undefined && (
                                  <div className="flex items-center space-x-1">
                                    <TrendingUp className="w-3 h-3" />
                                    <span>{tool.usageCount} uses</span>
                                  </div>
                                )}
                                
                                {tool.lastUsed && (
                                  <div className="flex items-center space-x-1">
                                    <Clock className="w-3 h-3" />
                                    <span>
                                      Last used {formatDistanceToNow(tool.lastUsed, { addSuffix: true })}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
