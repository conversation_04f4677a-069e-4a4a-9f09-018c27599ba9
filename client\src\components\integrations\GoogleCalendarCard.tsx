import React from 'react';
import { GoogleServiceCard } from './GoogleServiceCard';
import { 
  RefreshCwIcon, 
  DownloadIcon,
  ClockIcon,
  UsersIcon,
  VideoIcon
} from 'lucide-react';
import { IntegrationIcon } from '@/components/ui/integration-icons';
import { useToast } from '../../hooks/use-toast';

interface GoogleCalendarCardProps {
  integrationId: number;
  isConnected: boolean;
  onStatusChange?: (connected: boolean) => void;
  onConnect?: () => Promise<void>;
}

export function GoogleCalendarCard({
  integrationId,
  isConnected,
  onStatusChange,
  onConnect,
}: GoogleCalendarCardProps) {
  const { toast } = useToast();

  const syncRecentEvents = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/calendar/sync-recent`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Calendar Sync Started",
          description: "Syncing recent calendar events",
        });
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to sync calendar events",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sync calendar events",
        variant: "destructive",
      });
    }
  };

  const syncAllEvents = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/calendar/sync-all`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Full Calendar Sync Started",
          description: "This may take several minutes for large calendars",
        });
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to start full calendar sync",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start full calendar sync",
        variant: "destructive",
      });
    }
  };

  const manageCalendars = async () => {
    // Navigate to calendar management
    window.location.href = `/integrations/${integrationId}/calendar/manage`;
  };

  const actions = [
    {
      label: "Sync Recent",
      action: syncRecentEvents,
      variant: "default" as const,
      icon: <RefreshCwIcon className="h-3 w-3" />,
    },
    {
      label: "Sync All",
      action: syncAllEvents,
      variant: "outline" as const,
      icon: <DownloadIcon className="h-3 w-3" />,
    },
    {
      label: "Manage Calendars",
      action: manageCalendars,
      variant: "outline" as const,
      icon: <IntegrationIcon type="google-calendar" size={16} />,
    },
  ];

  const renderStats = (stats: any) => (
    <div className="grid grid-cols-2 gap-4 text-sm">
      {stats.totalEvents !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.totalEvents.toLocaleString()}</div>
          <div className="text-gray-500">Total Events</div>
        </div>
      )}
      {stats.upcomingEvents !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.upcomingEvents.toLocaleString()}</div>
          <div className="text-gray-500">Upcoming</div>
        </div>
      )}
      {stats.meetingEvents !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg flex items-center justify-center space-x-1">
            <VideoIcon className="h-4 w-4 text-blue-500" />
            <span>{stats.meetingEvents.toLocaleString()}</span>
          </div>
          <div className="text-gray-500">Meetings</div>
        </div>
      )}
      {stats.allDayEvents !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg flex items-center justify-center space-x-1">
            <ClockIcon className="h-4 w-4 text-green-500" />
            <span>{stats.allDayEvents.toLocaleString()}</span>
          </div>
          <div className="text-gray-500">All Day</div>
        </div>
      )}
      {stats.recurringEvents !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.recurringEvents.toLocaleString()}</div>
          <div className="text-gray-500">Recurring</div>
        </div>
      )}
      {stats.calendarsCount !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.calendarsCount.toLocaleString()}</div>
          <div className="text-gray-500">Calendars</div>
        </div>
      )}
    </div>
  );

  const renderProfile = (profile: any) => (
    <div className="flex items-center space-x-3">
      <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
        <IntegrationIcon type="google-calendar" size={16} />
      </div>
      <div>
        <div className="font-medium">{profile.user?.displayName || profile.user?.email}</div>
        {profile.user?.displayName && profile.user?.email && (
          <div className="text-sm text-gray-500">{profile.user.email}</div>
        )}
        {profile.timeZone && (
          <div className="text-xs text-gray-400">
            Time zone: {profile.timeZone}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <GoogleServiceCard
      serviceName="Google Calendar"
      serviceType="google_calendar"
      icon={
        <div className="h-10 w-10 bg-yellow-100 rounded-lg flex items-center justify-center">
          <IntegrationIcon type="google-calendar" size={24} />
        </div>
      }
      description="Sync and search your Google Calendar events"
      integrationId={integrationId}
      isConnected={isConnected}
      onStatusChange={onStatusChange}
      onConnect={onConnect}
      endpoints={{
        profile: "calendar/profile",
        stats: "calendar/stats",
        test: "calendar/test",
      }}
      actions={actions}
      renderStats={renderStats}
      renderProfile={renderProfile}
    />
  );
} 