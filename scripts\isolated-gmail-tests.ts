#!/usr/bin/env npx tsx

/**
 * Isolated Gmail Integration Tests
 * Tests all Gmail functionality in complete isolation
 */

import dotenv from 'dotenv';
dotenv.config();

// Test Results Interface
interface TestResult {
  component: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message?: string;
  error?: string;
}

class GmailIsolationTester {
  private results: TestResult[] = [];

  private log(message: string, type: 'info' | 'success' | 'error' | 'warn' = 'info') {
    const symbols = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      warn: '⚠️'
    };
    console.log(`${symbols[type]} ${message}`);
  }

  private addResult(component: string, test: string, status: 'PASS' | 'FAIL' | 'SKIP', message?: string, error?: string) {
    this.results.push({ component, test, status, message, error });
  }

  /**
   * Test 1: Gmail Service Class Import and Instantiation
   */
  async testGmailServiceImport() {
    this.log('Testing Gmail Service Import...', 'info');
    
    try {
      const { GmailService } = await import('../server/services/platform-integrations/google/gmail.service.js');
      const gmailService = new GmailService();
      
      // Test service properties
      if (gmailService && typeof gmailService.initialize === 'function') {
        this.addResult('GmailService', 'Import & Instantiation', 'PASS', 'Service imported and instantiated successfully');
        this.log('Gmail Service imported successfully', 'success');
        return gmailService;
      } else {
        throw new Error('Service missing required methods');
      }
    } catch (error: any) {
      this.addResult('GmailService', 'Import & Instantiation', 'FAIL', undefined, error.message);
      this.log(`Gmail Service import failed: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * Test 2: Gmail OAuth Service Import and Instantiation
   */
  async testGmailOAuthService() {
    this.log('Testing Gmail OAuth Service...', 'info');
    
    try {
      const { GoogleOAuthService } = await import('../server/services/platform-integrations/google/oauth.service.js');
      const oauthService = new GoogleOAuthService();
      
      // Test required scopes include Gmail
      if (oauthService && typeof oauthService.getRequiredScopes === 'function') {
        const scopes = oauthService.getRequiredScopes();
        const hasGmailScopes = scopes.some(scope => scope.includes('gmail'));
        
        if (hasGmailScopes) {
          this.addResult('GoogleOAuthService', 'Gmail Scopes', 'PASS', `Gmail scopes found: ${scopes.filter(s => s.includes('gmail')).join(', ')}`);
          this.log('OAuth Service has Gmail scopes configured', 'success');
          return oauthService;
        } else {
          throw new Error('Gmail scopes not found in OAuth configuration');
        }
      } else {
        throw new Error('OAuth service missing required methods');
      }
    } catch (error: any) {
      this.addResult('GoogleOAuthService', 'Gmail Scopes', 'FAIL', undefined, error.message);
      this.log(`OAuth Service test failed: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * Test 3: Gmail Controller Import and Instantiation
   */
  async testGmailController() {
    this.log('Testing Gmail Controller...', 'info');
    
    try {
      const { GoogleGmailController } = await import('../server/controllers/integration/google/google-gmail.controller.js');
      const controller = new GoogleGmailController();
      
      // Test controller methods exist
      const requiredMethods = ['getGmailProfile', 'syncAllEmails', 'syncRecentEmails', 'getEmailStats', 'testGmailConnection'];
      const methodsExist = requiredMethods.every(method => typeof controller[method] === 'function');
      
      if (methodsExist) {
        this.addResult('GoogleGmailController', 'Methods', 'PASS', `All required methods present: ${requiredMethods.join(', ')}`);
        this.log('Gmail Controller has all required methods', 'success');
        return controller;
      } else {
        throw new Error('Controller missing required methods');
      }
    } catch (error: any) {
      this.addResult('GoogleGmailController', 'Methods', 'FAIL', undefined, error.message);
      this.log(`Gmail Controller test failed: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * Test 4: Email Storage Service
   */
  async testEmailStorage() {
    this.log('Testing Email Storage...', 'info');
    
    try {
      const { EmailStorage } = await import('../server/storage/features/email.storage.js');
      const emailStorage = new EmailStorage();
      
      // Test database connection and email retrieval
      const emails = await emailStorage.getEmails('gmail', 'test-user', 1, 5);
      
      this.addResult('EmailStorage', 'Gmail Email Retrieval', 'PASS', `Retrieved ${emails.length} Gmail emails`);
      this.log(`Email Storage working - found ${emails.length} Gmail emails`, 'success');
      return emailStorage;
    } catch (error: any) {
      this.addResult('EmailStorage', 'Gmail Email Retrieval', 'FAIL', undefined, error.message);
      this.log(`Email Storage test failed: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * Test 5: Gmail Service Methods (Mocked)
   */
  async testGmailServiceMethods(gmailService: any) {
    if (!gmailService) {
      this.addResult('GmailService', 'Method Testing', 'SKIP', 'Service not available');
      return;
    }

    this.log('Testing Gmail Service Methods...', 'info');

    try {
      // Test method existence
      const methods = [
        'extractEmailContent',
        'hasAttachments',
        'buildEmailQuery',
        'getGmailProfile'
      ];

      const methodTests = methods.map(methodName => {
        if (typeof gmailService[methodName] === 'function') {
          return { method: methodName, exists: true };
        }
        return { method: methodName, exists: false };
      });

      const allMethodsExist = methodTests.every(test => test.exists);
      
      if (allMethodsExist) {
        this.addResult('GmailService', 'Method Existence', 'PASS', `All methods present: ${methods.join(', ')}`);
        this.log('All Gmail Service methods exist', 'success');
      } else {
        const missingMethods = methodTests.filter(test => !test.exists).map(test => test.method);
        throw new Error(`Missing methods: ${missingMethods.join(', ')}`);
      }

      // Test extractEmailContent with mock data
      try {
        const mockPayload = {
          mimeType: 'text/plain',
          body: { data: Buffer.from('Test email content').toString('base64') },
          parts: []
        };
        
        const content = gmailService.extractEmailContent(mockPayload);
        if (content && content.includes('Test email content')) {
          this.addResult('GmailService', 'Content Extraction', 'PASS', 'Successfully extracted content from mock payload');
          this.log('Content extraction working correctly', 'success');
        } else {
          throw new Error('Content extraction returned unexpected result');
        }
      } catch (error: any) {
        this.addResult('GmailService', 'Content Extraction', 'FAIL', undefined, error.message);
        this.log(`Content extraction test failed: ${error.message}`, 'warn');
      }

    } catch (error: any) {
      this.addResult('GmailService', 'Method Testing', 'FAIL', undefined, error.message);
      this.log(`Gmail Service method testing failed: ${error.message}`, 'error');
    }
  }

  /**
   * Test 6: Database Schema Validation
   */
  async testDatabaseSchema() {
    this.log('Testing Database Schema for Gmail...', 'info');

    try {
      // Test if we can access the database tables used by Gmail
      const { EmailStorage } = await import('../server/storage/features/email.storage.js');
      const emailStorage = new EmailStorage();
      
      // Test email table access
      try {
        const testUserId = 'test-user-schema';
        
        // Try to query emails (this will test table structure)
        const emails = await emailStorage.getEmails('gmail', testUserId, 1, 1);
        
        this.addResult('Database', 'Email Table Schema', 'PASS', 'Email table accessible and queryable');
        this.log('Database schema validation passed', 'success');
        
        // Test email search functionality
        const searchResults = await emailStorage.searchEmails('test query', testUserId, '5');
        this.addResult('Database', 'Email Search Schema', 'PASS', 'Email search functionality accessible');
        this.log('Email search table validation passed', 'success');
        
      } catch (dbError: any) {
        this.addResult('Database', 'Schema Access', 'FAIL', undefined, dbError.message);
        this.log(`Database schema test failed: ${dbError.message}`, 'error');
      }
      
    } catch (error: any) {
      this.addResult('Database', 'Connection', 'FAIL', undefined, error.message);
      this.log(`Database connection failed: ${error.message}`, 'error');
    }
  }

  /**
   * Test 7: Environment Configuration
   */
  async testEnvironmentConfig() {
    this.log('Testing Environment Configuration...', 'info');

    try {
      const requiredEnvVars = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'DATABASE_URL',
        'OPENAI_API_KEY'
      ];

      const envStatus = requiredEnvVars.map(varName => ({
        var: varName,
        exists: !!process.env[varName],
        hasValue: !!(process.env[varName] && process.env[varName].length > 0)
      }));

      const allEnvVarsPresent = envStatus.every(status => status.exists && status.hasValue);
      
      if (allEnvVarsPresent) {
        this.addResult('Environment', 'Required Variables', 'PASS', 'All required environment variables present');
        this.log('Environment configuration is complete', 'success');
      } else {
        const missing = envStatus.filter(status => !status.exists || !status.hasValue)
          .map(status => status.var);
        this.addResult('Environment', 'Required Variables', 'FAIL', undefined, `Missing or empty: ${missing.join(', ')}`);
        this.log(`Missing environment variables: ${missing.join(', ')}`, 'warn');
      }

    } catch (error: any) {
      this.addResult('Environment', 'Configuration', 'FAIL', undefined, error.message);
      this.log(`Environment test failed: ${error.message}`, 'error');
    }
  }

  /**
   * Test 8: Mock Gmail API Integration
   */
  async testMockGmailAPI() {
    this.log('Testing Mock Gmail API Integration...', 'info');

    try {
      // Test if we can create a mock OAuth client
      const mockCredentials = {
        access_token: 'mock_token',
        refresh_token: 'mock_refresh',
        expiry_date: Date.now() + 3600000,
        token_type: 'Bearer'
      };

      // Test OAuth2Client creation
      const { OAuth2Client } = await import('google-auth-library');
      const oauth2Client = new OAuth2Client(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        'http://localhost:8080/callback'
      );

      oauth2Client.setCredentials(mockCredentials);

      this.addResult('MockAPI', 'OAuth2Client Creation', 'PASS', 'Mock OAuth2Client created successfully');
      this.log('Mock Gmail API client created successfully', 'success');

      // Test Gmail API client creation
      const { google } = await import('googleapis');
      const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

      if (gmail && gmail.users) {
        this.addResult('MockAPI', 'Gmail API Client', 'PASS', 'Gmail API client created with auth');
        this.log('Gmail API client configuration successful', 'success');
      } else {
        throw new Error('Gmail API client creation failed');
      }

    } catch (error: any) {
      this.addResult('MockAPI', 'Integration Test', 'FAIL', undefined, error.message);
      this.log(`Mock Gmail API test failed: ${error.message}`, 'error');
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    this.log('🧪 Starting Isolated Gmail Integration Tests', 'info');
    console.log('='.repeat(60));

    // Run all tests
    const gmailService = await this.testGmailServiceImport();
    const oauthService = await this.testGmailOAuthService();
    const controller = await this.testGmailController();
    const emailStorage = await this.testEmailStorage();
    
    await this.testGmailServiceMethods(gmailService);
    await this.testDatabaseSchema();
    await this.testEnvironmentConfig();
    await this.testMockGmailAPI();

    // Print results
    this.printResults();
  }

  /**
   * Print test results
   */
  private printResults() {
    console.log('\n' + '='.repeat(60));
    this.log('📊 Gmail Integration Test Results', 'info');
    console.log('='.repeat(60));

    const componentGroups = this.results.reduce((groups, result) => {
      if (!groups[result.component]) {
        groups[result.component] = [];
      }
      groups[result.component].push(result);
      return groups;
    }, {} as Record<string, TestResult[]>);

    Object.entries(componentGroups).forEach(([component, tests]) => {
      console.log(`\n📦 ${component}:`);
      tests.forEach(test => {
        const statusSymbol = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⏸️';
        console.log(`  ${statusSymbol} ${test.test}`);
        if (test.message) {
          console.log(`      💬 ${test.message}`);
        }
        if (test.error) {
          console.log(`      🔍 Error: ${test.error}`);
        }
      });
    });

    // Summary
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const skippedTests = this.results.filter(r => r.status === 'SKIP').length;

    console.log('\n' + '='.repeat(60));
    console.log(`📈 Test Summary:`);
    console.log(`  Total Tests: ${totalTests}`);
    console.log(`  ✅ Passed: ${passedTests}`);
    console.log(`  ❌ Failed: ${failedTests}`);
    console.log(`  ⏸️ Skipped: ${skippedTests}`);
    console.log(`  📊 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

    if (failedTests === 0) {
      this.log('🎉 All Gmail integration tests passed!', 'success');
    } else {
      this.log(`⚠️ ${failedTests} tests failed. Review errors above.`, 'warn');
    }

    console.log('='.repeat(60));
  }
}

// Run the tests
async function main() {
  const tester = new GmailIsolationTester();
  await tester.runAllTests();
  process.exit(0);
}

main().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
}); 