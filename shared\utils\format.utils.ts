/**
 * Formatting utility functions
 */

// Format file size in human readable format
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Format date in relative time (e.g., "2 hours ago")
export const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
  
  return `${Math.floor(diffInSeconds / 31536000)} years ago`;
};

// Format date in standard format
export const formatDate = (date: Date, includeTime = false): string => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  
  if (includeTime) {
    options.hour = '2-digit';
    options.minute = '2-digit';
  }
  
  return date.toLocaleDateString('en-US', options);
};

// Format duration in human readable format
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};

// Truncate text with ellipsis
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

// Format platform name for display
export const formatPlatformName = (platform: string): string => {
  const platformNames: Record<string, string> = {
    'google_drive': 'Google Drive',
    'microsoft_teams': 'Microsoft Teams',
    'slack': 'Slack',
    'zoom': 'Zoom',
    'uploaded_files': 'Uploaded Files',
    'notion': 'Notion',
  };
  
  return platformNames[platform] || platform;
};

// Format integration status for display
export const formatIntegrationStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'connected': 'Connected',
    'disconnected': 'Disconnected',
    'error': 'Error',
    'configured': 'Configured',
    'pending': 'Pending',
  };
  
  return statusMap[status] || status;
};

// Format file type for display
export const formatFileType = (fileType: string): string => {
  const typeMap: Record<string, string> = {
    'transcript': 'Transcript',
    'doc': 'Document',
    'ppt': 'Presentation',
    'pdf': 'PDF',
    'video': 'Video',
    'audio': 'Audio',
    'image': 'Image',
    'text': 'Text',
    'spreadsheet': 'Spreadsheet',
  };
  
  return typeMap[fileType] || fileType;
};

// Format token count
export const formatTokenCount = (tokens: number): string => {
  if (tokens < 1000) return `${tokens} tokens`;
  if (tokens < 1000000) return `${(tokens / 1000).toFixed(1)}K tokens`;
  return `${(tokens / 1000000).toFixed(1)}M tokens`;
};

// Format similarity score as percentage
export const formatSimilarity = (similarity: number): string => {
  return `${Math.round(similarity * 100)}%`;
};

// Capitalize first letter
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Convert camelCase to Title Case
export const camelToTitle = (str: string): string => {
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
};

// Format error message for display
export const formatErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
};
