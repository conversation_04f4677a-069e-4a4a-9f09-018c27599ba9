import type { Express } from "express";

/**
 * Register legacy compatibility routes
 */
export function registerLegacyRoutes(app: Express) {
  // Chat sessions endpoint alias
  app.get('/api/chat-sessions', async (req, res) => {
    // Redirect to the correct chat sessions endpoint
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");
      const sessions = await storage.getChatSessions();
      res.json({ sessions, count: sessions.length });
    } catch (error: any) {
      console.error('Error getting chat sessions:', error);
      res.status(500).json({ message: 'Failed to get chat sessions', error: error.message });
    }
  });

  // Projects endpoint (placeholder)
  app.get('/api/projects', async (req, res) => {
    try {
      // For now, return empty projects list
      // TODO: Implement proper projects functionality
      res.json({ projects: [], count: 0 });
    } catch (error: any) {
      console.error('Error getting projects:', error);
      res.status(500).json({ message: 'Failed to get projects', error: error.message });
    }
  });
} 