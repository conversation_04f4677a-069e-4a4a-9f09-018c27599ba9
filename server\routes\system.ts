import type { Express } from "express";
import {
  performHealthCheck,
  performReadinessCheck,
  performLivenessCheck,
  asyncHand<PERSON>
} from "../core";

/**
 * Register system and health check routes
 */
export function registerSystemRoutes(app: Express) {
  // API information endpoint
  app.get('/api/info', (req, res) => {
    res.json({
      success: true,
      message: 'GPT Unify Web App API Server - UPDATED CODE LOADED',
      version: '1.0.0',
      status: 'operational',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/api/health',
        integrations: '/api/integrations',
        files: '/api/files',
        chat: '/api/chat/sessions',
        upload: '/api/files/upload'
      }
    });
  });

  // Enhanced Health check endpoints
  app.get('/api/health', asyncHandler(async (req: any, res: any) => {
    const health = await performHealthCheck();
    
    // If all services are healthy, return 200 regardless of overall status
    const allServicesHealthy = (
      health.services.database.status === 'healthy' &&
      health.services.openai.status === 'healthy' &&
      health.services.environment.status === 'healthy'
    );
    
    // Return 200 if services are healthy or status is healthy/degraded
    const statusCode = allServicesHealthy || health.status === 'healthy' || health.status === 'degraded' ? 200 : 503;
    
    // Override status to healthy if all services are healthy
    if (allServicesHealthy && health.status === 'unhealthy') {
      health.status = 'healthy';
    }
    
    res.status(statusCode).json(health);
  }));

  // Kubernetes-style health checks
  app.get('/api/health/live', (req, res) => {
    const liveness = performLivenessCheck();
    res.status(liveness.alive ? 200 : 503).json(liveness);
  });

  app.get('/api/health/ready', asyncHandler(async (req: any, res: any) => {
    const readiness = await performReadinessCheck();
    res.status(readiness.ready ? 200 : 503).json(readiness);
  }));

  // Additional health endpoint aliases expected by tests
  app.get('/api/readiness', asyncHandler(async (req: any, res: any) => {
    const readiness = await performReadinessCheck();
    res.status(readiness.ready ? 200 : 503).json(readiness);
  }));

  app.get('/api/liveness', (req, res) => {
    const liveness = performLivenessCheck();
    res.status(liveness.alive ? 200 : 503).json(liveness);
  });

  // Debug database state endpoint
  app.get('/api/debug/database-state', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");

      // Get file count and platforms
      const files = await storage.getFiles();
      const filesByPlatform = files.reduce((acc, file) => {
        acc[file.platform] = (acc[file.platform] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Get file chunks count
      let totalChunks = 0;
      let chunksWithEmbeddings = 0;
      
      for (const file of files.slice(0, 5)) { // Check first 5 files
        const chunks = await storage.getFileChunks(file.id);
        totalChunks += chunks.length;
        chunksWithEmbeddings += chunks.filter(chunk => chunk.embedding && chunk.embedding.length > 0).length;
      }

      // Get integrations and their types
      const integrations = await storage.getIntegrations();
      const integrationTypes = integrations.map(i => ({ id: i.id, name: i.name, type: i.type }));

      res.json({
        files: {
          total: files.length,
          byPlatform: filesByPlatform,
          sample: files.slice(0, 3).map(f => ({
            id: f.id,
            fileName: f.fileName,
            platform: f.platform,
            hasContent: !!f.fileContent
          }))
        },
        chunks: {
          total: totalChunks,
          withEmbeddings: chunksWithEmbeddings,
          sampleChecked: Math.min(5, files.length)
        },
        integrations: integrationTypes
      });
    } catch (error) {
      console.error('Debug endpoint error:', error);
      res.status(500).json({ error: (error as Error).message });
    }
  });

  // Additional diagnostic endpoints that were in main routes
  app.get('/api/diagnostic/storage', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");
      
      const integrations = await storage.getIntegrations();
      const files = await storage.getFiles();
      
      res.json({
        status: 'working',
        integrations: {
          count: integrations.length,
          types: Array.from(new Set(integrations.map(i => i.type)))
        },
        files: {
          count: files.length,
          platforms: Array.from(new Set(files.map(f => f.platform)))
        }
      });
    } catch (error: any) {
      console.error('Error in storage diagnostic:', error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });

  app.get('/api/diagnostic/database', async (req, res) => {
    try {
      // Get storage dynamically
      const { storage } = await import("../storage.js");
      
      // Test basic database connectivity
      const integrations = await storage.getIntegrations();
      
      res.json({
        status: 'connected',
        url: process.env.DATABASE_URL ? 'configured' : 'not configured',
        tables: {
          integrations: integrations.length
        }
      });
    } catch (error: any) {
      console.error('Error in database diagnostic:', error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });

  app.get('/api/diagnostic/services', async (req, res) => {
    try {
      const services = {
        storage: 'unknown',
        openai: 'unknown',
        embeddings: 'unknown'
      };

      // Test storage
      try {
        const { storage } = await import("../storage.js");
        await storage.getIntegrations();
        services.storage = 'working';
      } catch (error) {
        services.storage = 'error';
      }

      // Test OpenAI
      services.openai = process.env.OPENAI_API_KEY ? 'configured' : 'not configured';

      res.json({
        status: 'checked',
        services
      });
    } catch (error: any) {
      console.error('Error in services diagnostic:', error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });
} 