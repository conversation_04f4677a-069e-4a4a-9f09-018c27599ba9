import { Request, Response } from 'express';
import { BaseIntegrationController } from '../base/base-integration.controller.js';
import { schedulerService } from '../../../services/core/scheduler-service.js';
import { integrationConfigSchema, scheduleUpdateSchema } from '../../../../shared/index.js';

/**
 * Controller for integration CRUD operations
 * <PERSON>les create, read, update, delete operations for integrations
 */
export class IntegrationCrudController extends BaseIntegrationController {
  constructor() {
    super();
    // Bind all methods to preserve 'this' context
    this.getIntegrations = this.getIntegrations.bind(this);
    this.getIntegration = this.getIntegration.bind(this);
    this.createIntegration = this.createIntegration.bind(this);
    this.updateIntegration = this.updateIntegration.bind(this);
    this.deleteIntegration = this.deleteIntegration.bind(this);
    this.updateSchedule = this.updateSchedule.bind(this);
    this.getSchedules = this.getSchedules.bind(this);
  }

  /**
   * Get all integrations
   */
  async getIntegrations(req: Request, res: Response) {
    try {
      this.logAction('Getting integrations...');
      const storage = await this.getStorage();
      const integrations = await storage.getIntegrations();
      this.logAction(`Found ${integrations.length} integrations`);

      // Add next execution time if schedule exists
      const integrationsWithNextRun = integrations.map(integration => {
        const nextRunTime = integration.syncSchedule
          ? schedulerService.getNextExecutionTime(integration.id)
          : null;

        return {
          ...integration,
          nextRunTime,
        };
      });

      return this.successResponse(res, { integrations: integrationsWithNextRun });
    } catch (error: any) {
      return this.handleError(res, error, 'Failed to get integrations');
    }
  }

  /**
   * Get a single integration by ID
   */
  async getIntegration(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Add next execution time if schedule exists
      const nextRunTime = integration.syncSchedule
        ? schedulerService.getNextExecutionTime(integration.id)
        : null;

      return this.successResponse(res, {
        integration: {
          ...integration,
          nextRunTime,
        }
      });
    } catch (error: any) {
      return this.handleError(res, error, `Failed to get integration ${req.params.id}`);
    }
  }

  /**
   * Create a new integration
   */
  async createIntegration(req: Request, res: Response) {
    try {
      this.logAction('Creating new integration with data:', req.body);

      // Validate the request body
      const validationResult = integrationConfigSchema.safeParse(req.body);

      if (!validationResult.success) {
        this.logError('Validation failed:', validationResult.error.errors);
        return this.validationError(res, validationResult.error.errors, req.body);
      }

      this.logAction('Validation passed, creating integration...');
      const { type, name, config, sourceConfig, destinationConfig, isLlmEnabled, syncFilters, syncSchedule } = validationResult.data;

      let status = 'disconnected';
      let credentials: string | null = null;

      // Handle Google integrations with existing auth
      if (req.body.useExistingGoogleAuth && this.isGoogleIntegrationType(type)) {
        this.logAction('Attempting to use existing Google authentication...');
        
        try {
          // Find an existing connected Google integration to copy credentials from
          const storage = await this.getStorage();
          const existingIntegrations = await storage.getIntegrations();
          
          const existingGoogleIntegration = existingIntegrations.find(integration => 
            this.isGoogleIntegrationType(integration.type) && 
            integration.status === 'connected' && 
            integration.credentials
          );

          if (existingGoogleIntegration) {
            this.logAction(`Found existing Google integration with credentials: ${existingGoogleIntegration.id}`);
            
            // Copy the credentials from the existing integration
            credentials = existingGoogleIntegration.credentials;
            status = 'connected';
            
            this.logAction('Successfully copied credentials from existing Google integration');
          } else {
            this.logAction('No existing connected Google integration found, creating as disconnected');
          }
        } catch (credentialsError: any) {
          this.logError('Error copying Google credentials:', credentialsError);
          // Continue with disconnected status if credential copying fails
        }
      }

      // Create the integration
      const storage = await this.getStorage();
      const integration = await storage.createIntegration({
        type,
        name,
        status,
        credentials,
        config: config || {},
        sourceConfig: sourceConfig || {},
        destinationConfig: destinationConfig || {},
        isLlmEnabled: isLlmEnabled !== false,
        syncFilters: syncFilters || {},
        syncSchedule: syncSchedule || null,
        syncStatus: 'idle',
      });

      this.logAction('Successfully created integration:', integration);

      // If a schedule is provided, set up a cron job
      if (syncSchedule) {
        try {
          schedulerService.scheduleIntegrationSync(integration.id, syncSchedule);
          this.logAction('Scheduled sync job for integration:', integration.id);
        } catch (scheduleError) {
          this.logError('Failed to schedule sync job:', scheduleError);
          // Don't fail the integration creation if scheduling fails
        }
      }

      return this.successResponse(res, {
        integration,
      }, 'Integration created successfully', 201);
    } catch (error: any) {
      return this.handleError(res, error, 'Failed to create integration');
    }
  }

  /**
   * Helper method to check if a type is a Google integration
   */
  private isGoogleIntegrationType(type: string): boolean {
    const googleTypes = ['google_drive', 'google-drive', 'gmail', 'google_calendar', 'google-calendar', 'google_meet', 'google-meet'];
    return googleTypes.includes(type);
  }

  /**
   * Update an integration
   */
  async updateIntegration(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      // Handle status-only updates separately with less strict validation
      if (req.body && typeof req.body.status === 'string' && Object.keys(req.body).length === 1) {
        const storage = await this.getStorage();
        const existingIntegration = await storage.getIntegration(id!);
        if (!existingIntegration) {
          return res.status(404).json({ message: 'Integration not found' });
        }

        const updatedIntegration = await storage.updateIntegration(id!, {
          status: req.body.status
        });

        return this.successResponse(res, {
          integration: updatedIntegration,
        }, 'Integration status updated successfully');
      }

      // Support partial updates by only requiring name and type
      if (req.body.sourceConfig && !req.body.name && !req.body.type) {
        const storage = await this.getStorage();
        const existingIntegration = await storage.getIntegration(id!);
        if (!existingIntegration) {
          return res.status(404).json({ message: 'Integration not found' });
        }

        // Add required fields from existing integration for validation
        req.body.name = existingIntegration.name;
        req.body.type = existingIntegration.type;
      }

      // Validate the request body
      const validationResult = integrationConfigSchema.safeParse(req.body);

      if (!validationResult.success) {
        this.logError('Validation error:', validationResult.error.errors);
        return this.validationError(res, validationResult.error.errors);
      }

      const { name, config, sourceConfig, destinationConfig, isLlmEnabled, syncFilters, syncSchedule, status } = validationResult.data;

      // Check if integration exists
      const storage = await this.getStorage();
      const existingIntegration = await storage.getIntegration(id!);
      if (!existingIntegration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Update the integration
      const updatedIntegration = await storage.updateIntegration(id!, {
        name,
        config: config || existingIntegration.config,
        sourceConfig: sourceConfig || existingIntegration.sourceConfig,
        destinationConfig: destinationConfig || existingIntegration.destinationConfig,
        isLlmEnabled: isLlmEnabled !== undefined ? isLlmEnabled : existingIntegration.isLlmEnabled,
        syncFilters: syncFilters || existingIntegration.syncFilters,
        syncSchedule: syncSchedule !== undefined ? syncSchedule : existingIntegration.syncSchedule,
        status: status || existingIntegration.status,
      });

      // Update the schedule if it changed
      if (syncSchedule !== existingIntegration.syncSchedule) {
        if (syncSchedule) {
          schedulerService.scheduleIntegrationSync(id!, syncSchedule);
        } else {
          schedulerService.cancelSchedule(id!);
        }
      }

      return this.successResponse(res, {
        integration: updatedIntegration,
      }, 'Integration updated successfully');
    } catch (error: any) {
      return this.handleError(res, error, `Failed to update integration ${req.params.id}`);
    }
  }

  /**
   * Delete an integration
   */
  async deleteIntegration(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      // Check if integration exists
      const storage = await this.getStorage();
      const existingIntegration = await storage.getIntegration(id!);
      if (!existingIntegration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Delete the integration
      const success = await storage.deleteIntegration(id!);

      // Cancel any scheduled jobs
      schedulerService.cancelSchedule(id!);

      if (!success) {
        return res.status(500).json({ message: 'Failed to delete integration' });
      }

      return this.successResponse(res, {}, 'Integration deleted successfully');
    } catch (error: any) {
      return this.handleError(res, error, `Failed to delete integration ${req.params.id}`);
    }
  }

  /**
   * Update the sync schedule for an integration
   */
  async updateSchedule(req: Request, res: Response) {
    try {
      // Validate the request body
      const validationResult = scheduleUpdateSchema.safeParse(req.body);

      if (!validationResult.success) {
        return this.validationError(res, validationResult.error.errors);
      }

      const { integrationId, schedule } = validationResult.data;

      // Check if integration exists
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Update the integration schedule
      const updatedIntegration = await storage.updateIntegration(integrationId, {
        syncSchedule: schedule,
      });

      // Update the scheduler
      if (schedule) {
        schedulerService.scheduleIntegrationSync(integrationId, schedule);
      } else {
        schedulerService.cancelSchedule(integrationId);
      }

      // Get the next execution time
      const nextRunTime = schedule
        ? schedulerService.getNextExecutionTime(integrationId)
        : null;

      return this.successResponse(res, {
        integration: {
          ...updatedIntegration,
          nextRunTime,
        },
      }, 'Schedule updated successfully');
    } catch (error: any) {
      return this.handleError(res, error, 'Failed to update schedule');
    }
  }

  /**
   * Get all schedules
   */
  async getSchedules(_req: Request, res: Response) {
    try {
      const storage = await this.getStorage();
      const integrations = await storage.getIntegrations();
      const schedules = integrations.map((integration: any) => ({
        integrationId: integration.id,
        name: integration.name,
        type: integration.type,
        schedule: integration.syncSchedule,
        status: integration.status,
        lastSync: integration.lastSyncAt
      }));

      return this.successResponse(res, { schedules });
    } catch (error: any) {
      return this.handleError(res, error, 'Failed to fetch schedules');
    }
  }
}

export const integrationCrudController = new IntegrationCrudController(); 