import { OAuth2Client } from "google-auth-library";
import { BaseService } from "../../base/service.interface";
import { cryptoService } from "../../core/crypto-service";

/**
 * Google OAuth Service - handles authentication and token management
 */
export class GoogleOAuthService extends BaseService {
  private oauth2Client: OAuth2Client | null = null;

  constructor() {
    super('GoogleOAuth', '1.0.0', 'Google OAuth authentication and token management');
  }

  protected async onInitialize(): Promise<void> {
    this.validateEnvironment(['GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET']);
    this.log('info', 'Google OAuth service initialized');
  }

  protected getDependencies(): string[] {
    return ['crypto-service'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    const hasCredentials = !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET);
    return {
      hasCredentials,
      serverUrl: process.env.SERVER_URL || 'http://localhost:8080',
    };
  }

  /**
   * Create an OAuth2 client for Google APIs
   */
  createOAuth2Client(): OAuth2Client {
    this.ensureInitialized();
    
    const clientId = process.env.GOOGLE_CLIENT_ID!;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET!;

    this.oauth2Client = new OAuth2Client(
      clientId,
      clientSecret,
      `${process.env.SERVER_URL || "http://localhost:8080"}/api/integrations/oauth/callback`,
    );

    return this.oauth2Client;
  }

  /**
   * Generate an authorization URL for OAuth flow
   * @param redirectUri Original redirect URI after OAuth flow (not used - we use global callback)
   * @param integrationId The ID of the integration being connected (extracted from the request URL)
   * @returns Object containing the auth URL and state token
   */
  getAuthUrl(
    redirectUri: string,
    integrationId?: number,
  ): { url: string; state: string } {
    this.ensureInitialized();

    // Get the integration ID from the redirect URI if not provided
    if (!integrationId && redirectUri) {
      const matches = redirectUri.match(/\/api\/integrations\/(\d+)\/oauth/);
      if (matches && matches[1]) {
        integrationId = parseInt(matches[1], 10);
      }
    }

    this.log('info', `Generating OAuth URL for integration ID: ${integrationId}`);

    const clientId = process.env.GOOGLE_CLIENT_ID!;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET!;

    // Always use the global callback endpoint as configured in Google Cloud Console
    const globalRedirectUri = `${process.env.SERVER_URL || "http://localhost:8080"}/api/integrations/oauth/callback`;

    this.log('info', `Using global redirect URI: ${globalRedirectUri}`);

    // Create a client specifically for this auth request with the global redirect URI
    const oauth2Client = new OAuth2Client(
      clientId,
      clientSecret,
      globalRedirectUri,
    );

    const scopes = [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/drive.file",
      "https://www.googleapis.com/auth/drive.readonly",
      "https://www.googleapis.com/auth/drive.metadata.readonly",
      "https://www.googleapis.com/auth/documents.readonly",
      "https://www.googleapis.com/auth/gmail.readonly",
      "https://www.googleapis.com/auth/gmail.metadata",
      "https://www.googleapis.com/auth/calendar",
      "https://www.googleapis.com/auth/calendar.readonly",
      "https://www.googleapis.com/auth/calendar.events",
      "https://www.googleapis.com/auth/calendar.events.readonly",
      "profile",
      "email",
    ];

    // Generate a state token that includes the integration ID
    const randomPart = Math.random().toString(36).substring(2, 15);
    const state = `${integrationId || 0}_${randomPart}`;

    this.log('info', `Generated OAuth state token: ${state}`);

    const authUrl = oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      prompt: "consent",
      state,
      include_granted_scopes: true,
    });

    return {
      url: authUrl,
      state,
    };
  }

  /**
   * Exchange authorization code for tokens
   * @param code Authorization code from OAuth flow
   * @param redirectUri The redirect URI used in the auth request (not used)
   */
  async getTokensFromCode(code: string, redirectUri?: string): Promise<any> {
    this.ensureInitialized();
    this.validateString(code, 'authorization code');

    const clientId = process.env.GOOGLE_CLIENT_ID!;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET!;

    // Always use the global callback endpoint as configured in Google Cloud Console
    const globalRedirectUri = `${process.env.SERVER_URL || "http://localhost:8080"}/api/integrations/oauth/callback`;

    this.log('info', `Using global redirect URI for token exchange: ${globalRedirectUri}`);

    // Create a client with the global redirect URI
    const oauth2Client = new OAuth2Client(
      clientId,
      clientSecret,
      globalRedirectUri,
    );

    // Exchange the code for tokens
    try {
      this.log('info', 'Exchanging authorization code for tokens...');
      const { tokens } = await oauth2Client.getToken(code);
      oauth2Client.setCredentials(tokens);
      this.log('info', 'Successfully obtained tokens from Google');
      return tokens;
    } catch (error: any) {
      this.handleError(error, 'token exchange');
    }
  }

  /**
   * Get an authorized OAuth2Client using encrypted credentials
   * @param encryptedCredentials Encrypted credentials from storage
   */
  async getAuthorizedClient(encryptedCredentials: string): Promise<OAuth2Client> {
    this.ensureInitialized();
    this.validateString(encryptedCredentials, 'encrypted credentials');

    try {
      this.log('info', 'Getting authorized client...');
      this.log('info', `Encrypted credentials length: ${encryptedCredentials.length}`);

      // Validate the encrypted format first
      if (!cryptoService.validateEncryptedFormat(encryptedCredentials)) {
        throw new Error('Invalid encrypted credentials format - expected "iv:encryptedData"');
      }

      // Decrypt the credentials
      this.log('info', 'Decrypting credentials...');
      const credentialsJson = await cryptoService.decrypt(encryptedCredentials);
      this.log('info', `Decryption successful, JSON length: ${credentialsJson.length}`);

      // Parse the JSON
      let credentials: any;
      try {
        credentials = JSON.parse(credentialsJson);
        this.log('info', 'JSON parsing successful');
        this.log('info', `Credentials keys: ${Object.keys(credentials).join(', ')}`);
      } catch (parseError: any) {
        this.log('error', 'JSON parsing failed', parseError);
        throw new Error(`Failed to parse credentials JSON: ${parseError.message}`);
      }

      // Validate required fields
      if (!credentials.access_token) {
        throw new Error('Invalid credentials: missing access_token');
      }

      // Create a new OAuth2 client
      const oauth2Client = this.createOAuth2Client();

      // Set the credentials
      oauth2Client.setCredentials(credentials);
      this.log('info', 'OAuth2 client configured successfully');

      // Check if access token is expired and refresh if needed
      if (this.isTokenExpired(credentials)) {
        this.log('info', 'Token is expired, refreshing...');
        await this.refreshAccessToken(oauth2Client);
        this.log('info', 'Token refreshed successfully');
      } else {
        this.log('info', 'Token is still valid');
      }

      return oauth2Client;
    } catch (error: any) {
      this.handleError(error, 'getting authorized client');
    }
  }

  /**
   * Check if the access token is expired
   * @param credentials OAuth2 credentials
   */
  private isTokenExpired(credentials: any): boolean {
    if (!credentials.expiry_date) {
      return true;
    }

    // Consider token expired 5 minutes before actual expiry
    return credentials.expiry_date <= Date.now() + 5 * 60 * 1000;
  }

  /**
   * Refresh the access token
   * @param oauth2Client OAuth2 client
   */
  private async refreshAccessToken(oauth2Client: OAuth2Client): Promise<void> {
    try {
      const response = await oauth2Client.refreshAccessToken();
      oauth2Client.setCredentials(response.credentials);
    } catch (error) {
      this.handleError(error, 'refreshing access token');
    }
  }

  /**
   * Validate OAuth2 credentials
   * @param credentials Credentials to validate
   */
  validateCredentials(credentials: any): boolean {
    return !!(
      credentials &&
      credentials.access_token &&
      typeof credentials.access_token === 'string'
    );
  }

  /**
   * Get OAuth scopes for Google Drive access
   */
  getRequiredScopes(): string[] {
    return [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/drive.file",
      "https://www.googleapis.com/auth/drive.readonly",
      "https://www.googleapis.com/auth/drive.metadata.readonly",
      "https://www.googleapis.com/auth/documents.readonly",
      "https://www.googleapis.com/auth/gmail.readonly",
      "https://www.googleapis.com/auth/gmail.metadata",
      "https://www.googleapis.com/auth/calendar",
      "https://www.googleapis.com/auth/calendar.readonly",
      "https://www.googleapis.com/auth/calendar.events",
      "https://www.googleapis.com/auth/calendar.events.readonly",
      "profile",
      "email",
    ];
  }
}
