import fetch from 'node-fetch';

async function testTeamsOAuth() {
  const baseUrl = 'http://localhost:5000';
  
  console.log('🧪 Testing Microsoft Teams OAuth Implementation...\n');
  
  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch(`${baseUrl}/api/diagnostic/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.status);
    
    // Test 2: Create Microsoft Teams integration
    console.log('\n2. Creating Microsoft Teams integration...');
    const createResponse = await fetch(`${baseUrl}/api/integrations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'microsoft_teams',
        name: 'Test Microsoft Teams Integration',
        config: {
          description: 'Test integration for Microsoft Teams OAuth'
        },
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null
      })
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.json();
      console.error('❌ Failed to create integration:', errorData);
      return;
    }
    
    const integrationData = await createResponse.json();
    const integrationId = integrationData.integration.id;
    console.log('✅ Created Teams integration with ID:', integrationId);
    
    // Test 3: Generate OAuth URL
    console.log('\n3. Testing OAuth URL generation...');
    const redirectUri = `${baseUrl}/api/integrations/teams/oauth/callback`;
    const authUrlResponse = await fetch(
      `${baseUrl}/api/integrations/${integrationId}/auth-url?redirectUri=${encodeURIComponent(redirectUri)}`,
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    if (!authUrlResponse.ok) {
      const errorData = await authUrlResponse.json();
      console.error('❌ Failed to generate OAuth URL:', errorData);
      
      // Check if it's due to missing environment variables
      if (errorData.message && errorData.message.includes('Microsoft OAuth credentials not configured')) {
        console.log('\n📝 Microsoft OAuth Environment Variables Required:');
        console.log('   - MICROSOFT_CLIENT_ID: Your Azure AD app client ID');
        console.log('   - MICROSOFT_CLIENT_SECRET: Your Azure AD app client secret');
        console.log('   - MICROSOFT_TENANT_ID: Your Azure AD tenant ID');
        console.log('   - MICROSOFT_REDIRECT_URI: The callback URL (optional, defaults to teams callback)');
        console.log('\n🔗 How to set up Microsoft OAuth:');
        console.log('   1. Go to https://portal.azure.com');
        console.log('   2. Navigate to Azure Active Directory > App registrations');
        console.log('   3. Create a new registration or use existing one');
        console.log('   4. Add redirect URI: ' + redirectUri);
        console.log('   5. Generate client secret in "Certificates & secrets"');
        console.log('   6. Add required API permissions for Microsoft Graph');
      }
    } else {
      const authData = await authUrlResponse.json();
      console.log('✅ OAuth URL generated successfully');
      console.log('   Auth URL preview:', authData.authUrl?.substring(0, 100) + '...');
      
      // Test 4: Verify integration state was stored
      console.log('\n4. Verifying integration state storage...');
      const integrationResponse = await fetch(`${baseUrl}/api/integrations/${integrationId}`);
      const integration = await integrationResponse.json();
      
      if (integration.integration?.config?.oauthState) {
        console.log('✅ OAuth state stored successfully');
        console.log('   State:', integration.integration.config.oauthState);
      } else {
        console.log('❌ OAuth state not stored');
      }
    }
    
    // Test 5: Test connection without credentials (should fail gracefully)
    console.log('\n5. Testing connection without credentials...');
    const testResponse = await fetch(`${baseUrl}/api/integrations/${integrationId}/test-connection`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    
    const testData = await testResponse.json();
    if (testData.message && testData.message.includes('not connected')) {
      console.log('✅ Connection test properly handles missing credentials');
    } else {
      console.log('⚠️ Unexpected connection test response:', testData);
    }
    
    // Test 6: Cleanup - delete test integration
    console.log('\n6. Cleaning up test integration...');
    const deleteResponse = await fetch(`${baseUrl}/api/integrations/${integrationId}`, {
      method: 'DELETE'
    });
    
    if (deleteResponse.ok) {
      console.log('✅ Test integration cleaned up successfully');
    } else {
      console.log('⚠️ Could not clean up test integration');
    }
    
    console.log('\n🎉 Microsoft Teams OAuth Implementation Test Complete!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Teams service created with OAuth methods');
    console.log('   ✅ Integration controller supports Teams OAuth');
    console.log('   ✅ OAuth routes configured for Teams callback');
    console.log('   ✅ Teams integration type supported in database');
    console.log('   ✅ Connection testing implemented');
    
    if (!authUrlResponse.ok) {
      console.log('\n⚠️ Next Steps:');
      console.log('   1. Set up Microsoft Azure AD app registration');
      console.log('   2. Configure environment variables');
      console.log('   3. Test the complete OAuth flow');
    } else {
      console.log('\n✅ Ready for Microsoft OAuth credentials setup!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testTeamsOAuth(); 