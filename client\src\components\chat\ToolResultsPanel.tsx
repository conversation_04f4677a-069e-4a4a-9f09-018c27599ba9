import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronDown, 
  ChevronRight, 
  CheckCircle, 
  XCircle, 
  Clock,
  Zap,
  FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { ToolUsage } from './ToolUsageBadge';

interface ToolResultsPanelProps {
  toolUsages: ToolUsage[];
  className?: string;
}

export function ToolResultsPanel({ toolUsages, className }: ToolResultsPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (toolUsages.length === 0) return null;

  const getServerIcon = (serverName: string) => {
    switch (serverName.toLowerCase()) {
      case 'google drive':
        return '📁';
      case 'microsoft teams':
        return '👥';
      case 'file upload':
        return '📤';
      default:
        return '🔧';
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-700 bg-green-50' : 'text-red-700 bg-red-50';
  };

  return (
    <Card className={cn("mt-3 border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50/50 to-purple-50/30 shadow-sm hover:shadow-md transition-all duration-200", className)}>
      <CardHeader className="pb-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center justify-between w-full p-2 h-auto font-normal hover:bg-blue-100/50 rounded-lg transition-all duration-200"
        >
          <div className="flex items-center space-x-3">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-blue-600 transition-transform duration-200" />
            ) : (
              <ChevronRight className="w-4 h-4 text-blue-600 transition-transform duration-200" />
            )}
            <div className="flex items-center space-x-2">
              <div className="p-1.5 bg-blue-100 rounded-lg">
                <Zap className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <span className="text-sm font-semibold text-gray-800">
                  Tool Usage Details
                </span>
                <div className="text-xs text-gray-500">
                  {toolUsages.length} tool{toolUsages.length !== 1 ? 's' : ''} executed
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              {toolUsages.map((usage, index) => (
                <div
                  key={index}
                  className={cn(
                    "w-2.5 h-2.5 rounded-full shadow-sm",
                    usage.success ? "bg-green-500" : "bg-red-500"
                  )}
                  title={usage.success ? "Success" : "Failed"}
                />
              ))}
            </div>
            <Badge
              variant="secondary"
              className="text-xs bg-blue-100 text-blue-700 border-blue-200"
            >
              {toolUsages.filter(t => t.success).length}/{toolUsages.length}
            </Badge>
          </div>
        </Button>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0 px-4 pb-4">
          <div className="space-y-3">
            {toolUsages.map((usage, index) => (
              <div
                key={index}
                className={cn(
                  "p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-sm",
                  usage.success
                    ? "border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 hover:border-green-300"
                    : "border-red-200 bg-gradient-to-r from-red-50 to-pink-50 hover:border-red-300"
                )}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "p-2 rounded-lg text-lg shadow-sm",
                      usage.success ? "bg-green-100" : "bg-red-100"
                    )}>
                      {getServerIcon(usage.serverName)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-semibold text-sm text-gray-800">{usage.toolName}</span>
                        <div className={cn(
                          "flex items-center space-x-1 px-2 py-0.5 rounded-full text-xs font-medium",
                          usage.success ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                        )}>
                          {getStatusIcon(usage.success)}
                          <span>{usage.success ? "Success" : "Failed"}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-gray-600">
                        <span className="font-medium">{usage.serverName}</span>
                        <span>•</span>
                        <span>{formatDistanceToNow(usage.timestamp, { addSuffix: true })}</span>
                        {usage.duration && (
                          <>
                            <span>•</span>
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span className="font-mono">{usage.duration}ms</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Result or Error */}
                {usage.success && usage.result && (
                  <div className="mt-3 p-3 bg-white/80 rounded-lg border border-green-200/50 shadow-sm">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="p-1 bg-green-100 rounded">
                        <FileText className="w-3 h-3 text-green-600" />
                      </div>
                      <span className="text-xs font-semibold text-green-700">Result</span>
                    </div>
                    <div className="text-sm text-gray-700 leading-relaxed">{usage.result}</div>
                  </div>
                )}

                {!usage.success && usage.error && (
                  <div className="mt-3 p-3 bg-white/80 rounded-lg border border-red-200/50 shadow-sm">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="p-1 bg-red-100 rounded">
                        <XCircle className="w-3 h-3 text-red-600" />
                      </div>
                      <span className="text-xs font-semibold text-red-700">Error Details</span>
                    </div>
                    <div className="text-sm text-red-700 leading-relaxed">{usage.error}</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
