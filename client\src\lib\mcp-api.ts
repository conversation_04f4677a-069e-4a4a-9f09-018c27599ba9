// MCP API Client
import { MCPStatusResponse, MCPServer, MCPLogEntry } from '../components/mcp/types';

// Mock data for development - make it mutable for toggle functionality
let mockMCPStatus: MCPStatusResponse = {
  servers: [
    {
      id: 'google-drive',
      name: 'Google Drive',
      type: 'google-drive',
      status: { 
        connected: true, 
        healthy: true, 
        responseTime: 120,
        lastPing: new Date(Date.now() - 30000) // 30 seconds ago
      },
      lastConnected: new Date(Date.now() - 300000), // 5 minutes ago
      tools: [
        { 
          name: 'list_files', 
          description: 'List files in Google Drive',
          usageCount: 15,
          lastUsed: new Date(Date.now() - 120000) // 2 minutes ago
        },
        { 
          name: 'search_files', 
          description: 'Search files by query',
          usageCount: 8,
          lastUsed: new Date(Date.now() - 300000) // 5 minutes ago
        },
        { 
          name: 'get_file_content', 
          description: 'Get content of specific file',
          usageCount: 23,
          lastUsed: new Date(Date.now() - 60000) // 1 minute ago
        },
        { 
          name: 'create_file', 
          description: 'Create new file in Drive',
          usageCount: 3,
          lastUsed: new Date(Date.now() - 3600000) // 1 hour ago
        }
      ],
      config: { 
        enabled: true, 
        autoReconnect: true, 
        timeout: 5000, 
        retryAttempts: 3 
      }
    },
    {
      id: 'microsoft-teams',
      name: 'Microsoft Teams',
      type: 'microsoft-teams',
      status: { 
        connected: false, 
        healthy: false, 
        error: 'Authentication expired',
        lastPing: new Date(Date.now() - 3600000) // 1 hour ago
      },
      lastConnected: new Date(Date.now() - 3600000), // 1 hour ago
      error: 'OAuth token expired - please re-authenticate',
      tools: [
        { 
          name: 'list_teams', 
          description: 'List user teams',
          usageCount: 5,
          lastUsed: new Date(Date.now() - 7200000) // 2 hours ago
        },
        { 
          name: 'get_messages', 
          description: 'Get messages from channels',
          usageCount: 12,
          lastUsed: new Date(Date.now() - 7200000) // 2 hours ago
        },
        { 
          name: 'search_content', 
          description: 'Search across Teams content',
          usageCount: 7,
          lastUsed: new Date(Date.now() - 7200000) // 2 hours ago
        },
        { 
          name: 'get_meeting_transcripts', 
          description: 'Get meeting transcripts',
          usageCount: 2,
          lastUsed: new Date(Date.now() - 86400000) // 1 day ago
        }
      ],
      config: { 
        enabled: true, 
        autoReconnect: true, 
        timeout: 5000, 
        retryAttempts: 3 
      }
    },
    {
      id: 'file-upload',
      name: 'File Upload',
      type: 'file-upload',
      status: { 
        connected: true, 
        healthy: true, 
        responseTime: 45,
        lastPing: new Date(Date.now() - 10000) // 10 seconds ago
      },
      lastConnected: new Date(Date.now() - 60000), // 1 minute ago
      tools: [
        { 
          name: 'upload_file', 
          description: 'Upload and process new file',
          usageCount: 18,
          lastUsed: new Date(Date.now() - 180000) // 3 minutes ago
        },
        { 
          name: 'list_uploaded_files', 
          description: 'List all uploaded files',
          usageCount: 25,
          lastUsed: new Date(Date.now() - 90000) // 1.5 minutes ago
        },
        { 
          name: 'get_file_content', 
          description: 'Get content of uploaded file',
          usageCount: 31,
          lastUsed: new Date(Date.now() - 45000) // 45 seconds ago
        },
        { 
          name: 'delete_file', 
          description: 'Delete uploaded file',
          usageCount: 4,
          lastUsed: new Date(Date.now() - 1800000) // 30 minutes ago
        }
      ],
      config: { 
        enabled: true, 
        autoReconnect: true, 
        timeout: 5000, 
        retryAttempts: 3 
      }
    }
  ],
  totalTools: 12,
  connectedServers: 2,
  lastUpdate: new Date()
};

// API functions
export const mcpApi = {
  // Get MCP status
  async getStatus(): Promise<MCPStatusResponse> {
    try {
      const response = await fetch('/api/mcp/status');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.warn('MCP API not available, using mock data:', error);
      // Return current state with user modifications preserved
      return {
        ...mockMCPStatus,
        connectedServers: mockMCPStatus.servers?.filter(s => s.status.connected && s.config.enabled).length || 0,
        lastUpdate: new Date()
      };
    }
  },

  // Toggle server enabled/disabled
  async toggleServer(serverId: string, enabled: boolean): Promise<boolean> {
    try {
      const response = await fetch('/api/mcp/toggle-server', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ serverId, enabled })
      });
      return response.ok;
    } catch (error) {
      console.warn('MCP toggle server API not available, updating mock data:', error);

      // Update current mock state (persistent across refreshes)
      const server = mockMCPStatus.servers?.find((s: MCPServer) => s.id === serverId);
      if (server) {
        server.config.enabled = enabled;
        // If disabling, also disconnect
        if (!enabled) {
          server.status.connected = false;
          server.status.healthy = false;
        }
        console.log(`Mock: Toggled ${serverId} to ${enabled ? 'enabled' : 'disabled'}`);
      }

      return true; // Mock success
    }
  },

  // Reconnect server
  async reconnectServer(serverId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/mcp/reconnect/${serverId}`, {
        method: 'POST'
      });
      return response.ok;
    } catch (error) {
      console.warn('MCP reconnect server API not available:', error);
      return true; // Mock success
    }
  },

  // Get server logs
  async getLogs(serverId?: string): Promise<MCPLogEntry[]> {
    try {
      const url = serverId ? `/api/mcp/logs/${serverId}` : '/api/mcp/logs';
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.warn('MCP logs API not available:', error);
      // Return mock logs
      return [
        {
          id: '1',
          timestamp: new Date(Date.now() - 60000),
          level: 'info',
          message: 'Google Drive server connected successfully',
          serverId: 'google-drive'
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 3600000),
          level: 'error',
          message: 'Microsoft Teams authentication failed',
          serverId: 'microsoft-teams',
          details: { error: 'OAuth token expired' }
        },
        {
          id: '3',
          timestamp: new Date(Date.now() - 120000),
          level: 'info',
          message: 'File upload server health check passed',
          serverId: 'file-upload'
        }
      ];
    }
  },

  // Get available tools
  async getTools(): Promise<{ serverId: string; tools: any[] }[]> {
    try {
      const response = await fetch('/api/mcp/tools');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.warn('MCP tools API not available:', error);
      // Return mock tools from current state
      return mockMCPStatus.servers?.map((server: MCPServer) => ({
        serverId: server.id,
        tools: server.tools
      })) || [];
    }
  }
};

export default mcpApi;
