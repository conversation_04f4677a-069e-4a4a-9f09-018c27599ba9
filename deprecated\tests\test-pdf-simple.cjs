require('dotenv').config();

async function testPDFProcessing() {
  console.log('🔍 Testing PDF Processing Libraries...');
  
  try {
    // Test pdf-parse
    const pdf = require('pdf-parse');
    const simplePDF = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(Test PDF Content) Tj\nET\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000010 00000 n \n0000000053 00000 n \n0000000125 00000 n \n0000000185 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n279\n%%EOF');
    
    try {
      const pdfData = await pdf(simplePDF);
      console.log('✅ pdf-parse working:', pdfData.text.trim());
    } catch (pdfError) {
      console.log('⚠️ pdf-parse failed:', pdfError.message);
    }
    
    // Test PDF2JSON
    const PDFParser = require('pdf2json');
    console.log('✅ pdf2json loaded successfully');
    
    console.log('✅ Enhanced PDF processing ready');
    
    console.log('\n🎉 All PDF processing libraries are ready!');
    console.log('📋 Available processing methods:');
    console.log('   1. Enhanced PDF-Parse (primary)');
    console.log('   2. PDF2JSON (fallback)');
    
    // Test environment variables
    const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
    const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
    
    console.log('\n🔑 Environment Status:');
    console.log(`   Google OAuth: ${GOOGLE_CLIENT_ID && GOOGLE_CLIENT_SECRET ? '✅ Configured' : '❌ Missing'}`);
    
    if (GOOGLE_CLIENT_ID && GOOGLE_CLIENT_SECRET) {
      console.log('\n🚀 PDF processing should work with your current setup!');
      console.log('💡 Enhanced processing with intelligent text extraction and markdown formatting');
    } else {
      console.log('\n⚠️ Google OAuth credentials needed for PDF download from Google Drive');
    }
    
  } catch (error) {
    console.error('❌ Error testing PDF processing:', error.message);
  }
}

testPDFProcessing(); 