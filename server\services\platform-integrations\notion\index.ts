// Re-export all Notion service modules
export * from './auth.service';
export * from './database.service';
export * from './page.service';
export * from './content.service';
export * from './notion.facade';

// Convenience exports for commonly used classes
export { NotionAuthService } from './auth.service';
export { NotionDatabaseService } from './database.service';
export { NotionPageService } from './page.service';
export { NotionContentService } from './content.service';
export { NotionServiceFacade } from './notion.facade';

// Create and export the default Notion service instance
import { NotionServiceFacade } from './notion.facade';

// Initialize the Notion service facade
export const notionService = new NotionServiceFacade();

// Export for backward compatibility
export default notionService;
