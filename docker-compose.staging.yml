# =============================================================================
# MeetSync Staging Docker Compose Configuration
# =============================================================================
version: '3.8'

services:
  # =============================================================================
  # Application Service
  # =============================================================================
  meetsync-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: meetsync-app-staging
    restart: unless-stopped
    ports:
      - "${PORT:-8080}:8080"
    environment:
      # Application Settings
      NODE_ENV: staging
      PORT: 8080
      HOST: 0.0.0.0
      SERVER_URL: ${SERVER_URL:-http://staging.meetsync.com:8080}
      
      # Database Settings
      DATABASE_URL: postgresql://${POSTGRES_USER:-meetsync_staging}:${POSTGRES_PASSWORD:-meetsync_staging123}@postgres:5432/${POSTGRES_DB:-meetsync_staging}
      
      # Redis Settings
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_staging123}
      
      # Security Settings
      SESSION_SECRET: ${SESSION_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      
      # OpenAI Settings
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      
      # Google OAuth Settings
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      
      # Microsoft OAuth Settings
      MICROSOFT_CLIENT_ID: ${MICROSOFT_CLIENT_ID}
      MICROSOFT_CLIENT_SECRET: ${MICROSOFT_CLIENT_SECRET}
      MICROSOFT_TENANT_ID: ${MICROSOFT_TENANT_ID}
      MICROSOFT_REDIRECT_URI: ${MICROSOFT_REDIRECT_URI}
      
      # Notion Settings
      NOTION_API_KEY: ${NOTION_API_KEY}
      NOTION_DATABASE_ID: ${NOTION_DATABASE_ID}
      NOTION_INTEGRATION_SECRET: ${NOTION_INTEGRATION_SECRET}
      NOTION_PAGE_URL: ${NOTION_PAGE_URL}
      
      # RAG Settings
      CHUNK_SIZE: ${CHUNK_SIZE:-512}
      CHUNK_OVERLAP: ${CHUNK_OVERLAP:-20}
      TOP_K: ${TOP_K:-50}
      SYSTEM_PROMPT: ${SYSTEM_PROMPT:-You are GPT Unify - GPT AI Assistant (Staging), an intelligent AI assistant with access to data sources from multiple platforms including Google Drive, meeting transcripts, uploaded files, and other integrated platforms.}
      ENABLE_FUNCTION_TOOLS: ${ENABLE_FUNCTION_TOOLS:-true}
      ENABLE_FILE_CREATION: ${ENABLE_FILE_CREATION:-true}
      ENABLE_GOOGLE_DRIVE_ACTIONS: ${ENABLE_GOOGLE_DRIVE_ACTIONS:-true}
      
      # Staging Specific Settings
      LOG_LEVEL: debug
      LOG_FORMAT: json
      METRICS_ENABLED: true
      APM_ENABLED: true
      
      # Backup Settings
      BACKUP_ENABLED: true
      BACKUP_SCHEDULE: "0 3 * * *"  # Daily at 3 AM
      BACKUP_RETENTION_DAYS: 14
      BACKUP_S3_BUCKET: ${BACKUP_S3_BUCKET}
      BACKUP_S3_PREFIX: "meetsync/staging/backups"
      
      # Test Data Settings
      SEED_TEST_DATA: true
      ENABLE_DEBUG_ENDPOINTS: true
    volumes:
      - uploads_staging:/app/uploads
      - logs_staging:/app/logs
      - backups_staging:/app/backups
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - meetsync-staging-network
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    labels:
      - "environment=staging"
      - "service=meetsync-app"

  # =============================================================================
  # PostgreSQL Database with pgvector
  # =============================================================================
  postgres:
    image: pgvector/pgvector:pg16
    container_name: meetsync-postgres-staging
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-meetsync_staging}
      POSTGRES_USER: ${POSTGRES_USER:-meetsync_staging}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-meetsync_staging123}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data_staging:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./docker/postgres/init-staging.sql:/docker-entrypoint-initdb.d/02-staging.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - meetsync-staging-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-meetsync_staging} -d ${POSTGRES_DB:-meetsync_staging}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    labels:
      - "environment=staging"
      - "service=database"

  # =============================================================================
  # Redis (for sessions and caching)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: meetsync-redis-staging
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_staging123} --maxmemory 256mb --maxmemory-policy allkeys-lru
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_staging123}
    volumes:
      - redis_data_staging:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - meetsync-staging-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redis_staging123}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'
    labels:
      - "environment=staging"
      - "service=cache"

  # =============================================================================
  # Monitoring Stack (Staging Only)
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: meetsync-prometheus-staging
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data_staging:/prometheus
    ports:
      - "9090:9090"
    networks:
      - meetsync-staging-network
    labels:
      - "environment=staging"
      - "service=monitoring"

  grafana:
    image: grafana/grafana:latest
    container_name: meetsync-grafana-staging
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-staging123}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data_staging:/var/lib/grafana
      - ./docker/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3001:3000"
    networks:
      - meetsync-staging-network
    depends_on:
      - prometheus
    labels:
      - "environment=staging"
      - "service=monitoring"

  # =============================================================================
  # Backup Service (Staging)
  # =============================================================================
  backup:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: meetsync-backup-staging
    restart: "no"
    environment:
      NODE_ENV: staging
      DATABASE_URL: postgresql://${POSTGRES_USER:-meetsync_staging}:${POSTGRES_PASSWORD:-meetsync_staging123}@postgres:5432/${POSTGRES_DB:-meetsync_staging}
      BACKUP_DIR: /app/backups
      RETENTION_DAYS: 14
      BACKUP_S3_BUCKET: ${BACKUP_S3_BUCKET}
      BACKUP_S3_PREFIX: "meetsync/staging/backups"
      WEBHOOK_URL: ${BACKUP_WEBHOOK_URL}
    volumes:
      - backups_staging:/app/backups
      - uploads_staging:/app/uploads:ro
    networks:
      - meetsync-staging-network
    depends_on:
      postgres:
        condition: service_healthy
    command: ["/bin/sh", "/app/docker/scripts/backup.sh"]
    labels:
      - "environment=staging"
      - "service=backup"

# =============================================================================
# Networks
# =============================================================================
networks:
  meetsync-staging-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  postgres_data_staging:
    driver: local
  redis_data_staging:
    driver: local
  uploads_staging:
    driver: local
  logs_staging:
    driver: local
  backups_staging:
    driver: local
  prometheus_data_staging:
    driver: local
  grafana_data_staging:
    driver: local 