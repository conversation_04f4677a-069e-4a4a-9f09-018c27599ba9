import { storage } from "../../../../storage/index.js";
import { openaiService } from "../../../ai/openai-service.js";
import { BaseFileProcessor, type ProcessResult, type PlatformMetadata } from '../base/file-processor.interface';
import { unifiedContentExtractor } from '../../../ai/unified-content-extractor.service';
import { simpleEmbeddingService as embeddingService } from '../../../ai/simple-embedding-service.js';
import { google } from 'googleapis';

/**
 * Google Drive file processor
 * Handles processing files from Google Drive platform
 */
export class GoogleFileProcessor extends BaseFileProcessor {

  constructor() {
    super();
  }

  getPlatform(): string {
    return 'google_drive';
  }

  canProcess(platform: string, fileType?: string): boolean {
    return platform === 'google_drive';
  }

  /**
   * Process a Google Drive file
   * Note: For Google Drive, fileData contains the original file data and fileMetadata contains processed metadata
   */
  async processFile(
    fileData: any,  // This is now the processed metadata with proper Date objects
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    // For backward compatibility, if fileData has fileName, treat it as fileMetadata
    const fileMetadata = fileData.fileName ? fileData : fileData.metadata || fileData;
    const originalFile = fileData.fileName ? fileData.originalFile || fileData : fileData;
    const startTime = Date.now();
    
    try {
      this.log('info', `Processing Google Drive file: ${fileMetadata.fileName}`);

      // Check if file has changed (only if it exists)
      const sourceModified = fileMetadata.lastModified || new Date();
      const needsAIProcessing = this.needsProcessing(existingFile, sourceModified);

      if (!needsAIProcessing && existingFile?.status === 'active') {
        this.log('info', `Skipping unchanged file: ${fileMetadata.fileName}`);
        return this.createResult(true, true);
      }

      // Enhanced metadata for meeting transcripts using AI (only if needed)
      if (needsAIProcessing && fileMetadata.fileType === 'transcript' && integration.isLlmEnabled && openaiService.isInitialized()) {
        await this.extractAIMetadata(fileData, fileMetadata, integration);
      }

      // Save or update file record
      const savedFile = await this.saveFileRecord(fileMetadata, existingFile);
      
      // Generate embeddings for RAG functionality using unified content extractor
      let embeddingsGenerated = false;
      if (needsAIProcessing && embeddingService.isInitialized()) {
        try {
          // Create unified platform metadata
          const platformMetadata = {
            platform: 'google_drive',
            fileName: fileMetadata.fileName,
            fileType: fileMetadata.fileType,
            mimeType: fileMetadata.mimeType,
            sourceUrl: fileMetadata.sourceUrl,
            lastModified: fileMetadata.lastModified,
            owner: fileMetadata.extractedMetadata?.owner || 'Unknown',
            folderPath: originalFile.parents && originalFile.parents.length > 0 
              ? `Folder ID: ${originalFile.parents[0]}` 
              : 'Root',
            sourceContext: 'Google Drive'
          };

          // Try to download file buffer for content extraction
          let fileBuffer: Buffer | undefined;
          try {
            const { GoogleServiceFacade } = await import('../../../platform-integrations/google/google.facade.js');
            const googleService = new GoogleServiceFacade();
            await googleService.initialize();
            const auth = await googleService.getAuthorizedClient(integration.credentials);
            
                         // Determine if this is a Google Docs file that needs export or a binary file that needs download
            const isGoogleDocsFile = originalFile.mimeType?.includes('google-apps') || 
                                   originalFile.mimeType?.includes('docs.google.com');
            
            if (isGoogleDocsFile) {
              // Google Docs files need to be exported, not downloaded
              this.log('info', `File ${fileMetadata.fileName} is a Google Docs file (${originalFile.mimeType}), using export API`);
              
              if (originalFile.mimeType?.includes('document') || fileMetadata.fileType === 'document' || fileMetadata.fileType === 'transcript') {
                // Google Docs document
                this.log('info', `Extracting Google Docs content for: ${fileMetadata.fileName}`);
                const result = await googleService.extractDocContent(auth, originalFile.id || "");
                const fullContent = [result.transcriptText, result.notesText, result.summaryParagraph]
                  .filter(text => text && text.trim().length > 0)
                  .join('\n\n');
                if (fullContent) {
                  fileBuffer = Buffer.from(fullContent, 'utf-8');
                  this.log('info', `Extracted ${fullContent.length} characters from Google Doc: ${fileMetadata.fileName}`);
                }
              } else if (originalFile.mimeType?.includes('presentation') || fileMetadata.fileType === 'presentation') {
                // Google Slides presentation
                this.log('info', `Extracting Google Slides content for: ${fileMetadata.fileName}`);
                const result = await googleService.extractSlidesContent(auth, originalFile.id || "");
                if (result.content) {
                  fileBuffer = Buffer.from(result.content, 'utf-8');
                  this.log('info', `Extracted ${result.content.length} characters from Google Slides: ${fileMetadata.fileName}`);
                }
              } else if (originalFile.mimeType?.includes('spreadsheet') || fileMetadata.fileType === 'spreadsheet') {
                // Google Sheets spreadsheet
                this.log('info', `Extracting Google Sheets content for: ${fileMetadata.fileName}`);
                const result = await googleService.extractSheetsContent(auth, originalFile.id || "");
                if (result.content) {
                  fileBuffer = Buffer.from(result.content, 'utf-8');
                  this.log('info', `Extracted ${result.content.length} characters from Google Sheets: ${fileMetadata.fileName}`);
                }
              } else {
                // Unknown Google Apps file type, try as document
                this.log('info', `Unknown Google Apps file type, trying as document: ${fileMetadata.fileName}`);
                const result = await googleService.extractDocContent(auth, originalFile.id || "");
                const fullContent = [result.transcriptText, result.notesText, result.summaryParagraph]
                  .filter(text => text && text.trim().length > 0)
                  .join('\n\n');
                if (fullContent) {
                  fileBuffer = Buffer.from(fullContent, 'utf-8');
                }
              }
            } else {
              // Binary files can be downloaded directly
              this.log('info', `File ${fileMetadata.fileName} is a binary file (${originalFile.mimeType}), using download API`);
              
              if (originalFile.mimeType === 'application/pdf' || fileMetadata.fileType === 'pdf') {
                // PDF files
                this.log('info', `Downloading PDF content for: ${fileMetadata.fileName}`);
                const pdfBuffer = await googleService.downloadPDFContent(auth, originalFile.id || "", fileMetadata.fileName);
                fileBuffer = pdfBuffer || undefined;
                if (fileBuffer) {
                  this.log('info', `Downloaded ${fileBuffer.length} bytes for PDF: ${fileMetadata.fileName}`);
                }
              } else if (originalFile.mimeType?.includes('wordprocessingml') || originalFile.mimeType?.includes('msword') || fileMetadata.fileType === 'word' || fileMetadata.fileName.endsWith('.docx') || fileMetadata.fileName.endsWith('.doc')) {
                // Microsoft Word documents (.docx, .doc)
                this.log('info', `Downloading Word document content for: ${fileMetadata.fileName}`);
                const wordBuffer = await googleService.downloadWordContent(auth, originalFile.id || "", fileMetadata.fileName);
                fileBuffer = wordBuffer || undefined;
                if (fileBuffer) {
                  this.log('info', `Downloaded ${fileBuffer.length} bytes for Word document: ${fileMetadata.fileName}`);
                }
              } else if (originalFile.mimeType?.includes('spreadsheetml') || originalFile.mimeType?.includes('excel') || fileMetadata.fileType === 'spreadsheet' || fileMetadata.fileName.endsWith('.xlsx') || fileMetadata.fileName.endsWith('.xls')) {
                // Microsoft Excel spreadsheets (.xlsx, .xls) - download as binary and extract
                this.log('info', `Downloading Excel spreadsheet for: ${fileMetadata.fileName}`);
                try {
                  const response = await google.drive({ version: 'v3', auth }).files.get({
                    fileId: originalFile.id,
                    alt: 'media'
                  }, { responseType: 'arraybuffer' });
                  fileBuffer = Buffer.from(response.data as ArrayBuffer);
                  this.log('info', `Downloaded ${fileBuffer.length} bytes for Excel file: ${fileMetadata.fileName}`);
                } catch (downloadError) {
                  this.log('warn', `Failed to download Excel file: ${fileMetadata.fileName}`, downloadError);
                }
              } else if (originalFile.mimeType?.includes('presentationml') || originalFile.mimeType?.includes('powerpoint') || fileMetadata.fileType === 'presentation' || fileMetadata.fileName.endsWith('.pptx') || fileMetadata.fileName.endsWith('.ppt')) {
                // Microsoft PowerPoint presentations (.pptx, .ppt) - download as binary and extract
                this.log('info', `Downloading PowerPoint presentation for: ${fileMetadata.fileName}`);
                try {
                  const response = await google.drive({ version: 'v3', auth }).files.get({
                    fileId: originalFile.id,
                    alt: 'media'
                  }, { responseType: 'arraybuffer' });
                  fileBuffer = Buffer.from(response.data as ArrayBuffer);
                  this.log('info', `Downloaded ${fileBuffer.length} bytes for PowerPoint file: ${fileMetadata.fileName}`);
                } catch (downloadError) {
                  this.log('warn', `Failed to download PowerPoint file: ${fileMetadata.fileName}`, downloadError);
                }
              } else if (originalFile.mimeType === 'text/plain' || fileMetadata.fileType === 'text' || fileMetadata.fileName.endsWith('.txt')) {
                // Plain text files
                this.log('info', `Downloading text file content for: ${fileMetadata.fileName}`);
                try {
                  const response = await google.drive({ version: 'v3', auth }).files.get({
                    fileId: originalFile.id,
                    alt: 'media'
                  }, { responseType: 'text' });
                  fileBuffer = Buffer.from(response.data as string, 'utf-8');
                  this.log('info', `Downloaded ${fileBuffer.length} bytes for text file: ${fileMetadata.fileName}`);
                } catch (downloadError) {
                  this.log('warn', `Failed to download text file: ${fileMetadata.fileName}`, downloadError);
                }
              } else if (originalFile.mimeType === 'text/csv' || fileMetadata.fileName.endsWith('.csv')) {
                // CSV files
                this.log('info', `Downloading CSV file content for: ${fileMetadata.fileName}`);
                try {
                  const response = await google.drive({ version: 'v3', auth }).files.get({
                    fileId: originalFile.id,
                    alt: 'media'
                  }, { responseType: 'text' });
                  fileBuffer = Buffer.from(response.data as string, 'utf-8');
                  this.log('info', `Downloaded ${fileBuffer.length} bytes for CSV file: ${fileMetadata.fileName}`);
                } catch (downloadError) {
                  this.log('warn', `Failed to download CSV file: ${fileMetadata.fileName}`, downloadError);
                }
              } else {
                // Unknown file type - try generic download
                this.log('info', `Unknown file type, attempting generic download for: ${fileMetadata.fileName} (${originalFile.mimeType})`);
                try {
                  const response = await google.drive({ version: 'v3', auth }).files.get({
                    fileId: originalFile.id,
                    alt: 'media'
                  }, { responseType: 'arraybuffer' });
                  fileBuffer = Buffer.from(response.data as ArrayBuffer);
                  this.log('info', `Downloaded ${fileBuffer.length} bytes for unknown file type: ${fileMetadata.fileName}`);
                } catch (downloadError) {
                  this.log('warn', `Failed to download unknown file type: ${fileMetadata.fileName}`, downloadError);
                }
              }
            }
          } catch (downloadError) {
            this.log('warn', `Could not download file content for ${fileMetadata.fileName}, creating synthetic content buffer`, downloadError);
            
            // Create a comprehensive content buffer from metadata for embedding generation
            const syntheticContent = `
File: ${fileMetadata.fileName}
Type: ${fileMetadata.fileType}
Platform: google_drive
MIME Type: ${fileMetadata.mimeType}
Source: ${fileMetadata.sourceUrl || 'Google Drive'}
Location: ${originalFile.parents && originalFile.parents.length > 0 ? `Folder ID: ${originalFile.parents[0]}` : 'Root'}
Owner: ${fileMetadata.extractedMetadata?.owner || 'Unknown'}
Last Modified: ${fileMetadata.lastModified?.toISOString()}
Size: ${fileMetadata.fileSize || 'Unknown'}

--- File Description ---
This is a ${fileMetadata.fileType} file named "${fileMetadata.fileName}" stored in Google Drive.
The file was last modified on ${fileMetadata.lastModified?.toLocaleDateString()}.
${fileMetadata.extractedMetadata?.aiExtracted?.summary || ''}
${fileMetadata.extractedMetadata?.aiExtracted?.topics ? `Topics covered: ${fileMetadata.extractedMetadata.aiExtracted.topics.join(', ')}` : ''}
${fileMetadata.extractedMetadata?.aiExtracted?.attendees ? `Meeting attendees: ${fileMetadata.extractedMetadata.aiExtracted.attendees.join(', ')}` : ''}

--- Search Keywords ---
${fileMetadata.fileName.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/).filter((w: string) => w.length > 2).join(', ')}
${fileMetadata.fileType} document google drive file
${fileMetadata.extractedMetadata?.aiExtracted?.topics?.join(', ') || ''}
            `.trim();
            
            fileBuffer = Buffer.from(syntheticContent, 'utf-8');
            this.log('info', `Generated synthetic content buffer (${fileBuffer.length} bytes) for ${fileMetadata.fileName}`);
          }

          // Extract content using unified extractor
          const contentResult = await unifiedContentExtractor.extractContent(
            platformMetadata,
            fileBuffer
          );

          if (contentResult.success && contentResult.content) {
            // Generate embeddings using the simple embedding service
            await embeddingService.processFileForEmbeddings(savedFile.id, contentResult.content);
            embeddingsGenerated = true;
            this.log('info', `Generated embeddings for ${fileMetadata.fileName} using ${contentResult.metadata?.extractionMethod}`);
          } else {
            this.log('warn', `Content extraction failed for ${fileMetadata.fileName}: ${contentResult.error}`);
          }
        } catch (embeddingError) {
          this.log('error', `Error generating embeddings for ${fileMetadata.fileName}:`, embeddingError);
        }
      }

      const processingTime = Date.now() - startTime;
      this.log('info', `Successfully processed ${fileMetadata.fileName} in ${processingTime}ms`);

      return this.createResult(true, false, savedFile, undefined, {
        contentExtracted: true,
        embeddingsGenerated,
        aiMetadataExtracted: needsAIProcessing && integration.isLlmEnabled,
        processingTime
      });

    } catch (error: any) {
      this.log('error', `Error processing Google file ${fileMetadata.fileName}:`, error);
      return this.createResult(false, false, undefined, error.message);
    }
  }

  /**
   * Extract AI metadata for transcripts
   */
  private async extractAIMetadata(fileData: any, fileMetadata: any, integration: any): Promise<void> {
    try {
      this.log('info', `Extracting AI metadata for transcript: ${fileData.name}`);

      // For transcripts, we extract some content for AI analysis
      const { GoogleServiceFacade } = await import('../../../platform-integrations/google/google.facade.js');
      const googleService = new GoogleServiceFacade();
      await googleService.initialize();
      const auth = await googleService.getAuthorizedClient(integration.credentials);
      const { transcriptText } = await googleService.extractDocContent(auth, fileData.id || "");

      if (transcriptText) {
        const aiMetadata = await openaiService.extractMetadata(
          transcriptText.substring(0, 6000), // Limit content for AI analysis
          fileData.name || "Unknown File",
        );

        // Merge AI metadata into our file metadata
        fileMetadata.extractedMetadata = {
          ...fileMetadata.extractedMetadata,
          aiExtracted: {
            attendees: aiMetadata.attendees,
            topics: aiMetadata.topics,
            meetingTitle: aiMetadata.title,
            date: aiMetadata.date,
            time: aiMetadata.time,
            summary: aiMetadata.summary,
          }
        };
      }
    } catch (aiError) {
      this.log('error', `Error extracting AI metadata for ${fileData.name}:`, aiError);
      // Continue without AI metadata
    }
  }

  /**
   * Save or update file record in database
   */
  private async saveFileRecord(fileMetadata: any, existingFile: any): Promise<any> {
    if (existingFile) {
      // Update existing file with latest metadata
      const savedFile = await storage.updateFile(existingFile.id, {
        fileName: fileMetadata.fileName,
        lastModified: fileMetadata.lastModified,
        extractedMetadata: fileMetadata.extractedMetadata,
        isShared: fileMetadata.isShared,
        sharedWith: fileMetadata.sharedWith,
        status: 'active', // Ensure it's marked as active
        updatedAt: new Date(),
      });
      
      this.log('info', `Updated file reference: ${fileMetadata.fileName}`);
      return savedFile;
    } else {
      // Create new file reference
      const savedFile = await storage.createFile({
        externalId: fileMetadata.externalId,
        fileName: fileMetadata.fileName,
        fileType: fileMetadata.fileType,
        mimeType: fileMetadata.mimeType,
        fileSize: this.getSafeFileSize(fileMetadata.fileSize),
        platform: fileMetadata.platform,
        sourceUrl: fileMetadata.sourceUrl,
        downloadUrl: fileMetadata.downloadUrl,
        thumbnailUrl: fileMetadata.thumbnailUrl,
        userId: fileMetadata.userId,
        lastModified: fileMetadata.lastModified,
        isShared: fileMetadata.isShared,
        sharedWith: fileMetadata.sharedWith,
        extractedMetadata: fileMetadata.extractedMetadata as any,
        status: 'active',
      });
      
      this.log('info', `Created new file reference: ${fileMetadata.fileName}`);
      return savedFile;
    }
  }
}
