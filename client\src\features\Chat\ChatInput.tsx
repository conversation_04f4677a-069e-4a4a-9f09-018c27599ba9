import React, { useRef, useCallback, useEffect } from 'react';
import { FileItem } from '../../store/chat/store';
import { cn } from '../../lib/utils';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  onFileUpload: (files: File[]) => void;
  uploadingFiles: FileItem[];
  onRemoveFile: (fileId: string) => void;
  isLoading: boolean;
  isUploading: boolean;
  canSend: boolean;
  placeholder?: string;
}

export function ChatInput({
  value,
  onChange,
  onSend,
  onKeyPress,
  onFileUpload,
  uploadingFiles,
  onRemoveFile,
  isLoading,
  isUploading,
  canSend,
  placeholder = "Type your message..."
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = '40px';
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120);
      textarea.style.height = newHeight + 'px';
    }
  }, []);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
    adjustTextareaHeight();
  };

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      onFileUpload(files);
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle drag and drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      onFileUpload(files);
    }
  }, [onFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Adjust height when value changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [value, adjustTextareaHeight]);

  return (
    <div className="space-y-4">
      {/* File Uploads Display - Professional */}
      {uploadingFiles.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {uploadingFiles.map((file, index) => (
            <div 
              key={index} 
              className="flex items-center space-x-2 bg-gradient-to-r from-slate-100 to-blue-100 border border-slate-300 rounded-lg px-3 py-2 text-sm shadow-sm animate-fadeIn"
            >
              <span className="text-base">📎</span>
              <span className="text-slate-700 font-medium max-w-[200px] truncate">{file.name}</span>
              <span className="text-xs text-slate-500 font-medium">
                {(file.size / 1024).toFixed(1)}KB
              </span>
              <button
                onClick={() => onRemoveFile(file.id)}
                className="text-red-500 hover:text-red-700 transition-colors duration-200 transform hover:scale-105"
                type="button"
                aria-label="Remove file"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Main Input Container - Professional Design */}
      <div className="relative">
        <div className="bg-gradient-to-r from-white via-slate-50 to-blue-50 rounded-xl border border-slate-300 shadow-md hover:shadow-lg transition-all duration-300 p-3">
          <div className="flex items-center space-x-3">
            {/* File Upload Button - Professional */}
            <div className="flex-shrink-0">
              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileSelect}
                className="hidden"
                accept=".pdf,.doc,.docx,.txt,.md,.jpg,.jpeg,.png,.gif,.csv,.json,.xml"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading || isUploading}
                className={cn(
                  "w-10 h-10 rounded-lg transition-all duration-300 flex items-center justify-center shadow-sm transform hover:scale-105",
                  isLoading || isUploading
                    ? "bg-slate-200 cursor-not-allowed text-slate-400" 
                    : "bg-slate-100 hover:bg-slate-200 text-slate-600 border border-slate-300"
                )}
                type="button"
                title="Upload files"
              >
                {isUploading ? (
                  <div className="w-4 h-4 border-2 border-slate-400 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                )}
              </button>
            </div>

            {/* Text Input - Enhanced */}
            <div className="flex-1 relative">
              <textarea
                ref={textareaRef}
                value={value}
                onChange={handleChange}
                onKeyDown={onKeyPress}
                placeholder={placeholder}
                disabled={isLoading}
                className={cn(
                  "w-full bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg px-3 py-2.5 text-slate-900 placeholder-slate-400 resize-none transition-all duration-300 font-medium",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                  "scrollbar-hide shadow-inner hover:shadow-md",
                  isLoading ? "opacity-50 cursor-not-allowed" : ""
                )}
                style={{
                  height: '40px',
                  maxHeight: '120px',
                  lineHeight: '1.4',
                  overflowY: 'hidden',
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none'
                }}
              />
              
              {/* Character Count - Professional */}
              {value && (
                <div className="absolute bottom-1 right-2 text-xs font-medium">
                  <span className={cn(
                    "px-2 py-1 rounded-full",
                    value.length > 1000 
                      ? "bg-red-100 text-red-600" 
                      : value.length > 500 
                        ? "bg-yellow-100 text-yellow-600" 
                        : "bg-green-100 text-green-600"
                  )}>
                    {value.length}
                  </span>
                </div>
              )}
            </div>

            {/* Send Button - Professional */}
            <div className="flex-shrink-0">
              <button
                onClick={onSend}
                disabled={!canSend || isLoading}
                className={cn(
                  "w-10 h-10 rounded-lg transition-all duration-300 flex items-center justify-center shadow-sm transform hover:scale-105",
                  canSend && !isLoading
                    ? "bg-gradient-to-r from-blue-500 via-indigo-500 to-blue-600 hover:from-blue-600 hover:via-indigo-600 hover:to-blue-700 text-white shadow-md" 
                    : "bg-gradient-to-r from-slate-300 to-slate-400 text-slate-500 cursor-not-allowed"
                )}
                type="button"
                title={!canSend ? "Type a message to send" : "Send message (Ctrl+Enter)"}
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <span className="text-lg">🚀</span>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Progress indicator for long operations */}
        {(isLoading || isUploading) && (
          <div className="absolute -bottom-1 left-0 right-0 h-1 bg-slate-200 rounded-full overflow-hidden">
            <div className="h-full bg-gradient-to-r from-blue-400 via-indigo-500 to-blue-600 rounded-full"></div>
          </div>
        )}
      </div>

      {/* Professional Status Messages */}
      <div className="flex justify-between items-center text-sm">
        <div className="flex items-center space-x-4">
          {uploadingFiles.length > 0 && (
            <span className="text-blue-600 font-medium flex items-center space-x-1">
              <span className="text-sm">📁</span>
              <span>{uploadingFiles.length} file{uploadingFiles.length !== 1 ? 's' : ''} attached</span>
            </span>
          )}
          
          {isUploading && (
            <span className="text-indigo-600 font-medium flex items-center space-x-1">
              <span className="text-sm">⬆️</span>
              <span>Uploading files...</span>
            </span>
          )}
        </div>
        
        <div className="text-slate-500 font-medium flex items-center space-x-1">
          <span className="text-sm">💡</span>
          <span>Shift+Enter for new line</span>
        </div>
      </div>
    </div>
  );
} 