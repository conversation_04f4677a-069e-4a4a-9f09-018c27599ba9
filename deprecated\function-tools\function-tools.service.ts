import { ToolFactory } from './tool-factory';
import { FunctionTool } from './base/function-tool.interface';

/**
 * Main Function Tools Service
 * Maintains backward compatibility with the original FunctionToolsService interface
 * while delegating to the new modular system
 */
export class FunctionToolsService {
  private toolFactory: ToolFactory;
  private isInitialized: boolean = false;

  constructor() {
    this.toolFactory = ToolFactory.getInstance();
  }

  /**
   * Initialize the function tools service
   * This replaces the original constructor initialization
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      this.toolFactory.initialize();
      this.isInitialized = true;
      console.log('[FunctionToolsService] Service initialized with modular architecture');
    }
  }

  /**
   * Get all available tools for OpenAI function calling
   * Maintains exact compatibility with original method
   */
  getToolsForOpenAI(): any[] {
    this.ensureInitialized();
    return this.toolFactory.getToolsForOpenAI();
  }

  /**
   * Execute a function tool by name
   * Maintains exact compatibility with original method
   */
  async executeTool(name: string, parameters: any): Promise<string> {
    this.ensureInitialized();
    return this.toolFactory.executeTool(name, parameters);
  }

  /**
   * Check if a tool exists
   * Maintains exact compatibility with original method
   */
  hasTool(name: string): boolean {
    this.ensureInitialized();
    return this.toolFactory.hasTool(name);
  }

  /**
   * Get tool names
   * Maintains exact compatibility with original method
   */
  getToolNames(): string[] {
    this.ensureInitialized();
    return this.toolFactory.getToolNames();
  }

  /**
   * Get available tool categories
   * Maintains exact compatibility with original method
   */
  getToolCategories(): Record<string, string[]> {
    this.ensureInitialized();
    return this.toolFactory.getToolCategoriesReport();
  }

  // =============================================================================
  // DEPRECATED METHODS (maintained for backward compatibility)
  // =============================================================================

  /**
   * @deprecated Use the modular system instead
   * Initialize all available function tools (no-op in new system)
   */
  private initializeTools(): void {
    // No-op - handled by factory initialization
    console.warn('[FunctionToolsService] initializeTools() is deprecated and handled automatically');
  }

  /**
   * @deprecated Use the modular system instead
   * Initialize dynamic tools (no-op in new system)
   */
  private initializeDynamicTools(): void {
    // No-op - handled by factory initialization
    console.warn('[FunctionToolsService] initializeDynamicTools() is deprecated and handled automatically');
  }

  /**
   * @deprecated Use the modular system instead
   * Register a function tool (delegated to factory)
   */
  private registerTool(tool: FunctionTool): void {
    console.warn('[FunctionToolsService] registerTool() is deprecated - tools are registered via categories');
    // Could delegate to factory if needed for backward compatibility
  }

  /**
   * @deprecated Use the modular system instead
   * Register a dynamic function tool (delegated to factory)
   */
  private registerDynamicTool(tool: FunctionTool): void {
    console.warn('[FunctionToolsService] registerDynamicTool() is deprecated - tools are registered via categories');
    // Could delegate to factory if needed for backward compatibility
  }

  // =============================================================================
  // NEW METHODS (leveraging modular architecture)
  // =============================================================================

  /**
   * Get service statistics
   */
  getStats(): {
    categories: number;
    totalTools: number;
    toolsByCategory: Record<string, number>;
    isInitialized: boolean;
  } {
    this.ensureInitialized();
    return this.toolFactory.getStats();
  }

  /**
   * Create a custom tool at runtime
   */
  createCustomTool(
    name: string,
    description: string,
    parameters: any,
    handler: (params: any) => Promise<string> | string
  ): FunctionTool {
    this.ensureInitialized();
    return this.toolFactory.createTool(name, description, parameters, handler);
  }

  /**
   * Add a platform-specific tool
   */
  addPlatformTool(platform: string, toolConfig: {
    name: string;
    description: string;
    parameters: any;
    handler: (params: any) => Promise<string> | string;
  }): FunctionTool {
    this.ensureInitialized();
    return this.toolFactory.addPlatformTool(platform, toolConfig);
  }

  /**
   * Remove a tool
   */
  removeTool(name: string): boolean {
    this.ensureInitialized();
    return this.toolFactory.removeTool(name);
  }

  /**
   * Validate the service state
   */
  validate(): {
    isValid: boolean;
    issues: string[];
  } {
    this.ensureInitialized();
    return this.toolFactory.validate();
  }

  /**
   * Get the underlying tool factory (for advanced usage)
   */
  getFactory(): ToolFactory {
    this.ensureInitialized();
    return this.toolFactory;
  }

  /**
   * Reset the service (useful for testing)
   */
  reset(): void {
    this.toolFactory.clear();
    this.isInitialized = false;
    console.log('[FunctionToolsService] Service reset');
  }

  // =============================================================================
  // LEGACY UTILITY METHODS (maintained for compatibility)
  // =============================================================================

  /**
   * Get MIME type based on file extension
   * Maintained for backward compatibility
   */
  private getMimeType(fileType: string): string {
    const mimeTypes: Record<string, string> = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'json': 'application/json',
      'csv': 'text/csv',
      'html': 'text/html',
      'xml': 'application/xml',
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };

    return mimeTypes[fileType.toLowerCase()] || 'text/plain';
  }
}

// Export singleton instance for backward compatibility
export const functionToolsService = new FunctionToolsService(); 