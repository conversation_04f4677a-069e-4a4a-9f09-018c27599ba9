/**
 * Test Teams OAuth endpoint specifically
 */

const SERVER_URL = 'http://localhost:5000';

async function testTeamsOAuth() {
  console.log('🔐 TESTING TEAMS OAUTH ENDPOINT');
  console.log('================================\n');

  try {
    // Test Teams OAuth endpoint with redirect URI
    console.log('Testing Teams OAuth endpoint with redirect URI...');
    const url = `${SERVER_URL}/api/integrations/21/teams-auth-url?redirectUri=http%3A%2F%2Flocalhost%3A5000%2Fapi%2Fintegrations%2Foauth%2Fcallback`;
    
    const response = await fetch(url);
    const responseText = await response.text();
    
    console.log(`Response Status: ${response.status}`);
    console.log(`Response Headers:`, Object.fromEntries(response.headers.entries()));
    console.log(`Response Body:`, responseText);
    
    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log(`✅ Teams OAuth endpoint returned:`, data);
        
        if (data.authUrl) {
          console.log(`✅ Auth URL received: ${data.authUrl.substring(0, 100)}...`);
        } else {
          console.log(`❌ No authUrl in response`);
        }
      } catch (parseError) {
        console.log(`❌ Response is not valid JSON:`, parseError);
      }
    } else {
      console.log(`❌ Teams OAuth failed with status: ${response.status}`);
    }
    
    // Test without redirect URI to see expected error
    console.log('\nTesting Teams OAuth endpoint without redirect URI...');
    const response2 = await fetch(`${SERVER_URL}/api/integrations/21/teams-auth-url`);
    const data2 = await response2.json();
    console.log(`Status: ${response2.status}, Response:`, data2);

  } catch (error) {
    console.error('❌ Error testing Teams OAuth:', error);
  }
}

testTeamsOAuth().catch(console.error); 