// MCP SDK Imports
import {
  Resource,
  Prompt,
  CallToolR<PERSON>ult,
  ReadResourceResult,
  GetPromptResult
} from '@modelcontextprotocol/sdk/types.js';
import { ChatCompletionTool } from 'openai/resources/index.mjs';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { FunctionDefinition } from 'openai/resources/index.mjs';

/**
 * MCP Server Configuration (Enhanced with SDK features)
 */
export interface MCPServerConfig {
  name: string;
  version: string;
  type: 'local' | 'remote';
  connection: {
    // For local servers
    command?: string;
    args?: string[];
    credentials?: string;
    // For remote servers
    url?: string;
    headers?: Record<string, string>;
  };
  enabled: boolean;
  description?: string;
  capabilities?: {
    tools?: {};
    resources?: { subscribe?: boolean; listChanged?: boolean };
    prompts?: { listChanged?: boolean };
    logging?: {};
  };
}

/**
 * Enhanced MCP Tool with server information and SDK compatibility
 */
export type MCPTool = ChatCompletionTool & {
  serverName: string;
  category?: string;
  lastUsed?: Date;
  usageCount?: number;
  enabled?: boolean;
}

/**
 * Enhanced MCP Resource with SDK compatibility
 */
export interface MCPResource extends Resource {
  serverName: string;
  category?: string;
  template?: string;
  parameters?: Record<string, any>;
}

/**
 * Enhanced MCP Prompt with SDK compatibility
 */
export interface MCPPrompt extends Prompt {
  serverName: string;
  category?: string;
  template?: string;
}

/**
 * MCP Result format (SDK compatible)
 */
export interface MCPResult extends CallToolResult {
  isError?: boolean;
  metadata?: Record<string, any>;
}

/**
 * OAuth Token structure for MCP servers
 */
export interface MCPTokens {
  access_token: string;
  refresh_token?: string;
  expiry_date?: number;
  token_type?: string;
  scope?: string;
}

/**
 * MCP Server Connection status
 */
export interface MCPConnection {
  serverName: string;
  connected: boolean;
  tools: MCPTool[];
  resources?: MCPResource[];
  prompts?: MCPPrompt[];
  lastConnected?: Date;
  error?: string;
  client: Client;
  transport: StdioClientTransport | SSEClientTransport;
}

/**
 * Enhanced MCP Server interface with SDK features
 */
export interface IMCPServer {
  name: string;
  version: string;
  description: string;

  // Core lifecycle
  initialize(): Promise<void>;
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;

  // Tools
  getTools(): MCPTool[];
  callTool(toolName: string, args: any): Promise<MCPResult>;
  addTool(tool: FunctionDefinition, handler: (args: any) => Promise<MCPResult>): void;
  removeTool(toolName: string): void;

  // Resources
  getResources(): MCPResource[];
  readResource(uri: string): Promise<ReadResourceResult>;
  addResource(resource: MCPResource, handler: (uri: string) => Promise<ReadResourceResult>): void;
  removeResource(uri: string): void;

  // Prompts
  getPrompts(): MCPPrompt[];
  getPrompt(name: string, args?: any): Promise<GetPromptResult>;
  addPrompt(prompt: MCPPrompt, handler: (args: any) => GetPromptResult): void;
  removePrompt(name: string): void;

  // Capabilities
  getCapabilities(): MCPServerConfig['capabilities'];
}

/**
 * Tool call arguments base interface
 */
export interface MCPToolArgs {
  userId: string;
  [key: string]: any;
}

/**
 * File operation result
 */
export interface FileOperationResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

/**
 * Search result for files
 */
export interface FileSearchResult {
  id: string;
  name: string;
  path?: string;
  type: string;
  size?: number;
  modifiedTime?: string;
  content?: string;
  platform: string;
}
