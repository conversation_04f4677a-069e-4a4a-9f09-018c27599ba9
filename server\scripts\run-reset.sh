#!/bin/bash

# MeetSync Database Reset Script
# Runs the SQL reset script to clear all connected state and fetched data

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to clear uploads folder
clear_uploads_folder() {
    echo -e "${YELLOW}🗂️ Clearing uploads folder...${NC}"
    
    UPLOADS_DIR="uploads"
    
    if [ -d "$UPLOADS_DIR" ]; then
        rm -rf "$UPLOADS_DIR"/*
        echo "  - Removed all files from uploads folder"
    else
        mkdir -p "$UPLOADS_DIR"
        echo "  - Created uploads folder"
    fi
    
    # Ensure uploads folder exists (even if it was just cleared)
    mkdir -p "$UPLOADS_DIR"
    echo "  - Uploads folder is ready"
}

echo -e "${YELLOW}🔄 MeetSync Database Reset${NC}"
echo "This will clear all connected integrations, sync data, and files"
echo "Integration records (names/types) will be preserved"
echo ""

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo -e "${RED}❌ Error: DATABASE_URL environment variable is not set${NC}"
    echo "Please set your DATABASE_URL before running this script"
    exit 1
fi

# Confirm before proceeding
read -p "Are you sure you want to proceed? (y/N) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}ℹ️  Reset cancelled${NC}"
    exit 0
fi

echo -e "${YELLOW}🔄 Running database reset...${NC}"

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Run the SQL script
if command -v psql >/dev/null 2>&1; then
    # Use psql if available
    psql "$DATABASE_URL" -f "$SCRIPT_DIR/reset-connected-state.sql"
elif command -v docker >/dev/null 2>&1; then
    # Use docker with postgres image if psql not available
    echo -e "${YELLOW}ℹ️  psql not found, using Docker...${NC}"
    docker run --rm -i postgres:15 psql "$DATABASE_URL" < "$SCRIPT_DIR/reset-connected-state.sql"
else
    echo -e "${RED}❌ Error: Neither psql nor docker found${NC}"
    echo "Please install PostgreSQL client (psql) or Docker to run this script"
    echo "Alternatively, you can manually run the SQL file:"
    echo "  $SCRIPT_DIR/reset-connected-state.sql"
    exit 1
fi

# Clear uploads folder after successful database reset
clear_uploads_folder

echo -e "${GREEN}✅ Database reset completed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Start your backend: npm run dev"
echo "2. Check the integrations page - all should show 'Disconnected'"
echo "3. Check logs/files pages - should be empty"
echo "4. Uploads folder has been cleared"
echo ""
echo "You can also use the API endpoint (dev only):"
echo "  curl -X POST http://localhost:5000/api/dev/reset-connected-state" 