{"name": "mcp-server", "version": "1.0.0", "description": "Local MCP Servers", "type": "module", "scripts": {"build": "tsc --project ../tsconfig.build.json", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "file-upload", "upload"], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "@types/express": "^4.17.21", "cors": "^2.8.5", "express": "^4.21.2", "google-auth-library": "^10.0.0-rc.3", "googleapis": "^150.0.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/node": "^24.0.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}}