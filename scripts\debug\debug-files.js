const postgres = require('postgres');
require('dotenv').config();

async function debugFiles() {
  const sql = postgres(process.env.DATABASE_URL);
  
  try {
    console.log('🔍 Checking files in database...\n');
    
    // Get all files with their chunk counts
    const filesWithChunks = await sql`
      SELECT 
        f.id,
        f.file_name,
        f.platform,
        f.file_type,
        f.mime_type,
        f.status,
        f.created_at,
        COUNT(fc.id) as chunk_count,
        LENGTH(f.file_content) as content_length
      FROM files f
      LEFT JOIN file_chunks fc ON f.id = fc.file_id
      WHERE f.file_name ILIKE '%anant%' OR f.file_name ILIKE '%resume%'
      GROUP BY f.id, f.file_name, f.platform, f.file_type, f.mime_type, f.status, f.created_at, f.file_content
      ORDER BY f.created_at DESC
    `;
    
    console.log('📄 Files matching "anant" or "resume":');
    console.log('=====================================');
    
    if (filesWithChunks.length === 0) {
      console.log('❌ No files found matching "anant" or "resume"');
    } else {
      filesWithChunks.forEach(file => {
        console.log(`📁 ${file.file_name}`);
        console.log(`   ID: ${file.id}`);
        console.log(`   Platform: ${file.platform}`);
        console.log(`   Type: ${file.file_type}`);
        console.log(`   MIME: ${file.mime_type}`);
        console.log(`   Status: ${file.status}`);
        console.log(`   Chunks: ${file.chunk_count}`);
        console.log(`   Content Length: ${file.content_length || 0} chars`);
        console.log(`   Created: ${file.created_at}`);
        console.log('');
      });
    }
    
    // Check for any PDF files
    console.log('\n📄 All PDF files in database:');
    console.log('==============================');
    
    const pdfFiles = await sql`
      SELECT 
        f.id,
        f.file_name,
        f.platform,
        f.file_type,
        f.mime_type,
        f.status,
        COUNT(fc.id) as chunk_count,
        LENGTH(f.file_content) as content_length
      FROM files f
      LEFT JOIN file_chunks fc ON f.id = fc.file_id
      WHERE f.mime_type = 'application/pdf' OR f.file_type = 'pdf'
      GROUP BY f.id, f.file_name, f.platform, f.file_type, f.mime_type, f.status, f.file_content
      ORDER BY f.created_at DESC
    `;
    
    if (pdfFiles.length === 0) {
      console.log('❌ No PDF files found');
    } else {
      pdfFiles.forEach(file => {
        console.log(`📁 ${file.file_name}`);
        console.log(`   ID: ${file.id}`);
        console.log(`   Platform: ${file.platform}`);
        console.log(`   Chunks: ${file.chunk_count}`);
        console.log(`   Content Length: ${file.content_length || 0} chars`);
        console.log('');
      });
    }
    
    // Check sample chunks for any file containing "anant"
    console.log('\n🔍 Sample chunks containing "anant":');
    console.log('====================================');
    
    const anantChunks = await sql`
      SELECT 
        fc.id,
        fc.file_id,
        f.file_name,
        fc.content,
        fc.chunk_index
      FROM file_chunks fc
      JOIN files f ON fc.file_id = f.id
      WHERE fc.content ILIKE '%anant%'
      ORDER BY fc.id DESC
      LIMIT 5
    `;
    
    if (anantChunks.length === 0) {
      console.log('❌ No chunks found containing "anant"');
    } else {
      anantChunks.forEach(chunk => {
        console.log(`📄 File: ${chunk.file_name} (ID: ${chunk.file_id})`);
        console.log(`   Chunk ${chunk.chunk_index}: ${chunk.content.substring(0, 200)}...`);
        console.log('');
      });
    }
    
    // Check total counts
    const totalFiles = await sql`SELECT COUNT(*) as count FROM files WHERE status = 'active'`;
    const totalChunks = await sql`SELECT COUNT(*) as count FROM file_chunks`;
    const totalSources = await sql`SELECT COUNT(DISTINCT platform) as count FROM files WHERE status = 'active'`;
    
    console.log('\n📊 Database Summary:');
    console.log('====================');
    console.log(`Total active files: ${totalFiles[0].count}`);
    console.log(`Total chunks: ${totalChunks[0].count}`);
    console.log(`Total platforms: ${totalSources[0].count}`);
    
  } catch (error) {
    console.error('❌ Error querying database:', error);
  } finally {
    await sql.end();
  }
}

debugFiles().catch(console.error);
