#!/usr/bin/env node

/**
 * Test script for OpenAI Batch Processing
 * Tests the new batch embedding functionality
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '.env') });

console.log('🧪 Testing OpenAI Batch Processing for Embeddings\n');

async function testBatchProcessing() {
  try {
    // Test 1: Initialize Batch Service
    console.log('1️⃣ Testing Batch Embedding Service...');
    const { batchEmbeddingService } = await import('./server/services/batch-embedding-service.js');
    
    if (batchEmbeddingService.isInitialized()) {
      console.log('✅ Batch embedding service initialized successfully');
    } else {
      console.log('❌ Batch embedding service failed to initialize');
      return;
    }

    // Test 2: Test RAG Service Batch Methods
    console.log('\n2️⃣ Testing RAG Service Batch Integration...');
    const { ragService } = await import('./server/services/rag-service.js');
    
    // Check for pending files
    console.log('Checking for files that need embeddings...');
    const batchId = await ragService.processPendingFilesBatch();
    
    if (batchId) {
      console.log(`✅ Batch job submitted: ${batchId}`);
      console.log('📋 Batch job details:');
      console.log(`   - Job ID: ${batchId}`);
      console.log(`   - Status: Submitted to OpenAI`);
      console.log(`   - Processing: Up to 24 hours`);
      console.log(`   - Cost: 50% cheaper than regular API`);
      
      // Check initial status
      console.log('\n3️⃣ Checking initial batch status...');
      const status = await ragService.checkBatchStatus(batchId);
      console.log(`✅ Batch status: ${status.status}`);
      console.log(`📊 Progress: ${status.request_counts?.completed || 0}/${status.request_counts?.total || 0}`);
      
    } else {
      console.log('ℹ️ No files need embedding processing');
    }

    // Test 3: List Pending Batches
    console.log('\n4️⃣ Listing all pending batch jobs...');
    const pendingBatches = await ragService.listPendingBatches();
    
    if (pendingBatches.length > 0) {
      console.log(`✅ Found ${pendingBatches.length} pending batch jobs:`);
      pendingBatches.forEach((batch, index) => {
        console.log(`   ${index + 1}. ${batch.id} - Status: ${batch.status}`);
      });
    } else {
      console.log('ℹ️ No pending batch jobs found');
    }

    // Test 4: Manual Batch Processing
    console.log('\n5️⃣ Testing manual batch processing...');
    const { storage } = await import('./server/storage/index.js');
    const allFiles = await storage.getFiles();
    
    if (allFiles.length > 0) {
      console.log(`Found ${allFiles.length} total files in database`);
      
      // Get first few files for testing
      const testFileIds = allFiles.slice(0, 3).map(f => f.id);
      console.log(`Testing batch processing with file IDs: ${testFileIds.join(', ')}`);
      
      try {
        const manualBatchId = await ragService.processBatchEmbeddings(testFileIds);
        console.log(`✅ Manual batch job submitted: ${manualBatchId}`);
      } catch (error) {
        console.log(`ℹ️ Manual batch processing: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Error in batch processing test:', error.message);
  }
}

async function monitorBatchJob(batchId) {
  console.log(`\n🔍 Monitoring batch job: ${batchId}`);
  
  try {
    const { ragService } = await import('./server/services/rag-service.js');
    
    let attempts = 0;
    const maxAttempts = 5;
    
    while (attempts < maxAttempts) {
      const status = await ragService.checkBatchStatus(batchId);
      
      console.log(`\n📊 Attempt ${attempts + 1}/${maxAttempts}:`);
      console.log(`   Status: ${status.status}`);
      console.log(`   Progress: ${status.request_counts?.completed || 0}/${status.request_counts?.total || 0}`);
      
      if (status.status === 'completed') {
        console.log('🎉 Batch job completed successfully!');
        console.log('✅ Embeddings have been processed and stored in database');
        break;
      } else if (status.status === 'failed') {
        console.log('❌ Batch job failed');
        console.log('Error details:', status.errors);
        break;
      } else if (status.status === 'cancelled') {
        console.log('⚠️ Batch job was cancelled');
        break;
      }
      
      attempts++;
      if (attempts < maxAttempts) {
        console.log('⏳ Waiting 30 seconds before next check...');
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
    
    if (attempts >= maxAttempts) {
      console.log('⏰ Monitoring timeout reached');
      console.log('💡 Batch jobs can take up to 24 hours to complete');
      console.log(`💡 Check status later with: ragService.checkBatchStatus("${batchId}")`);
    }
    
  } catch (error) {
    console.error('❌ Error monitoring batch job:', error.message);
  }
}

// Main execution
async function main() {
  await testBatchProcessing();
  
  // If a batch ID is provided as argument, monitor it
  const batchId = process.argv[2];
  if (batchId) {
    await monitorBatchJob(batchId);
  } else {
    console.log('\n💡 Usage Tips:');
    console.log('📋 To monitor a specific batch job:');
    console.log('   node test-batch-processing.js <batch-id>');
    console.log('\n🔄 To check batch status in your app:');
    console.log('   ragService.checkBatchStatus("batch-id")');
    console.log('\n📊 Batch API Benefits:');
    console.log('   ✅ 50% cheaper than regular API calls');
    console.log('   ✅ No rate limits (processes in background)');
    console.log('   ✅ Perfect for bulk operations');
    console.log('   ✅ Up to 24-hour processing window');
  }
}

main().catch(console.error);
