#!/usr/bin/env node

/**
 * Check the status of your batch processing job
 */

import dotenv from 'dotenv';
dotenv.config();

const batchId = process.argv[2];

if (!batchId) {
  console.log('❌ Please provide a batch ID');
  console.log('Usage: node scripts/check-batch-status.js <batch-id>');
  process.exit(1);
}

console.log(`🔍 Checking status of batch job: ${batchId}\n`);

async function checkStatus() {
  try {
    const { ragService } = await import('./server/services/rag-service.js');
    
    const status = await ragService.checkBatchStatus(batchId);
    
    console.log('📊 Batch Job Status:');
    console.log(`   ID: ${status.id}`);
    console.log(`   Status: ${status.status}`);
    console.log(`   Created: ${new Date(status.created_at * 1000).toLocaleString()}`);
    
    if (status.request_counts) {
      const { total, completed, failed } = status.request_counts;
      console.log(`   Progress: ${completed}/${total} completed`);
      if (failed > 0) {
        console.log(`   Failed: ${failed} requests`);
      }
    }
    
    console.log(`   Completion window: ${status.completion_window}`);
    
    if (status.status === 'validating') {
      console.log('\n⏳ Status: Validating your batch file...');
    } else if (status.status === 'in_progress') {
      console.log('\n🔄 Status: Processing your embeddings...');
      console.log('💡 This can take 10 minutes to 6 hours');
    } else if (status.status === 'finalizing') {
      console.log('\n🔧 Status: Finalizing results...');
      console.log('💡 Almost done!');
    } else if (status.status === 'completed') {
      console.log('\n🎉 Status: COMPLETED!');
      console.log('✅ All embeddings have been processed and stored');
      console.log('🚀 Your chat system is now ready to use');
    } else if (status.status === 'failed') {
      console.log('\n❌ Status: FAILED');
      console.log('Error details:', status.errors);
    } else if (status.status === 'cancelled') {
      console.log('\n⚠️ Status: CANCELLED');
    }
    
    if (status.metadata) {
      console.log('\n📋 Job Details:');
      console.log(`   Description: ${status.metadata.description}`);
      console.log(`   Files: ${status.metadata.file_count}`);
      console.log(`   Chunks: ${status.metadata.chunk_count}`);
    }
    
    return status;
    
  } catch (error) {
    console.error('❌ Error checking status:', error.message);
    throw error;
  }
}

checkStatus()
  .then(status => {
    if (status.status === 'completed') {
      console.log('\n🎯 Next Steps:');
      console.log('   1. Test your chat system');
      console.log('   2. All files are now searchable');
      console.log('   3. RAG functionality is fully working');
    } else if (['validating', 'in_progress', 'finalizing'].includes(status.status)) {
      console.log('\n⏰ Check again later:');
      console.log(`   node scripts/check-batch-status.js ${batchId}`);
    }
  })
  .catch(error => {
    console.error('\n💥 Status check failed:', error.message);
  });
