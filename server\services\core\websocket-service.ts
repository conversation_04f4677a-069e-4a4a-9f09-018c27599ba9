import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';

interface WebSocketMessage {
  type: string;
  data: any;
}

interface IntegrationStatusUpdate {
  integrationId: number;
  status: string;
  message?: string;
  syncLogId?: number;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Set<WebSocket> = new Set();

  /**
   * Initialize WebSocket server
   */
  initialize(server: Server) {
    this.wss = new WebSocketServer({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', (ws: WebSocket, request) => {
      console.log('[WebSocket] New client connected from:', request.socket.remoteAddress);
      
      // Add client to our set
      this.clients.add(ws);

      // Send welcome message
      this.sendToClient(ws, {
        type: 'connection',
        data: { message: 'Connected to GPT Unify WebSocket server' }
      });

      // Handle client messages
      ws.on('message', (data: Buffer) => {
        try {
          const message = JSON.parse(data.toString()) as WebSocketMessage;
          this.handleClientMessage(ws, message);
        } catch (error) {
          console.error('[WebSocket] Error parsing client message:', error);
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        console.log('[WebSocket] Client disconnected');
        this.clients.delete(ws);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error('[WebSocket] Client error:', error);
        this.clients.delete(ws);
      });
    });

    console.log('[WebSocket] Server initialized on /ws endpoint');
  }

  /**
   * Handle incoming messages from clients
   */
  private handleClientMessage(ws: WebSocket, message: WebSocketMessage) {
    console.log('[WebSocket] Received message:', message);

    switch (message.type) {
      case 'ping':
        this.sendToClient(ws, { type: 'pong', data: { timestamp: Date.now() } });
        break;
      case 'subscribe':
        // Client can subscribe to specific events if needed
        console.log('[WebSocket] Client subscribed to:', message.data);
        break;
      default:
        console.log('[WebSocket] Unknown message type:', message.type);
    }
  }

  /**
   * Send message to a specific client
   */
  private sendToClient(ws: WebSocket, message: WebSocketMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('[WebSocket] Error sending message to client:', error);
      }
    }
  }

  /**
   * Broadcast message to all connected clients
   */
  private broadcast(message: WebSocketMessage) {
    const messageStr = JSON.stringify(message);
    let sentCount = 0;

    this.clients.forEach((ws) => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(messageStr);
          sentCount++;
        } catch (error) {
          console.error('[WebSocket] Error broadcasting to client:', error);
          this.clients.delete(ws);
        }
      } else {
        // Remove dead connections
        this.clients.delete(ws);
      }
    });

    console.log(`[WebSocket] Broadcasted message to ${sentCount} clients`);
  }

  /**
   * Send integration status update to all clients
   */
  broadcastIntegrationStatusUpdate(update: IntegrationStatusUpdate) {
    console.log('[WebSocket] Broadcasting integration status update:', update);
    
    this.broadcast({
      type: 'integration_status_update',
      data: update
    });
  }

  /**
   * Send sync log update to all clients
   */
  broadcastSyncLogUpdate(syncLog: any) {
    console.log('[WebSocket] Broadcasting sync log update:', syncLog);
    
    this.broadcast({
      type: 'sync_log_update',
      data: syncLog
    });
  }

  /**
   * Send general notification to all clients
   */
  broadcastNotification(notification: { type: 'success' | 'error' | 'info', title: string, message: string }) {
    console.log('[WebSocket] Broadcasting notification:', notification);
    
    this.broadcast({
      type: 'notification',
      data: notification
    });
  }

  /**
   * Get number of connected clients
   */
  getConnectedClientsCount(): number {
    return this.clients.size;
  }

  /**
   * Close all connections and shutdown
   */
  shutdown() {
    if (this.wss) {
      console.log('[WebSocket] Shutting down WebSocket server...');
      
      // Close all client connections
      this.clients.forEach((ws) => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close(1000, 'Server shutting down');
        }
      });
      
      // Close the server
      this.wss.close(() => {
        console.log('[WebSocket] WebSocket server closed');
      });
      
      this.clients.clear();
      this.wss = null;
    }
  }
}

// Export singleton instance
export const websocketService = new WebSocketService(); 