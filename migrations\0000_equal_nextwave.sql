CREATE TABLE "file_access" (
	"id" serial PRIMARY KEY NOT NULL,
	"file_id" integer NOT NULL,
	"user_email" text NOT NULL,
	"access_level" text DEFAULT 'read' NOT NULL,
	"granted_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "files" (
	"id" serial PRIMARY KEY NOT NULL,
	"external_id" text NOT NULL,
	"file_name" text NOT NULL,
	"file_type" text NOT NULL,
	"mime_type" text,
	"file_size" integer,
	"platform" text NOT NULL,
	"file_content" text,
	"file_url" text,
	"download_url" text,
	"thumbnail_url" text,
	"parent_folder" text,
	"tags" text[],
	"extracted_metadata" jsonb,
	"source_url" text,
	"user_id" text,
	"organization_id" text,
	"sync_item_id" integer,
	"notion_page_id" text,
	"last_modified" timestamp,
	"is_shared" boolean DEFAULT false,
	"shared_with" text[],
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "integrations" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"status" text DEFAULT 'disconnected' NOT NULL,
	"credentials" text,
	"config" jsonb,
	"last_sync_at" timestamp,
	"next_sync_at" timestamp,
	"sync_schedule" text,
	"sync_status" text DEFAULT 'idle',
	"source_config" jsonb,
	"destination_config" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"is_llm_enabled" boolean DEFAULT true,
	"sync_filters" jsonb
);
--> statement-breakpoint
CREATE TABLE "meeting_access" (
	"id" serial PRIMARY KEY NOT NULL,
	"meeting_id" integer NOT NULL,
	"user_email" text NOT NULL,
	"access_level" text DEFAULT 'read' NOT NULL,
	"granted_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "meetings" (
	"id" serial PRIMARY KEY NOT NULL,
	"external_id" text NOT NULL,
	"meeting_name" text NOT NULL,
	"datetime" timestamp NOT NULL,
	"platform" text NOT NULL,
	"transcript_text" text,
	"transcript_file_url" text,
	"attendees" text[],
	"extracted_metadata" jsonb,
	"source_url" text,
	"user_id" text,
	"organization_id" text,
	"sync_item_id" integer,
	"notion_page_id" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sync_items" (
	"id" serial PRIMARY KEY NOT NULL,
	"integration_id" integer NOT NULL,
	"sync_log_id" integer,
	"external_id" text NOT NULL,
	"title" text NOT NULL,
	"type" text NOT NULL,
	"source_url" text,
	"destination_url" text,
	"metadata" jsonb,
	"status" text NOT NULL,
	"processed_at" timestamp,
	"error" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sync_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"integration_id" integer NOT NULL,
	"start_time" timestamp DEFAULT now() NOT NULL,
	"end_time" timestamp,
	"status" text NOT NULL,
	"items_processed" integer DEFAULT 0,
	"items_success" integer DEFAULT 0,
	"items_failed" integer DEFAULT 0,
	"details" jsonb,
	"error" text
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"username" text NOT NULL,
	"password" text NOT NULL,
	CONSTRAINT "users_username_unique" UNIQUE("username")
);
