import { storage } from '../../../storage/index.js';
import { 
  BaseToolCategory, 
  ToolCategory, 
  FunctionTool,
  ToolExecutionContext 
} from '../base/function-tool.interface';

/**
 * File management tools category
 * Handles file creation, search, and information retrieval with HITL patterns
 */
export class FileToolsCategory extends BaseToolCategory {
  constructor() {
    super(ToolCategory.FILE_MANAGEMENT);
  }

  initialize(): void {
    this.log('info', 'Initializing file management tools...');
    
    // Register file management tools
    this.registerTool(this.createDraftFileCreationTool());
    this.registerTool(this.createConfirmFileCreationTool());
    this.registerTool(this.createSearchFilesTool());
    this.registerTool(this.createFileInfoTool());
    
    this.log('info', `Initialized ${this.tools.size} file management tools`);
  }

  /**
   * Draft File Creation Tool (Step 1 of HITL)
   */
  private createDraftFileCreationTool(): FunctionTool {
    return {
      name: "draftFileCreation",
      description: "Draft a new file creation. This is a required step before creating any file. Prompts user for confirmation before actual creation.",
      parameters: this.createParameters({
        fileName: {
          type: "string",
          description: "Name of the file to create (with extension)",
        },
        content: {
          type: "string",
          description: "Content of the file to create",
        },
        fileType: {
          type: "string",
          description: "Type of file (txt, md, json, etc.)",
        },
        description: {
          type: "string",
          description: "Brief description of what this file contains",
        },
      }, ["fileName", "content", "fileType"]),
      handler: (params) => this.handleDraftFileCreation(params),
    };
  }

  /**
   * Confirm File Creation Tool (Step 2 of HITL)
   */
  private createConfirmFileCreationTool(): FunctionTool {
    return {
      name: "confirmFileCreation",
      description: "Confirm and create a file after drafting. Only use this after a draft has been created and user has given affirmative confirmation.",
      parameters: this.createParameters({
        confirmation: {
          type: "string",
          description: "User's affirmative confirmation to create the file",
        },
        fileName: {
          type: "string",
          description: "Name of the file to create",
        },
        content: {
          type: "string",
          description: "Content of the file",
        },
        fileType: {
          type: "string",
          description: "Type of file",
        },
      }, ["confirmation", "fileName", "content", "fileType"]),
      handler: (params) => this.handleConfirmFileCreation(params),
    };
  }

  /**
   * Search Files Tool
   */
  private createSearchFilesTool(): FunctionTool {
    return {
      name: "searchFiles",
      description: "Search for files by name, content, or description across all connected sources.",
      parameters: this.createParameters({
        query: {
          type: "string",
          description: "Search query (file name, content keywords, or description)",
        },
        platform: {
          type: "string",
          description: "Optional: specific platform to search (google_drive, uploaded_files, etc.)",
        },
        fileType: {
          type: "string",
          description: "Optional: specific file type to search for",
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return (default: 10)",
        },
      }, ["query"]),
      handler: (params) => this.handleSearchFiles(params),
    };
  }

  /**
   * Get File Info Tool
   */
  private createFileInfoTool(): FunctionTool {
    return {
      name: "getFileInfo",
      description: "Get detailed information about a specific file by ID or name.",
      parameters: this.createParameters({
        fileId: {
          type: "number",
          description: "File ID to get information for",
        },
        fileName: {
          type: "string",
          description: "Alternative: file name to search for",
        },
      }, []),
      handler: (params) => this.handleGetFileInfo(params),
    };
  }

  // =============================================================================
  // TOOL HANDLERS
  // =============================================================================

  /**
   * Handle draft file creation
   */
  private handleDraftFileCreation(params: any): string {
    const { fileName, content, fileType, description } = params;
    
    this.log('info', `Drafting file creation for: ${fileName}`);
    
    return `📄 **File Creation Draft**

**File Name:** ${fileName}
**File Type:** ${fileType}
**Description:** ${description || 'No description provided'}

**Content Preview:**
\`\`\`
${content.substring(0, 200)}${content.length > 200 ? '...' : ''}
\`\`\`

**Full Content Length:** ${content.length} characters

⚠️ **This is a draft only.** The file has not been created yet.

Would you like me to proceed with creating this file? Please confirm by saying "yes, create the file" or similar.`;
  }

  /**
   * Handle confirm file creation
   */
  private async handleConfirmFileCreation(params: any): Promise<string> {
    const { confirmation, fileName, content, fileType } = params;
    
    this.log('info', `Processing file creation confirmation for: ${fileName}`);
    
    // Check for affirmative confirmation
    if (!this.isAffirmativeConfirmation(confirmation)) {
      this.log('warn', `File creation cancelled - no affirmative confirmation: ${confirmation}`);
      return "❌ File creation cancelled. No affirmative confirmation detected.";
    }

    try {
      // Create file in uploaded_files platform
      const fileData = {
        externalId: `uploaded_${Date.now()}_${fileName}`,
        fileName,
        fileType,
        mimeType: this.getMimeType(fileType),
        platform: 'uploaded_files',
        fileContent: content,
        fileSize: content.length,
        userId: 'anonymous', // For MVP
        status: 'active',
        tags: ['ai-created'],
        extractedMetadata: {
          createdBy: 'GPT Unify',
          creationMethod: 'agentic-action',
          description: `File created via AI assistant`,
        },
      };

      const createdFile = await storage.createFile(fileData);
      
      this.log('info', `File created successfully: ${fileName} (ID: ${createdFile.id})`);
      
      return `✅ **File Created Successfully!**

**File ID:** ${createdFile.id}
**File Name:** ${fileName}
**File Type:** ${fileType}
**Size:** ${content.length} characters
**Platform:** Uploaded Files

The file has been created and is now available in your uploaded files. You can search for it or reference it in future conversations.`;
      
    } catch (error: any) {
      this.log('error', `Error creating file ${fileName}:`, error);
      return `❌ **Error Creating File:** ${error.message}`;
    }
  }

  /**
   * Handle search files
   */
  private async handleSearchFiles(params: any): Promise<string> {
    const { query, platform, fileType, limit = 10 } = params;
    
    this.log('info', `Searching files with query: ${query}`, { platform, fileType, limit });
    
    try {
      const files = await storage.searchFiles(query, platform, fileType);
      const limitedFiles = files.slice(0, limit);
      
      if (limitedFiles.length === 0) {
        return `🔍 **No files found** matching "${query}"${platform ? ` in ${platform}` : ''}${fileType ? ` with type ${fileType}` : ''}.`;
      }
      
      let result = `🔍 **Found ${limitedFiles.length} file(s)** matching "${query}":\n\n`;
      
      limitedFiles.forEach((file, index) => {
        result += `**${index + 1}. ${file.fileName}**\n`;
        result += `   - Platform: ${file.platform}\n`;
        result += `   - Type: ${file.fileType}\n`;
        result += `   - Size: ${file.fileSize || 'Unknown'} bytes\n`;
        result += `   - Modified: ${file.lastModified || file.updatedAt}\n`;
        if (file.extractedMetadata?.description) {
          result += `   - Description: ${file.extractedMetadata.description}\n`;
        }
        result += `\n`;
      });
      
      this.log('info', `Search completed: found ${limitedFiles.length} files`);
      return result;
    } catch (error: any) {
      this.log('error', `Search error for query ${query}:`, error);
      return `❌ **Search Error:** ${error.message}`;
    }
  }

  /**
   * Handle get file info
   */
  private async handleGetFileInfo(params: any): Promise<string> {
    const { fileId, fileName } = params;
    
    if (!fileId && !fileName) {
      return "❌ Please provide either fileId or fileName.";
    }
    
    this.log('info', `Getting file info`, { fileId, fileName });
    
    try {
      let file;
      
      if (fileId) {
        file = await storage.getFile(fileId);
      } else if (fileName) {
        const files = await storage.searchFiles(fileName);
        file = files.find(f => f.fileName === fileName) || files[0];
      }
      
      if (!file) {
        const identifier = fileId ? `ID ${fileId}` : `name "${fileName}"`;
        this.log('warn', `File not found with ${identifier}`);
        return `❌ File not found with ${identifier}.`;
      }
      
      this.log('info', `File info retrieved for: ${file.fileName} (ID: ${file.id})`);
      
      return `📄 **File Information**

**Name:** ${file.fileName}
**ID:** ${file.id}
**Type:** ${file.fileType}
**Platform:** ${file.platform}
**Size:** ${file.fileSize || 'Unknown'} bytes
**Status:** ${file.status}
**Created:** ${file.createdAt}
**Modified:** ${file.lastModified || file.updatedAt}
**User:** ${file.userId || 'Unknown'}

**Metadata:**
${file.extractedMetadata ? JSON.stringify(file.extractedMetadata, null, 2) : 'No metadata available'}

**Tags:** ${file.tags?.join(', ') || 'No tags'}`;
      
    } catch (error: any) {
      this.log('error', `Error getting file info:`, error);
      return `❌ **Error getting file info:** ${error.message}`;
    }
  }
} 