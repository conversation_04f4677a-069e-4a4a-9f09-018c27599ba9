import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

export default function Sidebar() {
  const [location] = useLocation();
  
  return (
    <aside className="w-64 bg-card shadow-md z-10 flex flex-col border-r border-border">
      <div className="p-4 border-b border-border">
        <h1 className="text-lg font-bold text-primary flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          GPT Unify
        </h1>
      </div>
      
      <nav className="flex-1 overflow-y-auto pt-2 scrollbar-hide">
        <ul>
          <li>
            <Link href="/dashboard">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/dashboard" 
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/dashboard" ? "text-primary" : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                Dashboard
              </div>
            </Link>
          </li>
          <li>
            <Link href="/chat">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/chat" 
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/chat" ? "text-primary" : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                Chat
              </div>
            </Link>
          </li>
          <li>
            <Link href="/integrations">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/integrations" || location.startsWith("/integrations/")
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/integrations" || location.startsWith("/integrations/") 
                    ? "text-primary" 
                    : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Integrations
              </div>
            </Link>
          </li>
          <li>
            <Link href="/files">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/files" 
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/files" ? "text-primary" : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Files
              </div>
            </Link>
          </li>
          <li>
            <Link href="/emails">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/emails" 
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/emails" ? "text-primary" : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Emails
              </div>
            </Link>
          </li>
          <li>
            <Link href="/schedules">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/schedules" 
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/schedules" ? "text-primary" : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Schedules
              </div>
            </Link>
          </li>
          <li>
            <Link href="/logs">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/logs" 
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/logs" ? "text-primary" : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Logs
              </div>
            </Link>
          </li>
          <li>
            <Link href="/settings">
              <div className={cn(
                "flex items-center px-4 py-3 hover:bg-accent cursor-pointer",
                location === "/settings" 
                  ? "bg-accent text-primary font-medium" 
                  : "text-muted-foreground"
              )}>
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-5 w-5 mr-3", 
                  location === "/settings" ? "text-primary" : "text-muted-foreground"
                )} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Settings
              </div>
            </Link>
          </li>
        </ul>
      </nav>
      
      <div className="p-4 border-t border-border">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-medium">
            AM
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-foreground">Alex Morgan</p>
            <p className="text-xs text-muted-foreground"><EMAIL></p>
          </div>
        </div>
      </div>
    </aside>
  );
}
