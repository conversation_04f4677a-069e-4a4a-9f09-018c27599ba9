import { Link, useLocation } from "wouter";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { 
  LayoutGrid, 
  MessageSquare, 
  Zap, 
  FolderOpen, 
  Mail, 
  Calendar, 
  FileText, 
  Settings,
  ChevronLeft,
  ChevronRight,
  User,
  Bot,
  Brain,
  Sparkles,
  Network,
  Database,
  Activity,
  Shield
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { ThemeToggle } from "@/components/ui/theme-toggle";

// Updated navigation items with 2025 AI-focused icons and modern design
const navigationItems = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutGrid,
    description: "Analytics & insights",
    gradient: "from-blue-500 to-cyan-500",
    category: "core"
  },
  {
    name: "Chat",
    href: "/chat", 
    icon: Bot, // Updated to Bot for AI emphasis
    description: "AI Assistant",
    gradient: "from-purple-500 to-pink-500",
    category: "ai",
    badge: "AI"
  },
  {
    name: "Integrations",
    href: "/integrations",
    icon: Network, // More modern than Zap
    description: "Connected platforms",
    gradient: "from-emerald-500 to-teal-500",
    category: "core"
  },
  {
    name: "Files",
    href: "/files",
    icon: Database, // More modern approach to files
    description: "Data management",
    gradient: "from-orange-500 to-amber-500",
    category: "data"
  },
  {
    name: "Emails",
    href: "/emails",
    icon: Mail,
    description: "Communication hub",
    gradient: "from-red-500 to-rose-500",
    category: "data"
  },
  {
    name: "Schedules",
    href: "/schedules",
    icon: Calendar,
    description: "Task automation",
    gradient: "from-indigo-500 to-purple-500",
    category: "productivity"
  },
  {
    name: "Logs",
    href: "/logs",
    icon: Activity, // More dynamic than FileText
    description: "System activity",
    gradient: "from-gray-500 to-slate-500",
    category: "admin"
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    description: "Configuration",
    gradient: "from-gray-600 to-gray-700",
    category: "admin"
  }
];

// Category colors for visual grouping (2025 trend)
const categoryColors = {
  core: "bg-blue-500/10 border-blue-200",
  ai: "bg-purple-500/10 border-purple-200",
  data: "bg-orange-500/10 border-orange-200", 
  productivity: "bg-green-500/10 border-green-200",
  admin: "bg-gray-500/10 border-gray-200"
};

export default function Sidebar() {
  const [location] = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  return (
    <aside className={cn(
      "bg-card/95 backdrop-blur-xl border-r border-border/50 transition-all duration-300 ease-in-out flex flex-col shadow-xl h-screen",
      "bg-gradient-to-b from-card via-card to-card/50", // Subtle gradient background
      isCollapsed ? "w-16" : "w-64"
    )}>
      {/* Header with enhanced branding */}
      <div className="p-4 border-b border-border/50">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">GU</span>
                </div>
                {/* AI indicator */}
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2 h-2 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  GPT Unify
                </h1>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Brain className="w-3 h-3" />
                  AI-Powered Platform
                </p>
              </div>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0 hover:bg-accent/50 transition-all duration-200"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
      
      {/* Navigation with enhanced styling */}
      <nav className="flex-1 overflow-y-auto py-4 space-y-2 scrollbar-hide min-h-0">
        {navigationItems.map((item, index) => {
          const isActive = location === item.href || 
            (item.href !== "/" && location.startsWith(item.href));
          const IconComponent = item.icon;
          
          return (
            <Link key={item.name} href={item.href}>
              <div className={cn(
                "group relative mx-2 rounded-xl transition-all duration-300 ease-out",
                "hover:bg-accent/60 hover:scale-105 hover:shadow-md cursor-pointer",
                "border border-transparent hover:border-border/30",
                isActive 
                  ? "bg-gradient-to-r shadow-lg scale-105 border-primary/20" 
                  : "text-muted-foreground hover:text-foreground",
                isActive && `bg-gradient-to-r ${item.gradient}`,
                isActive && "text-white"
              )}>
                <div className={cn(
                  "flex items-center py-3 px-3 relative",
                  isCollapsed ? "justify-center" : "space-x-3"
                )}>
                  {/* Icon with enhanced styling */}
                  <div className={cn(
                    "relative flex items-center justify-center",
                    "w-8 h-8 rounded-lg transition-all duration-200",
                    isActive 
                      ? "bg-white/20 shadow-inner" 
                      : "group-hover:bg-accent/30"
                  )}>
                    <IconComponent className={cn(
                      "h-5 w-5 flex-shrink-0 transition-all duration-200",
                      isActive ? "text-white" : "",
                      "group-hover:scale-110"
                    )} />
                    
                    {/* AI badge for AI-related items */}
                    {item.badge && !isCollapsed && (
                      <div className="absolute -top-2 -right-2">
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-green-400 to-emerald-500 text-white shadow-sm">
                          {item.badge}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {!isCollapsed && (
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className={cn(
                          "text-sm font-semibold truncate",
                          isActive ? "text-white" : ""
                        )}>
                          {item.name}
                        </p>
                        {item.badge && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-white/20 text-white/90 shadow-sm ml-2">
                            {item.badge}
                          </span>
                        )}
                      </div>
                      <p className={cn(
                        "text-xs truncate transition-colors mt-0.5",
                        isActive 
                          ? "text-white/80" 
                          : "text-muted-foreground group-hover:text-foreground/70"
                      )}>
                        {item.description}
                      </p>
                    </div>
                  )}
                </div>
                
                {/* Enhanced active indicator */}
                {isActive && (
                  <>
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white/60 rounded-l-full" />
                    <div className="absolute inset-0 bg-white/5 rounded-xl" />
                  </>
                )}
                
                {/* Enhanced tooltip for collapsed state */}
                {isCollapsed && (
                  <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-3 px-3 py-2 bg-popover/95 backdrop-blur-sm text-popover-foreground text-sm rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none z-50 border border-border/50">
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-muted-foreground">{item.description}</div>
                    {/* Tooltip arrow */}
                    <div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-popover/95" />
                  </div>
                )}
              </div>
            </Link>
          );
        })}
      </nav>
      
      {/* Enhanced footer with modern styling */}
      <div className="border-t border-border/50 p-4 space-y-3 bg-gradient-to-t from-card/50 to-transparent">
        {!isCollapsed && (
          <div className="flex items-center justify-between">
            <ThemeToggle />
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Shield className="w-3 h-3" />
              Secure
            </div>
          </div>
        )}
        
        <div className={cn(
          "flex items-center transition-all duration-200",
          "hover:bg-accent/30 rounded-lg p-2 -m-2",
          isCollapsed ? "justify-center" : "space-x-3"
        )}>
          <Avatar className="h-9 w-9 ring-2 ring-primary/20">
            <AvatarFallback className="bg-gradient-to-br from-green-400 to-blue-500 text-white font-semibold">
              AM
            </AvatarFallback>
          </Avatar>
          
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-foreground truncate">Alex Morgan</p>
              <p className="text-xs text-muted-foreground truncate flex items-center gap-1">
                <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                <EMAIL>
              </p>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
}
