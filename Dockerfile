# =============================================================================
# MeetSync Application - Multi-Stage Docker Build
# =============================================================================

# -----------------------------------------------------------------------------
# Stage 1: Base Dependencies
# -----------------------------------------------------------------------------
FROM node:20-alpine AS base

# Install system dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# -----------------------------------------------------------------------------
# Stage 2: Development Dependencies
# -----------------------------------------------------------------------------
FROM base AS dev-deps

# Install all dependencies including dev dependencies
RUN npm ci

# -----------------------------------------------------------------------------
# Stage 3: Frontend Build
# -----------------------------------------------------------------------------
FROM dev-deps AS frontend-build

# Copy source code needed for frontend build
COPY client/ ./client/
COPY shared/ ./shared/
COPY vite.config.ts ./
COPY tsconfig.json ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./
COPY components.json ./

# Build frontend with Vite
RUN npm run build

# -----------------------------------------------------------------------------
# Stage 4: Backend Build
# -----------------------------------------------------------------------------
FROM dev-deps AS backend-build

# Copy source code needed for backend build
COPY server/ ./server/
COPY shared/ ./shared/
COPY tsconfig.json ./

# Build backend with esbuild
RUN npm run build

# -----------------------------------------------------------------------------
# Stage 5: Production Runtime
# -----------------------------------------------------------------------------
FROM node:20-alpine AS production

# Install runtime dependencies for native modules and health checks
RUN apk add --no-cache \
    tini \
    dumb-init \
    curl \
    cairo \
    jpeg \
    pango \
    musl \
    giflib \
    pixman \
    libjpeg-turbo \
    freetype

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S meetsync -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy production dependencies
COPY --from=base /app/node_modules ./node_modules/
COPY --from=base /app/package*.json ./

# Copy built applications
COPY --from=frontend-build /app/dist/public ./dist/public/
COPY --from=backend-build /app/dist ./dist/

# Copy shared modules and configurations
COPY shared/ ./shared/
COPY migrations/ ./migrations/
COPY drizzle.config.ts ./
COPY uploads/ ./uploads/

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /app/uploads /app/temp && \
    chown -R meetsync:nodejs /app

# Copy health check script
COPY docker/healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Switch to non-root user
USER meetsync

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Use tini for proper signal handling
ENTRYPOINT ["/sbin/tini", "--"]

# Start the application
CMD ["node", "dist/index.js"] 