-- Fix email_chunks table to use vector type for embeddings
-- This migration changes the embedding column from text to vector

-- First, ensure the vector extension is enabled
CREATE EXTENSION IF NOT EXISTS vector;

-- Convert the existing embedding column from text to vector type
-- First handle any existing data (convert NULL text to NULL vector)
UPDATE email_chunks SET embedding = NULL WHERE embedding IS NOT NULL AND embedding = '';

-- Now alter the column type with proper casting
ALTER TABLE email_chunks 
ALTER COLUMN embedding TYPE vector(1536) USING 
  CASE 
    WHEN embedding IS NULL THEN NULL 
    ELSE embedding::vector(1536)
  END;

-- Add comment for clarity
COMMENT ON COLUMN email_chunks.embedding IS 'Vector embedding for semantic search (1536 dimensions for OpenAI text-embedding-3-small)';

-- Add index for vector similarity search
CREATE INDEX IF NOT EXISTS email_chunks_embedding_idx 
ON email_chunks USING ivfflat (embedding vector_cosine_ops); 