import { Request, Response } from "express";
import OpenAI from 'openai';
import { ragService } from "../services/rag";
import { embeddingService } from "../services/ai/embedding-service.js";
import { createChatSessionSchema, sendChatMessageSchema } from "../../shared/index.js";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Controller for chat and RAG operations with OpenAI integration
 */
class ChatController {
  /**
   * Create a new chat session
   */
  async createSession(req: Request, res: Response) {
    try {
      const validationResult = createChatSessionSchema.safeParse(req.body);

      if (!validationResult.success) {
        return res.status(400).json({
          message: "Invalid session data",
          errors: validationResult.error.errors,
        });
      }

      const { title, enabledSources, userId } = validationResult.data;

      const session = await ragService.createChatSession(
        title,
        enabledSources,
        userId || "anonymous"
      );

      return res.json({
        message: "Chat session created successfully",
        session,
      });
    } catch (error: any) {
      console.error("Error creating chat session:", error);
      return res.status(500).json({
        message: "Failed to create chat session",
        error: error.message,
      });
    }
  }

  /**
   * Get chat sessions for a user
   */
  async getSessions(req: Request, res: Response) {
    try {
      const userId = req.query.userId as string;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;

      const sessions = await ragService.getChatSessions(userId, limit);

      return res.json({
        sessions,
        count: sessions.length,
      });
    } catch (error: any) {
      console.error("Error getting chat sessions:", error);
      return res.status(500).json({
        message: "Failed to get chat sessions",
        error: error.message,
      });
    }
  }

  /**
   * Get a specific chat session
   */
  async getSession(req: Request, res: Response) {
    try {
      const sessionId = parseInt(req.params.id);
      if (isNaN(sessionId)) {
        return res.status(400).json({ message: "Invalid session ID" });
      }

      const session = await ragService.getChatSessions();
      const foundSession = session.find(s => s.id === sessionId);

      if (!foundSession) {
        return res.status(404).json({ message: "Chat session not found" });
      }

      return res.json({ session: foundSession });
    } catch (error: any) {
      console.error("Error getting chat session:", error);
      return res.status(500).json({
        message: "Failed to get chat session",
        error: error.message,
      });
    }
  }

  /**
   * Get messages for a chat session
   */
  async getMessages(req: Request, res: Response) {
    try {
      const sessionId = parseInt(req.params.id);
      if (isNaN(sessionId)) {
        return res.status(400).json({ message: "Invalid session ID" });
      }

      const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

      const messages = await ragService.getChatMessages(sessionId.toString(), limit);

      return res.json({
        messages,
        count: messages.length,
        sessionId,
      });
    } catch (error: any) {
      console.error("Error getting chat messages:", error);
      return res.status(500).json({
        message: "Failed to get chat messages",
        error: error.message,
      });
    }
  }

  /**
   * Send a message and get AI response using OpenAI
   */
  async sendMessage(req: Request, res: Response) {
    try {
      const sessionIdParam = req.params.id;
      
      // Try to parse as number first
      let sessionId = parseInt(sessionIdParam);
      
      // If it's not a valid number (like string session IDs), find or create a session
      if (isNaN(sessionId)) {
        // Look for an existing session or create a new one
        const existingSessions = await ragService.getChatSessions(undefined, 1);
        if (existingSessions.length > 0) {
          sessionId = existingSessions[0].id;
          console.log(`Using existing session ID: ${sessionId} for string session: ${sessionIdParam}`);
        } else {
          // Create a new session
          const newSession = await ragService.createChatSession(
            'New Chat Session',
            [],
            'anonymous'
          );
          sessionId = newSession.id;
          console.log(`Created new session ID: ${sessionId} for string session: ${sessionIdParam}`);
        }
      }

      const validationResult = sendChatMessageSchema.safeParse(req.body);

      if (!validationResult.success) {
        return res.status(400).json({
          message: "Invalid message data",
          errors: validationResult.error.errors,
        });
      }

      const { content, enabledSources, openaiConfig = {} } = validationResult.data;

      console.log(`Sending message to session ${sessionId}:`, content);
      console.log(`Enabled sources:`, enabledSources);

      // Get conversation history for context
      const messageHistory = await ragService.getChatMessages(sessionId.toString(), 20);

      // Prepare RAG context based on enabled sources using vector search
      let ragContext = '';
      let sourcesUsed: string[] = [];
      let relevantChunks: any[] = [];

      if (enabledSources && enabledSources.length > 0) {
        try {
          console.log('Searching with RAG for relevant context...');
          const contextResult = await ragService.getContextService().getRelevantContext(content, enabledSources, 5);
          
          relevantChunks = contextResult.chunks;
          ragContext = contextResult.context;
          
          sourcesUsed = Array.from(new Set(contextResult.chunks.map((chunk: any) => 
            chunk.platform || chunk.fileName || 'unknown'
          )));

          console.log(`Found ${contextResult.chunks.length} relevant chunks from sources: ${sourcesUsed.join(', ')}`);
        } catch (error) {
          console.error('Error in RAG search:', error);
          ragContext = 'Error retrieving context from enabled sources.';
        }
      }

      // Build message history for OpenAI
      const messages = [
        {
          role: 'system' as const,
          content: `You are GPT Unify, an AI assistant with access to integrated data sources.

${ragContext ? `CONTEXT FROM ENABLED SOURCES:
${ragContext}

Use this context to provide accurate, helpful responses. Reference specific sources when appropriate.` : 'No specific context available for this query.'}

ENABLED SOURCES: ${enabledSources?.join(', ') || 'None'}
AVAILABLE SOURCES: ${sourcesUsed.join(', ') || 'None'}

Provide direct, helpful responses based on the available information.`
        },
        ...messageHistory.slice(-10).map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        {
          role: 'user' as const,
          content: content
        }
      ];

      // Configure OpenAI parameters
      const openaiParams = {
        model: openaiConfig.model || process.env.OPENAI_MODEL || 'gpt-4',
        messages: messages,
        temperature: openaiConfig.temperature || 0.7,
        max_tokens: openaiConfig.maxTokens || 2000,
        top_p: openaiConfig.topP || 1,
      };

      // Call OpenAI API
      const completion = await openai.chat.completions.create(openaiParams);
      
      const aiResponse = completion.choices[0]?.message?.content || 'No response generated';
      const usage = completion.usage;

      // Save the conversation using existing RAG service
      // Note: We need to manually store the messages since we're bypassing the AI response service
      let result = {
        userMessage: null,
        aiMessage: null,
        relevantChunks: 0,
        sourcesUsed: enabledSources || [],
      };

      try {
        // Store user message
        const userMessage = await ragService.getChatService().storeUserMessage(
          sessionId.toString(),
          content,
          enabledSources || []
        );

        // Store AI response message  
        const aiMessage = await ragService.getChatService().storeAIMessage(
          sessionId.toString(),
          aiResponse,
          enabledSources || [],
          [] // No relevant chunks from our direct OpenAI call
        );

        result = {
          userMessage,
          aiMessage,
          relevantChunks: relevantChunks.length,
          sourcesUsed: enabledSources || [],
        };
      } catch (storageError) {
        console.error('Error storing messages:', storageError);
        // Continue even if storage fails
      }

      return res.json({
        message: "Message sent successfully",
        aiMessage: {
          content: aiResponse,
          model: openaiParams.model,
          tokenCount: usage?.total_tokens || 0,
        },
        sourcesUsed,
        usage: {
          prompt_tokens: usage?.prompt_tokens || 0,
          completion_tokens: usage?.completion_tokens || 0,
          total_tokens: usage?.total_tokens || 0,
        },
        userMessage: result.userMessage,
        storedAiMessage: result.aiMessage,
        relevantChunks: result.relevantChunks,
      });

    } catch (error: any) {
      console.error("Error sending message:", error);
      
      // Handle specific OpenAI errors
      if (error.code === 'insufficient_quota') {
        return res.status(429).json({ 
          message: 'OpenAI quota exceeded. Please check your billing details.',
          error: 'insufficient_quota'
        });
      }
      
      if (error.code === 'invalid_api_key') {
        return res.status(401).json({ 
          message: 'Invalid OpenAI API key. Please check your configuration.',
          error: 'invalid_api_key'
        });
      }

      return res.status(500).json({
        message: "Failed to send message",
        error: error.message,
      });
    }
  }

  /**
   * Send a message with streaming response - LobeChat style
   */
  async sendStreamingMessage(req: Request, res: Response) {
    try {
      let sessionId = req.params.id;
      let sessionCreated = false;
      
      console.log(`Starting streaming response for session ${sessionId}`);

      // Check if session exists, if not create it
      if (sessionId) {
        try {
          const existingSession = await ragService.getChatSession(sessionId);
          if (!existingSession) {
            console.log(`Session ${sessionId} not found, creating new session with this ID`);
            const newSession = await ragService.createChatSession(
              'New Chat Session',
              [],
              'anonymous'
            );
            // Create the session with the specific ID the frontend expects
            if (newSession) {
              // Delete the auto-generated session and create one with our desired ID
              try {
                const { storage } = await import("../storage/index.js");
                await storage.deleteChatSession(newSession.id);
                
                // Create session with the frontend-provided ID
                const sessionWithCorrectId = await storage.createChatSession({
                  id: sessionId,
                  title: 'New Chat Session',
                  enabledSources: [],
                  userId: 'anonymous'
                });
                console.log(`Created session with correct ID: ${sessionWithCorrectId.id}`);
              } catch (createError) {
                console.log(`Could not create session with specific ID, using generated ID ${newSession.id}`);
                sessionId = newSession.id;
              }
            }
            sessionCreated = true;
          }
        } catch (error) {
          console.log(`Error checking session ${sessionId}, creating new one:`, error);
          try {
            // Try to create session with the frontend-provided ID directly
            const { storage } = await import("../storage/index.js");
            const sessionWithCorrectId = await storage.createChatSession({
              id: sessionId,
              title: 'New Chat Session',
              enabledSources: [],
              userId: 'anonymous'
            });
            console.log(`Created session with specific ID: ${sessionWithCorrectId.id}`);
            sessionCreated = true;
          } catch (createError) {
            console.log(`Could not create session with specific ID, falling back to auto-generated:`, createError);
            const newSession = await ragService.createChatSession(
              'New Chat Session',
              [],
              'anonymous'
            );
            sessionId = newSession.id;
            sessionCreated = true;
          }
        }
      } else {
        // No session ID provided, create a new session
        const newSession = await ragService.createChatSession(
          'New Chat Session',
          [],
          'anonymous'
        );
        sessionId = newSession.id;
        sessionCreated = true;
      }

      const validationResult = sendChatMessageSchema.safeParse(req.body);

      if (!validationResult.success) {
        return res.status(400).json({
          message: "Invalid message data",
          errors: validationResult.error.errors,
        });
      }

      const { content, enabledSources = [] } = validationResult.data;

      // Set up Server-Sent Events
      res.writeHead(200, {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      console.log(`Using session ${sessionId} for streaming response${sessionCreated ? ' (newly created)' : ''}`);

      // Get conversation history for context
      const messageHistory = await ragService.getChatMessages(sessionId, 20);

      // Prepare RAG context
      let ragContext = '';
      let relevantChunks: any[] = [];

      if (enabledSources.length > 0) {
        try {
          const contextResult = await ragService.getContextService().getRelevantContext(content, enabledSources, 5);
          relevantChunks = contextResult.chunks;
          ragContext = contextResult.context;
        } catch (error) {
          console.error('Error in RAG search:', error);
          ragContext = 'Error retrieving context from enabled sources.';
        }
      }

      // Store user message first
      const userMessage = await ragService.getChatService().storeUserMessage(
        sessionId,
        content,
        enabledSources
      );

      let accumulatedResponse = '';

      // Use the RAG service's streaming method
      await ragService.getAIResponseService().generateStreamingAIResponse(
        content,
        ragContext,
        messageHistory,
        enabledSources,
        relevantChunks,
        // onChunk callback - send chunk to client
        (chunk: string) => {
          accumulatedResponse += chunk;
          res.write(chunk);
        },
        // onComplete callback - finish the response
        async (fullResponse: string) => {
          try {
            // Store AI response message
            await ragService.getChatService().storeAIMessage(
              sessionId,
              fullResponse,
              enabledSources,
              relevantChunks
            );
            
            console.log(`Streaming completed for session ${sessionId}, total length: ${fullResponse.length}`);
            res.end();
          } catch (error) {
            console.error('Error storing AI message:', error);
            res.end();
          }
        },
        // onError callback - handle errors
        (error: Error) => {
          console.error('Streaming error:', error);
          res.write(`\n\n**Error:** ${error.message}`);
          res.end();
        }
      );

    } catch (error: any) {
      console.error("Error in streaming message:", error);
      
      if (!res.headersSent) {
        return res.status(500).json({
          message: "Failed to send streaming message",
          error: error.message,
        });
      } else {
        res.write(`\n\n**Error:** ${error.message}`);
        res.end();
      }
    }
  }

  /**
   * Update a chat session (e.g., change enabled sources)
   */
  async updateSession(req: Request, res: Response) {
    try {
      const sessionId = parseInt(req.params.id);
      if (isNaN(sessionId)) {
        return res.status(400).json({ message: "Invalid session ID" });
      }

      const { title, enabledSources } = req.body;

      const updatedSession = await ragService.updateChatSession(sessionId.toString(), {
        title,
        enabledSources,
      });

      if (!updatedSession) {
        return res.status(404).json({ message: "Chat session not found" });
      }

      return res.json({
        message: "Chat session updated successfully",
        session: updatedSession,
      });
    } catch (error: any) {
      console.error("Error updating chat session:", error);
      return res.status(500).json({
        message: "Failed to update chat session",
        error: error.message,
      });
    }
  }

  /**
   * Delete a chat session
   */
  async deleteSession(req: Request, res: Response) {
    try {
      const sessionId = parseInt(req.params.id);
      if (isNaN(sessionId)) {
        return res.status(400).json({ message: "Invalid session ID" });
      }

      const success = await ragService.deleteChatSession(sessionId.toString());

      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }

      return res.json({
        message: "Chat session deleted successfully",
      });
    } catch (error: any) {
      console.error("Error deleting chat session:", error);
      return res.status(500).json({
        message: "Failed to delete chat session",
        error: error.message,
      });
    }
  }

  /**
   * Get available sources (integrations) for RAG
   */
  async getAvailableSources(req: Request, res: Response) {
    try {
      const userId = req.query.userId as string;

      const sources = await ragService.getAvailableSources(userId);

      return res.json({
        sources,
        count: sources.length,
      });
    } catch (error: any) {
      console.error("Error getting available sources:", error);
      return res.status(500).json({
        message: "Failed to get available sources",
        error: error.message,
      });
    }
  }

  /**
   * Process a file for embeddings (manual trigger)
   */
  async processFileForEmbeddings(req: Request, res: Response) {
    try {
      const fileId = parseInt(req.params.fileId);
      if (isNaN(fileId)) {
        return res.status(400).json({ message: "Invalid file ID" });
      }

      const { content } = req.body;
      if (!content) {
        return res.status(400).json({ message: "File content is required" });
      }

      if (!embeddingService.isInitialized()) {
        return res.status(503).json({ 
          message: "Embedding service not available. Please check OpenAI API key configuration." 
        });
      }

      await embeddingService.processFileForEmbeddings(fileId, content);

      return res.json({
        message: "File processed for embeddings successfully",
        fileId,
      });
    } catch (error: any) {
      console.error("Error processing file for embeddings:", error);
      return res.status(500).json({
        message: "Failed to process file for embeddings",
        error: error.message,
      });
    }
  }

  /**
   * Search similar content (for testing RAG functionality)
   */
  async searchSimilar(req: Request, res: Response) {
    try {
      const { query, enabledSources, limit } = req.body;

      if (!query) {
        return res.status(400).json({ message: "Search query is required" });
      }

      if (!embeddingService.isInitialized()) {
        return res.status(503).json({ 
          message: "Embedding service not available. Please check OpenAI API key configuration." 
        });
      }

      const results = await embeddingService.searchSimilarChunks(
        query,
        enabledSources || [],
        limit || 10
      );

      return res.json({
        results,
        count: results.length,
        query,
        enabledSources: enabledSources || [],
      });
    } catch (error: any) {
      console.error("Error searching similar content:", error);
      return res.status(500).json({
        message: "Failed to search similar content",
        error: error.message,
      });
    }
  }
}

// Export singleton instance
export const chatController = new ChatController();
