import type { Express } from "express";
import { storage } from "../storage";
import { integrationConfigSchema } from "../../shared/index.js";
import { cryptoService } from "../services/core/crypto-service";
import { googleService } from "../services/platform-integrations/google";

export function registerDebugRoutes(app: Express) {
  // Health check endpoint
  app.get('/api/diagnostic/health', async (req, res) => {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        databaseUrl: process.env.DATABASE_URL ? 'configured' : 'not configured',
        googleCredentials: {
          clientId: process.env.GOOGLE_CLIENT_ID ? 'configured' : 'not configured',
          clientSecret: process.env.GOOGLE_CLIENT_SECRET ? 'configured' : 'not configured'
        },
        encryptionKey: process.env.ENCRYPTION_KEY ? 'configured' : 'using default',
        services: {
          storage: 'unknown',
          scheduler: 'unknown'
        }
      };

      // Test storage
      try {
        const integrations = await storage.getIntegrations();
        health.services.storage = 'working';
        console.log(`[DIAGNOSTIC] Storage working - found ${integrations.length} integrations`);
      } catch (error) {
        health.services.storage = 'error';
        console.error('[DIAGNOSTIC] Storage error:', error);
      }

      res.json(health);
    } catch (error: any) {
      console.error('[DIAGNOSTIC] Health check error:', error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });

  // Test encryption/decryption
  app.post('/api/diagnostic/test-encryption', async (req, res) => {
    try {
      const testData = req.body.testData || 'test encryption data';
      console.log('[DIAGNOSTIC] Testing encryption with data:', testData);

      // Test encryption
      const encrypted = await cryptoService.encrypt(testData);
      console.log('[DIAGNOSTIC] Encryption successful');

      // Test format validation
      const isValidFormat = cryptoService.validateEncryptedFormat(encrypted);
      console.log('[DIAGNOSTIC] Format validation:', isValidFormat);

      // Test decryption
      const decrypted = await cryptoService.decrypt(encrypted);
      console.log('[DIAGNOSTIC] Decryption successful');

      const success = decrypted === testData;

      res.json({
        success,
        encrypted,
        decrypted,
        originalLength: testData.length,
        encryptedLength: encrypted.length,
        decryptedLength: decrypted.length,
        isValidFormat,
        match: success
      });

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Encryption test failed:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Test Google credentials for a specific integration
  app.get('/api/diagnostic/test-google-credentials/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log('[DIAGNOSTIC] Testing Google credentials for integration:', id);

      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration has no credentials' });
      }

      // Test credential format validation
      const isValidFormat = cryptoService.validateEncryptedFormat(integration.credentials);
      console.log('[DIAGNOSTIC] Credentials format valid:', isValidFormat);

      if (!isValidFormat) {
        return res.json({
          success: false,
          error: 'Invalid credential format',
          credentialsPreview: integration.credentials.substring(0, 50),
          expectedFormat: 'iv:encryptedData (base64)'
        });
      }

      // Test decryption
      let decryptedCredentials;
      try {
        decryptedCredentials = await cryptoService.decrypt(integration.credentials);
        console.log('[DIAGNOSTIC] Credentials decrypted successfully');
      } catch (decryptError: any) {
        return res.json({
          success: false,
          error: 'Decryption failed',
          details: decryptError.message,
          credentialsPreview: integration.credentials.substring(0, 50)
        });
      }

      // Test JSON parsing
      let parsedCredentials;
      try {
        parsedCredentials = JSON.parse(decryptedCredentials);
        console.log('[DIAGNOSTIC] Credentials JSON parsed successfully');
      } catch (parseError: any) {
        return res.json({
          success: false,
          error: 'JSON parsing failed',
          details: parseError.message,
          decryptedPreview: decryptedCredentials.substring(0, 100)
        });
      }

      // Test Google client creation
      try {
        const client = await googleService.getAuthorizedClient(integration.credentials);
        console.log('[DIAGNOSTIC] Google client created successfully');
        
        res.json({
          success: true,
          message: 'Google credentials are working correctly',
          credentialKeys: Object.keys(parsedCredentials),
          hasAccessToken: !!parsedCredentials.access_token,
          hasRefreshToken: !!parsedCredentials.refresh_token,
          expiryDate: parsedCredentials.expiry_date,
          tokenType: parsedCredentials.token_type
        });

      } catch (clientError: any) {
        res.json({
          success: false,
          error: 'Google client creation failed',
          details: clientError.message,
          credentialKeys: Object.keys(parsedCredentials),
          hasAccessToken: !!parsedCredentials.access_token
        });
      }

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Google credentials test failed:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Test integration creation with various payloads
  app.post('/api/diagnostic/test-integration-creation', async (req, res) => {
    try {
      console.log('[DIAGNOSTIC] Testing integration creation with payload:', req.body);
      
      // Test with minimal valid payload
      const testPayload = {
        type: 'google-drive',
        name: 'Test Google Drive Integration',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: false,
        syncFilters: {},
        syncSchedule: null
      };

      console.log('[DIAGNOSTIC] Testing with minimal payload:', testPayload);

      // Validate the schema
      const validationResult = integrationConfigSchema.safeParse(testPayload);
      if (!validationResult.success) {
        console.error('[DIAGNOSTIC] Schema validation failed:', validationResult.error.errors);
        return res.status(400).json({
          message: 'Schema validation failed',
          errors: validationResult.error.errors
        });
      }

      // Test storage creation
      const integration = await storage.createIntegration({
        type: testPayload.type,
        name: testPayload.name,
        status: 'disconnected',
        credentials: null,
        config: testPayload.config || {},
        sourceConfig: testPayload.sourceConfig || {},
        destinationConfig: testPayload.destinationConfig || {},
        isLlmEnabled: testPayload.isLlmEnabled !== false,
        syncFilters: testPayload.syncFilters || {},
        syncSchedule: testPayload.syncSchedule || null,
        syncStatus: 'idle',
      });

      console.log('[DIAGNOSTIC] Successfully created integration:', integration);

      // Verify we can retrieve it
      const allIntegrations = await storage.getIntegrations();
      console.log('[DIAGNOSTIC] Total integrations after creation:', allIntegrations.length);

      res.json({
        success: true,
        integration,
        totalIntegrations: allIntegrations.length,
        message: 'Integration creation test successful'
      });

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Integration creation test failed:', error);
      res.status(500).json({
        success: false,
        message: 'Integration creation test failed',
        error: error.message,
        stack: error.stack
      });
    }
  });

  // Seed initial data for testing
  app.post('/api/diagnostic/seed-data', async (req, res) => {
    try {
      console.log('[DIAGNOSTIC] Seeding initial test data...');

      // Create Google Drive integration (first)
      const googleDriveIntegration = await storage.createIntegration({
        type: 'google-drive',
        name: 'Google Drive Integration',
        status: 'connected',
        credentials: null,
        config: {
          description: 'Integration for syncing all Google Drive files'
        },
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
        syncStatus: 'idle'
      });

      // Create Gmail integration (second)
      const gmailIntegration = await storage.createIntegration({
        type: 'gmail',
        name: 'Gmail Integration',
        status: 'disconnected',
        credentials: null,
        config: {
          description: 'Integration for syncing Gmail emails'
        },
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
        syncStatus: 'idle'
      });

      // Create Google Calendar integration (third)
      const googleCalendarIntegration = await storage.createIntegration({
        type: 'google_calendar',
        name: 'Google Calendar Integration',
        status: 'disconnected',
        credentials: null,
        config: {
          description: 'Integration for syncing Google Calendar events'
        },
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
        syncStatus: 'idle'
      });

      // Create Microsoft Teams integration (fourth)
      const teamsIntegration = await storage.createIntegration({
        type: 'microsoft_teams',
        name: 'Microsoft Teams Integration',
        status: 'disconnected',
        credentials: null,
        config: {
          description: 'Integration for syncing Microsoft Teams transcripts'
        },
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
        syncStatus: 'idle'
      });

      // Create Slack integration (fifth)
      const slackIntegration = await storage.createIntegration({
        type: 'slack',
        name: 'Slack Integration',
        status: 'disconnected',
        credentials: null,
        config: {
          description: 'Integration for syncing Slack conversations'
        },
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
        syncStatus: 'idle'
      });

      const allIntegrations = await storage.getIntegrations();
      console.log('[DIAGNOSTIC] Seeded data successfully. Total integrations:', allIntegrations.length);

      res.json({
        success: true,
        message: 'Test data seeded successfully',
        integrations: [googleDriveIntegration, gmailIntegration, googleCalendarIntegration, teamsIntegration, slackIntegration],
        totalIntegrations: allIntegrations.length
      });

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Failed to seed data:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to seed test data',
        error: error.message
      });
    }
  });

  // Clear all integrations
  app.delete('/api/diagnostic/clear-integrations', async (req, res) => {
    try {
      console.log('[DIAGNOSTIC] Clearing all integrations...');
      
      const integrations = await storage.getIntegrations();
      let deletedCount = 0;

      for (const integration of integrations) {
        await storage.deleteIntegration(integration.id);
        deletedCount++;
      }

      const remainingIntegrations = await storage.getIntegrations();
      console.log('[DIAGNOSTIC] Cleared integrations. Deleted:', deletedCount, 'Remaining:', remainingIntegrations.length);

      res.json({
        success: true,
        deletedCount,
        remainingCount: remainingIntegrations.length,
        message: 'All integrations cleared'
      });

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Failed to clear integrations:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clear integrations',
        error: error.message
      });
    }
  });

  // Test specific integration retrieval
  app.get('/api/diagnostic/integration/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log('[DIAGNOSTIC] Testing integration retrieval for ID:', id);

      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      const integration = await storage.getIntegration(id);
      
      if (!integration) {
        console.log('[DIAGNOSTIC] Integration not found for ID:', id);
        return res.status(404).json({ message: 'Integration not found' });
      }

      console.log('[DIAGNOSTIC] Successfully retrieved integration:', integration);

      res.json({
        success: true,
        integration,
        message: 'Integration retrieved successfully'
      });

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Integration retrieval test failed:', error);
      res.status(500).json({
        success: false,
        message: 'Integration retrieval test failed',
        error: error.message
      });
    }
  });

  // Test schema validation with various payloads
  app.post('/api/diagnostic/test-schema', async (req, res) => {
    try {
      console.log('[DIAGNOSTIC] Testing schema validation with payload:', req.body);

      const validationResult = integrationConfigSchema.safeParse(req.body);

      if (validationResult.success) {
        res.json({
          success: true,
          message: 'Schema validation passed',
          data: validationResult.data
        });
      } else {
        res.json({
          success: false,
          message: 'Schema validation failed',
          errors: validationResult.error.errors
        });
      }

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Schema test error:', error);
      res.status(500).json({
        success: false,
        message: 'Schema test error',
        error: error.message
      });
    }
  });

  // Debug frontend-backend communication
  app.all('/api/diagnostic/echo', (req, res) => {
    console.log('[DIAGNOSTIC] Echo endpoint called:', {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      query: req.query
    });

    res.json({
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      query: req.query,
      timestamp: new Date().toISOString()
    });
  });

  // Test credential encryption/decryption flow end-to-end
  app.post('/api/diagnostic/test-credential-flow', async (req, res) => {
    try {
      console.log('[DIAGNOSTIC] Testing complete credential flow...');
      
      // Simulate OAuth tokens from Google
      const mockTokens = {
        access_token: 'mock_access_token_12345',
        refresh_token: 'mock_refresh_token_67890',
        scope: 'https://www.googleapis.com/auth/drive',
        token_type: 'Bearer',
        expiry_date: Date.now() + 3600000
      };
      
      console.log('[DIAGNOSTIC] Step 1: Test initial encryption');
      const encryptedCredentials = await cryptoService.encrypt(JSON.stringify(mockTokens));
      console.log('[DIAGNOSTIC] Initial encryption successful');
      
      console.log('[DIAGNOSTIC] Step 2: Test storage (should not double encrypt)');
      // Create a test integration 
      const testIntegration = await storage.createIntegration({
        type: 'google-drive',
        name: 'Credential Flow Test',
        status: 'disconnected',
        credentials: null,
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
        syncStatus: 'idle'
      });
      
      // Update with encrypted credentials (this should NOT double encrypt)
      const updatedIntegration = await storage.updateIntegration(testIntegration.id, {
        credentials: encryptedCredentials,
        status: 'connected'
      });
      
      console.log('[DIAGNOSTIC] Step 3: Test decryption');
      if (updatedIntegration?.credentials) {
        const decryptedCredentials = await cryptoService.decrypt(updatedIntegration.credentials);
        const parsedCredentials = JSON.parse(decryptedCredentials);
        
        console.log('[DIAGNOSTIC] Credential flow test completed successfully');
        
        // Clean up test integration
        await storage.deleteIntegration(testIntegration.id);
        
        res.json({
          success: true,
          message: 'Credential flow test passed',
          testResults: {
            originalTokens: mockTokens,
            encryptedLength: encryptedCredentials.length,
            decryptedTokens: parsedCredentials,
            tokenMatch: parsedCredentials.access_token === mockTokens.access_token
          }
        });
      } else {
        throw new Error('Updated integration has no credentials');
      }
      
    } catch (error: any) {
      console.error('[DIAGNOSTIC] Credential flow test failed:', error);
      res.status(500).json({
        success: false,
        error: error.message,
        details: error.stack
      });
    }
  });

  // Clear corrupted credentials for an integration
  app.post('/api/diagnostic/clear-credentials/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log('[DIAGNOSTIC] Clearing credentials for integration:', id);

      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Clear the credentials and reset status
      const updatedIntegration = await storage.updateIntegration(id, {
        credentials: null,
        status: 'disconnected'
      });

      console.log('[DIAGNOSTIC] Credentials cleared successfully');

      res.json({
        success: true,
        message: 'Credentials cleared successfully',
        integration: updatedIntegration
      });

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Failed to clear credentials:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Test sync functionality
  app.post('/api/diagnostic/test-sync/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log('[DIAGNOSTIC] Testing sync functionality for integration:', id);

      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration has no credentials' });
      }

      // Test Google credentials
      try {
        const client = await googleService.getAuthorizedClient(integration.credentials);
        console.log('[DIAGNOSTIC] Google credentials working');
      } catch (credError: any) {
        return res.json({
          success: false,
          error: 'Google credentials failed',
          details: credError.message
        });
      }

      // Test source folder access
      const sourceConfig = integration.sourceConfig || {};
      const sourceFolderId = sourceConfig.driveId;
      
      if (!sourceFolderId) {
        return res.json({
          success: false,
          error: 'No source folder configured',
          details: 'Please configure a Google Drive folder for this integration'
        });
      }

      try {
        const auth = await googleService.getAuthorizedClient(integration.credentials);
        const files = await googleService.listFiles(auth, sourceFolderId);
        console.log(`[DIAGNOSTIC] Found ${files.length} files in source folder`);
      } catch (folderError: any) {
        return res.json({
          success: false,
          error: 'Source folder access failed',
          details: folderError.message
        });
      }

      // Test Notion setup (if configured)
      const destinationConfig = integration.destinationConfig || {};
      let notionStatus = 'not configured';
      
      if (process.env.NOTION_API_KEY) {
        try {
          const { notionService } = await import('../services/platform-integrations/notion');
          await notionService.initializeFromEncrypted(process.env.NOTION_API_KEY);

          if (process.env.NOTION_PAGE_URL) {
            const { extractPageIdFromUrl } = await import('../utils');
            const pageId = extractPageIdFromUrl(process.env.NOTION_PAGE_URL);
            const databaseId = await notionService.findOrCreateTranscriptsDatabase(pageId);
            notionStatus = `ready (database: ${databaseId})`;
          } else {
            notionStatus = 'API key configured but no page URL';
          }
        } catch (notionError: any) {
          notionStatus = `error: ${notionError.message}`;
        }
      }

      res.json({
        success: true,
        message: 'Sync test completed',
        results: {
          googleCredentials: 'working',
          sourceFolderAccess: 'working',
          notionSetup: notionStatus,
          integrationId: integration.id,
          integrationName: integration.name,
          sourceFolder: sourceFolderId
        }
      });

    } catch (error: any) {
      console.error('[DIAGNOSTIC] Sync test failed:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });
} 