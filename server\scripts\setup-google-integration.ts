/**
 * Setup script for Google Drive integration
 * for comprehensive document synchronization.
 *
 * Usage: npm run setup-google
 */

// Storage will be imported dynamically when needed

/**
 * Create a Google Drive integration with default settings
 */
async function setupGoogleIntegration() {
  try {
    console.log('Setting up Google Drive integration...');

    // Get storage dynamically
    const { storage } = await import('../storage/index.js');

    // Check if integration already exists
    const existingIntegrations = await storage.getIntegrationsByType('google_drive');
    
    if (existingIntegrations.length > 0) {
      console.log('Google Drive integration already exists, skipping setup');
      return;
    }
    
    // Create a new integration for Google Drive
    const integration = await storage.createIntegration({
      type: 'google_drive',
      name: 'Google Drive Documents',
      status: 'disconnected',
      credentials: null,
      config: {
        // Default configuration settings
        syncEnabled: true,
        processingEnabled: true,
      },
      sourceConfig: {
        // Google Drive folder will be selected by the user during setup
        folderId: '',
        includeSubfolders: false,
        filenamePattern: '*',
      },
      destinationConfig: {
        // Database storage for comprehensive document indexing
        createNewPages: true,
        updateExisting: true,
      },
      syncFilters: {
        // Default sync filter settings
        skipProcessed: true,
        dateRange: {
          start: null, // No start date limit
          end: null,   // No end date limit
        },
        fileTypes: ['document', 'presentation', 'spreadsheet', 'pdf', 'other'],
      },
      syncSchedule: null, // No default schedule
      isLlmEnabled: true, // Enable LLM for metadata extraction by default
      syncStatus: 'idle',
    });
    
    console.log(`Created Google Drive integration with ID: ${integration.id}`);
    
    // Display next steps
    console.log('\nNext steps:');
    console.log('1. Configure Google OAuth credentials in the application');
    console.log('2. Sync your Google Drive documents for AI-powered search');
  } catch (error) {
    console.error('Error setting up Google Drive integration:', error);
  }
}

async function main() {
  await setupGoogleIntegration();
  console.log('Setup complete!');
  process.exit(0);
}

// Run the main function if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}