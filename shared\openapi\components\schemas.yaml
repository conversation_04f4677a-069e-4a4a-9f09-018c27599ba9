# Common Response Schemas
ApiResponse:
  type: object
  properties:
    success:
      type: boolean
    message:
      type: string
    data:
      type: object
    error:
      type: string

ErrorResponse:
  type: object
  properties:
    success:
      type: boolean
      default: false
    message:
      type: string
    error:
      type: string

# Health & System Schemas
HealthStatus:
  type: object
  properties:
    status:
      type: string
      enum: [healthy, degraded, unhealthy]
    timestamp:
      type: string
      format: date-time
    services:
      type: object
      properties:
        database:
          $ref: '#/ServiceHealth'
        openai:
          $ref: '#/ServiceHealth'
        environment:
          $ref: '#/ServiceHealth'

ServiceHealth:
  type: object
  properties:
    status:
      type: string
      enum: [healthy, unhealthy]
    message:
      type: string
    details:
      type: object

# Integration Schemas
Integration:
  type: object
  properties:
    id:
      type: integer
    name:
      type: string
    type:
      type: string
      enum: [google-drive, microsoft_teams, slack]
    status:
      type: string
      enum: [connected, disconnected, error]
      default: disconnected
    credentials:
      type: string
      description: Encrypted OAuth tokens or API keys
    config:
      type: object
      description: Platform-specific configuration
    lastSyncAt:
      type: string
      format: date-time
      nullable: true
    nextSyncAt:
      type: string
      format: date-time
      nullable: true
    syncSchedule:
      type: string
      description: Cron expression
      nullable: true
    syncStatus:
      type: string
      enum: [idle, running, success, failed]
      default: idle
    sourceConfig:
      type: object
      description: Configuration for source (e.g., Google Drive folder ID)
    destinationConfig:
      type: object
      description: Configuration for destination (e.g., Notion database ID)
    createdAt:
      type: string
      format: date-time
    updatedAt:
      type: string
      format: date-time
    isLlmEnabled:
      type: boolean
      default: true
    syncFilters:
      type: object
      description: Any filters to apply to sync process

CreateIntegration:
  type: object
  required:
    - name
    - type
    - status
  properties:
    name:
      type: string
    type:
      type: string
      enum: [google-drive, microsoft_teams, slack]
    status:
      type: string
      enum: [connected, disconnected, error]
    credentials:
      type: string
    config:
      type: object
    syncSchedule:
      type: string
    sourceConfig:
      type: object
    destinationConfig:
      type: object
    isLlmEnabled:
      type: boolean
    syncFilters:
      type: object

# Sync Schemas
SyncLog:
  type: object
  properties:
    id:
      type: integer
    integrationId:
      type: integer
    startTime:
      type: string
      format: date-time
    endTime:
      type: string
      format: date-time
      nullable: true
    status:
      type: string
      enum: [success, partial, failed]
    itemsProcessed:
      type: integer
      default: 0
    itemsSuccess:
      type: integer
      default: 0
    itemsFailed:
      type: integer
      default: 0
    details:
      type: object
    error:
      type: string
      nullable: true

SyncItem:
  type: object
  properties:
    id:
      type: integer
    integrationId:
      type: integer
    syncLogId:
      type: integer
      nullable: true
    externalId:
      type: string
      description: ID in the source system (e.g., Google Doc ID)
    title:
      type: string
    type:
      type: string
      enum: [transcript, chat_log]
    sourceUrl:
      type: string
      nullable: true
    destinationUrl:
      type: string
      nullable: true
    metadata:
      type: object
      description: Extracted metadata like meeting name, date, time, attendees
    status:
      type: string
      enum: [pending, processing, success, failed, skipped]
    processedAt:
      type: string
      format: date-time
      nullable: true
    error:
      type: string
      nullable: true
    createdAt:
      type: string
      format: date-time

# Chat Schemas
ChatSession:
  type: object
  properties:
    id:
      type: integer
    userId:
      type: string
      maxLength: 255
      nullable: true
    title:
      type: string
      maxLength: 500
      nullable: true
    enabledSources:
      type: array
      items:
        type: string
      description: Array of integration IDs or platform names
    createdAt:
      type: string
      format: date-time
    updatedAt:
      type: string
      format: date-time

CreateChatSession:
  type: object
  properties:
    userId:
      type: string
      maxLength: 255
    title:
      type: string
      maxLength: 500
    enabledSources:
      type: array
      items:
        type: string

ChatMessage:
  type: object
  properties:
    id:
      type: integer
    sessionId:
      type: integer
    role:
      type: string
      enum: [user, assistant]
    content:
      type: string
    sourcesUsed:
      type: array
      items:
        type: string
      description: Which integrations/files were used for this response
    relevantChunks:
      type: array
      items:
        type: integer
      description: IDs of file chunks used for context
    tokenCount:
      type: integer
      nullable: true
    model:
      type: string
      maxLength: 100
      default: gpt-4.1-nano
      description: Track which model was used
    createdAt:
      type: string
      format: date-time

SendChatMessage:
  type: object
  required:
    - sessionId
    - role
    - content
  properties:
    sessionId:
      type: integer
    role:
      type: string
      enum: [user, assistant]
    content:
      type: string
    sourcesUsed:
      type: array
      items:
        type: string
    relevantChunks:
      type: array
      items:
        type: integer
    tokenCount:
      type: integer
    model:
      type: string
      maxLength: 100

# File Schemas
File:
  type: object
  properties:
    id:
      type: integer
    externalId:
      type: string
      description: Platform-native file ID
    fileName:
      type: string
    fileType:
      type: string
      enum: [transcript, doc, ppt, pdf, video, audio]
    mimeType:
      type: string
      nullable: true
    fileSize:
      type: integer
      format: int64
      nullable: true
    platform:
      type: string
      enum: [google_drive, microsoft_teams, zoom, slack]
    fileContent:
      type: string
      nullable: true
      description: For backwards compatibility, will be phased out
    fileUrl:
      type: string
      nullable: true
    downloadUrl:
      type: string
      nullable: true
    thumbnailUrl:
      type: string
      nullable: true
    parentFolder:
      type: string
      nullable: true
    tags:
      type: array
      items:
        type: string
    extractedMetadata:
      type: object
      description: AI-extracted metadata
    sourceUrl:
      type: string
      nullable: true
      description: Direct link to file
    userId:
      type: string
      nullable: true
      description: Owner
    organizationId:
      type: string
      nullable: true
    syncItemId:
      type: integer
      nullable: true
    notionPageId:
      type: string
      nullable: true
      description: For backwards compatibility
    lastModified:
      type: string
      format: date-time
      nullable: true
    isShared:
      type: boolean
      default: false
    sharedWith:
      type: array
      items:
        type: string
      description: Array of user emails
    status:
      type: string
      enum: [active, deleted, inactive]
      default: active
    createdAt:
      type: string
      format: date-time
    updatedAt:
      type: string
      format: date-time

FileChunk:
  type: object
  properties:
    id:
      type: integer
    fileId:
      type: integer
    chunkIndex:
      type: integer
    content:
      type: string
    embedding:
      type: array
      items:
        type: number
      description: OpenAI text-embedding-3-small (1536 dimensions)
    tokenCount:
      type: integer
      nullable: true
    metadata:
      type: object
      description: Chunk-specific metadata
    createdAt:
      type: string
      format: date-time

# Search & RAG Schemas
SearchRequest:
  type: object
  required:
    - query
  properties:
    query:
      type: string
    limit:
      type: integer
      default: 10
    platform:
      type: string
      enum: [google_drive, microsoft_teams, zoom, slack]
    fileType:
      type: string
      enum: [transcript, doc, ppt, pdf, video, audio]

SearchResult:
  type: object
  properties:
    files:
      type: array
      items:
        $ref: '#/File'
    total:
      type: integer
    query:
      type: string

RagSearchRequest:
  type: object
  required:
    - query
  properties:
    query:
      type: string
    limit:
      type: integer
      default: 5
    threshold:
      type: number
      format: float
      default: 0.7

RagSearchResult:
  type: object
  properties:
    results:
      type: array
      items:
        type: object
        properties:
          content:
            type: string
          score:
            type: number
            format: float
          fileId:
            type: integer
          fileName:
            type: string
          chunkIndex:
            type: integer

# Upload Schemas
UploadStats:
  type: object
  properties:
    totalFiles:
      type: integer
    totalSize:
      type: integer
      format: int64
    byMimeType:
      type: object
      additionalProperties:
        type: integer

# OAuth & Authentication Schemas
OAuthUrl:
  type: object
  properties:
    authUrl:
      type: string
      format: uri
    state:
      type: string

# Google Drive Schemas
GoogleDriveFolder:
  type: object
  properties:
    id:
      type: string
    name:
      type: string
    parents:
      type: array
      items:
        type: string
    mimeType:
      type: string

DriveStructure:
  type: object
  properties:
    folders:
      type: array
      items:
        $ref: '#/GoogleDriveFolder'
    files:
      type: array
      items:
        type: object

# Microsoft Teams Schemas
TeamsSource:
  type: object
  properties:
    id:
      type: string
    name:
      type: string
    type:
      type: string
      enum: [team, channel, chat]

TeamsChannel:
  type: object
  properties:
    id:
      type: string
    displayName:
      type: string
    description:
      type: string
    membershipType:
      type: string 