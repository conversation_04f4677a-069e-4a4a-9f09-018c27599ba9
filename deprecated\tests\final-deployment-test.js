#!/usr/bin/env node

/**
 * 🚀 FINAL DEPLOYMENT READINESS TEST
 * 
 * This script performs focused testing on the core functionality that matters
 * for safe deployment of the modularized integration controller.
 */

import { promises as fs } from 'fs';
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

class DeploymentReadinessTest {
  constructor() {
    this.results = {
      coreApiEndpoints: [],
      methodCoverage: [],
      criticalPaths: []
    };
    this.totalTests = 0;
    this.passedTests = 0;
  }

  async runDeploymentTest() {
    console.log('🚀 FINAL DEPLOYMENT READINESS TEST');
    console.log('==================================');
    
    await this.testCoreApiEndpoints();
    await this.testMethodCoverage();
    await this.testCriticalPaths();
    await this.generateDeploymentReport();
  }

  async testCoreApiEndpoints() {
    console.log('\n✅ Testing Core API Endpoints...');
    
    const coreEndpoints = [
      { method: 'GET', path: '/api/integrations', expected: 200 },
      { method: 'POST', path: '/api/integrations', expected: 400, body: {} }, // Expect validation error
      { method: 'GET', path: '/api/integrations/999', expected: 404 },
      { method: 'DELETE', path: '/api/integrations/999', expected: 404 },
      { method: 'POST', path: '/api/schedules', expected: 400, body: {} },
      { method: 'GET', path: '/api/schedules', expected: 200 }
    ];

    for (const endpoint of coreEndpoints) {
      try {
        const response = await fetch(`${BASE_URL}${endpoint.path}`, {
          method: endpoint.method,
          headers: { 'Content-Type': 'application/json' },
          body: endpoint.body ? JSON.stringify(endpoint.body) : undefined
        });

        const success = response.status === endpoint.expected;
        this.recordResult('coreApiEndpoints', 
          `${endpoint.method} ${endpoint.path}`,
          success,
          `Expected ${endpoint.expected}, got ${response.status}`
        );

        if (success) {
          console.log(`  ✅ ${endpoint.method} ${endpoint.path}: ${response.status}`);
        } else {
          console.log(`  ❌ ${endpoint.method} ${endpoint.path}: Expected ${endpoint.expected}, got ${response.status}`);
        }
      } catch (error) {
        this.recordResult('coreApiEndpoints', 
          `${endpoint.method} ${endpoint.path}`,
          false,
          `Request failed: ${error.message}`
        );
        console.log(`  ❌ ${endpoint.method} ${endpoint.path}: Request failed`);
      }
    }
  }

  async testMethodCoverage() {
    console.log('\n✅ Testing Method Coverage...');
    
    const criticalMethods = [
      'getIntegrations',
      'getIntegration', 
      'createIntegration',
      'updateIntegration',
      'deleteIntegration',
      'getAuthUrl',
      'handleOAuthCallback',
      'testConnection',
      'updateSchedule',
      'getSchedules'
    ];

    try {
      const facadeContent = await fs.readFile('server/controllers/integration/index.ts', 'utf-8');
      
      for (const method of criticalMethods) {
        const hasMethod = facadeContent.includes(`async ${method}(`);
        this.recordResult('methodCoverage', method, hasMethod, hasMethod ? 'Present' : 'Missing');
        
        if (hasMethod) {
          console.log(`  ✅ ${method}: Present in facade`);
        } else {
          console.log(`  ❌ ${method}: Missing from facade`);
        }
      }
    } catch (error) {
      console.log(`  ❌ Error reading facade file: ${error.message}`);
    }
  }

  async testCriticalPaths() {
    console.log('\n✅ Testing Critical Integration Paths...');
    
    // Test basic integration lifecycle
    try {
      // 1. Create integration
      const createResponse = await fetch(`${BASE_URL}/api/integrations`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'google-drive',
          name: 'Deployment Test Integration',
          config: {},
          sourceConfig: {},
          destinationConfig: {},
          isLlmEnabled: false,
          syncFilters: {},
          syncSchedule: null
        })
      });

      if (createResponse.status === 201) {
        const createResult = await createResponse.json();
        const integrationId = createResult.integration.id;
        
        this.recordResult('criticalPaths', 'Create Integration', true, `Created integration ${integrationId}`);
        console.log(`  ✅ Create Integration: Created integration ${integrationId}`);

        // 2. Read integration
        const readResponse = await fetch(`${BASE_URL}/api/integrations/${integrationId}`);
        if (readResponse.status === 200) {
          this.recordResult('criticalPaths', 'Read Integration', true, 'Successfully retrieved integration');
          console.log(`  ✅ Read Integration: Successfully retrieved`);
        } else {
          this.recordResult('criticalPaths', 'Read Integration', false, `Failed with status ${readResponse.status}`);
          console.log(`  ❌ Read Integration: Failed with status ${readResponse.status}`);
        }

        // 3. Delete integration (cleanup)
        const deleteResponse = await fetch(`${BASE_URL}/api/integrations/${integrationId}`, {
          method: 'DELETE'
        });
        if (deleteResponse.status === 200) {
          this.recordResult('criticalPaths', 'Delete Integration', true, 'Successfully deleted integration');
          console.log(`  ✅ Delete Integration: Successfully deleted`);
        } else {
          this.recordResult('criticalPaths', 'Delete Integration', false, `Failed with status ${deleteResponse.status}`);
          console.log(`  ❌ Delete Integration: Failed with status ${deleteResponse.status}`);
        }

      } else {
        this.recordResult('criticalPaths', 'Create Integration', false, `Failed with status ${createResponse.status}`);
        console.log(`  ❌ Create Integration: Failed with status ${createResponse.status}`);
      }

    } catch (error) {
      this.recordResult('criticalPaths', 'Integration Lifecycle', false, `Error: ${error.message}`);
      console.log(`  ❌ Integration Lifecycle: Error - ${error.message}`);
    }
  }

  recordResult(category, test, success, details) {
    this.totalTests++;
    if (success) this.passedTests++;
    
    this.results[category].push({
      test,
      success,
      details
    });
  }

  async generateDeploymentReport() {
    console.log('\n🚀 DEPLOYMENT READINESS REPORT');
    console.log('==============================');
    
    const successRate = ((this.passedTests / this.totalTests) * 100).toFixed(2);
    
    console.log(`\n📊 SUMMARY:`);
    console.log(`Total Tests: ${this.totalTests}`);
    console.log(`Passed: ${this.passedTests}`);
    console.log(`Failed: ${this.totalTests - this.passedTests}`);
    console.log(`Success Rate: ${successRate}%`);

    console.log(`\n📋 RESULTS BY CATEGORY:`);
    for (const [category, tests] of Object.entries(this.results)) {
      const passed = tests.filter(t => t.success).length;
      const total = tests.length;
      console.log(`  ${category}: ${passed}/${total} passed`);
      
      const failed = tests.filter(t => !t.success);
      if (failed.length > 0) {
        failed.forEach(test => {
          console.log(`    ❌ ${test.test}: ${test.details}`);
        });
      }
    }

    // Deployment recommendation
    console.log('\n🎯 DEPLOYMENT RECOMMENDATION:');
    
    if (successRate >= 95) {
      console.log('✅ 🟢 READY FOR DEPLOYMENT!');
      console.log('✨ The modularized integration controller is production-ready');
      console.log('🗑️ ✅ SAFE TO DELETE the original integration.ts file');
      console.log('🚀 The modular architecture provides:');
      console.log('   • Better maintainability and testability');
      console.log('   • Clear separation of concerns');
      console.log('   • Easier debugging and development');
      console.log('   • Foundation for microservices architecture');
    } else if (successRate >= 90) {
      console.log('⚠️ 🟡 MOSTLY READY - Minor issues detected');
      console.log('🔧 Fix the minor issues above before full deployment');
      console.log('✅ Core functionality is working correctly');
    } else {
      console.log('❌ 🔴 NOT READY - Critical issues found');
      console.log('🔧 Resolve all critical issues before deployment');
      console.log('⛔ DO NOT delete the original integration.ts file yet');
    }

    // Save detailed report
    const reportData = {
      timestamp: new Date().toISOString(),
      successRate: parseFloat(successRate),
      totalTests: this.totalTests,
      passedTests: this.passedTests,
      failedTests: this.totalTests - this.passedTests,
      readyForDeployment: successRate >= 95,
      safeToDeleteOriginal: successRate >= 95,
      results: this.results
    };

    await fs.writeFile('deployment-readiness-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n📄 Detailed report saved to: deployment-readiness-report.json');
  }
}

// Run the deployment test
const tester = new DeploymentReadinessTest();
tester.runDeploymentTest().catch(err => {
  console.error(`Deployment test failed: ${err.message}`);
  process.exit(1);
}); 