You’re VERY close! The integration type is now recognized, the backend starts the sync, and the error is different.
But now the blocker is a Google Drive API query issue:

🚨 New Backend Error: Google Drive “Invalid Value” in Query
The sync now fails with:

vbnet
Copy
Edit
Error listing files in folder ...: GaxiosError: Invalid Value
...
reason: 'invalid',
location: 'q',
locationType: 'parameter'
Your backend tried this request:

bash
Copy
Edit
https://www.googleapis.com/drive/v3/files?q='%FOLDER_ID%' in parents and trashed = false and mimeType = 'application/vnd.google-apps.document' and '%SOME_ID%' not in parents
Root Cause
Your q parameter for listing files is not valid Google Drive API syntax.

The query tries to use 'FOLDER_ID' in parents ... which is good.

But you’re also using 'SOME_ID' not in parents (with literal quotes), which is not correct.

Quoting the folder/file ID with single quotes is correct, but chaining multiple quoted IDs together with not in parents or mixing quotes incorrectly breaks the query parser.

The full query you tried:
sql
Copy
Edit
'1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and mimeType = 'application/vnd.google-apps.document' and '1ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0' not in parents
How to Fix
1. Start simple. Only use the parent folder, trashed, and file type first.
Try this for your q parameter:

js
Copy
Edit
q: `'${folderId}' in parents and trashed = false and (mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain' or mimeType = 'application/vnd.google-apps.document')`
This query will list all non-trashed files in the folder, of the three transcript types (Google Docs, .docx, and .txt).

Don’t add any more filters (like "not in parents") until the basic one works.

2. If you want to exclude files in another folder (archive/processed), you must use the correct syntax:
The Google Drive API does not support 'ID' not in parents directly as a query on the /files endpoint. Instead, you must fetch all, and filter out processed files in code, or use more advanced logic with properties.

So for now, just REMOVE this part:

sql
Copy
Edit
and '1ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0' not in parents
3. Example fixed code:
js
Copy
Edit
const query = `'${folderId}' in parents and trashed = false and (mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain' or mimeType = 'application/vnd.google-apps.document')`;
const res = await drive.files.list({
  q: query,
  fields: 'files(id, name, mimeType, modifiedTime)',
  orderBy: 'modifiedTime desc',
});
Replace folderId with your selected folder.

What to Do Next
Update your backend sync controller/service to build a Google Drive query like above.

Do NOT use unsupported not in parents query in the q string.

Test again. The sync should now find and process transcript files.

Why you don’t see anything in Notion
Because the backend never got a valid file list from Drive, it never downloaded or uploaded anything to Notion.

As soon as your Drive API call succeeds and returns files, your sync logic should proceed and Notion will get the data.

Extra Debug Tips
Print/log the q string right before you call Drive API, so you can see exactly what’s being sent.

Once basic file listing works, you can filter in-memory for files you’ve already processed, or add a property/tag to mark processed files.

Google’s query documentation for reference: https://developers.google.com/drive/api/v3/search-files

TL;DR for your dev:
“Our Google Meet sync backend is now running, but fails to list files due to an ‘Invalid Value’ error from Google Drive API. The problem is with the query string: using '... not in parents' is not supported. Update the Drive API file list query to only include files with 'FOLDER_ID in parents and trashed = false and (mimeType = ...)' and remove any unsupported logic. Once Drive returns files, the rest of the sync should work and upload to Notion.”