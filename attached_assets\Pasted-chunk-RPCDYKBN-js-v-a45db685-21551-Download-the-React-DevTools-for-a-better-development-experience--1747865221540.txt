chunk-RPCDYKBN.js?v=a45db685:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
queryClient.ts:15 
            
            
           POST https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/sync-now 500 (Internal Server Error)
apiRequest @ queryClient.ts:15
startSync @ api.ts:61
mutationFn @ IntegrationCard.tsx:69
fn @ @tanstack_react-query.js?v=a45db685:1189
run @ @tanstack_react-query.js?v=a45db685:494
start @ @tanstack_react-query.js?v=a45db685:536
execute @ @tanstack_react-query.js?v=a45db685:1225
await in execute
mutate @ @tanstack_react-query.js?v=a45db685:2630
(anonymous) @ @tanstack_react-query.js?v=a45db685:3295
onClick @ IntegrationCard.tsx:202
callCallback2 @ chunk-RPCDYKBN.js?v=a45db685:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=a45db685:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=a45db685:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=a45db685:3736
executeDispatch @ chunk-RPCDYKBN.js?v=a45db685:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=a45db685:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=a45db685:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=a45db685:7051
(anonymous) @ chunk-RPCDYKBN.js?v=a45db685:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=a45db685:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=a45db685:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=a45db685:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=a45db685:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=a45db685:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=a45db685:5449
queryClient.ts:15 
            
            
           POST https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/sync-now 500 (Internal Server Error)
apiRequest @ queryClient.ts:15
startSync @ api.ts:61
mutationFn @ IntegrationCard.tsx:69
fn @ @tanstack_react-query.js?v=a45db685:1189
run @ @tanstack_react-query.js?v=a45db685:494
start @ @tanstack_react-query.js?v=a45db685:536
execute @ @tanstack_react-query.js?v=a45db685:1225
await in execute
mutate @ @tanstack_react-query.js?v=a45db685:2630
(anonymous) @ @tanstack_react-query.js?v=a45db685:3295
onClick @ IntegrationCard.tsx:202
callCallback2 @ chunk-RPCDYKBN.js?v=a45db685:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=a45db685:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=a45db685:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=a45db685:3736
executeDispatch @ chunk-RPCDYKBN.js?v=a45db685:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=a45db685:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=a45db685:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=a45db685:7051
(anonymous) @ chunk-RPCDYKBN.js?v=a45db685:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=a45db685:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=a45db685:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=a45db685:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=a45db685:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=a45db685:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=a45db685:5449
