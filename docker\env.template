# =============================================================================
# MeetSync Environment Configuration Template
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env files to version control

# =============================================================================
# Application Settings
# =============================================================================
NODE_ENV=production
PORT=8080
HOST=0.0.0.0
SERVER_URL=http://localhost:8080

# =============================================================================
# Database Configuration
# =============================================================================
# PostgreSQL with pgvector
DATABASE_URL=****************************************************/meetsync

# Individual PostgreSQL settings (alternative to DATABASE_URL)
POSTGRES_DB=meetsync
POSTGRES_USER=meetsync
POSTGRES_PASSWORD=your_secure_db_password
POSTGRES_PORT=5432

# =============================================================================
# Redis Configuration (Sessions and Caching)
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password

# =============================================================================
# Security Settings (REQUIRED)
# =============================================================================
# Generate secure random strings for production
# You can use: openssl rand -base64 32
SESSION_SECRET=your_very_secure_session_secret
ENCRYPTION_KEY=your_very_secure_encryption_key_here

# =============================================================================
# OpenAI Configuration
# =============================================================================
OPENAI_API_KEY=sk-your_openai_api_key_here

# =============================================================================
# Google Drive Integration
# =============================================================================
# Get these from Google Cloud Console
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# =============================================================================
# Microsoft Teams/Graph Integration
# =============================================================================
# Get these from Azure App Registration
MICROSOFT_CLIENT_ID=your_microsoft_app_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_app_client_secret
MICROSOFT_TENANT_ID=your_microsoft_tenant_id
MICROSOFT_REDIRECT_URI=http://localhost:8080/api/microsoft/callback

# =============================================================================
# Notion Integration (Optional)
# =============================================================================
NOTION_API_KEY=secret_your_notion_integration_secret
NOTION_DATABASE_ID=your_notion_database_id
NOTION_INTEGRATION_SECRET=your_notion_integration_secret
NOTION_PAGE_URL=https://www.notion.so/your-page-url

# =============================================================================
# RAG (Retrieval-Augmented Generation) Settings
# =============================================================================
CHUNK_SIZE=512
CHUNK_OVERLAP=20
TOP_K=50
SYSTEM_PROMPT=You are GPT Unify - GPT AI Assistant, an intelligent AI assistant with access to data sources from multiple platforms including Google Drive, meeting transcripts, uploaded files, and other integrated platforms.

# Function Tools Settings
ENABLE_FUNCTION_TOOLS=true
ENABLE_FILE_CREATION=true
ENABLE_GOOGLE_DRIVE_ACTIONS=true

# =============================================================================
# Development Tools (Development Only)
# =============================================================================
# Database Management
ADMINER_PORT=8081

# Redis Management
REDIS_COMMANDER_PORT=8082
REDIS_COMMANDER_USER=admin
REDIS_COMMANDER_PASSWORD=admin123

# Debugging
DEBUG=meetsync:*
LOG_LEVEL=info

# Vite Development Server
VITE_PORT=5173

# =============================================================================
# Docker Compose Port Overrides
# =============================================================================
# Use these if you have port conflicts on your system

# Application port (default: 8080)
# PORT=8080

# PostgreSQL port mapping (default: 5432 for production, 5433 for development)
# POSTGRES_PORT=5432

# Redis port mapping (default: 6379 for production, 6380 for development)
# REDIS_PORT=6379

# Development database port (to avoid conflicts with local PostgreSQL)
# In development, PostgreSQL runs on port 5433 externally
# DEV_POSTGRES_PORT=5433

# Development Redis port (to avoid conflicts with local Redis)
# In development, Redis runs on port 6380 externally
# DEV_REDIS_PORT=6380

# =============================================================================
# Production Specific Settings
# =============================================================================
# SSL/TLS Configuration (when using reverse proxy)
# SSL_CERT_PATH=/path/to/ssl/cert
# SSL_KEY_PATH=/path/to/ssl/key

# Cookie settings for production
# COOKIE_DOMAIN=yourdomain.com

# Monitoring and Logging
# LOG_FORMAT=json
# METRICS_ENABLED=true
# HEALTH_CHECK_INTERVAL=30s

# Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
# BACKUP_RETENTION_DAYS=30

# =============================================================================
# Resource Limits (Production Tuning)
# =============================================================================
# APP_MEMORY_LIMIT=2G
# APP_CPU_LIMIT=1.0
# DB_MEMORY_LIMIT=1G
# DB_CPU_LIMIT=0.5
# REDIS_MEMORY_LIMIT=256M
# REDIS_CPU_LIMIT=0.25

# =============================================================================
# Notes
# =============================================================================
# 1. Replace all placeholder values with your actual configuration
# 2. For production, use strong, randomly generated passwords
# 3. Keep your .env file secure and never commit it to version control
# 4. Redis is now integrated for session persistence across container restarts
# 5. Database migrations will run automatically on startup
# 6. Development uses different ports to avoid conflicts with local services:
#    - App: 8080 (same for dev/prod)
#    - PostgreSQL: 5432 (prod) / 5433 (dev)
#    - Redis: 6379 (prod) / 6380 (dev)
# 7. Check the README-Docker.md for detailed setup instructions 