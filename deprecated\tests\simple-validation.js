import { promises as fs } from 'fs';

console.log('🔍 Simple Reference Validation');
console.log('==============================\n');

// Check if routes.ts has been updated
try {
  const routesContent = await fs.readFile('server/routes.ts', 'utf-8');
  
  if (routesContent.includes('from "./controllers/integration/index.js"')) {
    console.log('✅ server/routes.ts: Updated to use modular controller');
  } else if (routesContent.includes('from "./controllers/integration"')) {
    console.log('❌ server/routes.ts: Still using old controller reference');
  } else {
    console.log('ℹ️ server/routes.ts: No integration controller imports found');
  }
} catch (error) {
  console.log(`❌ Error reading routes.ts: ${error.message}`);
}

// Check if modular controller exists and exports correctly
try {
  const indexContent = await fs.readFile('server/controllers/integration/index.ts', 'utf-8');
  
  if (indexContent.includes('export const integrationController')) {
    console.log('✅ Modular controller: Exports integrationController correctly');
  } else {
    console.log('❌ Modular controller: Missing integrationController export');
  }
} catch (error) {
  console.log(`❌ Error reading modular controller: ${error.message}`);
}

// Check original file still exists
try {
  await fs.access('server/controllers/integration.ts');
  console.log('ℹ️ Original integration.ts: Still exists (ready for deletion)');
} catch (error) {
  console.log('✅ Original integration.ts: Already deleted');
}

console.log('\n🎯 Summary:');
console.log('If you see all ✅ marks above, you can safely delete the original integration.ts file.'); 