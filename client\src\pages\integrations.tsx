import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { getIntegrations } from "@/lib/api";
import AppLayout from "@/components/layout/AppLayout";
import IntegrationCard from "@/components/integrations/IntegrationCard";
import AccountManager from "@/components/integrations/AccountManager";
import IntegrationSelector from "@/components/integrations/IntegrationSelector";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { UserIcon, PlusIcon } from "lucide-react";

interface Integration {
  id: number;
  name: string;
  type: string;
  status: string;
  lastSyncAt: string | null;
  nextSyncAt: string | null;
  sourceConfig: any;
  destinationConfig: any;
}

export default function Integrations() {
  const [location, navigate] = useLocation();

  const [showAccountManager, setShowAccountManager] = useState(false);
  const [showIntegrationSelector, setShowIntegrationSelector] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: getIntegrations,
  });

  const integrations: Integration[] = data?.integrations || [];
  
  // Check if any integration is syncing
  const hasSyncingIntegrations = integrations.some(
    (integration) => integration.status === 'syncing'
  );

  // Set up polling with useEffect for dynamic control
  useEffect(() => {
    if (!hasSyncingIntegrations) return;

    const interval = setInterval(() => {
      refetch();
    }, 2000);

    return () => clearInterval(interval);
  }, [hasSyncingIntegrations, refetch]);
  
  const filteredIntegrations = integrations;
  
  // Create integration mutation
  const createIntegrationMutation = useMutation({
    mutationFn: async ({ type, account }: { type: string; account: any }) => {
      const payload = {
        type,
        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Integration`,
        useExistingGoogleAuth: account !== 'new'
      };

      if (account === 'new') {
        // Trigger OAuth flow for new account
        window.location.href = `/api/integrations/oauth/start?type=${type}`;
        return null;
      } else {
        // Use existing account
        const response = await fetch('/api/integrations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
        if (!response.ok) throw new Error('Failed to create integration');
        return response.json();
      }
    },
    onSuccess: (data) => {
      if (data) {
        toast({
          title: 'Integration created',
          description: `Successfully created ${data.integration.name}`,
        });
        queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to create integration',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleAddIntegration = () => {
    setShowIntegrationSelector(true);
  };
  
  const handleConfigureIntegration = (id: number) => {
    navigate(`/integrations/${id}/setup`);
  };

  const handleIntegrationCreate = (type: string, account: any) => {
    createIntegrationMutation.mutate({ type, account });
  };

  return (
    <AppLayout 
      title="Integrations" 
      onAddIntegration={handleAddIntegration}
    >
      <section className="mb-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h3 className="text-2xl font-bold text-foreground">Integrations</h3>
            <p className="text-gray-600 mt-1">Connect your platforms to enable AI-powered search across all your documents</p>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowAccountManager(true)}
              className="flex items-center gap-2"
            >
              <UserIcon className="h-4 w-4" />
              Manage Accounts
            </Button>
            <button className="px-3 py-1.5 bg-card border border-border rounded text-sm text-muted-foreground flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              Filter
            </button>
            <button className="px-3 py-1.5 bg-card border border-border rounded text-sm text-muted-foreground flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Sync All
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="space-y-12">
            {/* Loading skeleton for sections */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <div className="h-7 bg-gray-200 rounded w-48 animate-pulse"></div>
                <div className="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {/* Loading skeleton cards */}
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                      <div className="flex-1">
                        <div className="h-5 bg-gray-200 rounded w-full mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    </div>
                    <div className="pt-4 border-t border-gray-100">
                      <div className="flex justify-between items-center">
                        <div className="h-3 bg-gray-200 rounded w-16"></div>
                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : error ? (
          <div className="bg-card rounded-xl shadow-sm border border-destructive/20 p-8 text-center">
            <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-destructive" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Error Loading Integrations</h3>
            <p className="text-destructive mb-6">{error.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive/90 
                         transition-colors focus:ring-4 focus:ring-destructive/20 focus:outline-none"
            >
              Try Again
            </button>
          </div>
        ) : filteredIntegrations.length === 0 ? (
          <div className="bg-card rounded-xl shadow-sm border border-border p-12 text-center">
            <div className="mx-auto w-20 h-20 bg-gradient-to-br from-primary/10 to-primary/20 
                            rounded-full flex items-center justify-center mb-6">
              <svg className="w-10 h-10 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
                      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-foreground mb-3">
              No integrations yet
            </h3>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto leading-relaxed">
              Get started by connecting your first platform to access and search all your documents with AI-powered conversations
            </p>
            {(
              <button 
                onClick={handleAddIntegration}
                className="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 
                           text-white rounded-xl font-semibold shadow-lg hover:shadow-xl 
                           hover:from-blue-600 hover:to-blue-700 
                           transform transition-all duration-200 hover:scale-105
                           focus:ring-4 focus:ring-blue-200 focus:outline-none"
              >
                <div className="flex items-center">
                  <svg className="w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300" 
                       fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Your First Integration
                </div>
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-12">
            {/* Connected Integrations Section */}
            {filteredIntegrations.filter(integration => integration.status === 'connected' || integration.status === 'configured' || integration.status === 'syncing').length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Connected Integrations</h3>
                  <span className="text-sm text-gray-500 bg-green-50 px-3 py-1 rounded-full border border-green-200">
                    {filteredIntegrations.filter(integration => integration.status === 'connected' || integration.status === 'configured' || integration.status === 'syncing').length} active
                  </span>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                  {filteredIntegrations
                    .filter(integration => integration.status === 'connected' || integration.status === 'configured' || integration.status === 'syncing')
                    .map(integration => (
                      <IntegrationCard
                        key={integration.id}
                        integration={integration}
                        onConfigure={handleConfigureIntegration}
                      />
                    ))}
                </div>
              </div>
            )}

            {/* Available Integrations Section */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Available Integrations</h3>
                <span className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full border border-gray-200">
                  {filteredIntegrations.filter(integration => integration.status === 'disconnected').length} available
                </span>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {filteredIntegrations
                  .filter(integration => integration.status === 'disconnected')
                  .map(integration => (
                    <IntegrationCard
                      key={integration.id}
                      integration={integration}
                      onConfigure={handleConfigureIntegration}
                    />
                  ))}
                
                {/* Add New Integration Card */}
                <div 
                  className="group bg-white rounded-lg border-2 border-dashed border-gray-300 
                             hover:border-blue-400
                             flex flex-col items-center justify-center p-6 cursor-pointer 
                             transition-all duration-200 overflow-hidden"
                  onClick={handleAddIntegration}
                >
                  <div className="w-12 h-12 rounded-lg bg-white 
                                  group-hover:bg-blue-50 
                                  flex items-center justify-center mb-4 transition-all duration-200">
                    <PlusIcon className="h-6 w-6 text-blue-600 group-hover:scale-110 transition-transform" />
                  </div>
                  <h4 className="font-medium text-gray-900 text-base mb-2 group-hover:text-blue-600 transition-colors">
                    Add Integration
                  </h4>
                  <div className="w-full text-center py-2.5 bg-blue-50 text-blue-600 rounded-md text-sm font-medium
                                  group-hover:bg-blue-100 transition-colors duration-200 border border-blue-200">
                    Browse All
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </section>

      {/* Account Manager Dialog */}
      <AccountManager
        isOpen={showAccountManager}
        onClose={() => setShowAccountManager(false)}
      />

      {/* Integration Selector Dialog */}
      <IntegrationSelector
        isOpen={showIntegrationSelector}
        onClose={() => setShowIntegrationSelector(false)}
        onIntegrationCreate={handleIntegrationCreate}
      />
    </AppLayout>
  );
}
