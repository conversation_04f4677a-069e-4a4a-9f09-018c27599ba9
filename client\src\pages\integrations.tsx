import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { getIntegrations } from "@/lib/api";
import AppLayout from "@/components/layout/AppLayout";
import IntegrationCard from "@/components/integrations/IntegrationCard";
import AccountManager from "@/components/integrations/AccountManager";
import IntegrationSelector from "@/components/integrations/IntegrationSelector";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { UserIcon, PlusIcon } from "lucide-react";

interface Integration {
  id: number;
  name: string;
  type: string;
  status: string;
  lastSyncAt: string | null;
  nextSyncAt: string | null;
  sourceConfig: any;
  destinationConfig: any;
}

export default function Integrations() {
  const [location, navigate] = useLocation();
  const [currentTab, setCurrentTab] = useState("All Integrations");
  const [showAccountManager, setShowAccountManager] = useState(false);
  const [showIntegrationSelector, setShowIntegrationSelector] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: getIntegrations,
  });
  
  const integrations: Integration[] = data?.integrations || [];
  
  const filteredIntegrations = integrations.filter((integration: Integration) => {
    if (currentTab === "All Integrations") return true;
    if (currentTab === "Connected") return integration.status === "connected" || integration.status === "configured" || integration.status === "syncing";
    if (currentTab === "Disconnected") return integration.status === "disconnected" || integration.status === "error";
    return true;
  });
  
  // Create integration mutation
  const createIntegrationMutation = useMutation({
    mutationFn: async ({ type, account }: { type: string; account: any }) => {
      const payload = {
        type,
        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Integration`,
        useExistingGoogleAuth: account !== 'new'
      };

      if (account === 'new') {
        // Trigger OAuth flow for new account
        window.location.href = `/api/integrations/oauth/start?type=${type}`;
        return null;
      } else {
        // Use existing account
        const response = await fetch('/api/integrations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
        if (!response.ok) throw new Error('Failed to create integration');
        return response.json();
      }
    },
    onSuccess: (data) => {
      if (data) {
        toast({
          title: 'Integration created',
          description: `Successfully created ${data.integration.name}`,
        });
        queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to create integration',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleAddIntegration = () => {
    setShowIntegrationSelector(true);
  };
  
  const handleConfigureIntegration = (id: number) => {
    navigate(`/integrations/${id}/setup`);
  };

  const handleIntegrationCreate = (type: string, account: any) => {
    createIntegrationMutation.mutate({ type, account });
  };

  return (
    <AppLayout 
      title="Integrations" 
      onAddIntegration={handleAddIntegration}
      tabs={["All Integrations", "Connected", "Disconnected"]}
      currentTab={currentTab}
      onTabChange={setCurrentTab}
    >
      <section className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-foreground">Active Integrations</h3>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowAccountManager(true)}
              className="flex items-center gap-2"
            >
              <UserIcon className="h-4 w-4" />
              Manage Accounts
            </Button>
            <button className="px-3 py-1.5 bg-card border border-border rounded text-sm text-muted-foreground flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              Filter
            </button>
            <button className="px-3 py-1.5 bg-card border border-border rounded text-sm text-muted-foreground flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Sync All
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Loading skeleton cards */}
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-card rounded-xl shadow-sm border border-border p-6 animate-pulse">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-muted rounded-lg"></div>
                  <div className="ml-4">
                    <div className="h-5 bg-muted rounded w-32 mb-2"></div>
                    <div className="h-4 bg-muted rounded w-24"></div>
                  </div>
                </div>
                <div className="space-y-3 bg-muted/50 rounded-lg p-4">
                  <div className="flex justify-between">
                    <div className="h-4 bg-muted rounded w-20"></div>
                    <div className="h-4 bg-muted rounded w-32"></div>
                  </div>
                  <div className="flex justify-between">
                    <div className="h-4 bg-muted rounded w-24"></div>
                    <div className="h-4 bg-muted rounded w-28"></div>
                  </div>
                </div>
                <div className="mt-4 h-12 bg-muted rounded-lg"></div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="bg-card rounded-xl shadow-sm border border-destructive/20 p-8 text-center">
            <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-destructive" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Error Loading Integrations</h3>
            <p className="text-destructive mb-6">{error.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive/90 
                         transition-colors focus:ring-4 focus:ring-destructive/20 focus:outline-none"
            >
              Try Again
            </button>
          </div>
        ) : filteredIntegrations.length === 0 ? (
          <div className="bg-card rounded-xl shadow-sm border border-border p-12 text-center">
            <div className="mx-auto w-20 h-20 bg-gradient-to-br from-primary/10 to-primary/20 
                            rounded-full flex items-center justify-center mb-6">
              <svg className="w-10 h-10 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
                      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-foreground mb-3">
              {currentTab === "All Integrations" ? "No integrations yet" : 
               currentTab === "Connected" ? "No connected integrations" : 
               "No disconnected integrations"}
            </h3>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto leading-relaxed">
              {currentTab === "All Integrations" ? 
                "Get started by connecting your first platform to access and search all your documents with AI-powered conversations" :
                currentTab === "Connected" ? 
                "Connect your platforms to start syncing files and enable AI-powered document search" :
                "All your integrations are currently connected and working properly"
              }
            </p>
            {currentTab !== "Disconnected" && (
              <button 
                onClick={handleAddIntegration}
                className="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 
                           text-white rounded-xl font-semibold shadow-lg hover:shadow-xl 
                           hover:from-blue-600 hover:to-blue-700 
                           transform transition-all duration-200 hover:scale-105
                           focus:ring-4 focus:ring-blue-200 focus:outline-none"
              >
                <div className="flex items-center">
                  <svg className="w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300" 
                       fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Your First Integration
                </div>
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredIntegrations.map(integration => (
              <IntegrationCard
                key={integration.id}
                integration={integration}
                onConfigure={handleConfigureIntegration}
              />
            ))}
            
            {/* Enhanced Add New Integration Card */}
            <div 
              className="group bg-card rounded-xl shadow-sm border-2 border-dashed border-border 
                         hover:border-primary hover:shadow-lg
                         flex flex-col items-center justify-center p-8 cursor-pointer 
                         transition-all duration-300 ease-in-out
                         transform hover:-translate-y-1"
              onClick={handleAddIntegration}
            >
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 
                              group-hover:from-primary/20 group-hover:to-primary/30
                              flex items-center justify-center mb-4 transition-all duration-300">
                <PlusIcon className="h-8 w-8 text-primary group-hover:text-primary 
                               group-hover:scale-110 transition-all duration-300" />
              </div>
              <h4 className="font-semibold text-foreground text-lg mb-2 group-hover:text-primary transition-colors">
                Add Integration
              </h4>
              <p className="text-sm text-muted-foreground text-center leading-relaxed group-hover:text-muted-foreground transition-colors">
                Connect a new platform to access your documents and enable AI-powered search
              </p>
              <div className="mt-4 px-4 py-2 bg-primary/10 text-primary rounded-lg text-sm font-medium
                              group-hover:bg-primary/20 transition-colors">
                Choose Platform
              </div>
            </div>
          </div>
        )}
      </section>

      {/* Account Manager Dialog */}
      <AccountManager
        isOpen={showAccountManager}
        onClose={() => setShowAccountManager(false)}
      />

      {/* Integration Selector Dialog */}
      <IntegrationSelector
        isOpen={showIntegrationSelector}
        onClose={() => setShowIntegrationSelector(false)}
        onIntegrationCreate={handleIntegrationCreate}
      />
    </AppLayout>
  );
}
