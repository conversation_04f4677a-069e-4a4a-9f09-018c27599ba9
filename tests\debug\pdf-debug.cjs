const { google } = require('googleapis');
const pdf = require('pdf-parse');

async function testPDFProcessing() {
  try {
    console.log('🔍 Testing PDF processing for Anant A. Resume.pdf...');
    
    // Test PDF file details
    const fileId = '1Zk6ZgZHomhCXxjvHx31a8DMIoiVzrSqx';
    const fileName = 'Anant A. Resume.pdf';
    
    console.log(`📄 File ID: ${fileId}`);
    console.log(`📄 File Name: ${fileName}`);
    
    // Try to load Google auth (this might be the issue)
    try {
      // Load the Google service key
      const GOOGLE_SERVICE_KEY = process.env.GOOGLE_SERVICE_KEY;
      if (!GOOGLE_SERVICE_KEY) {
        console.log('❌ GOOGLE_SERVICE_KEY not found in environment');
        return;
      }
      
      const credentials = JSON.parse(GOOGLE_SERVICE_KEY);
      console.log('✅ Google credentials loaded successfully');
      
      // Test auth setup
      const auth = new google.auth.GoogleAuth({
        credentials,
        scopes: ['https://www.googleapis.com/auth/drive.readonly'],
      });
      
      const authClient = await auth.getClient();
      console.log('✅ Google auth client created successfully');
      
      // Test drive API
      const drive = google.drive({ version: 'v3', auth: authClient });
      console.log('✅ Google Drive API client created');
      
      // Try to get file metadata first
      console.log('📋 Fetching file metadata...');
      const fileInfo = await drive.files.get({
        fileId: fileId,
        fields: 'id,name,mimeType,size,webViewLink'
      });
      
      console.log('✅ File metadata retrieved:');
      console.log(`  Name: ${fileInfo.data.name}`);
      console.log(`  MimeType: ${fileInfo.data.mimeType}`);
      console.log(`  Size: ${fileInfo.data.size} bytes`);
      
      // Now try to download the PDF content
      console.log('📥 Downloading PDF content...');
      const response = await drive.files.get({
        fileId: fileId,
        alt: 'media',
      }, {
        responseType: 'arraybuffer',
      });
      
      if (response.data) {
        const buffer = Buffer.from(response.data);
        console.log(`✅ PDF downloaded successfully: ${buffer.length} bytes`);
        
        // Check PDF header
        const pdfHeader = buffer.subarray(0, 4).toString();
        console.log(`📄 PDF Header: "${pdfHeader}"`);
        
        if (pdfHeader.startsWith('%PDF')) {
          console.log('✅ Valid PDF format confirmed');
          
          // Try to extract text using pdf-parse
          console.log('🔤 Extracting text from PDF...');
          const pdfData = await pdf(buffer);
          
          console.log(`✅ PDF text extraction successful!`);
          console.log(`📊 Pages: ${pdfData.numpages}`);
          console.log(`📊 Text length: ${pdfData.text.length} characters`);
          console.log(`📄 First 500 characters:`);
          console.log('---');
          console.log(pdfData.text.substring(0, 500));
          console.log('---');
          
          if (pdfData.text.length > 1000) {
            console.log('🎉 SUCCESS: PDF content extraction working perfectly!');
          } else {
            console.log('⚠️ WARNING: PDF text seems short for a resume');
          }
          
        } else {
          console.log(`❌ Invalid PDF format. Header: "${pdfHeader}"`);
        }
        
      } else {
        console.log('❌ No data received from Google Drive');
      }
      
    } catch (authError) {
      console.log('❌ Google Auth Error:', authError.message);
    }
    
  } catch (error) {
    console.log('❌ Error in PDF processing test:', error.message);
    console.log('Stack:', error.stack);
  }
}

// Run the test
testPDFProcessing().then(() => {
  console.log('✅ PDF processing test completed');
  process.exit(0);
}).catch(error => {
  console.log('❌ PDF processing test failed:', error.message);
  process.exit(1);
}); 