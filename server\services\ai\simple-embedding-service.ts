import OpenAI from "openai";
import { storage } from "../../storage/index.js";
import type { InsertFileChunk } from "../../../shared/index.js";

class SimpleEmbeddingService {
  private openai!: OpenAI;
  private initialized: boolean = false;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn(
        "OPENAI_API_KEY not found in environment. Embedding features will be disabled.",
      );
      return;
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.initialized = true;
    console.log("Simple Embedding service initialized successfully");
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Generate embedding for a single text using OpenAI API
   */
  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.initialized) {
      throw new Error("Embedding service not initialized");
    }

    try {
      console.log(`Generating embedding for text (${text.length} chars): "${text.substring(0, 100)}..."`);

      const response = await this.openai.embeddings.create({
        model: "text-embedding-3-small",
        input: text,
      });

      const embedding = response.data[0].embedding;
      console.log(`Successfully generated embedding with ${embedding.length} dimensions`);

      return embedding;
    } catch (error: any) {
      console.error("Error generating embedding:", error);
      throw new Error(`Failed to generate embedding: ${error.message}`);
    }
  }

  /**
   * Split text into chunks for embedding (your original method)
   */
  splitTextIntoChunks(text: string, chunkSize: number = 500, overlap: number = 50): string[] {
    const chunks: string[] = [];
    const words = text.split(/\s+/);

    for (let i = 0; i < words.length; i += chunkSize - overlap) {
      const chunk = words.slice(i, i + chunkSize).join(' ');
      if (chunk.trim().length > 0) {
        chunks.push(chunk.trim());
      }
    }

    return chunks;
  }

  /**
   * Process a file and generate embeddings for its chunks (your original method with smart delays)
   */
  async processFileForEmbeddings(fileId: number, content: string): Promise<void> {
    if (!this.initialized) {
      console.warn("Embedding service not initialized, skipping embedding generation");
      return;
    }

    try {
      console.log(`Processing file ${fileId} for embeddings...`);

      // Split content into chunks
      const chunks = this.splitTextIntoChunks(content);
      console.log(`Split content into ${chunks.length} chunks`);

      // Generate embeddings for each chunk with smart delays
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];

        try {
          console.log(`Generating embedding for chunk ${i + 1}/${chunks.length}`);
          
          // Faster delay for Tier 2 sequential processing (20 requests/second)
          if (i > 0) {
            const delay = 50; // 50ms = 20 requests/second (well under 83/second limit)
            await new Promise(resolve => setTimeout(resolve, delay));
          }
          
          const embedding = await this.generateEmbedding(chunk);

          // Get file metadata for enhanced context
          const file = await storage.getFile(fileId);

          // Clean content to remove null bytes and other problematic characters
          const cleanedChunk = this.cleanContentForDatabase(chunk);
          
          // Store chunk with embedding
          const chunkData: InsertFileChunk = {
            fileId,
            chunkIndex: i,
            content: cleanedChunk,
            embedding,
            tokenCount: this.estimateTokenCount(cleanedChunk),
            metadata: {
              chunkSize: cleanedChunk.length,
              wordCount: cleanedChunk.split(/\s+/).length,
              tokenCount: this.estimateTokenCount(cleanedChunk),
              // Enhanced metadata
              fileType: file?.fileType || 'unknown',
              platform: file?.platform || 'unknown',
              fileName: file?.fileName || 'unknown',
              mimeType: file?.mimeType || null,
              userId: file?.userId || null,
              accessLevel: 'public',
              chunkingStrategy: 'word-based-simple',
              chunkSizeWords: 500,
              overlapWords: 50,
            },
          };

          await storage.createFileChunk(chunkData);

        } catch (chunkError: any) {
          console.error(`Error processing chunk ${i}:`, chunkError);
          
          // If rate limited, wait longer and continue
          if (chunkError.message.includes('429')) {
            console.log('Rate limit hit, waiting 30 seconds before continuing...');
            await new Promise(resolve => setTimeout(resolve, 30000));
          }
          // Continue with other chunks even if one fails
        }
      }

      console.log(`Successfully processed ${chunks.length} chunks for file ${fileId}`);

    } catch (error: any) {
      console.error(`Error processing file ${fileId} for embeddings:`, error);
      throw error;
    }
  }

  /**
   * Search for similar chunks using vector similarity
   */
  async searchSimilarChunks(
    query: string,
    enabledSources: string[] = [],
    limit: number = 10
  ): Promise<any[]> {
    if (!this.initialized) {
      console.warn("Embedding service not initialized, returning empty results");
      return [];
    }

    try {
      console.log(`Generating embedding for query: "${query}"`);

      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);

      console.log(`Generated embedding with length: ${queryEmbedding ? queryEmbedding.length : 'undefined'}`);

      if (!queryEmbedding || !Array.isArray(queryEmbedding) || queryEmbedding.length === 0) {
        console.error("Failed to generate valid embedding for query");
        return [];
      }

      // Search for similar chunks in the database
      const similarChunks = await storage.searchSimilarChunks(
        queryEmbedding,
        enabledSources,
        limit
      );

      return similarChunks;

    } catch (error: any) {
      console.error("Error searching similar chunks:", error);
      return [];
    }
  }

  /**
   * Clean content to remove null bytes and other problematic characters for database storage
   */
  private cleanContentForDatabase(content: string): string {
    return content
      // Remove null bytes that cause UTF8 encoding errors
      .replace(/\x00/g, '')
      // Remove other control characters that might cause issues
      .replace(/[\x01-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Estimate token count for a text (rough approximation)
   */
  private estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Check if a file already has embeddings
   */
  async hasEmbeddings(fileId: number): Promise<boolean> {
    try {
      const chunks = await storage.getFileChunks(fileId);
      return chunks.length > 0;
    } catch (error) {
      console.error(`Error checking embeddings for file ${fileId}:`, error);
      return false;
    }
  }

  /**
   * Regenerate embeddings for a file (useful when content changes)
   */
  async regenerateEmbeddings(fileId: number, content: string): Promise<void> {
    try {
      // Delete existing chunks
      await storage.deleteFileChunks(fileId);

      // Generate new embeddings
      await this.processFileForEmbeddings(fileId, content);

      console.log(`Regenerated embeddings for file ${fileId}`);
    } catch (error: any) {
      console.error(`Error regenerating embeddings for file ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Process files one at a time to avoid rate limits
   */
  async processMultipleFiles(files: Array<{fileId: number, content: string}>): Promise<void> {
    console.log(`Processing ${files.length} files sequentially to avoid rate limits...`);
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`\n📁 Processing file ${i + 1}/${files.length}: ${file.fileId}`);
      
      try {
        await this.processFileForEmbeddings(file.fileId, file.content);
        console.log(`✅ File ${file.fileId} completed successfully`);
        
        // Minimal wait between files (sequential processing is very safe)
        if (i < files.length - 1) {
          console.log('Waiting 200ms before next file...');
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
      } catch (error: any) {
        console.error(`❌ File ${file.fileId} failed:`, error.message);
        
        // If rate limited, wait longer
        if (error.message.includes('429')) {
          console.log('Rate limit hit, waiting 60 seconds before continuing...');
          await new Promise(resolve => setTimeout(resolve, 60000));
        }
      }
    }
    
    console.log(`\n🎉 Completed processing ${files.length} files`);
  }
}

// Export singleton instance
export const simpleEmbeddingService = new SimpleEmbeddingService();
