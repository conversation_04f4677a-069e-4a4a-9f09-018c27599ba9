import OpenAI from "openai";
import { storage } from "../../storage/index.js";
import type { InsertFileChunk } from "../../../shared/index.js";
import fs from 'fs/promises';
import path from 'path';

/**
 * Batch Embedding Service using OpenAI Batch API
 * 50% cheaper and no rate limits - perfect for bulk processing
 */

interface BatchRequest {
  custom_id: string;
  method: string;
  url: string;
  body: {
    model: string;
    input: string;
  };
}

interface BatchJob {
  id: string;
  fileId: number;
  chunkIndex: number;
  content: string;
  customId: string;
}

export class BatchEmbeddingService {
  private openai!: OpenAI;
  private initialized: boolean = false;
  private embeddingModel: string;
  private batchDir: string;
  private pendingJobs: Map<string, BatchJob[]> = new Map();

  constructor() {
    this.embeddingModel = process.env.EMBEDDING_MODEL || "text-embedding-3-small";
    this.batchDir = path.join(process.cwd(), 'batch-jobs');
    
    if (!process.env.OPENAI_API_KEY) {
      console.warn("OPENAI_API_KEY not found. Batch embedding service disabled.");
      return;
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.initialized = true;
    console.log("Batch Embedding service initialized successfully");
    
    // Ensure batch directory exists
    this.ensureBatchDirectory();
  }

  private async ensureBatchDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.batchDir, { recursive: true });
    } catch (error) {
      console.error("Error creating batch directory:", error);
    }
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Process multiple files for embeddings using batch API
   */
  async processFilesForEmbeddingsBatch(files: Array<{fileId: number, content: string}>): Promise<string> {
    if (!this.initialized) {
      throw new Error("Batch embedding service not initialized");
    }

    console.log(`Starting batch processing for ${files.length} files`);
    
    const batchRequests: BatchRequest[] = [];
    const batchJobs: BatchJob[] = [];
    
    // Prepare batch requests for all files
    for (const file of files) {
      const chunks = this.splitTextIntoChunks(file.content);
      console.log(`File ${file.fileId}: Split into ${chunks.length} chunks`);
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const customId = `file_${file.fileId}_chunk_${i}_${Date.now()}`;
        
        // Create batch request
        const batchRequest: BatchRequest = {
          custom_id: customId,
          method: "POST",
          url: "/v1/embeddings",
          body: {
            model: this.embeddingModel,
            input: chunk,
          },
        };
        
        batchRequests.push(batchRequest);
        
        // Track the job
        const batchJob: BatchJob = {
          id: customId,
          fileId: file.fileId,
          chunkIndex: i,
          content: chunk,
          customId,
        };
        
        batchJobs.push(batchJob);
      }
    }

    console.log(`Created ${batchRequests.length} batch requests`);

    // Save batch file
    const batchId = `batch_${Date.now()}`;
    const batchFilePath = path.join(this.batchDir, `${batchId}.jsonl`);
    
    // Write JSONL file (one JSON object per line)
    const jsonlContent = batchRequests.map(req => JSON.stringify(req)).join('\n');
    await fs.writeFile(batchFilePath, jsonlContent);
    
    console.log(`Batch file saved: ${batchFilePath}`);

    // Upload batch file to OpenAI
    const fileBuffer = await fs.readFile(batchFilePath);
    const fileBlob = new File([fileBuffer], path.basename(batchFilePath), { type: 'application/jsonl' });
    const fileUpload = await this.openai.files.create({
      file: fileBlob,
      purpose: "batch",
    });

    console.log(`Batch file uploaded: ${fileUpload.id}`);

    // Create batch job
    const batch = await this.openai.batches.create({
      input_file_id: fileUpload.id,
      endpoint: "/v1/embeddings",
      completion_window: "24h",
      metadata: {
        description: `Embedding batch for ${files.length} files`,
        file_count: files.length.toString(),
        chunk_count: batchRequests.length.toString(),
      },
    });

    console.log(`Batch job created: ${batch.id}`);
    console.log(`Status: ${batch.status}`);
    console.log(`Estimated completion: ${batch.completion_window}`);

    // Store job tracking info
    this.pendingJobs.set(batch.id, batchJobs);

    // Save job info to file for persistence
    const jobInfoPath = path.join(this.batchDir, `${batch.id}_jobs.json`);
    await fs.writeFile(jobInfoPath, JSON.stringify(batchJobs, null, 2));

    return batch.id;
  }

  /**
   * Check status of a batch job
   */
  async checkBatchStatus(batchId: string): Promise<any> {
    if (!this.initialized) {
      throw new Error("Batch embedding service not initialized");
    }

    try {
      const batch = await this.openai.batches.retrieve(batchId);
      
      console.log(`Batch ${batchId} status: ${batch.status}`);
      console.log(`Progress: ${batch.request_counts?.completed || 0}/${batch.request_counts?.total || 0}`);
      
      if (batch.status === 'completed' && batch.output_file_id) {
        console.log(`Batch completed! Processing results...`);
        await this.processBatchResults(batchId, batch.output_file_id);
      } else if (batch.status === 'failed') {
        console.error(`Batch ${batchId} failed:`, batch.errors);
      }
      
      return batch;
    } catch (error) {
      console.error(`Error checking batch status:`, error);
      throw error;
    }
  }

  /**
   * Process completed batch results
   */
  private async processBatchResults(batchId: string, outputFileId: string): Promise<void> {
    try {
      // Download results file
      const fileResponse = await this.openai.files.content(outputFileId);
      const resultsContent = await fileResponse.text();
      
      // Load job tracking info
      const jobInfoPath = path.join(this.batchDir, `${batchId}_jobs.json`);
      const jobsData = await fs.readFile(jobInfoPath, 'utf-8');
      const jobs: BatchJob[] = JSON.parse(jobsData);
      
      // Parse results (JSONL format)
      const results = resultsContent.trim().split('\n').map(line => JSON.parse(line));
      
      console.log(`Processing ${results.length} batch results`);
      
      // Process each result
      for (const result of results) {
        if (result.response?.status_code === 200) {
          const customId = result.custom_id;
          const embedding = result.response.body.data[0].embedding;
          
          // Find corresponding job
          const job = jobs.find(j => j.customId === customId);
          if (job) {
            await this.storeBatchEmbedding(job, embedding);
          }
        } else {
          console.error(`Batch request failed:`, result);
        }
      }
      
      console.log(`Batch ${batchId} processing completed`);
      
      // Cleanup
      this.pendingJobs.delete(batchId);
      
    } catch (error) {
      console.error(`Error processing batch results:`, error);
      throw error;
    }
  }

  /**
   * Store batch embedding result in database
   */
  private async storeBatchEmbedding(job: BatchJob, embedding: number[]): Promise<void> {
    try {
      // Get file metadata
      const file = await storage.getFile(job.fileId);
      
      const chunkData: InsertFileChunk = {
        fileId: job.fileId,
        chunkIndex: job.chunkIndex,
        content: job.content,
        embedding,
        tokenCount: this.estimateTokenCount(job.content),
        metadata: {
          chunkSize: job.content.length,
          wordCount: job.content.split(/\s+/).length,
          tokenCount: this.estimateTokenCount(job.content),
          fileType: file?.fileType || 'unknown',
          platform: file?.platform || 'unknown',
          fileName: file?.fileName || 'unknown',
          mimeType: file?.mimeType || null,
          userId: file?.userId || null,
          accessLevel: 'public',
          chunkingStrategy: 'token-based-batch',
          chunkSizeTokens: this.getChunkSize(),
          overlapTokens: this.getChunkOverlap(),
          batchProcessed: true,
        },
      };

      await storage.createFileChunk(chunkData);
      console.log(`Stored embedding for file ${job.fileId}, chunk ${job.chunkIndex}`);
      
    } catch (error) {
      console.error(`Error storing batch embedding:`, error);
    }
  }

  /**
   * List all pending batch jobs
   */
  async listPendingBatches(): Promise<any[]> {
    if (!this.initialized) {
      return [];
    }

    try {
      const batches = await this.openai.batches.list({ limit: 20 });
      return batches.data.filter(batch => 
        batch.status === 'validating' || 
        batch.status === 'in_progress' || 
        batch.status === 'finalizing'
      );
    } catch (error) {
      console.error("Error listing batches:", error);
      return [];
    }
  }

  /**
   * Cancel a batch job
   */
  async cancelBatch(batchId: string): Promise<void> {
    if (!this.initialized) {
      throw new Error("Batch embedding service not initialized");
    }

    try {
      await this.openai.batches.cancel(batchId);
      this.pendingJobs.delete(batchId);
      console.log(`Batch ${batchId} cancelled`);
    } catch (error) {
      console.error(`Error cancelling batch:`, error);
      throw error;
    }
  }

  // =============================================================================
  // UTILITY METHODS (Same as regular embedding service)
  // =============================================================================

  private splitTextIntoChunks(text: string): string[] {
    const chunkSize = this.getChunkSize();
    const overlap = this.getChunkOverlap();
    
    const chunkSizeChars = chunkSize * 4;
    const overlapChars = overlap * 4;
    
    const chunks: string[] = [];
    
    for (let i = 0; i < text.length; i += chunkSizeChars - overlapChars) {
      const chunk = text.slice(i, i + chunkSizeChars);
      if (chunk.trim().length > 0) {
        chunks.push(chunk.trim());
      }
    }

    return chunks;
  }

  private getChunkSize(): number {
    return parseInt(process.env.CHUNK_SIZE || "512");
  }

  private getChunkOverlap(): number {
    return parseInt(process.env.CHUNK_OVERLAP || "20");
  }

  private estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  }
}

// Export singleton instance
export const batchEmbeddingService = new BatchEmbeddingService();
