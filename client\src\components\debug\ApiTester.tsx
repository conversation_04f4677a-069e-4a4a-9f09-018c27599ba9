import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ApiResponse {
  status: number;
  data: any;
  error?: string;
  timestamp: string;
}

export default function ApiTester() {
  const [responses, setResponses] = useState<{ [key: string]: ApiResponse }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});

  const makeRequest = async (endpoint: string, method: string = 'GET', body?: any) => {
    const key = `${method} ${endpoint}`;
    setLoading(prev => ({ ...prev, [key]: true }));

    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (body) {
        options.body = JSON.stringify(body);
      }

      const response = await fetch(endpoint, options);
      const data = await response.json();

      setResponses(prev => ({
        ...prev,
        [key]: {
          status: response.status,
          data,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (error: any) {
      setResponses(prev => ({
        ...prev,
        [key]: {
          status: 0,
          data: null,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [key]: false }));
    }
  };

  const testEndpoints = [
    {
      name: 'Health Check',
      endpoint: '/api/diagnostic/health',
      method: 'GET'
    },
    {
      name: 'Test Encryption',
      endpoint: '/api/diagnostic/test-encryption',
      method: 'POST',
      body: { testData: 'Hello, encryption test!' }
    },
    {
      name: 'Test Credential Flow',
      endpoint: '/api/diagnostic/test-credential-flow',
      method: 'POST'
    },
    {
      name: 'Get Integrations',
      endpoint: '/api/integrations',
      method: 'GET'
    },
    {
      name: 'Get Sync Logs',
      endpoint: '/api/sync-logs',
      method: 'GET'
    },
    {
      name: 'Test Google Creds ID 1',
      endpoint: '/api/diagnostic/test-google-credentials/1',
      method: 'GET'
    },
    {
      name: 'Test Google Creds ID 2',
      endpoint: '/api/diagnostic/test-google-credentials/2',
      method: 'GET'
    },
    {
      name: 'Clear Credentials ID 1',
      endpoint: '/api/diagnostic/clear-credentials/1',
      method: 'POST'
    },
    {
      name: 'Test Sync ID 1',
      endpoint: '/api/diagnostic/test-sync/1',
      method: 'POST'
    },
    {
      name: 'Seed Test Data',
      endpoint: '/api/diagnostic/seed-data',
      method: 'POST'
    },
    {
      name: 'Test Integration Creation',
      endpoint: '/api/diagnostic/test-integration-creation',
      method: 'POST'
    },
    {
      name: 'Create Google Drive Integration',
      endpoint: '/api/integrations',
      method: 'POST',
      body: {
        type: 'google-drive',
        name: 'Frontend Test Google Drive',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null
      }
    },
    {
      name: 'Echo Test',
      endpoint: '/api/diagnostic/echo',
      method: 'POST',
      body: { test: 'frontend-data', timestamp: Date.now() }
    }
  ];

  const clearResponses = () => {
    setResponses({});
  };

  const clearIntegrations = () => {
    makeRequest('/api/diagnostic/clear-integrations', 'DELETE');
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">API Tester & Debugger</h2>
        <div className="space-x-2">
          <Button onClick={clearResponses} variant="outline">
            Clear Results
          </Button>
          <Button onClick={clearIntegrations} variant="destructive">
            Clear Integrations
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {testEndpoints.map((test) => {
          const key = `${test.method} ${test.endpoint}`;
          const response = responses[key];
          const isLoading = loading[key];

          return (
            <Card key={key} className="h-fit">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">
                  {test.name}
                </CardTitle>
                <div className="text-xs text-gray-500">
                  <Badge variant="outline" className="mr-2">
                    {test.method}
                  </Badge>
                  {test.endpoint}
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={() => makeRequest(test.endpoint, test.method, test.body)}
                  disabled={isLoading}
                  className="w-full"
                  size="sm"
                >
                  {isLoading ? 'Testing...' : 'Test'}
                </Button>

                {response && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Badge
                        variant={response.status >= 200 && response.status < 300 ? 'default' : 'destructive'}
                      >
                        {response.status || 'Error'}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(response.timestamp).toLocaleTimeString()}
                      </span>
                    </div>

                    <div className="bg-gray-50 p-2 rounded text-xs max-h-32 overflow-y-auto">
                      <pre className="whitespace-pre-wrap">
                        {response.error 
                          ? `Error: ${response.error}`
                          : JSON.stringify(response.data, null, 2)
                        }
                      </pre>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {Object.keys(responses).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Full Response Log</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-50 p-4 rounded overflow-x-auto">
              {JSON.stringify(responses, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 