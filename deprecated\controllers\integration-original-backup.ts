// @ts-nocheck
// DEPRECATED BACKUP FILE - This file is for reference only and should not be used in production
// All imports and functionality have been migrated to the modular architecture

import { Request, Response } from 'express';
// Storage will be imported dynamically when needed
import { googleService } from '../services/google';
import { teamsService } from '../services/teams-service';
import { notionService } from '../services/notion';
import { cryptoService } from '../services/crypto-service';
import { schedulerService } from '../services/scheduler-service';
import { integrationConfigSchema, scheduleUpdateSchema } from '../../shared/schema';
import { google } from 'googleapis';
import { extractPageIdFromUrl } from '../utils';
import { googleOAuthSchema, syncNowSchema } from "../../shared/schema";
// Sync controller import removed - not used in this file
// import { syncController } from "./sync.js";
import { microsoftService } from "../services/microsoft";



/**
 * Controller for managing integrations with external services
 */
class IntegrationController {
  constructor() {
    // Bind all methods to preserve 'this' context
    this.getIntegrations = this.getIntegrations.bind(this);
    this.getIntegration = this.getIntegration.bind(this);
    this.createIntegration = this.createIntegration.bind(this);
    this.updateIntegration = this.updateIntegration.bind(this);
    this.deleteIntegration = this.deleteIntegration.bind(this);
    this.getAuthUrl = this.getAuthUrl.bind(this);
    this.handleOAuthCallback = this.handleOAuthCallback.bind(this);
    this.getGoogleDriveFolders = this.getGoogleDriveFolders.bind(this);
    this.getDriveStructure = this.getDriveStructure.bind(this);
    this.debugDriveFolders = this.debugDriveFolders.bind(this);
    this.getTeamsAuthUrl = this.getTeamsAuthUrl.bind(this);
    this.handleTeamsOAuthCallback = this.handleTeamsOAuthCallback.bind(this);
    this.getTeamsSources = this.getTeamsSources.bind(this);
    this.getTeamsFolders = this.getTeamsFolders.bind(this);
    this.getTeamsChannels = this.getTeamsChannels.bind(this);
    this.testTeamsConnection = this.testTeamsConnection.bind(this);
    this.updateSchedule = this.updateSchedule.bind(this);
    this.getSchedules = this.getSchedules.bind(this);
  }

  /**
   * Get storage instance dynamically
   */
  private async getStorage() {
          const { storage } = await import('../storage/index.js');
    return storage;
  }
  /**
   * Get all integrations
   */
  async getIntegrations(req: Request, res: Response) {
    try {
      console.log('Getting integrations...');
      const storage = await this.getStorage();
      const integrations = await storage.getIntegrations();
      console.log(`Found ${integrations.length} integrations`);

      // Add next execution time if schedule exists
      const integrationsWithNextRun = integrations.map(integration => {
        const nextRunTime = integration.syncSchedule
          ? schedulerService.getNextExecutionTime(integration.id)
          : null;

        return {
          ...integration,
          nextRunTime,
        };
      });

      return res.json({ integrations: integrationsWithNextRun });
    } catch (error: any) {
      console.error('Error getting integrations:', error);
      console.error('Error stack:', error.stack);
      return res.status(500).json({
        message: 'Failed to get integrations',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get a single integration by ID
   */
  async getIntegration(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      const storage = await this.getStorage();
      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Add next execution time if schedule exists
      const nextRunTime = integration.syncSchedule
        ? schedulerService.getNextExecutionTime(integration.id)
        : null;

      return res.json({
        integration: {
          ...integration,
          nextRunTime,
        }
      });
    } catch (error: any) {
      console.error(`Error getting integration ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Failed to get integration' });
    }
  }

  /**
   * Create a new integration
   */
  async createIntegration(req: Request, res: Response) {
    try {
      console.log('[INTEGRATION] Creating new integration with data:', req.body);

      // Validate the request body
      const validationResult = integrationConfigSchema.safeParse(req.body);

      if (!validationResult.success) {
        console.error('[INTEGRATION] Validation failed:', validationResult.error.errors);
        return res.status(400).json({
          message: 'Invalid integration configuration',
          errors: validationResult.error.errors,
          receivedData: req.body
        });
      }

      console.log('[INTEGRATION] Validation passed, creating integration...');
      const { type, name, config, sourceConfig, destinationConfig, isLlmEnabled, syncFilters, syncSchedule } = validationResult.data;

      // Create the integration
      const storage = await this.getStorage();
      const integration = await storage.createIntegration({
        type,
        name,
        status: 'disconnected',
        credentials: null,
        config: config || {},
        sourceConfig: sourceConfig || {},
        destinationConfig: destinationConfig || {},
        isLlmEnabled: isLlmEnabled !== false,
        syncFilters: syncFilters || {},
        syncSchedule: syncSchedule || null,
        syncStatus: 'idle',
      });

      console.log('[INTEGRATION] Successfully created integration:', integration);

      // If a schedule is provided, set up a cron job
      if (syncSchedule) {
        try {
          schedulerService.scheduleIntegrationSync(integration.id, syncSchedule);
          console.log('[INTEGRATION] Scheduled sync job for integration:', integration.id);
        } catch (scheduleError) {
          console.error('[INTEGRATION] Failed to schedule sync job:', scheduleError);
          // Don't fail the integration creation if scheduling fails
        }
      }

      return res.status(201).json({
        message: 'Integration created successfully',
        integration,
      });
    } catch (error: any) {
      console.error('[INTEGRATION] Error creating integration:', error);
      console.error('[INTEGRATION] Error stack:', error.stack);
      return res.status(500).json({
        message: 'Failed to create integration',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
        receivedData: process.env.NODE_ENV === 'development' ? req.body : undefined
      });
    }
  }

  /**
   * Update an integration
   */
  async updateIntegration(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      // Handle status-only updates separately with less strict validation
      if (req.body && typeof req.body.status === 'string' && Object.keys(req.body).length === 1) {
        const storage = await this.getStorage();
        const existingIntegration = await storage.getIntegration(id);
        if (!existingIntegration) {
          return res.status(404).json({ message: 'Integration not found' });
        }

        const updatedIntegration = await storage.updateIntegration(id, {
          status: req.body.status
        });

        return res.json({
          message: 'Integration status updated successfully',
          integration: updatedIntegration,
        });
      }

      // Support partial updates by only requiring name and type
      if (req.body.sourceConfig && !req.body.name && !req.body.type) {
        const storage = await this.getStorage();
        const existingIntegration = await storage.getIntegration(id);
        if (!existingIntegration) {
          return res.status(404).json({ message: 'Integration not found' });
        }

        // Add required fields from existing integration for validation
        req.body.name = existingIntegration.name;
        req.body.type = existingIntegration.type;
      }

      // Validate the request body
      const validationResult = integrationConfigSchema.safeParse(req.body);

      if (!validationResult.success) {
        console.error('Validation error:', validationResult.error.errors);
        return res.status(400).json({
          message: 'Invalid integration configuration',
          errors: validationResult.error.errors,
        });
      }

      const { name, config, sourceConfig, destinationConfig, isLlmEnabled, syncFilters, syncSchedule, status } = validationResult.data;

      // Check if integration exists
      const storage = await this.getStorage();
      const existingIntegration = await storage.getIntegration(id);
      if (!existingIntegration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Update the integration
      const updatedIntegration = await storage.updateIntegration(id, {
        name,
        config: config || existingIntegration.config,
        sourceConfig: sourceConfig || existingIntegration.sourceConfig,
        destinationConfig: destinationConfig || existingIntegration.destinationConfig,
        isLlmEnabled: isLlmEnabled !== undefined ? isLlmEnabled : existingIntegration.isLlmEnabled,
        syncFilters: syncFilters || existingIntegration.syncFilters,
        syncSchedule: syncSchedule !== undefined ? syncSchedule : existingIntegration.syncSchedule,
        status: status || existingIntegration.status,
      });

      // Update the schedule if it changed
      if (syncSchedule !== existingIntegration.syncSchedule) {
        if (syncSchedule) {
          schedulerService.scheduleIntegrationSync(id, syncSchedule);
        } else {
          schedulerService.cancelSchedule(id);
        }
      }

      return res.json({
        message: 'Integration updated successfully',
        integration: updatedIntegration,
      });
    } catch (error: any) {
      console.error(`Error updating integration ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Failed to update integration' });
    }
  }

  /**
   * Delete an integration
   */
  async deleteIntegration(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      // Check if integration exists
      const storage = await this.getStorage();
      const existingIntegration = await storage.getIntegration(id);
      if (!existingIntegration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Delete the integration
      const success = await storage.deleteIntegration(id);

      // Cancel any scheduled jobs
      schedulerService.cancelSchedule(id);

      if (!success) {
        return res.status(500).json({ message: 'Failed to delete integration' });
      }

      return res.json({
        message: 'Integration deleted successfully',
      });
    } catch (error: any) {
      console.error(`Error deleting integration ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Failed to delete integration' });
    }
  }

  /**
   * Generate an authorization URL for OAuth flow
   */
  async getAuthUrl(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      // Check if integration exists
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Get the redirect URI from the query parameters
      const redirectUri = req.query.redirectUri as string;
      if (!redirectUri) {
        return res.status(400).json({ message: 'Redirect URI is required' });
      }

      // Generate auth URL based on integration type
      let authUrl = '';
      let oauthState = '';

      // Normalize Google integration types (handle both formats: google_drive, google-drive)
      const isGoogleIntegration = integration.type.includes('google') &&
        (integration.type.includes('meet') || integration.type.includes('chat') || integration.type.includes('drive'));

      if (isGoogleIntegration) {
        try {
          console.log(`Processing Google OAuth for integration type: ${integration.type}`);

          // Generate Google OAuth URL with integration ID included
          const result = googleService.getAuthUrl(redirectUri, id);
          authUrl = result.url;
          oauthState = result.state;

          // Store the state temporarily in the integration config
          const updatedConfig = integration.config || {};
          await storage.updateIntegration(id, {
            config: {
              ...updatedConfig,
              oauthState,
              redirectUri,
            },
          });
        } catch (error) {
          console.error("Error generating Google OAuth URL:", error);
          return res.status(500).json({
            message: 'Failed to generate Google authorization URL. Please check your Google API credentials.'
          });
        }
      } else if (integration.type === 'microsoft_teams' || integration.type === 'microsoft-teams') {
        // Handle Microsoft Teams OAuth
        try {
          console.log(`Processing Microsoft Teams OAuth for integration type: ${integration.type}`);

          // Generate Microsoft Teams OAuth URL with integration ID included
          const result = teamsService.getAuthUrl(redirectUri, id);
          authUrl = result.url;
          oauthState = result.state;

          // Store the state temporarily in the integration config
          const updatedConfig = integration.config || {};
          await storage.updateIntegration(id, {
            config: {
              ...updatedConfig,
              oauthState,
              redirectUri,
            },
          });
        } catch (error) {
          console.error("Error generating Microsoft Teams OAuth URL:", error);
          return res.status(500).json({
            message: 'Failed to generate Microsoft Teams authorization URL. Please check your Microsoft API credentials.'
          });
        }
      } else if (integration.type === 'notion') {
        // For Notion, use direct API key integration
        try {
          // Verify if we have the notion secret
          if (!process.env.NOTION_INTEGRATION_SECRET) {
            return res.status(400).json({
              message: 'Notion integration secret is not configured. Please add NOTION_INTEGRATION_SECRET to environment variables.'
            });
          }

          // Create a mock redirect URL for Notion that leads to token setup page
          authUrl = `/integrations/${id}/setup?step=token&type=notion`;
          oauthState = Math.random().toString(36).substring(2);

          // Store the state temporarily in the integration config
          const updatedConfig = integration.config || {};
          await storage.updateIntegration(id, {
            config: {
              ...updatedConfig,
              oauthState,
              redirectUri,
            },
          });
        } catch (error) {
          console.error("Error setting up Notion integration:", error);
          return res.status(500).json({
            message: 'Failed to set up Notion integration'
          });
        }
      } else {
        return res.status(400).json({
          message: `OAuth not implemented for integration type: ${integration.type}`
        });
      }

      return res.json({ authUrl });
    } catch (error: any) {
      console.error(`Error generating auth URL for integration ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Failed to generate authorization URL' });
    }
  }

  /**
   * Handle OAuth callback and store tokens
   */
  async handleOAuthCallback(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      console.log('[OAUTH CALLBACK] Received id param:', req.params.id, 'Parsed id:', id);

      if (isNaN(id)) {
        console.error('[OAUTH CALLBACK] Invalid integration ID:', req.params.id);
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      const storage = await this.getStorage();
      const integration = await storage.getIntegration(id);
      if (!integration) {
        console.error('[OAUTH CALLBACK] Integration not found for id:', id);
        const allIntegrations = await storage.getIntegrations();
        console.error('[OAUTH CALLBACK] All integrations in DB:', allIntegrations.map((i: any) => i.id));
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Get code and state from query parameters
      const { code, state } = req.query;
      if (!code) {
        return res.status(400).json({ message: 'Authorization code is missing' });
      }

      if (!integration.config || (integration.config as Record<string, any>).oauthState === undefined || state !== (integration.config as Record<string, any>).oauthState) {
        return res.status(400).json({ message: 'Invalid state parameter' });
      }

      // Handle based on integration type
      let credentials: any = null;
      let success = false;
      let message = '';
      let details: any = {};

      // Normalize Google integration types (handle both formats: google_drive, google-drive)
      const isGoogleIntegration = integration.type.includes('google') &&
        (integration.type.includes('meet') || integration.type.includes('chat') || integration.type.includes('drive'));

      console.log(`[OAUTH CALLBACK] Processing integration type: ${integration.type}, isGoogleIntegration: ${isGoogleIntegration}`);

      if (isGoogleIntegration) {
        // Get the redirect URI from the integration config
        const redirectUri = (integration.config as Record<string, any>)?.redirectUri;
        if (!redirectUri) {
          return res.status(400).json({ message: 'Missing redirect URI in integration config' });
        }

        // Get tokens from Google
        const tokens = await googleService.getTokensFromCode(code as string, redirectUri);

        // Encrypt the credentials
        const encryptedTokens = await cryptoService.encrypt(JSON.stringify(tokens));
        credentials = encryptedTokens;
      } else if (integration.type === 'microsoft_teams' || integration.type === 'microsoft-teams') {
        // Handle Microsoft Teams OAuth callback
        // Get the redirect URI from the integration config
        const redirectUri = (integration.config as Record<string, any>)?.redirectUri;
        if (!redirectUri) {
          return res.status(400).json({ message: 'Missing redirect URI in integration config' });
        }

        // Get tokens from Microsoft Teams
        const tokens = await teamsService.getTokensFromCode(code as string, redirectUri);

        // Encrypt the credentials
        const encryptedTokens = await cryptoService.encrypt(JSON.stringify(tokens));
        credentials = encryptedTokens;
      } else if (integration.type === 'notion') {
        // For Notion, use direct API key integration
        try {
          // Verify if we have the notion secret
          if (!process.env.NOTION_INTEGRATION_SECRET) {
            return res.status(400).json({
              message: 'Notion integration secret is not configured. Please add NOTION_INTEGRATION_SECRET to environment variables.'
            });
          }

          // Test by getting page details to ensure it works
          const notionConfig = integration.config as Record<string, any> || {};
          const pageId = notionConfig.notionPageId ||
                       (process.env.NOTION_PAGE_URL ?
                         extractPageIdFromUrl(process.env.NOTION_PAGE_URL) : null);

          if (pageId) {
            await notionService.getPage(pageId);
            details = { pageId };
          }

          success = true;
          message = 'Connection to Notion successful';
        } catch (error: any) {
          success = false;
          message = `Connection to Notion failed: ${error.message}`;
          details = { error: error.message };
        }
      } else {
        return res.status(400).json({
          message: `OAuth not implemented for integration type: ${integration.type}`
        });
      }

      // Update integration with credentials and status
      await storage.updateIntegration(id, {
        credentials,
        status: 'connected',
      });

      // Redirect to the frontend
      return res.redirect(`/integrations/${id}/setup?step=2&success=true`);
    } catch (error: any) {
      console.error(`Error handling OAuth callback for integration ${req.params.id}:`, error);

      // Redirect to the frontend with error
      return res.redirect(`/integrations/setup/${req.params.id}?success=false&error=${encodeURIComponent(error.message)}`);
    }
  }

  /**
   * Test the integration connection and update integration status
   */
  async testConnection(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      // Get the integration
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      let success = false;
      let details = {};
      let message = '';

      // Test the connection based on integration type
      // Handle both underscore and hyphen formats
      if (integration.type === 'google_drive' || integration.type === 'google-drive') {
        // Test Google connection
        try {
          console.log('Testing Google connection for integration:', {
            id: integration.id,
            type: integration.type,
            hasSourceConfig: !!integration.sourceConfig,
            requestSourceConfig: req.body.sourceConfig
          });

          const auth = await googleService.getAuthorizedClient(integration.credentials);

          // Get folder ID(s) from request body if provided
          const { sourceConfig } = req.body;
          let folderIds: string[] = [];

          // Support both new multi-folder and legacy single folder configurations
          if (sourceConfig?.driveIds && Array.isArray(sourceConfig.driveIds)) {
            folderIds = sourceConfig.driveIds;
          } else if (sourceConfig?.driveId) {
            folderIds = [sourceConfig.driveId];
          }

          // Initialize Google Drive API
          const drive = google.drive({ version: 'v3', auth });

          // Get folder IDs from either request body or integration config
          let effectiveFolderIds: string[] = [];
          if (folderIds.length > 0) {
            effectiveFolderIds = folderIds;
          } else if (integration.sourceConfig) {
            // Safely access sourceConfig which could be a string or object
            if (typeof integration.sourceConfig === 'object' && integration.sourceConfig !== null) {
              const config = integration.sourceConfig as Record<string, any>;
              if (config.driveIds && Array.isArray(config.driveIds)) {
                effectiveFolderIds = config.driveIds;
              } else if (config.driveId) {
                effectiveFolderIds = [config.driveId];
              }
            } else if (typeof integration.sourceConfig === 'string') {
              try {
                const parsedConfig = JSON.parse(integration.sourceConfig);
                if (parsedConfig.driveIds && Array.isArray(parsedConfig.driveIds)) {
                  effectiveFolderIds = parsedConfig.driveIds;
                } else if (parsedConfig.driveId) {
                  effectiveFolderIds = [parsedConfig.driveId];
                }
              } catch (e) {
                console.log('Failed to parse sourceConfig string:', e);
              }
            }
          }

          if (effectiveFolderIds.length > 0) {
            // Test specific folder(s) access & check for transcript files
            console.log(`Testing connection to Google Drive folders: ${effectiveFolderIds.join(', ')}`);

            try {
              let totalFilesFound = 0;
              const folderResults: any[] = [];

              // Test each folder
              for (const folderId of effectiveFolderIds) {
                try {
                  // First, verify folder exists and is accessible
                  const folder = await drive.files.get({
                    fileId: folderId,
                    fields: 'id,name,mimeType',
                    supportsAllDrives: true
                  });

                  // Search for transcript files in the folder
                  const response = await drive.files.list({
                    q: `'${folderId}' in parents and (mimeType='application/vnd.google-apps.document' or mimeType='text/plain')`,
                    pageSize: 20,
                    fields: 'files(id,name,mimeType)',
                    supportsAllDrives: true
                  });

                  const files = response.data.files || [];
                  totalFilesFound += files.length;

                  folderResults.push({
                    id: folderId,
                    name: folder.data.name,
                    filesFound: files.length,
                    accessible: true
                  });
                } catch (folderError: any) {
                  folderResults.push({
                    id: folderId,
                    name: 'Unknown',
                    filesFound: 0,
                    accessible: false,
                    error: folderError.message
                  });
                }
              }

              // Update the integration with the verified source config if from request
              if (folderIds.length > 0) {
                const updateConfig = sourceConfig.driveIds ? {
                  driveIds: effectiveFolderIds,
                  driveNames: folderResults.filter(f => f.accessible).map(f => f.name)
                } : {
                  driveId: effectiveFolderIds[0],
                  driveName: folderResults.find(f => f.accessible)?.name || 'Unknown'
                };

                console.log('Updating integration with verified folder config:', updateConfig);

                // Update the integration with the source config and set status to connected
                const storage = await this.getStorage();
                await storage.updateIntegration(id, {
                  sourceConfig: updateConfig,
                  status: 'connected' // Set the status to connected
                });
              }

              const accessibleFolders = folderResults.filter(f => f.accessible);
              const inaccessibleFolders = folderResults.filter(f => !f.accessible);

              if (accessibleFolders.length === effectiveFolderIds.length) {
                success = true;
                message = `Successfully connected to ${accessibleFolders.length} folder(s)`;
              } else if (accessibleFolders.length > 0) {
                success = true;
                message = `Connected to ${accessibleFolders.length}/${effectiveFolderIds.length} folders. ${inaccessibleFolders.length} folder(s) had access issues.`;
              } else {
                success = false;
                message = `Could not access any of the ${effectiveFolderIds.length} folder(s)`;
              }

              details = {
                totalFolders: effectiveFolderIds.length,
                accessibleFolders: accessibleFolders.length,
                totalFilesFound,
                folderResults
              };
            } catch (generalError: any) {
              success = false;
              message = `Could not test folders: ${generalError.message}`;
              details = { error: generalError.message };
            }
          } else {
            // Just check general Google Drive access if no folders specified
            await drive.files.list({ pageSize: 5 });
            success = true;
            message = 'Connection to Google Drive API successful';
            details = { note: 'No specific folders tested. Please select folders to test completely.' };
          }
        } catch (error: any) {
          success = false;
          message = `Connection to Google failed: ${error.message}`;
          details = { error: error.message };
        }
      } else if (integration.type === 'microsoft_teams' || integration.type === 'microsoft-teams') {
        // Test Microsoft Teams connection
        try {
          console.log('Testing Microsoft Teams connection for integration:', {
            id: integration.id,
            type: integration.type,
            hasCredentials: !!integration.credentials
          });

          // Use the teams service to test connection
          const testResult = await teamsService.testConnection(integration.credentials);

          if (testResult.success) {
            success = true;
            message = `Successfully connected to Microsoft Teams as ${testResult.userInfo?.displayName}`;
            details = {
              user: testResult.userInfo?.displayName,
              email: testResult.userInfo?.email,
              userId: testResult.userInfo?.id,
              teamsAccess: 'confirmed'
            };
          } else {
            success = false;
            message = `Microsoft Teams connection failed: ${testResult.error}`;
            details = { error: testResult.error };
          }
        } catch (error: any) {
          success = false;
          message = `Connection to Microsoft Teams failed: ${error.message}`;
          details = { error: error.message };
        }
      } else if (integration.type === 'notion') {
        // Test Notion connection
        try {
          // Try using credentials from integration first
          if (integration.credentials) {
            // Notion uses direct API token stored in credentials
            const apiKey = await cryptoService.decrypt(integration.credentials);
            await notionService.initializeWithApiKey(apiKey);
          } else if (process.env.NOTION_INTEGRATION_SECRET) {
            // Fall back to environment variable if available
            await notionService.initializeWithApiKey(process.env.NOTION_INTEGRATION_SECRET);
          } else {
            throw new Error('No Notion API token available');
          }

          // Test by getting page details to ensure it works
          const notionConfig = integration.config as Record<string, any> || {};
          const pageId = notionConfig.notionPageId ||
                       (process.env.NOTION_PAGE_URL ?
                         extractPageIdFromUrl(process.env.NOTION_PAGE_URL) : null);

          if (pageId) {
            await notionService.getPage(pageId);
            details = { pageId };
          }

          success = true;
          message = 'Connection to Notion successful';
        } catch (error: any) {
          success = false;
          message = `Connection to Notion failed: ${error.message}`;
          details = { error: error.message };
        }
      } else {
        message = `Testing not implemented for integration type: ${integration.type}`;
        success = false;
      }

      // If the test was successful, update the integration status to "connected"
      if (success) {
        const storage = await this.getStorage();
        await storage.updateIntegrationStatus(id, 'connected');
      }

      return res.json({ success, message, details });
    } catch (error: any) {
      console.error(`Error testing connection for integration ${req.params.id}:`, error);
      return res.status(500).json({
        success: false,
        message: 'Failed to test connection',
        details: { error: error.message },
      });
    }
  }

  /**
   * Update the sync schedule for an integration
   */
  async updateSchedule(req: Request, res: Response) {
    try {
      // Validate the request body
      const validationResult = scheduleUpdateSchema.safeParse(req.body);

      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid schedule update',
          errors: validationResult.error.errors,
        });
      }

      const { integrationId, schedule } = validationResult.data;

      // Check if integration exists
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Update the integration schedule
      const updatedIntegration = await storage.updateIntegration(integrationId, {
        syncSchedule: schedule,
      });

      // Update the scheduler
      if (schedule) {
        schedulerService.scheduleIntegrationSync(integrationId, schedule);
      } else {
        schedulerService.cancelSchedule(integrationId);
      }

      // Get the next execution time
      const nextRunTime = schedule
        ? schedulerService.getNextExecutionTime(integrationId)
        : null;

      return res.json({
        message: 'Schedule updated successfully',
        integration: {
          ...updatedIntegration,
          nextRunTime,
        },
      });
    } catch (error: any) {
      console.error('Error updating schedule:', error);
      return res.status(500).json({ message: 'Failed to update schedule' });
    }
  }

  /**
   * Debug endpoint to see all folders in Google Drive (for troubleshooting computer detection)
   */
  async debugDriveFolders(req: Request, res: Response) {
    try {
      const integrationId = parseInt(req.params.id);
      if (isNaN(integrationId)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        const auth = await googleService.getAuthorizedClient(integration.credentials);
        const drive = google.drive({ version: "v3", auth });

        // Get ALL folders in the drive
        const allFoldersResponse = await drive.files.list({
          q: "mimeType='application/vnd.google-apps.folder' and trashed=false",
          fields: "files(id, name, parents, driveId, shared, owners, createdTime, modifiedTime)",
          pageSize: 1000,
          orderBy: "name",
          includeItemsFromAllDrives: true,
          supportsAllDrives: true,
        });

        const allFolders = allFoldersResponse.data.files || [];

        // Categorize folders
        const rootFolders = allFolders.filter(f => f.parents?.includes('root'));
        const nonRootFolders = allFolders.filter(f => !f.parents?.includes('root'));

        return res.json({
          success: true,
          debug: {
            totalFolders: allFolders.length,
            rootFolders: rootFolders.length,
            nonRootFolders: nonRootFolders.length,
            allFolders: allFolders.map(f => ({
              id: f.id,
              name: f.name,
              parents: f.parents,
              isRoot: f.parents?.includes('root'),
              shared: f.shared,
              owners: f.owners?.map(o => o.displayName || o.emailAddress),
              created: f.createdTime,
              modified: f.modifiedTime
            }))
          }
        });
      } catch (error: any) {
        console.error('Error in debug folders:', error);
        return res.status(500).json({
          message: 'Failed to debug folders',
          error: error.message
        });
      }
    } catch (error: any) {
      console.error('Error in debugDriveFolders:', error);
      return res.status(500).json({
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Get complete Google Drive structure including My Drive, Shared Drives, Computers, and Shared with me
   */
  async getDriveStructure(req: Request, res: Response) {
    try {
      const integrationId = parseInt(req.params.id);
      if (isNaN(integrationId)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        // Get authorized client with fresh token
        const auth = await googleService.getAuthorizedClient(integration.credentials);

        console.log('Getting complete Drive structure...');

        // Get the complete drive structure
        const driveStructure = await googleService.getDriveStructure(auth);

        // Format the response for the frontend
        const response = {
          success: true,
          structure: {
            myDrive: {
              id: 'root',
              name: 'My Drive',
              type: 'root',
              folders: driveStructure.myDrive.map(folder => ({
                id: folder.id,
                name: folder.name,
                type: 'folder',
                parent: 'root',
                shared: folder.shared || false,
              }))
            },
            sharedDrives: driveStructure.sharedDrives.map(drive => ({
              id: drive.id,
              name: drive.name,
              type: 'shared_drive',
              capabilities: drive.capabilities,
              folders: (drive.folders || []).map((folder: any) => ({
                id: folder.id,
                name: folder.name,
                type: 'folder',
                parent: drive.id,
                shared: folder.shared || false,
              }))
            })),
            computers: driveStructure.computers.map(computer => ({
              id: computer.id,
              name: computer.name,
              type: 'computer',
              shared: computer.shared || false,
            })),
            sharedWithMe: driveStructure.sharedWithMe.map(folder => ({
              id: folder.id,
              name: folder.name,
              type: 'shared_folder',
              owners: folder.owners,
              shared: true,
            }))
          }
        };

        console.log(`Successfully retrieved Drive structure with ${driveStructure.myDrive.length + driveStructure.sharedDrives.length + driveStructure.computers.length + driveStructure.sharedWithMe.length} total items`);

        return res.json(response);
      } catch (error: any) {
        console.error('Error getting Drive structure:', error);
        return res.status(500).json({
          message: 'Failed to get Drive structure',
          error: error.message
        });
      }
    } catch (error: any) {
      console.error('Error in getDriveStructure:', error);
      return res.status(500).json({
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * List Google Drive folders for integration setup
   */
  async getGoogleDriveFolders(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      console.log(`Fetching Google Drive folders for integration ${id}`);

      // Get the integration
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(id);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Only allow Google integrations (Meet or Drive)
      if (integration.type !== 'google-drive' && integration.type !== 'google_drive') {
        return res.status(400).json({ message: 'This endpoint is only for Google integrations.' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      // Get query parameter for parent folder ID
      const { driveId } = req.query;
      const parentFolderId = (driveId as string) || 'root';
      console.log(`Listing folders in parent folder: ${parentFolderId}`);

      try {
        // Get authorized client with fresh token
        const auth = await googleService.getAuthorizedClient(integration.credentials);

        console.log('Getting Drive API client...');

        // First make a simple API request to test connection
        console.log('Testing Drive API connection...');
        const drive = google.drive({ version: 'v3', auth });
        const testResult = await drive.about.get({
          fields: 'user,storageQuota'
        });

        console.log(`Drive API connection successful. User: ${testResult.data.user?.displayName}`);

        // Use our improved folder listing method
        const folders = await googleService.listDriveFolders(auth, parentFolderId !== 'root' ? parentFolderId : null);

        // If no folders found, provide a useful message
        if (folders.length === 0) {
          console.log('No folders found in Google Drive');

          return res.json({
            folders: [],
            user: testResult.data.user?.displayName || 'Unknown User',
            email: testResult.data.user?.emailAddress,
            message: 'No folders found in your Google Drive. You may need to create a folder first.',
            canCreateFolder: true
          });
        }

        // Return the found folders with user info
        return res.json({
          folders,
          user: testResult.data.user?.displayName || 'Unknown User',
          email: testResult.data.user?.emailAddress,
          message: `Found ${folders.length} folders in Google Drive`,
          canCreateFolder: true
        });

      } catch (error: any) {
        console.error('Authentication or API error with Google Drive:', error);

        // Get detailed error information
        let errorDetails = error.message || 'Unknown error';
        let errorCode = 'API_ERROR';

        // If there's a structured API error response, extract it
        if (error.response && error.response.data) {
          errorDetails = JSON.stringify(error.response.data);
          if (error.response.data.error) {
            errorCode = error.response.data.error.code || errorCode;
          }
        }

        // Handle specific error types
        if (errorDetails.includes('invalid_grant') || errorDetails.includes('expired')) {
          return res.status(401).json({
            message: 'Your Google authentication has expired. Please reconnect your account.',
            error: 'EXPIRED_TOKEN',
            details: errorDetails
          });
        }

        // For permission errors
        if (errorDetails.includes('permission') || errorDetails.includes('scope') ||
            errorDetails.includes('403') || errorCode === '403') {
          return res.status(403).json({
            message: 'You need to grant additional permissions for accessing Google Drive folders.',
            error: 'INSUFFICIENT_PERMISSIONS',
            details: errorDetails
          });
        }

        // For Drive API not enabled
        if (errorDetails.includes('Drive API') || errorDetails.includes('not enabled') ||
            errorCode === '403' || errorCode === '404') {
          return res.status(403).json({
            message: 'Google Drive API may not be enabled for your project. Please check your Google Cloud Console.',
            error: 'API_NOT_ENABLED',
            details: errorDetails
          });
        }

        // Generic API error
        return res.status(500).json({
          message: 'Error accessing Google Drive',
          error: errorCode,
          details: errorDetails
        });
      }

    } catch (error: any) {
      console.error(`Error listing Drive folders for integration ${req.params.id}:`, error);

      return res.status(500).json({
        message: 'Failed to list Google Drive folders',
        error: error.message,
        details: error.stack
      });
    }
  }

  // Microsoft Teams specific methods
  async getTeamsAuthUrl(req: Request, res: Response) {
    try {
      const integrationId = req.params.id;
      const state = JSON.stringify({ integrationId, type: 'microsoft' });
      const authUrl = microsoftService.getAuthUrl(state);
      res.json({ authUrl });
    } catch (error: any) {
      console.error('Error getting Teams auth URL:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async handleTeamsOAuthCallback(req: Request, res: Response) {
    try {
      const { code, error, state } = req.query;
      const integrationId = parseInt(req.params.id);

      console.log('[TEAMS CALLBACK] Starting OAuth callback for integration:', integrationId);
      console.log('[TEAMS CALLBACK] Query params:', { code: !!code, error, state });

      if (error) {
        console.error('[TEAMS CALLBACK] Teams OAuth error:', error);
        return res.redirect(`/integrations/${integrationId}/setup?error=${encodeURIComponent(error as string)}`);
      }

      if (!code) {
        console.error('[TEAMS CALLBACK] No authorization code received');
        return res.redirect(`/integrations/${integrationId}/setup?error=no_code`);
      }

      // Get the integration to verify state if needed
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        console.error('[TEAMS CALLBACK] Integration not found:', integrationId);
        return res.redirect(`/integrations/${integrationId}/setup?error=integration_not_found`);
      }

      console.log('[TEAMS CALLBACK] Found integration:', { id: integration.id, type: integration.type, status: integration.status });

      // Exchange code for tokens
      console.log('[TEAMS CALLBACK] Exchanging authorization code for tokens...');
      const credentials = await microsoftService.exchangeCodeForToken(code as string);
      console.log('[TEAMS CALLBACK] Successfully received tokens:', { 
        hasAccessToken: !!credentials.access_token,
        hasRefreshToken: !!credentials.refresh_token,
        tokenType: credentials.token_type,
        expiresIn: credentials.expires_in
      });
      
      // Encrypt the credentials before storing
      console.log('[TEAMS CALLBACK] Encrypting credentials...');
      const encryptedCredentials = await cryptoService.encrypt(JSON.stringify(credentials));
      console.log('[TEAMS CALLBACK] Credentials encrypted, length:', encryptedCredentials.length);
      
      // Update integration in database
      console.log('[TEAMS CALLBACK] Updating integration in database...');
      await storage.updateIntegration(integrationId, {
        credentials: encryptedCredentials,
        status: 'connected'
      });

      console.log('[TEAMS CALLBACK] Integration updated successfully - redirecting to success page');
      // Redirect to the success page
      res.redirect(`/integrations/${integrationId}/setup?step=2&success=true`);
    } catch (error: any) {
      console.error('[TEAMS CALLBACK] Error in Teams OAuth callback:', error);
      console.error('[TEAMS CALLBACK] Error stack:', error.stack);
      const integrationId = req.params.id;
      res.redirect(`/integrations/${integrationId}/setup?error=${encodeURIComponent(error.message)}`);
    }
  }

  async getTeamsSources(req: Request, res: Response) {
    try {
      const integrationId = parseInt(req.params.id);
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);

      if (!integration || !integration.credentials) {
        return res.status(400).json({ message: 'Integration not found or not authenticated' });
      }

      // Decrypt credentials
      const credentialsJson = await cryptoService.decrypt(integration.credentials);
      const credentials = JSON.parse(credentialsJson);
      
      const sources = await microsoftService.getAvailableSources(credentials);
      
      res.json({ sources });
    } catch (error: any) {
      console.error('Error fetching Teams sources:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async getTeamsChannels(req: Request, res: Response) {
    try {
      const integrationId = parseInt(req.params.id);
      const teamId = req.params.teamId;
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);

      if (!integration || !integration.credentials) {
        return res.status(400).json({ message: 'Integration not found or not authenticated' });
      }

      // Decrypt credentials
      const credentialsJson = await cryptoService.decrypt(integration.credentials);
      const credentials = JSON.parse(credentialsJson);
      
      const channels = await microsoftService.getTeamsChannels(credentials, teamId);
      
      res.json({ channels });
    } catch (error: any) {
      console.error('Error fetching Teams channels:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async testTeamsConnection(req: Request, res: Response) {
    try {
      const integrationId = parseInt(req.params.id);
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);

      if (!integration || !integration.credentials) {
        return res.status(400).json({ message: 'Integration not found or not authenticated' });
      }

      // Decrypt credentials
      const credentialsJson = await cryptoService.decrypt(integration.credentials);
      const credentials = JSON.parse(credentialsJson);
      
      const result = await microsoftService.testConnection(credentials);
      
      res.json(result);
    } catch (error: any) {
      console.error('Error testing Teams connection:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async getSchedules(_req: Request, res: Response) {
    try {
      const storage = await this.getStorage();
      const integrations = await storage.getIntegrations();
      const schedules = integrations.map((integration: any) => ({
        integrationId: integration.id,
        name: integration.name,
        type: integration.type,
        schedule: integration.syncSchedule,
        status: integration.status,
        lastSync: integration.lastSyncAt
      }));

      res.json({ schedules });
    } catch (error: any) {
      console.error('Error fetching schedules:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async getTeamsFolders(req: Request, res: Response) {
    try {
      const integrationId = parseInt(req.params.id);
      const storage = await this.getStorage();
      const integration = await storage.getIntegration(integrationId);

      if (!integration || !integration.credentials) {
        return res.status(400).json({ message: 'Integration not found or not authenticated' });
      }

      // Decrypt credentials
      const credentialsJson = await cryptoService.decrypt(integration.credentials);
      const credentials = JSON.parse(credentialsJson);
      
      const foldersData = await microsoftService.getFoldersForSelection(credentials);
      
      res.json(foldersData);
    } catch (error: any) {
      console.error('Error fetching Teams folders:', error);
      res.status(500).json({ 
        message: 'Failed to fetch Teams folders',
        error: error.message 
      });
    }
  }
}

export const integrationController = new IntegrationController();