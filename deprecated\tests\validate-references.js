#!/usr/bin/env node

/**
 * 🔍 COMPREHENSIVE REFERENCE VALIDATION
 * 
 * This script validates that all references to the old integration controller
 * have been properly updated to use the modular version.
 */

import { promises as fs } from 'fs';
import { join, relative } from 'path';

class ReferenceValidator {
  constructor() {
    this.results = {
      updatedReferences: [],
      remainingOldReferences: [],
      verifiedImports: [],
      errors: []
    };
  }

  async validateAllReferences() {
    console.log('🔍 COMPREHENSIVE REFERENCE VALIDATION');
    console.log('=====================================\n');

    await this.scanForOldReferences();
    await this.verifyNewReferences();
    await this.testImports();
    this.generateReport();
  }

  async scanForOldReferences() {
    console.log('🔍 Scanning for old integration controller references...\n');

    const filesToCheck = [
      'server/routes.ts',
      'docs/project-backend.md',
      'docs/project-overview.md',
      'tests/validate-modularization.js'
    ];

    for (const filePath of filesToCheck) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        
        // Check for old import patterns
        const oldPatterns = [
          /from\s+["']\.\/controllers\/integration["']/g,
          /import.*controllers\/integration["'](?!\/)/g,
          /["']\.\/controllers\/integration["'](?!\/)/g
        ];

        let hasOldReferences = false;
        
        for (const pattern of oldPatterns) {
          const matches = content.match(pattern);
          if (matches) {
            hasOldReferences = true;
            this.results.remainingOldReferences.push({
              file: filePath,
              matches: matches
            });
            console.log(`❌ ${filePath}: Found old reference - ${matches.join(', ')}`);
          }
        }

        // Check for new import patterns
        const newPatterns = [
          /from\s+["']\.\/controllers\/integration\/index\.js["']/g,
          /import.*controllers\/integration\/index\.js["']/g
        ];

        let hasNewReferences = false;
        
        for (const pattern of newPatterns) {
          const matches = content.match(pattern);
          if (matches) {
            hasNewReferences = true;
            this.results.updatedReferences.push({
              file: filePath,
              matches: matches
            });
            console.log(`✅ ${filePath}: Found updated reference - ${matches.join(', ')}`);
          }
        }

        if (!hasOldReferences && !hasNewReferences) {
          console.log(`ℹ️ ${filePath}: No integration controller references found`);
        }

      } catch (error) {
        this.results.errors.push({
          file: filePath,
          error: error.message
        });
        console.log(`❌ ${filePath}: Error reading file - ${error.message}`);
      }
    }
  }

  async verifyNewReferences() {
    console.log('\n🔍 Verifying new modular references...\n');

    // Check that the modular controller exports correctly
    try {
      const indexContent = await fs.readFile('server/controllers/integration/index.ts', 'utf-8');
      
      if (indexContent.includes('export const integrationController')) {
        console.log('✅ Modular controller exports integrationController correctly');
        this.results.verifiedImports.push('integrationController export verified');
      } else {
        console.log('❌ Modular controller missing integrationController export');
        this.results.errors.push({
          file: 'server/controllers/integration/index.ts',
          error: 'Missing integrationController export'
        });
      }

      // Check that all required methods are present
      const requiredMethods = [
        'getIntegrations',
        'getIntegration',
        'createIntegration',
        'updateIntegration',
        'deleteIntegration',
        'getAuthUrl',
        'handleOAuthCallback',
        'testConnection'
      ];

      let methodsFound = 0;
      for (const method of requiredMethods) {
        if (indexContent.includes(`async ${method}(`)) {
          methodsFound++;
        }
      }

      if (methodsFound === requiredMethods.length) {
        console.log(`✅ All ${requiredMethods.length} required methods found in modular controller`);
        this.results.verifiedImports.push(`All ${requiredMethods.length} methods verified`);
      } else {
        console.log(`❌ Only ${methodsFound}/${requiredMethods.length} required methods found`);
        this.results.errors.push({
          file: 'server/controllers/integration/index.ts',
          error: `Missing ${requiredMethods.length - methodsFound} required methods`
        });
      }

    } catch (error) {
      console.log(`❌ Error verifying modular controller: ${error.message}`);
      this.results.errors.push({
        file: 'server/controllers/integration/index.ts',
        error: error.message
      });
    }
  }

  async testImports() {
    console.log('\n🔍 Testing import resolution...\n');

    try {
      // Test dynamic import of the modular controller (try both .js and .ts extensions)
      let integrationController;
      try {
        const module = await import('./server/controllers/integration/index.js');
        integrationController = module.integrationController;
      } catch (jsError) {
        try {
          const module = await import('./server/controllers/integration/index.ts');
          integrationController = module.integrationController;
        } catch (tsError) {
          throw new Error(`Could not import from .js or .ts: ${jsError.message}`);
        }
      }
      
      if (integrationController && typeof integrationController.getIntegrations === 'function') {
        console.log('✅ Dynamic import of modular controller successful');
        this.results.verifiedImports.push('Dynamic import successful');
      } else {
        console.log('❌ Dynamic import failed - controller not properly exported');
        this.results.errors.push({
          file: 'import test',
          error: 'Controller not properly exported'
        });
      }
    } catch (error) {
      console.log(`❌ Dynamic import failed: ${error.message}`);
      this.results.errors.push({
        file: 'import test',
        error: error.message
      });
    }
  }

  generateReport() {
    console.log('\n📊 REFERENCE VALIDATION REPORT');
    console.log('==============================\n');

    console.log(`📋 SUMMARY:`);
    console.log(`✅ Updated References: ${this.results.updatedReferences.length}`);
    console.log(`❌ Remaining Old References: ${this.results.remainingOldReferences.length}`);
    console.log(`✅ Verified Imports: ${this.results.verifiedImports.length}`);
    console.log(`❌ Errors: ${this.results.errors.length}\n`);

    if (this.results.remainingOldReferences.length > 0) {
      console.log(`❌ REMAINING OLD REFERENCES:`);
      this.results.remainingOldReferences.forEach(ref => {
        console.log(`  ${ref.file}: ${ref.matches.join(', ')}`);
      });
      console.log();
    }

    if (this.results.errors.length > 0) {
      console.log(`❌ ERRORS FOUND:`);
      this.results.errors.forEach(error => {
        console.log(`  ${error.file}: ${error.error}`);
      });
      console.log();
    }

    // Final determination
    const allGood = this.results.remainingOldReferences.length === 0 && 
                    this.results.errors.length === 0 && 
                    this.results.updatedReferences.length > 0;

    console.log('🎯 FINAL DETERMINATION:');
    if (allGood) {
      console.log('✅ 🟢 ALL REFERENCES PROPERLY UPDATED!');
      console.log('✨ All files now reference the modular integration controller');
      console.log('🗑️ ✅ SAFE TO DELETE the original integration.ts file');
    } else {
      console.log('❌ 🔴 REFERENCES NEED ATTENTION');
      console.log('🔧 Fix the issues above before deleting the original file');
    }

    // Save detailed report
    const reportData = {
      timestamp: new Date().toISOString(),
      allReferencesUpdated: allGood,
      results: this.results
    };

    fs.writeFile('reference-validation-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n📄 Detailed report saved to: reference-validation-report.json');
  }
}

// Run the validation
const validator = new ReferenceValidator();
validator.validateAllReferences().catch(err => {
  console.error(`Reference validation failed: ${err.message}`);
  process.exit(1);
}); 