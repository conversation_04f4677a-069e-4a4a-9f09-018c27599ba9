import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface SystemHealth {
  api: 'healthy' | 'error' | 'unknown';
  storage: 'working' | 'error' | 'unknown';
  integrations: number;
  lastChecked: string;
  details?: any;
}

export default function SystemStatus() {
  const [health, setHealth] = useState<SystemHealth>({
    api: 'unknown',
    storage: 'unknown',
    integrations: 0,
    lastChecked: 'Never'
  });
  const [checking, setChecking] = useState(false);

  const checkSystemHealth = async () => {
    setChecking(true);
    try {
      // Test API health
      const healthResponse = await fetch('/api/diagnostic/health');
      const healthData = await healthResponse.json();

      // Test integrations
      const integrationsResponse = await fetch('/api/integrations');
      const integrationsData = await integrationsResponse.json();

      setHealth({
        api: healthResponse.ok ? 'healthy' : 'error',
        storage: healthData.services?.storage || 'unknown',
        integrations: integrationsData.integrations?.length || 0,
        lastChecked: new Date().toLocaleTimeString(),
        details: {
          health: healthData,
          integrations: integrationsData
        }
      });

    } catch (error: any) {
      setHealth(prev => ({
        ...prev,
        api: 'error',
        storage: 'error',
        lastChecked: new Date().toLocaleTimeString(),
        details: { error: error.message }
      }));
    } finally {
      setChecking(false);
    }
  };

  useEffect(() => {
    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'working':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'working':
        return <Badge variant="default" className="bg-green-500">Healthy</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            System Status
            {getStatusIcon(health.api)}
          </CardTitle>
          <Button
            onClick={checkSystemHealth}
            disabled={checking}
            size="sm"
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${checking ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">API Connection</span>
              {getStatusBadge(health.api)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Storage System</span>
              {getStatusBadge(health.storage)}
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Integrations</span>
              <Badge variant="outline">{health.integrations}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Last Checked</span>
              <span className="text-xs text-gray-500">{health.lastChecked}</span>
            </div>
          </div>
        </div>

        {health.details && (
          <details className="mt-4">
            <summary className="cursor-pointer text-sm font-medium">
              System Details
            </summary>
            <pre className="mt-2 text-xs bg-gray-50 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify(health.details, null, 2)}
            </pre>
          </details>
        )}

        {health.api === 'error' && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">
              ⚠️ Cannot connect to the backend API. Make sure the server is running on port 5000.
            </p>
          </div>
        )}

        {health.integrations === 0 && health.api === 'healthy' && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              ℹ️ No integrations found. This might be why you're seeing "Failed to load integration" errors.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 