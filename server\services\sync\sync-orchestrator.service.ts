import { storage } from "../../storage/index.js";
import { googleSyncService } from "./google-sync.service.js";
import { microsoftSyncService } from "./microsoft-sync.service.js";

/**
 * Core sync orchestrator service that coordinates all sync operations
 */
export class SyncOrchestratorService {
  /**
   * Start a synchronization job for an integration
   * @param integrationId Integration ID to sync
   * @returns The created sync log
   */
  async startSync(integrationId: number): Promise<any> {
    try {
      // Get the integration
      const integration = await storage.getIntegration(integrationId);
      if (!integration) {
        throw new Error(`Integration ${integrationId} not found`);
      }

      // Check if integration is connected or configured
      if (
        (integration.status !== "connected" &&
          integration.status !== "configured") ||
        !integration.credentials
      ) {
        throw new Error(
          `Integration ${integrationId} is not properly configured`,
        );
      }

      // Update integration status to syncing
      await storage.updateIntegrationStatus(integrationId, "syncing");

      // Create a sync log
      const syncLog = await storage.createSyncLog({
        status: "running",
        integrationId,
        startTime: new Date(),
        itemsProcessed: 0,
        itemsSuccess: 0,
        itemsFailed: 0,
        details: {},
        error: null,
      });

      // Run the sync process in the background
      this.runSync(integration, syncLog.id).catch((error) => {
        console.error(`Error in sync job ${syncLog.id}:`, error);
      });

      return syncLog;
    } catch (error: any) {
      console.error(
        `Error starting sync for integration ${integrationId}:`,
        error,
      );

      // Update integration status back to connected
      await storage.updateIntegrationStatus(integrationId, "connected");

      throw error;
    }
  }

  /**
   * Run the synchronization process by delegating to platform-specific services
   * @param integration Integration to sync
   * @param syncLogId ID of the sync log
   */
  private async runSync(integration: any, syncLogId: number): Promise<void> {
    try {
      console.log(`Starting sync for integration ${integration.id} (${integration.type})`);

      if (integration.type === "google-drive" || integration.type === "google_drive") {
        await googleSyncService.runSync(integration, syncLogId);
      } else if (integration.type === "microsoft-teams" || integration.type === "microsoft_teams") {
        await microsoftSyncService.runSync(integration, syncLogId);
      } else {
        console.log(`Integration type ${integration.type} not yet supported in reference-based sync`);
        throw new Error(`Integration type ${integration.type} not yet supported`);
      }

    } catch (error: any) {
      console.error(`Error in sync for integration ${integration.id}:`, error);
      throw error;
    }
  }
}

export const syncOrchestratorService = new SyncOrchestratorService(); 