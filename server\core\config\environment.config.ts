import dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * Environment configuration management
 */

export interface EnvironmentConfig {
  // Application settings
  nodeEnv: string;
  port: number;
  host: string;
  serverUrl: string;
  
  // Database settings
  databaseUrl?: string;
  pgDatabase?: string;
  pgHost?: string;
  pgPort?: number;
  pgUser?: string;
  pgPassword?: string;
  
  // OpenAI settings
  openaiApiKey?: string;
  
  // OAuth settings
  googleClientId?: string;
  googleClientSecret?: string;
  microsoftClientId?: string;
  microsoftClientSecret?: string;
  microsoftTenantId?: string;
  microsoftRedirectUri?: string;
  
  // Notion settings
  notionApiKey?: string;
  notionDatabaseId?: string;
  notionIntegrationSecret?: string;
  notionPageUrl?: string;
  
  // Security settings
  sessionSecret?: string;
  encryptionKey?: string;
  
  // RAG settings
  chunkSize: number;
  chunkOverlap: number;
  topK: number;
  systemPrompt: string;
  enableFunctionTools: boolean;
  enableFileCreation: boolean;
  enableGoogleDriveActions: boolean;
}

// Parse environment variables with defaults
export const environmentConfig: EnvironmentConfig = {
  // Application settings
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '8080'),
  host: process.env.HOST || 'localhost',
  serverUrl: process.env.SERVER_URL || 'http://localhost:8080',
  
  // Database settings
  databaseUrl: process.env.DATABASE_URL,
  pgDatabase: process.env.PGDATABASE,
  pgHost: process.env.PGHOST,
  pgPort: process.env.PGPORT ? parseInt(process.env.PGPORT) : undefined,
  pgUser: process.env.PGUSER,
  pgPassword: process.env.PGPASSWORD,
  
  // OpenAI settings
  openaiApiKey: process.env.OPENAI_API_KEY,
  
  // OAuth settings
  googleClientId: process.env.GOOGLE_CLIENT_ID,
  googleClientSecret: process.env.GOOGLE_CLIENT_SECRET,
  microsoftClientId: process.env.MICROSOFT_CLIENT_ID,
  microsoftClientSecret: process.env.MICROSOFT_CLIENT_SECRET,
  microsoftTenantId: process.env.MICROSOFT_TENANT_ID,
  microsoftRedirectUri: process.env.MICROSOFT_REDIRECT_URI,
  
  // Notion settings
  notionApiKey: process.env.NOTION_API_KEY,
  notionDatabaseId: process.env.NOTION_DATABASE_ID,
  notionIntegrationSecret: process.env.NOTION_INTEGRATION_SECRET,
  notionPageUrl: process.env.NOTION_PAGE_URL,
  
  // Security settings
  sessionSecret: process.env.SESSION_SECRET,
  encryptionKey: process.env.ENCRYPTION_KEY,
  
  // RAG settings
  chunkSize: parseInt(process.env.CHUNK_SIZE || '512'),
  chunkOverlap: parseInt(process.env.CHUNK_OVERLAP || '20'),
  topK: parseInt(process.env.TOP_K || '50'),
  systemPrompt: process.env.SYSTEM_PROMPT || 'You are GPT Unify - GPT AI Assistant, an intelligent AI assistant with access to data sources from multiple platforms including Google Drive, meeting transcripts, uploaded files, and other integrated platforms.',
  enableFunctionTools: process.env.ENABLE_FUNCTION_TOOLS === 'true',
  enableFileCreation: process.env.ENABLE_FILE_CREATION === 'true',
  enableGoogleDriveActions: process.env.ENABLE_GOOGLE_DRIVE_ACTIONS === 'true',
};

// Validation functions
export const validateEnvironment = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check required environment variables
  if (!environmentConfig.sessionSecret) {
    errors.push('SESSION_SECRET is required');
  }
  
  if (!environmentConfig.encryptionKey) {
    errors.push('ENCRYPTION_KEY is required');
  }
  
  // Warn about optional but recommended variables
  if (!environmentConfig.openaiApiKey) {
    console.warn('⚠️  OPENAI_API_KEY not set - RAG features will be disabled');
  }
  
  if (!environmentConfig.databaseUrl && !environmentConfig.pgHost) {
    console.warn('⚠️  DATABASE_URL not set - using in-memory storage');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
};

// Environment helpers
export const isDevelopment = () => environmentConfig.nodeEnv === 'development';
export const isProduction = () => environmentConfig.nodeEnv === 'production';
export const isTest = () => environmentConfig.nodeEnv === 'test';

// Get database connection string
export const getDatabaseUrl = (): string | null => {
  if (environmentConfig.databaseUrl) {
    return environmentConfig.databaseUrl;
  }
  
  if (environmentConfig.pgHost && environmentConfig.pgUser && environmentConfig.pgDatabase) {
    const password = environmentConfig.pgPassword ? `:${environmentConfig.pgPassword}` : '';
    const port = environmentConfig.pgPort || 5432;
    return `postgresql://${environmentConfig.pgUser}${password}@${environmentConfig.pgHost}:${port}/${environmentConfig.pgDatabase}`;
  }
  
  return null;
};

// Export the config as default
export default environmentConfig;
