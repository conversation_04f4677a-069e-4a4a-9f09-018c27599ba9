import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation, useRoute } from "wouter";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { apiRequest } from "@/lib/queryClient";
import AppLayout from "@/components/layout/AppLayout";
import { FolderIcon, CheckCircleIcon, Loader2Icon, TrashIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { getIntegration } from "@/lib/api";
import { EnhancedDriveBrowser } from "@/components/EnhancedDriveBrowser";

interface Integration {
  id: number;
  name: string;
  type: string;
  status: string;
  sourceConfig?: any;
  destinationConfig?: any;
  isLlmEnabled?: boolean;
}

interface IntegrationQueryResult {
  integration: Integration;
}

export default function IntegrationSetup() {
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const [, params] = useRoute('/integrations/:id/setup');
  const { toast } = useToast();

  // Check if this is a new integration setup
  const isNewIntegration = window.location.pathname === '/integrations/new/setup';
  const integrationId = !isNewIntegration && params?.id ? parseInt(params.id) : null;
  const searchParams = new URLSearchParams(window.location.search);
  const step = searchParams.get('step') || '1';
  const oauthSuccess = searchParams.get('success') === 'true';
  const oauthError = searchParams.get('error');

  // Updated state for multi-folder selection
  const [selectedFolders, setSelectedFolders] = useState<Array<{id: string, name: string, type?: string}>>([]);
  const [selectedNotionDatabase, setSelectedNotionDatabase] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);

  // State for folder search
  const [folderSearch, setFolderSearch] = useState<string>('');

  // State for new integration creation
  const [selectedIntegrationType, setSelectedIntegrationType] = useState<string>('');
  const [integrationName, setIntegrationName] = useState<string>('');
  const [isCreatingIntegration, setIsCreatingIntegration] = useState(false);

  // Fetch integration data (only for existing integrations)
  const {
    data: integrationData,
    isLoading: isLoadingIntegration,
    error: integrationError,
    refetch: refetchIntegration
  } = useQuery<IntegrationQueryResult | undefined>({
    queryKey: ['/api/integrations', integrationId],
    queryFn: () => integrationId ? getIntegration(integrationId) : Promise.resolve(undefined),
    enabled: !!integrationId && !isNewIntegration,
  });

  // Integration type guards
  const isGoogleIntegration = integrationData?.integration?.type === 'google-drive' || integrationData?.integration?.type === 'google_drive';
  const isTeamsIntegration = integrationData?.integration?.type === 'microsoft_teams' || integrationData?.integration?.type === 'microsoft-teams';

  // Fetch Google Drive folders when integration is connected (Google only)
  const {
    data: foldersData,
    isLoading: isLoadingFolders,
    error: foldersError,
    refetch: refetchFolders
  } = useQuery<{ folders: any[]; user?: string; email?: string } | undefined, unknown>({
    queryKey: [`/api/integrations/${integrationId}/folders`],
    queryFn: async () => {
      if (!isGoogleIntegration) return { folders: [] };
      console.log(`Fetching folders for integration ${integrationId}...`);
      const response = await fetch(`/api/integrations/${integrationId}/folders`);
      if (!response.ok) {
        throw new Error(`Failed to fetch folders: ${response.statusText}`);
      }
      const data = await response.json();
      console.log(`Fetched folders data:`, data);
      return data;
    },
    enabled: !!integrationId && isGoogleIntegration && integrationData?.integration?.status === 'connected',
    refetchOnWindowFocus: false,
    refetchInterval: false,
    retry: 3,
    retryDelay: 1000
  });

  // Fetch Teams folders/sources when integration is connected (Teams only)
  const {
    data: teamsFoldersData,
    isLoading: isLoadingTeamsFolders,
    error: teamsFoldersError,
    refetch: refetchTeamsFolders
  } = useQuery<{ sources: any[]; user?: { displayName: string; email: string; id: string } } | undefined, unknown>({
    queryKey: [`/api/integrations/${integrationId}/teams-folders`],
    queryFn: async () => {
      if (!isTeamsIntegration) return { sources: [] };
      console.log(`Fetching Teams folders for integration ${integrationId}...`);
      const response = await fetch(`/api/integrations/${integrationId}/teams-folders`);
      if (!response.ok) {
        throw new Error(`Failed to fetch Teams folders: ${response.statusText}`);
      }
      const data = await response.json();
      console.log(`Fetched Teams folders data:`, data);
      return data;
    },
    enabled: !!integrationId && isTeamsIntegration && integrationData?.integration?.status === 'connected',
    refetchOnWindowFocus: false,
    refetchInterval: false,
    retry: 3,
    retryDelay: 1000
  });

  // Safe refetchFolders fallback
  const safeRefetchFolders = typeof refetchFolders === 'function' ? refetchFolders : () => {};
  const safeRefetchTeamsFolders = typeof refetchTeamsFolders === 'function' ? refetchTeamsFolders : () => {};

  // Mutation for creating new integration
  const createIntegrationMutation = useMutation({
    mutationFn: async ({ type, name }: { type: string; name: string }) => {
      const response = await fetch('/api/integrations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type,
          name,
          status: 'disconnected',
          config: {},
          sourceConfig: {},
          destinationConfig: {},
          isLlmEnabled: true,
          syncFilters: {},
          syncSchedule: null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create integration');
      }

      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Integration Created",
        description: `${data.integration.name} has been created successfully.`,
        variant: "default",
      });

      // Redirect to the setup page for the new integration
      setLocation(`/integrations/${data.integration.id}/setup?step=1`);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to Create Integration",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle OAuth success/failure when returning from authentication
  useEffect(() => {
    if (oauthSuccess && integrationId) {
      if (isTeamsIntegration) {
        // For Teams, refresh integration data and trigger folder fetching
        console.log('Teams OAuth success detected, refreshing integration data and triggering folder fetch...');
        // Clean up URL parameters
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('success');
        newUrl.searchParams.delete('error');
        window.history.replaceState({}, '', newUrl.toString());
        // Show success toast
        toast({
          title: "Microsoft Teams Connected",
          description: "Successfully connected to Microsoft Teams. Loading folders...",
          variant: "default"
        });
        // Refresh integration data immediately
        refetchIntegration();
        // Force Teams folder fetch after a short delay to ensure integration status is updated
        setTimeout(() => {
          refetchTeamsFolders();
        }, 1000);
        return;
      }
      if (isGoogleIntegration) {
        console.log('OAuth success detected, refreshing integration data and triggering folder fetch...');
        // Clean up URL parameters
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('success');
        newUrl.searchParams.delete('error');
        window.history.replaceState({}, '', newUrl.toString());
        // Show success toast
        toast({
          title: "Google Account Connected",
          description: "Successfully connected to Google Drive. Loading folders...",
          variant: "default"
        });
        // Refresh integration data immediately
        refetchIntegration();
        // Force folder fetch after a short delay to ensure integration status is updated
        setTimeout(() => {
          refetchFolders();
        }, 1000);
      }
    } else if (oauthError) {
      console.error('OAuth error detected:', oauthError);
      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('success');
      newUrl.searchParams.delete('error');
      window.history.replaceState({}, '', newUrl.toString());
      // Show error toast
      toast({
        title: "Authentication Failed",
        description: `Failed to connect account: ${decodeURIComponent(oauthError)}`,
        variant: "destructive"
      });
    }
  }, [oauthSuccess, oauthError, integrationId, toast, refetchIntegration, refetchFolders, refetchTeamsFolders, isGoogleIntegration, isTeamsIntegration]);

  // Auto-refresh folders when integration status changes to connected (Google only)
  useEffect(() => {
    if (isGoogleIntegration && integrationData?.integration?.status === 'connected') {
      console.log('Integration connected, refreshing Google Drive folders...');
      let retryCount = 0;
      const maxRetries = 3;
      const attemptFolderFetch = () => {
        refetchFolders()
          .then(result => {
            if (result && result.data && Array.isArray(result.data.folders) && result.data.folders.length > 0) {
              toast({
                title: "Google Drive Connected",
                description: `Successfully connected to Google Drive. Found ${result.data.folders.length} folders.`,
                variant: "default"
              });
            } else if (retryCount < maxRetries - 1) {
              retryCount++;
              const delay = 2000 * retryCount;
              setTimeout(attemptFolderFetch, delay);
            }
          })
          .catch(error => {
            if (retryCount < maxRetries - 1) {
              retryCount++;
              const delay = 1000 * retryCount;
              setTimeout(attemptFolderFetch, delay);
            }
          });
      };
      setTimeout(attemptFolderFetch, 500);
    }
  }, [isGoogleIntegration, integrationData?.integration?.status, refetchFolders, toast]);

  // Auto-refresh Teams folders when integration status changes to connected (Teams only)
  useEffect(() => {
    if (isTeamsIntegration && integrationData?.integration?.status === 'connected') {
      console.log('Integration connected, refreshing Teams folders...');
      let retryCount = 0;
      const maxRetries = 3;
      const attemptTeamsFolderFetch = () => {
        refetchTeamsFolders()
          .then(result => {
            if (result && result.data && Array.isArray(result.data.sources) && result.data.sources.length > 0) {
              toast({
                title: "Microsoft Teams Connected",
                description: `Successfully connected to Microsoft Teams. Found ${result.data.sources.length} sources.`,
                variant: "default"
              });
            } else if (retryCount < maxRetries - 1) {
              retryCount++;
              const delay = 2000 * retryCount;
              setTimeout(attemptTeamsFolderFetch, delay);
            }
          })
          .catch(error => {
            if (retryCount < maxRetries - 1) {
              retryCount++;
              const delay = 1000 * retryCount;
              setTimeout(attemptTeamsFolderFetch, delay);
            }
          });
      };
      setTimeout(attemptTeamsFolderFetch, 500);
    }
  }, [isTeamsIntegration, integrationData?.integration?.status, refetchTeamsFolders, toast]);

  // Function to handle OAuth URL request
  const handleConnectAccount = async () => {
    if (!integrationId) return;

    try {
      setIsConnecting(true);
      
      let url: string;
      
      if (isTeamsIntegration) {
        // Use Teams-specific auth URL endpoint with redirect URI
        const redirectUri = `${window.location.origin}/api/integrations/oauth/callback`;
        url = `/api/integrations/${integrationId}/teams-auth-url?redirectUri=${encodeURIComponent(redirectUri)}`;
      } else {
        // Use the global OAuth callback endpoint that matches what's in Google Cloud Console
        const redirectUri = `${window.location.origin}/api/integrations/oauth/callback`;
        url = `/api/integrations/${integrationId}/auth-url?redirectUri=${encodeURIComponent(redirectUri)}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || response.statusText);
      }

      const data = await response.json();

      // Redirect to OAuth page
      if (data.authUrl) {
        window.location.href = data.authUrl;
      } else {
        throw new Error('No authorization URL provided');
      }
    } catch (error: any) {
      console.error('Failed to get auth URL:', error);
      toast({
        title: 'Authentication Error',
        description: `Failed to start authentication: ${error.message}`,
        variant: 'destructive'
      });
      setIsConnecting(false);
    }
  };

  // Mutation for updating integration with selected folder
  const updateFolderMutation = useMutation({
    mutationFn: async () => {
      // Validate required fields first
      if (selectedFolders.length === 0) {
        throw new Error('Please select at least one folder first');
      }

      console.log('Saving integration configuration with folders:', selectedFolders);
      
      // Build sourceConfig based on integration type
      let sourceConfig;
      if (isTeamsIntegration) {
        // Teams: store sourceIds, sourceNames, and sourceTypes for multi-source support
        sourceConfig = {
          sourceIds: selectedFolders.map(folder => folder.id),
          sourceNames: selectedFolders.map(folder => folder.name),
          sourceTypes: selectedFolders.map(folder => folder.type || 'unknown'),
        };
      } else {
        // Google Drive: keep existing driveIds/driveNames structure
        sourceConfig = {
          driveIds: selectedFolders.map(folder => folder.id),
          driveNames: selectedFolders.map(folder => folder.name),
        };
      }
      
      const response = await fetch(
        `/api/integrations/${integrationId}`,
        {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: integrationData?.integration?.name,
            type: integrationData?.integration?.type,
            sourceConfig,
            destinationConfig: {
              // We need to ensure destinationConfig is properly set
              notionDatabaseId: integrationData?.integration?.destinationConfig?.notionDatabaseId || null
            },
            isLlmEnabled: integrationData?.integration?.isLlmEnabled,
            status: 'configured'
          }),
          credentials: 'include'
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.message || response.statusText;
        console.error('Server response error:', errorData);
        throw new Error(errorMessage || 'Failed to save configuration');
      }

      return response.json();
    },
    onSuccess: () => {
      // Show success toast
      toast({
        title: "Configuration Saved!",
        description: "Integration setup was successfully saved",
        variant: "default",
      });

      // Invalidate queries and redirect
      queryClient.invalidateQueries({queryKey: ['/api/integrations']});
      queryClient.invalidateQueries({queryKey: [`/api/integrations/${integrationId}`]});

      // Redirect after a short delay to show the toast
      setTimeout(() => {
        setLocation(`/integrations`);
      }, 1000);
    },
    onError: (error: Error) => {
      console.error('Update folder error:', error);
      toast({
        title: "Failed to Save Configuration",
        description: error.message || "There was a problem saving your configuration. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Handle folder selection (multi-select)
  const handleFolderSelect = (folderId: string, name: string, type?: string) => {
    setSelectedFolders(prev => {
      const existingIndex = prev.findIndex(f => f.id === folderId);
      if (existingIndex >= 0) {
        // Remove folder if already selected
        return prev.filter(f => f.id !== folderId);
      } else {
        // Add folder to selection
        return [...prev, { id: folderId, name, type }];
      }
    });
  };

  // Remove a specific folder from selection
  const handleRemoveFolder = (folderId: string) => {
    setSelectedFolders(prev => prev.filter(f => f.id !== folderId));
  };

  // Clear all selected folders
  const handleClearAllFolders = () => {
    setSelectedFolders([]);
  };

  // Handle save configuration with validation
  const handleSaveConfig = () => {
    // Extra validation before saving
    if (selectedFolders.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please select at least one folder before saving the configuration.",
        variant: "destructive",
      });
      return;
    }

    // All validation passed, proceed with saving
    updateFolderMutation.mutate();
  };

  // Handle new integration creation
  const handleCreateIntegration = () => {
    if (!selectedIntegrationType || !integrationName.trim()) {
      toast({
        title: "Missing Information",
        description: "Please select an integration type and enter a name.",
        variant: "destructive",
      });
      return;
    }

    createIntegrationMutation.mutate({
      type: selectedIntegrationType,
      name: integrationName.trim(),
    });
  };

  // Render the setup step UI based on current step
  const renderStep = () => {
    if (isNewIntegration) {
      return (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Choose Integration Type</CardTitle>
              <CardDescription>
                Select the platform you want to connect to MeetSync
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Google Services Group */}
                <div 
                  className="border-2 border-dashed border-blue-200 rounded-lg p-6 cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all group"
                  onClick={() => setLocation('/integrations/google/setup')}
                >
                  <div className="text-center space-y-3">
                    <div className="mx-auto w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
                      <div className="w-6 h-6 bg-red-500 rounded-full" />
                    </div>
                    <div>
                      <h3 className="font-medium text-lg">Google Services</h3>
                      <p className="text-sm text-gray-600">
                        Drive, Gmail, Calendar & more
                      </p>
                    </div>
                    <div className="flex flex-wrap gap-1 justify-center">
                      <Badge variant="secondary" className="text-xs">Drive</Badge>
                      <Badge variant="secondary" className="text-xs">Gmail</Badge>
                      <Badge variant="secondary" className="text-xs">Calendar</Badge>
                    </div>
                  </div>
                </div>

                {/* Microsoft Teams */}
                <div 
                  className="border-2 border-dashed border-purple-200 rounded-lg p-6 cursor-pointer hover:border-purple-300 hover:bg-purple-50 transition-all group"
                  onClick={() => {
                    setSelectedIntegrationType('microsoft_teams');
                    setIntegrationName('Microsoft Teams');
                  }}
                >
                  <div className="text-center space-y-3">
                    <div className="mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                      <svg className="w-6 h-6 text-purple-600" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M24 12a12 12 0 1 1-24 0 12 12 0 0 1 24 0zm-12 8a8 8 0 1 0 0-16 8 8 0 0 0 0 16z"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-lg">Microsoft Teams</h3>
                      <p className="text-sm text-gray-600">
                        Teams conversations & files
                      </p>
                    </div>
                  </div>
                </div>

                {/* Notion */}
                <div 
                  className="border-2 border-dashed border-gray-200 rounded-lg p-6 cursor-pointer hover:border-gray-300 hover:bg-gray-50 transition-all group"
                  onClick={() => {
                    setSelectedIntegrationType('notion');
                    setIntegrationName('Notion');
                  }}
                >
                  <div className="text-center space-y-3">
                    <div className="mx-auto w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                      <svg className="w-6 h-6 text-gray-600" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M4.459 4.208c.746.606 1.026.56 2.428.466l13.215-.793c.28 0 .047-.28-.046-.326L17.86 1.968c-.42-.326-.981-.7-2.055-.607L3.01 2.295c-.466.047-.56.28-.374.466l1.823 1.447zm.793 3.08v13.904c0 .747.373 1.027 1.214.98l14.523-.84c.841-.046.935-.56.935-1.167V6.354c0-.606-.233-.933-.747-.887l-15.177.887c-.56.047-.748.327-.748.934zm14.337.745c.093.42 0 .84-.42.888l-.7.14v10.264c-.608.327-1.168.514-1.635.514-.748 0-.935-.234-1.495-.933l-4.577-7.186v6.952L12.21 19s0 .84-1.168.84l-3.222.186c-.093-.186 0-.653.327-.746l.84-.233V9.854L7.822 9.76c-.094-.42.14-1.026.793-1.073l3.456-.233 4.764 7.279v-6.44l-1.215-.139c-.093-.514.28-.887.747-.933l3.268-.187z"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-lg">Notion</h3>
                      <p className="text-sm text-gray-600">
                        Pages & databases
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">Coming Soon</Badge>
                  </div>
                </div>
              </div>

              {/* Selected integration creation form */}
              {(selectedIntegrationType && selectedIntegrationType !== 'google') && (
                <div className="mt-6 p-4 border rounded-lg bg-gray-50">
                  <h4 className="font-medium mb-3">Create {selectedIntegrationType} Integration</h4>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="integration-name">Integration Name</Label>
                      <Input
                        id="integration-name"
                        value={integrationName}
                        onChange={(e) => setIntegrationName(e.target.value)}
                        placeholder={`Enter name for ${selectedIntegrationType} integration`}
                      />
                    </div>
                    <div className="flex space-x-2">
                      <Button 
                        onClick={handleCreateIntegration}
                        disabled={!integrationName.trim() || isCreatingIntegration}
                      >
                        {isCreatingIntegration ? "Creating..." : "Create Integration"}
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => {
                          setSelectedIntegrationType('');
                          setIntegrationName('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    // Wait for integration data to load before rendering (for existing integrations)
    if (!isNewIntegration && (isLoadingIntegration || !integrationData?.integration)) {
      return (
        <Card>
          <CardHeader>
            <CardTitle>Loading Integration...</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-12 w-full" />
          </CardContent>
        </Card>
      );
    }
    // Step 1: OAuth Connection
    if (step === '1') {
      if (integrationData?.integration.status === 'connected') {
        if (isTeamsIntegration) {
          // Teams: after OAuth, prompt for folder selection
          return (
            <Card>
              <CardHeader>
                <CardTitle>Microsoft Teams Connected</CardTitle>
                <CardDescription>
                  Your Microsoft Teams account has been successfully connected to the integration.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 text-blue-600">
                  <CheckCircleIcon />
                  <span>Authentication successful</span>
                </div>
                <p className="mt-4">Now let's configure which Teams channels, OneDrive folders, and SharePoint sites contain your meeting files.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => setLocation(`/integrations/${integrationId}/setup?step=2`)}>
                  Continue to Source Selection
                </Button>
              </CardFooter>
            </Card>
          );
        }
        if (isGoogleIntegration) {
          // Google: after OAuth, prompt for folder selection
          return (
            <Card>
              <CardHeader>
                <CardTitle>Google Account Connected</CardTitle>
                <CardDescription>
                  Your Google account has been successfully connected to the integration.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircleIcon />
                  <span>Authentication successful</span>
                </div>
                <p className="mt-4">Now let's configure which Google Drive folders contain your documents and files for AI-powered search.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => setLocation(`/integrations/${integrationId}/setup?step=2`)}>
                  Continue to Folder Selection
                </Button>
              </CardFooter>
            </Card>
          );
        }
        // Fallback for other types
        return (
          <Card>
            <CardHeader>
              <CardTitle>Setup Complete</CardTitle>
              <CardDescription>
                This integration is connected. No further configuration is needed at this time.
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button onClick={() => setLocation(`/integrations`)}>
                Finish
              </Button>
            </CardFooter>
          </Card>
        );
      }
      // If not connected, show connect button
      if (isTeamsIntegration) {
        // Teams: pre-OAuth connect card
        return (
          <Card>
            <CardHeader>
              <CardTitle>Connect Microsoft Teams</CardTitle>
              <CardDescription>
                Connect your Microsoft Teams account to access all your files and documents from Teams.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">This will allow the application to:</p>
              <ul className="list-disc pl-5 space-y-1 mb-4">
                <li>Access all your Teams files, documents, meeting recordings, and chat logs</li>
                <li>Read content from OneDrive and SharePoint connected to Teams</li>
                <li>Process PDFs, Word documents, PowerPoint presentations, and other files from selected Teams channels and folders</li>
              </ul>
              <p className="mb-4 text-sm text-gray-500">
                We'll only access the specific sources you select in the next step.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleConnectAccount}
                disabled={isConnecting}
              >
                {isConnecting ? (
                  <>
                    <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : 'Connect Microsoft Teams'}
              </Button>
            </CardFooter>
          </Card>
        );
      }
      if (isGoogleIntegration) {
        // Google: pre-OAuth connect card
        return (
          <Card>
            <CardHeader>
              <CardTitle>Connect Google Drive</CardTitle>
              <CardDescription>
                Connect your Google account to access all your files and documents from Google Drive.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">This will allow the application to:</p>
              <ul className="list-disc pl-5 space-y-1 mb-4">
                <li>Access all your Google Drive files including documents, presentations, and spreadsheets</li>
                <li>Process Google Docs, PDFs, Word documents, PowerPoint presentations, and other file types</li>
                <li>Read meeting recordings, transcripts, and related metadata when available</li>
              </ul>
              <p className="mb-4 text-sm text-gray-500">
                We'll only access the specific folders you select in the next step.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleConnectAccount}
                disabled={isConnecting}
              >
                {isConnecting ? (
                  <>
                    <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : 'Connect Google Account'}
              </Button>
            </CardFooter>
          </Card>
        );
      }
      // Fallback for unknown types
      return (
        <Card>
          <CardHeader>
            <CardTitle>Connect Integration</CardTitle>
            <CardDescription>
              Connect your account to access all your files and documents.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={handleConnectAccount} disabled={isConnecting}>
              {isConnecting ? (
                <>
                  <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                  Connecting...
                </>
              ) : 'Connect Account'}
            </Button>
          </CardFooter>
        </Card>
      );
    }
    // Step 2: Folder Selection
    if (step === '2') {
      if (isTeamsIntegration) {
        // Teams: show folder/source selection
        return (
          <Card>
            <CardHeader>
              <CardTitle>Select Teams Sources</CardTitle>
              <CardDescription>
                Choose the Teams channels, OneDrive folders, and SharePoint sites containing your meeting files.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingTeamsFolders ? (
                <div className="space-y-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : teamsFoldersError ? (
                <Alert variant="destructive">
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>
                    Failed to load Teams sources. Please try again or reconnect your Microsoft Teams account.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <div className="mb-4">
                    <Label htmlFor="search-teams-sources">Search Sources</Label>
                    <Input 
                      id="search-teams-sources"
                      placeholder="Search Teams channels, OneDrive folders, SharePoint sites..." 
                      className="mt-1"
                      value={folderSearch}
                      onChange={(e) => setFolderSearch(e.target.value)}
                    />
                  </div>

                  {/* Selected Sources Display */}
                  {selectedFolders.length > 0 && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <Label className="text-sm font-semibold">
                          Selected Sources ({selectedFolders.length})
                        </Label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleClearAllFolders}
                          className="text-xs"
                        >
                          Clear All
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {selectedFolders.map((folder) => (
                          <Badge
                            key={folder.id}
                            variant="secondary"
                            className="pr-1 max-w-[200px] truncate"
                          >
                            {folder.type === 'teams_channel' && <span className="mr-1 text-xs">👥</span>}
                            {folder.type === 'onedrive_folder' && <FolderIcon className="mr-1 h-3 w-3" />}
                            {folder.type === 'sharepoint_site' && <span className="mr-1 text-xs">🏢</span>}
                            <span className="truncate">{folder.name}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-1 h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveFolder(folder.id);
                              }}
                            >
                              <TrashIcon className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div className="border rounded-md h-60 overflow-y-auto relative">
                    {isLoadingTeamsFolders ? (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center p-4">
                          <Loader2Icon className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
                          <p className="text-gray-500">Loading Teams sources...</p>
                        </div>
                      </div>
                    ) : teamsFoldersError instanceof Error ? (
                      <div className="p-4 text-center">
                        <p className="text-red-500 mb-2">Error loading sources</p>
                        <p className="text-sm text-gray-400 mb-2">
                          {typeof teamsFoldersError === 'object' && teamsFoldersError && 'message' in teamsFoldersError && typeof (teamsFoldersError as any).message === 'string'
                            ? (teamsFoldersError as any).message
                            : "There was a problem accessing your Microsoft Teams sources."}
                        </p>
                        <div className="flex flex-col gap-2 mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => safeRefetchTeamsFolders()}
                          >
                            Try Again
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleConnectAccount()}
                          >
                            Reconnect Microsoft Teams
                          </Button>
                        </div>
                      </div>
                    ) : teamsFoldersData && teamsFoldersData.sources ? (
                      <div className="p-2">
                        {teamsFoldersData.user && (
                          <div className="p-2 mb-2 text-sm text-gray-500 border-b">
                            <span>Connected as: <strong>{teamsFoldersData.user.displayName}</strong></span>
                            {teamsFoldersData.user.email && <p className="text-xs">{teamsFoldersData.user.email}</p>}
                          </div>
                        )}
                        
                        {teamsFoldersData.sources.length > 0 ? (
                          <>
                            {teamsFoldersData.sources
                              .filter((source: any) => 
                                folderSearch === '' || 
                                source.name.toLowerCase().includes(folderSearch.toLowerCase()) ||
                                (source.description && source.description.toLowerCase().includes(folderSearch.toLowerCase()))
                              )
                              .map((source: any) => {
                                const isSelected = selectedFolders.some((f) => f.id === source.id);
                                const getSourceIcon = (type: string) => {
                                  if (type === 'teams_channel') return '👥';
                                  if (type === 'sharepoint_site') return '🏢';
                                  return <FolderIcon className="h-5 w-5 text-blue-500" />;
                                };
                                
                                return (
                                  <div 
                                    key={source.id}
                                    className={`flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md cursor-pointer ${
                                      isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : ''
                                    }`}
                                    onClick={() => handleFolderSelect(source.id, source.name, source.type)}
                                  >
                                    <Checkbox
                                      checked={isSelected}
                                      onChange={() => {}} // Handled by parent onClick
                                      className="mr-3"
                                    />
                                    <div className="mr-2">
                                      {typeof getSourceIcon(source.type) === 'string' ? (
                                        <span className="text-lg">{getSourceIcon(source.type)}</span>
                                      ) : (
                                        getSourceIcon(source.type)
                                      )}
                                    </div>
                                    <div className="flex-1">
                                      <div className="font-medium">{source.name}</div>
                                      {source.description && (
                                        <div className="text-xs text-gray-500">{source.description}</div>
                                      )}
                                      {source.itemCount !== undefined && (
                                        <div className="text-xs text-gray-400">{source.itemCount} items</div>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                              
                              {teamsFoldersData.sources.filter((source: any) => 
                                folderSearch === '' || 
                                source.name.toLowerCase().includes(folderSearch.toLowerCase()) ||
                                (source.description && source.description.toLowerCase().includes(folderSearch.toLowerCase()))
                              ).length === 0 && (
                                <p className="p-4 text-center text-gray-500">No matching sources found</p>
                              )}
                          </>
                        ) : (
                          <div className="p-4 text-center">
                            <p className="text-gray-500 mb-2">No sources found in your Microsoft Teams</p>
                            <p className="text-sm text-gray-400 mb-3">
                              You may need to join Teams or create OneDrive folders first
                            </p>
                            <div className="flex justify-center">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open('https://teams.microsoft.com', '_blank')}
                                className="mr-2"
                              >
                                Open Teams
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => safeRefetchTeamsFolders()}
                              >
                                Refresh
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center p-4">
                          <p className="text-gray-500 mb-2">No sources available</p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => safeRefetchTeamsFolders()}
                          >
                            Reload Sources
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setLocation('/integrations')}>
                Cancel
              </Button>
              <div className="space-x-2">
                <Button
                  onClick={handleSaveConfig}
                  disabled={selectedFolders.length === 0 || updateFolderMutation.isPending}
                >
                  {updateFolderMutation.isPending ? (
                    <>
                      <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : 'Save Configuration'}
                </Button>
              </div>
            </CardFooter>
          </Card>
        );
      }
      if (isGoogleIntegration) {
        // Google: always show folder picker until config is saved
        return (
          <Card>
            <CardHeader>
              <CardTitle>Select Google Drive Folders</CardTitle>
              <CardDescription>
                Choose the folders containing your documents, files, and meeting content.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {integrationId && (
                <EnhancedDriveBrowser
                  integrationId={integrationId}
                  selectedFolders={selectedFolders}
                  onFolderSelect={handleFolderSelect}
                  onRemoveFolder={handleRemoveFolder}
                  onClearAll={handleClearAllFolders}
                />
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setLocation('/integrations')}>
                Cancel
              </Button>
              <div className="space-x-2">
                <Button
                  onClick={handleSaveConfig}
                  disabled={selectedFolders.length === 0 || updateFolderMutation.isPending}
                >
                  {updateFolderMutation.isPending ? (
                    <>
                      <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : 'Save Configuration'}
                </Button>
              </div>
            </CardFooter>
          </Card>
        );
      }
      // Fallback for unknown types
      return (
        <Card>
          <CardHeader>
            <CardTitle>Setup Complete</CardTitle>
            <CardDescription>
              This integration is connected. No further configuration is needed at this time.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => setLocation(`/integrations`)}>
              Finish
            </Button>
          </CardFooter>
        </Card>
      );
    }
    // Default fallback
    return (
      <Card>
        <CardHeader>
          <CardTitle>Invalid Step</CardTitle>
          <CardDescription>
            The requested setup step is invalid.
          </CardDescription>
        </CardHeader>
        <CardFooter>
          <Button onClick={() => setLocation(`/integrations/${integrationId}/setup?step=1`)}>
            Return to Step 1
          </Button>
        </CardFooter>
      </Card>
    );
  };

  if (isLoadingIntegration) {
    return (
      <AppLayout title="Integration Setup">
        <div className="space-y-4">
          <Skeleton className="h-12 w-1/4" />
          <Skeleton className="h-[300px] w-full" />
        </div>
      </AppLayout>
    );
  }

  if (!isNewIntegration && (!integrationId || integrationError)) {
    return (
      <AppLayout title="Integration Setup">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load integration. Please try again or return to the integrations page.
          </AlertDescription>
        </Alert>
        <Button className="mt-4" onClick={() => setLocation('/integrations')}>
          Back to Integrations
        </Button>
      </AppLayout>
    );
  }

  const integration = integrationData?.integration;

  return (
    <AppLayout title={isNewIntegration ? 'Create Integration' : `Setup ${integration?.name || 'Integration'}`}>
      <div className="space-y-4 max-w-3xl mx-auto">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">
            {isNewIntegration ? 'Create New Integration' : `${integration?.name} Setup`}
          </h2>
          {!isNewIntegration && (
            <div className="text-sm text-gray-500">
              Step {step} of 2
            </div>
          )}
        </div>
        {renderStep()}
      </div>
    </AppLayout>
  );
}