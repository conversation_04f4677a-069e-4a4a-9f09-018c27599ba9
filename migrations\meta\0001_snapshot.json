{"id": "4bf5180f-049e-465f-83f7-98e49072a257", "prevId": "36827350-d2de-488d-a83a-362b79e2b76d", "version": "7", "dialect": "postgresql", "tables": {"public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "sources_used": {"name": "sources_used", "type": "jsonb", "primaryKey": false, "notNull": false}, "relevant_chunks": {"name": "relevant_chunks", "type": "jsonb", "primaryKey": false, "notNull": false}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'gpt-4.1-nano'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_messages_session_id_chat_sessions_id_fk": {"name": "chat_messages_session_id_chat_sessions_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_sessions": {"name": "chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "enabled_sources": {"name": "enabled_sources", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.file_chunks": {"name": "file_chunks", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": true}, "chunk_index": {"name": "chunk_index", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": false}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"file_chunks_file_id_files_id_fk": {"name": "file_chunks_file_id_files_id_fk", "tableFrom": "file_chunks", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true}, "file_content": {"name": "file_content", "type": "text", "primaryKey": false, "notNull": false}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": false}, "download_url": {"name": "download_url", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "parent_folder": {"name": "parent_folder", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "extracted_metadata": {"name": "extracted_metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": false}, "sync_item_id": {"name": "sync_item_id", "type": "integer", "primaryKey": false, "notNull": false}, "notion_page_id": {"name": "notion_page_id", "type": "text", "primaryKey": false, "notNull": false}, "last_modified": {"name": "last_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_shared": {"name": "is_shared", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "shared_with": {"name": "shared_with", "type": "text[]", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.integrations": {"name": "integrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'disconnected'"}, "credentials": {"name": "credentials", "type": "text", "primaryKey": false, "notNull": false}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "next_sync_at": {"name": "next_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "sync_schedule": {"name": "sync_schedule", "type": "text", "primaryKey": false, "notNull": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'idle'"}, "source_config": {"name": "source_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "destination_config": {"name": "destination_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_llm_enabled": {"name": "is_llm_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "sync_filters": {"name": "sync_filters", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "enabled_sources": {"name": "enabled_sources", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sync_items": {"name": "sync_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "integration_id": {"name": "integration_id", "type": "integer", "primaryKey": false, "notNull": true}, "sync_log_id": {"name": "sync_log_id", "type": "integer", "primaryKey": false, "notNull": false}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "destination_url": {"name": "destination_url", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sync_logs": {"name": "sync_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "integration_id": {"name": "integration_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "items_processed": {"name": "items_processed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "items_success": {"name": "items_success", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "items_failed": {"name": "items_failed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}