import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { getIntegrations, getSyncLogs, startSync } from "@/lib/api";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { MetricsCard, StatsGrid } from "@/components/ui/metrics-card";
import { DashboardSkeleton } from "@/components/ui/loading-skeleton";
import { motion } from "framer-motion";
import {
  Users,
  Zap,
  Calendar,
  TrendingUp,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  Activity,
  BarChart3,
  Settings,
  Plus,
  ArrowRight,
  Sparkles,
  Brain,
  Target,
  Wifi,
  Database
} from "lucide-react";
import { formatDate, formatRelativeTime } from "@/lib/utils/date";
import { getIntegrationName, getStatusBadgeColor } from "@/lib/utils/integrations";
import { PieChart, Pie, Cell, ResponsiveContainer, AreaChart, Area, XAxis, YAxis, Tooltip, BarChart, Bar, Legend } from "recharts";

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

export default function Dashboard() {
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();

  const { toast } = useToast();
  const [syncingIntegrationId, setSyncingIntegrationId] = useState<number | null>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const { data: integrationsData, isLoading: isLoadingIntegrations } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: getIntegrations,
  });
  
  const { data: syncLogsData, isLoading: isLoadingLogs } = useQuery({
    queryKey: ['/api/sync-logs'],
    queryFn: () => getSyncLogs({ limit: 10 }),
  });
  
  // Mutation for starting a sync
  const syncMutation = useMutation({
    mutationFn: (integrationId: number) => startSync(integrationId),
    onMutate: (integrationId: number) => {
      setSyncingIntegrationId(integrationId);
    },
    onSuccess: (data: any) => {
      toast({
        title: "Sync started successfully",
        description: `Sync job created with ID: ${data.syncLog.id}`,
      });
      queryClient.invalidateQueries({ queryKey: ['/api/sync-logs'] });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      startPollingForSyncCompletion();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to start sync",
        description: error.message || "An error occurred while trying to start the sync process.",
        variant: "destructive",
      });
    },
    onSettled: () => {
      setSyncingIntegrationId(null);
    },
  });
  
  const startPollingForSyncCompletion = () => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
    }

    let pollCount = 0;
    const maxPolls = 60;
    
    pollIntervalRef.current = setInterval(() => {
      pollCount++;
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      queryClient.invalidateQueries({ queryKey: ['/api/sync-logs'] });
      
      if (pollCount >= maxPolls) {
        if (pollIntervalRef.current) {
          clearInterval(pollIntervalRef.current);
          pollIntervalRef.current = null;
        }
      }
    }, 5000);
  };

  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };
  }, []);
  
  const integrations = integrationsData?.integrations || [];
  const syncLogs = syncLogsData?.logs || [];
  
  // Enhanced statistics
  const integrationStats = {
    total: integrations.length,
    connected: integrations.filter((i: any) => 
      i.status === 'connected' || i.status === 'configured' || i.status === 'syncing'
    ).length,
    syncing: integrations.filter((i: any) => i.status === 'syncing').length,
    error: integrations.filter((i: any) => i.status === 'error').length,
  };
  
  const syncStats = {
    total: syncLogs.length,
    success: syncLogs.filter((log: any) => log.status === 'success').length,
    partial: syncLogs.filter((log: any) => log.status === 'partial').length,
    failed: syncLogs.filter((log: any) => log.status === 'failed').length,
  };
  
  // Calculate success rate
  const successRate = syncStats.total > 0 ? 
    Math.round((syncStats.success / syncStats.total) * 100) : 0;

  // Enhanced stats data
  const dashboardStats = [
    {
      title: "Total Integrations",
      value: integrationStats.total,
      description: "Connected platforms",
      icon: <Zap className="h-5 w-5 text-blue-500" />,
      trend: integrationStats.total > 0 ? {
        value: 12,
        label: "vs last month",
        type: "increase" as const
      } : undefined,
      badge: integrationStats.syncing > 0 ? {
        text: `${integrationStats.syncing} syncing`,
        variant: "secondary" as const
      } : undefined,
      onClick: () => setLocation("/integrations")
    },
    {
      title: "Sync Success Rate",
      value: `${successRate}%`,
      description: "Last 7 days",
      icon: <Target className="h-5 w-5 text-green-500" />,
      trend: {
        value: 8,
        label: "vs last week",
        type: "increase" as const
      },
      badge: syncStats.success > 0 ? {
        text: `${syncStats.success} successful`,
        variant: "outline" as const
      } : undefined,
      onClick: () => setLocation("/logs")
    },
    {
      title: "Active Connections",
      value: integrationStats.connected,
      description: "Ready to sync",
      icon: <Wifi className="h-5 w-5 text-purple-500" />,
      trend: integrationStats.connected > 0 ? {
        value: 5,
        label: "vs yesterday",
        type: "increase" as const
      } : undefined,
      badge: integrationStats.error > 0 ? {
        text: `${integrationStats.error} errors`,
        variant: "destructive" as const
      } : undefined,
    },
    {
      title: "Data Processed",
      value: "2.4K",
      description: "Items this week",
      icon: <Database className="h-5 w-5 text-orange-500" />,
      trend: {
        value: 15,
        label: "vs last week",
        type: "increase" as const
      },
      onClick: () => setLocation("/files")
    }
  ];

  // Activity chart data
  const activityData = [
    { name: 'Mon', syncs: 4, errors: 0 },
    { name: 'Tue', syncs: 3, errors: 1 },
    { name: 'Wed', syncs: 8, errors: 0 },
    { name: 'Thu', syncs: 6, errors: 1 },
    { name: 'Fri', syncs: 9, errors: 0 },
    { name: 'Sat', syncs: 2, errors: 0 },
    { name: 'Sun', syncs: 5, errors: 0 }
  ];

  if (isLoadingIntegrations || isLoadingLogs) {
    return (
      <AppLayout title="Dashboard">
        <DashboardSkeleton />
      </AppLayout>
    );
  }

  return (
    <AppLayout 
      title="Dashboard" 
    >
      <motion.div 
        variants={container}
        initial="hidden"
        animate="show"
        className="space-y-8"
      >
          {/* Welcome Section */}
          <motion.div variants={item} className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Brain className="h-6 w-6 text-blue-600" />
                  <h2 className="text-2xl font-bold text-foreground">Welcome back, Alex!</h2>
                </div>
                                 <p className="text-muted-foreground">
                   Your AI-powered unified assistant is ready. {integrationStats.connected} platforms connected and syncing.
                 </p>
              </div>
              <Button onClick={() => setLocation("/chat")} className="flex items-center space-x-2">
                <Sparkles className="h-4 w-4" />
                <span>Ask AI</span>
              </Button>
            </div>
          </motion.div>

          {/* Stats Grid */}
          <motion.div variants={item}>
            <StatsGrid stats={dashboardStats} columns={4} />
          </motion.div>
            
          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Chart */}
            <motion.div variants={item}>
            <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Activity className="h-5 w-5" />
                    <span>Weekly Activity</span>
                  </CardTitle>
                  <CardDescription>
                    Sync operations over the last 7 days
                  </CardDescription>
              </CardHeader>
              <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={activityData}>
                        <defs>
                          <linearGradient id="syncGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Area 
                          type="monotone" 
                          dataKey="syncs" 
                          stroke="#3b82f6" 
                          fillOpacity={1} 
                          fill="url(#syncGradient)" 
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
              </CardContent>
            </Card>
            </motion.div>
            
            {/* System Health */}
            <motion.div variants={item}>
            <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>System Health</span>
                  </CardTitle>
                  <CardDescription>
                    All systems operational
                  </CardDescription>
              </CardHeader>
                <CardContent className="space-y-4">
                  {[
                    { name: "API Services", status: "operational", uptime: 99.9 },
                    { name: "AI Processing", status: "operational", uptime: 98.5 },
                    { name: "Data Storage", status: "operational", uptime: 99.8 },
                    { name: "Sync Engine", status: "operational", uptime: 97.2 }
                  ].map((service) => (
                    <div key={service.name} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium">{service.name}</span>
                  </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-muted-foreground">{service.uptime}%</span>
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          Operational
                        </Badge>
                  </div>
                </div>
                  ))}
              </CardContent>
            </Card>
            </motion.div>
          </div>
          
          {/* Recent Activity & Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
            <motion.div variants={item} className="lg:col-span-2">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                        <div>
                    <CardTitle>Recent Activity</CardTitle>
                    <CardDescription>Latest sync operations and events</CardDescription>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => setLocation("/logs")}>
                    View all
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  {syncLogs.length > 0 ? (
                    syncLogs.slice(0, 5).map((log: any) => {
                      const integration = integrations.find((i: any) => i.id === log.integrationId);
                      return (
                        <div key={log.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            log.status === 'success' ? 'bg-green-100 text-green-600' :
                            log.status === 'partial' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {log.status === 'success' ? <CheckCircle className="h-5 w-5" /> :
                             log.status === 'partial' ? <Clock className="h-5 w-5" /> :
                             <AlertCircle className="h-5 w-5" />}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {getIntegrationName(integration?.type || '')} Sync
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {log.itemsSuccess} of {log.itemsProcessed} items processed
                          </p>
                        </div>
                        <div className="text-right">
                            <p className="text-xs font-medium">{formatRelativeTime(log.startTime)}</p>
                            <p className="text-xs text-muted-foreground">{formatDate(log.startTime)}</p>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                      <p className="text-muted-foreground">No recent activity</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Quick Actions */}
            <motion.div variants={item}>
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common tasks and shortcuts</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    onClick={() => setLocation("/integrations")}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Integration
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    onClick={() => setLocation("/chat")}
                  >
                    <Brain className="h-4 w-4 mr-2" />
                    Ask AI Assistant
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    onClick={() => setLocation("/schedules")}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Manage Schedules
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    onClick={() => setLocation("/settings")}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
              </CardContent>
            </Card>
            </motion.div>
          </div>
          
          {/* Connected Integrations */}
          <motion.div variants={item}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
          <div>
                  <CardTitle>Connected Integrations</CardTitle>
                  <CardDescription>Manage your platform connections</CardDescription>
                </div>
                <Button onClick={() => setLocation("/integrations")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Integration
                </Button>
              </CardHeader>
              <CardContent>
                {integrations.length === 0 ? (
                  <div className="text-center py-12">
                    <Zap className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No integrations yet</h3>
                    <p className="text-muted-foreground mb-4">Connect your first platform to get started</p>
                    <Button onClick={() => setLocation("/integrations")}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Integration
                    </Button>
                  </div>
              ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {integrations.map((integration: any) => (
                      <div key={integration.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                            <Zap className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-medium">{integration.name}</h4>
                            <div className="flex items-center space-x-2">
                              <p className="text-sm text-muted-foreground">
                              {getIntegrationName(integration.type)}
                              </p>
                              <Badge variant={integration.status === 'connected' || integration.status === 'configured' ? 'outline' : 'destructive'}>
                                {integration.status}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        {(integration.status === 'connected' || integration.status === 'configured') && (
                          <Button 
                            variant="ghost"
                            size="sm"
                            onClick={() => syncMutation.mutate(integration.id)}
                            disabled={syncingIntegrationId === integration.id || integration.status === 'syncing'}
                          >
                            <RefreshCw className={`h-4 w-4 ${syncingIntegrationId === integration.id || integration.status === 'syncing' ? 'animate-spin' : ''}`} />
                          </Button>
                        )}
                      </div>
                    ))}
                      </div>
                  )}
              </CardContent>
            </Card>
          </motion.div>
                 </motion.div>
    </AppLayout>
  );
}
