import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { getIntegrations, getSyncLogs, startSync } from "@/lib/api";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, Legend } from "recharts";
import { formatDate, formatRelativeTime } from "@/lib/utils/date";
import { getIntegrationName, getStatusBadgeColor } from "@/lib/utils/integrations";
import { Button } from "@/components/ui/button";
import { Loader2Icon, RefreshCwIcon, AlertCircleIcon, CheckCircleIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

export default function Dashboard() {
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState("Overview");
  const { toast } = useToast();
  const [syncingIntegrationId, setSyncingIntegrationId] = useState<number | null>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const { data: integrationsData, isLoading: isLoadingIntegrations } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: getIntegrations,
  });
  
  const { data: syncLogsData, isLoading: isLoadingLogs } = useQuery({
    queryKey: ['/api/sync-logs'],
    queryFn: () => getSyncLogs({ limit: 5 }),
  });
  
  // Mutation for starting a sync
  const syncMutation = useMutation({
    mutationFn: (integrationId: number) => startSync(integrationId),
    onMutate: (integrationId: number) => {
      setSyncingIntegrationId(integrationId);
    },
    onSuccess: (data: any) => {
      toast({
        title: "Sync started successfully",
        description: `Sync job created with ID: ${data.syncLog.id}`,
      });
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/sync-logs'] });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      
      // Start polling for sync completion
      startPollingForSyncCompletion();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to start sync",
        description: error.message || "An error occurred while trying to start the sync process.",
        variant: "destructive",
      });
    },
    onSettled: () => {
      setSyncingIntegrationId(null);
    },
  });
  
  // Function to start polling for sync completion
  const startPollingForSyncCompletion = () => {
    // Clear any existing polling
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
    }

    let pollCount = 0;
    const maxPolls = 60; // Poll for up to 5 minutes (60 * 5 seconds)
    
    pollIntervalRef.current = setInterval(() => {
      pollCount++;
      
      // Refetch integration and sync log data to check status
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      queryClient.invalidateQueries({ queryKey: ['/api/sync-logs'] });
      
      // Stop polling after max attempts
      if (pollCount >= maxPolls) {
        if (pollIntervalRef.current) {
          clearInterval(pollIntervalRef.current);
          pollIntervalRef.current = null;
        }
      }
    }, 5000); // Poll every 5 seconds
  };

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };
  }, []);
  
  const integrations = integrationsData?.integrations || [];
  const syncLogs = syncLogsData?.logs || [];
  
  // Integration status stats - calculate statistics from integration data
  const integrationStats = {
    total: integrations.length,
    connected: integrations.filter((i: any) => 
      i.status === 'connected' || 
      i.status === 'configured' || 
      i.status === 'syncing'
    ).length,
    error: integrations.filter((i: any) => i.status === 'error').length,
  };
  
  const pieData = [
    { name: 'Connected', value: integrationStats.connected, color: '#4ade80' },
    { name: 'Error', value: integrationStats.error, color: '#f87171' },
    { name: 'Disconnected', value: integrationStats.total - integrationStats.connected - integrationStats.error, color: '#d1d5db' },
  ];
  
  // Sync stats
  const syncStats = {
    total: syncLogs.length,
    success: syncLogs.filter((log: any) => log.status === 'success').length,
    partial: syncLogs.filter((log: any) => log.status === 'partial').length,
    failed: syncLogs.filter((log: any) => log.status === 'failed').length,
  };
  
  const barData = [
    { name: 'Google Meet', success: 12, failed: 1 },
    { name: 'Google Chat', success: 8, failed: 0 },
    { name: 'Microsoft Teams', success: 0, failed: 0 },
  ];

  return (
    <AppLayout 
      title="Dashboard" 
      tabs={["Overview", "Activity"]}
      currentTab={activeTab}
      onTabChange={setActiveTab}
    >
      {activeTab === "Overview" && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Integrations Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Integrations</CardTitle>
                <CardDescription>Connected platforms</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-3xl font-bold">{integrationStats.total}</p>
                    <p className="text-sm text-muted-foreground">Total integrations</p>
                    <div className="mt-2">
                      <p className="text-sm flex"><span className="h-2 w-2 rounded-full bg-green-500 mt-1.5 mr-2"></span> {integrationStats.connected} Connected</p>
                      <p className="text-sm flex"><span className="h-2 w-2 rounded-full bg-red-500 mt-1.5 mr-2"></span> {integrationStats.error} Error</p>
                    </div>
                  </div>
                  <div className="h-24 w-24">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={pieData}
                          cx="50%"
                          cy="50%"
                          innerRadius={25}
                          outerRadius={40}
                          dataKey="value"
                        >
                          {pieData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                <button 
                  className="w-full mt-4 text-sm text-primary font-medium"
                  onClick={() => setLocation("/integrations")}
                >
                  View all integrations →
                </button>
              </CardContent>
            </Card>
            
            {/* Syncs Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Sync Activity</CardTitle>
                <CardDescription>Last 7 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-3xl font-bold">{syncStats.total}</p>
                    <p className="text-sm text-muted-foreground">Total syncs</p>
                    <div className="mt-2">
                      <p className="text-sm flex"><span className="h-2 w-2 rounded-full bg-green-500 mt-1.5 mr-2"></span> {syncStats.success} Success</p>
                      <p className="text-sm flex"><span className="h-2 w-2 rounded-full bg-yellow-500 mt-1.5 mr-2"></span> {syncStats.partial} Partial</p>
                      <p className="text-sm flex"><span className="h-2 w-2 rounded-full bg-red-500 mt-1.5 mr-2"></span> {syncStats.failed} Failed</p>
                    </div>
                  </div>
                  <div className="h-24 w-28">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={barData}>
                        <XAxis dataKey="name" hide />
                        <Bar dataKey="success" stackId="a" fill="#4ade80" />
                        <Bar dataKey="failed" stackId="a" fill="#f87171" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                <button 
                  className="w-full mt-4 text-sm text-primary font-medium"
                  onClick={() => setLocation("/logs")}
                >
                  View all activity →
                </button>
              </CardContent>
            </Card>
            
            {/* Status Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>System Status</CardTitle>
                <CardDescription>All systems operational</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">API Services</span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Operational</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Scheduler</span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Operational</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Storage</span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Operational</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">AI Processing</span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Operational</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Recent Activity */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Recent Activity</h3>
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {isLoadingLogs ? (
                    <div className="p-4 text-center text-sm text-gray-500">Loading recent activity...</div>
                  ) : syncLogs.length > 0 ? (
                    syncLogs.map((log: any) => (
                      <div key={log.id} className="flex items-center justify-between p-4">
                        <div>
                          <div className="flex items-center">
                            <span className={`h-2 w-2 rounded-full mr-2 ${
                              log.status === 'success' ? 'bg-green-500' : 
                              log.status === 'partial' ? 'bg-yellow-500' : 'bg-red-500'
                            }`}></span>
                            <span className="font-medium">
                              {getIntegrationName(integrations.find((i: any) => i.id === log.integrationId)?.type || '')} Sync
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">
                            {log.itemsSuccess} of {log.itemsProcessed} items processed successfully
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{formatDate(log.startTime)}</p>
                          <p className="text-xs text-gray-500">{formatRelativeTime(log.startTime)}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-sm text-gray-500">No recent activity</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Connected Integrations */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Connected Integrations</h3>
            <div className="mb-6">
              {isLoadingIntegrations ? (
                <Card>
                  <CardContent className="p-6 flex items-center justify-center">
                    <Loader2Icon className="h-8 w-8 animate-spin text-muted-foreground" />
                  </CardContent>
                </Card>
              ) : integrations.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <p className="text-muted-foreground mb-2">No integrations connected</p>
                    <Button
                      variant="outline"
                      onClick={() => setLocation('/integrations')}
                    >
                      Add Integration
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {integrations.map((integration: any) => (
                    <Card key={integration.id}>
                      <CardContent className="p-4 flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="bg-primary/10 rounded-full p-2 mr-3">
                            <RefreshCwIcon className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-medium">{integration.name}</h4>
                            <div className="flex items-center text-sm text-muted-foreground">
                              {getIntegrationName(integration.type)}
                              <Badge variant={
                                integration.status === 'connected' || integration.status === 'configured' || integration.status === 'syncing' 
                                  ? 'outline' 
                                  : 'destructive'
                              } className="ml-2">
                                {integration.status}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        {(integration.status === 'connected' || integration.status === 'configured') && (
                          <Button 
                            variant="outline"
                            onClick={() => syncMutation.mutate(integration.id)}
                            disabled={syncingIntegrationId === integration.id || integration.status === 'syncing'}
                          >
                            {syncingIntegrationId === integration.id || integration.status === 'syncing' ? (
                              <>
                                <Loader2Icon className="h-4 w-4 mr-2 animate-spin" />
                                Syncing…
                              </>
                            ) : (
                              <>
                                <RefreshCwIcon className="h-4 w-4 mr-2" />
                                Sync Now
                              </>
                            )}
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setLocation("/integrations")}>
                <CardContent className="p-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <div>
                    <h4 className="font-medium">Add Integration</h4>
                    <p className="text-sm text-gray-500">Connect a new platform</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setLocation("/schedules")}>
                <CardContent className="p-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h4 className="font-medium">Manage Schedules</h4>
                    <p className="text-sm text-gray-500">Configure sync timing</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setLocation("/settings")}>
                <CardContent className="p-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <div>
                    <h4 className="font-medium">Settings</h4>
                    <p className="text-sm text-gray-500">Configure application</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      )}
      
      {activeTab === "Activity" && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Integration Activity</h3>
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {isLoadingLogs ? (
                    <div className="p-4 text-center text-sm text-gray-500">Loading activity...</div>
                  ) : syncLogs.length > 0 ? (
                    syncLogs.map((log: any) => (
                      <div key={log.id} className="flex items-center justify-between p-4">
                        <div>
                          <div className="flex items-center">
                            <span className={`h-2 w-2 rounded-full mr-2 ${
                              log.status === 'success' ? 'bg-green-500' : 
                              log.status === 'partial' ? 'bg-yellow-500' : 'bg-red-500'
                            }`}></span>
                            <span className="font-medium">
                              {getIntegrationName(integrations.find((i: any) => i.id === log.integrationId)?.type || '')} Sync
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">
                            {log.itemsSuccess} of {log.itemsProcessed} items processed successfully
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{formatDate(log.startTime)}</p>
                          <p className="text-xs text-gray-500">{formatRelativeTime(log.startTime)}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-sm text-gray-500">No activity found</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Integration Status</h3>
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {isLoadingIntegrations ? (
                    <div className="p-4 text-center text-sm text-gray-500">Loading integrations...</div>
                  ) : integrations.length > 0 ? (
                    integrations.map((integration: any) => (
                      <div key={integration.id} className="flex items-center justify-between p-4">
                        <div className="flex items-center">
                          <span className={`h-2 w-2 rounded-full mr-2 ${getStatusBadgeColor(integration.status)}`}></span>
                          <div>
                            <p className="font-medium">{integration.name}</p>
                            <p className="text-sm text-gray-500">{getIntegrationName(integration.type)}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">Last sync: {formatDate(integration.lastSyncAt)}</p>
                          <p className="text-xs text-gray-500">Next sync: {formatDate(integration.nextSyncAt)}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-sm text-gray-500">No integrations found</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </AppLayout>
  );
}
