# 🛠️ Modular Service Layer

This directory contains the new modular service system that breaks down large service files into focused, maintainable modules. The system is designed for better team collaboration, testing, and feature isolation.

## 📁 Directory Structure

```
server/services/
├── base/
│   ├── service.interface.ts   # Base service interface and abstract class
│   └── index.ts              # Base exports
├── google/
│   ├── oauth.service.ts      # Google OAuth authentication
│   ├── drive.service.ts      # Google Drive operations
│   ├── content.service.ts    # Content extraction from Google files
│   ├── google.facade.ts      # Facade for backward compatibility
│   └── index.ts             # Google service exports
├── rag/
│   ├── chat.service.ts       # Chat management
│   ├── ai-response.service.ts # AI responses
│   ├── context.service.ts    # Context building
│   ├── batch.service.ts      # Batch processing
│   ├── rag.facade.ts         # RAG facade
│   └── index.ts             # RAG exports
├── microsoft/
│   ├── oauth.service.ts      # Microsoft OAuth authentication
│   ├── teams.service.ts      # Teams operations
│   ├── onedrive.service.ts   # OneDrive operations
│   ├── sharepoint.service.ts # SharePoint operations
│   ├── content.service.ts    # Content extraction
│   ├── microsoft.facade.ts   # Microsoft facade
│   └── index.ts             # Microsoft exports
├── notion/
│   ├── auth.service.ts       # Notion authentication (300 lines)
│   ├── database.service.ts   # Database operations (300 lines)
│   ├── page.service.ts       # Page operations (300 lines)
│   ├── content.service.ts    # Content management (300 lines)
│   ├── notion.facade.ts      # Notion facade (300 lines)
│   └── index.ts             # Notion exports
├── file-upload/
│   ├── config.service.ts     # Upload configuration (300 lines)
│   ├── processing.service.ts # File processing (300 lines)
│   ├── content.service.ts    # Content extraction (300 lines)
│   ├── management.service.ts # File management (300 lines)
│   ├── file-upload.facade.ts # File upload facade (300 lines)
│   └── index.ts             # File upload exports
├── google-service.ts         # Original service (deprecated, kept for compatibility)
├── rag-service.ts           # Original service (deprecated, kept for compatibility)
├── microsoft-service.ts     # Original service (deprecated, kept for compatibility)
├── notion-service.ts        # Original (deprecated, kept for compatibility)
├── file-upload-service.ts   # Original (deprecated, kept for compatibility)
└── README.md               # This documentation
```

## 🎯 Key Benefits

### **1. Modular Architecture**
- **Single Responsibility**: Each service class handles one specific domain
- **Feature Isolation**: Teams can work on different service features independently
- **Reduced Complexity**: Smaller, focused files are easier to understand and maintain

### **2. Backward Compatibility**
- **Facade Pattern**: Service facades maintain the original interfaces
- **Drop-in Replacement**: Existing code continues to work without changes
- **Gradual Migration**: Teams can migrate to direct module usage over time

### **3. Better Testing**
- **Unit Testing**: Each service module can be tested independently
- **Mocking**: Easy to mock individual service components
- **Isolation**: Test failures are isolated to specific domains

### **4. Improved Maintainability**
- **Clear Boundaries**: Each file has a clear purpose and scope
- **Easy Navigation**: Developers can quickly find relevant service code
- **Consistent Patterns**: All services follow the same base structure

## 🏗️ Base Service Architecture

### **BaseService Class**
All services extend the `BaseService` abstract class which provides:

- **Lifecycle Management**: `initialize()`, `cleanup()` methods
- **Health Monitoring**: `getHealthStatus()` with custom health checks
- **Error Handling**: Consistent error handling patterns
- **Logging**: Structured logging with service context
- **Validation**: Input validation utilities
- **Retry Logic**: Built-in retry mechanisms for operations

### **Service Interface**
```typescript
interface IBaseService {
  initialize?(): Promise<void>;
  getHealthStatus?(): Promise<HealthStatus>;
  getServiceInfo?(): ServiceInfo;
  cleanup?(): Promise<void>;
}
```

## 🔧 Service Modularization Progress

### **✅ Google Service Modularization (Completed)**

#### **Before: Monolithic Structure**
- **`google-service.ts`**: 1,938 lines with mixed responsibilities
- OAuth, Drive operations, content extraction all in one file
- Difficult to test, maintain, and extend

#### **After: Modular Structure**

#### **`GoogleOAuthService`** (300 lines)
- OAuth2 authentication flow
- Token management and refresh
- Credential encryption/decryption
- Scope management

#### **`GoogleDriveService`** (300 lines)
- File listing and filtering
- Folder operations
- Drive structure navigation
- File type validation

#### **`GoogleContentService`** (300 lines)
- Content extraction from Google Docs
- Google Sheets data extraction
- Google Slides content parsing
- Metadata extraction

#### **`GoogleServiceFacade`** (200 lines)
- Backward compatibility layer
- Unified interface to all Google services
- Service orchestration

### **✅ RAG Service Modularization (Completed)**

#### **Before: Monolithic Structure**
- **`rag-service.ts`**: 669 lines with mixed AI responsibilities
- Chat management, AI responses, context building, batch processing all in one file
- Complex interdependencies and difficult to test individual components

#### **After: Modular Structure**

#### **`RAGChatService`** (300 lines)
- Chat session management
- Message storage and retrieval
- Session statistics and cleanup
- Chat history for context

#### **`RAGAIResponseService`** (300 lines)
- OpenAI integration and response generation
- Function calling and agentic actions
- Model management and configuration
- Token estimation and limits

#### **`RAGContextService`** (300 lines)
- Context building from relevant chunks
- Source management and validation
- File search by description
- Context truncation and optimization

#### **`RAGBatchService`** (300 lines)
- Batch processing for embeddings
- Smart processing decisions (real-time vs batch)
- Cost optimization (50% savings with batch API)
- Pending file processing

#### **`RAGServiceFacade`** (300 lines)
- Backward compatibility layer
- Unified interface to all RAG services
- Service orchestration and health monitoring

### **✅ Microsoft Service Modularization (Completed)**

#### **Before: Monolithic Structure**
- **`microsoft-service.ts`**: 1,214 lines with mixed enterprise responsibilities
- OAuth, Teams, OneDrive, SharePoint, content extraction all in one file
- Complex enterprise authentication and multiple API endpoints

#### **After: Modular Structure**

#### **`MicrosoftOAuthService`** (300 lines)
- Microsoft OAuth authentication and token management
- Graph client creation and management
- Token refresh and validation
- Enterprise authentication patterns

#### **`MicrosoftTeamsService`** (300 lines)
- Teams operations and channel management
- Teams file sync and discovery
- Chat history and message retrieval
- Teams folder structure for selection

#### **`MicrosoftOneDriveService`** (300 lines)
- OneDrive operations and file management
- Personal and business OneDrive sync
- File search and folder structure
- OneDrive content download

#### **`MicrosoftSharePointService`** (300 lines)
- SharePoint site operations and management
- Document library sync and discovery
- SharePoint search and file operations
- Site structure and permissions

#### **`MicrosoftContentService`** (300 lines)
- Content extraction from Microsoft files
- Meeting metadata enhancement
- File type detection and processing
- PDF and Word document text extraction

#### **`MicrosoftServiceFacade`** (300 lines)
- Backward compatibility layer
- Unified interface to all Microsoft services
- Enterprise service orchestration and health monitoring

### **✅ Notion Service Modularization (Completed)**

#### **Before: Monolithic Structure**
- **`notion-service.ts`**: 729 lines with mixed productivity responsibilities
- Authentication, database operations, page management, content chunking all in one file
- Complex block-based content model and sub-page management

#### **After: Modular Structure**

#### **`NotionAuthService`** (300 lines)
- Notion API authentication and client management
- API key validation and encrypted key support
- Connection testing and user information retrieval
- Authentication state management

#### **`NotionDatabaseService`** (300 lines)
- Database operations and management
- Meeting Transcripts database creation and discovery
- Database querying and pagination
- Schema validation and structure management

#### **`NotionPageService`** (300 lines)
- Page operations and management
- Page creation, retrieval, and updates
- Block management and content operations
- Page search and metadata extraction

#### **`NotionContentService`** (300 lines)
- Content creation, chunking, and management
- Intelligent text chunking with natural boundaries
- Sub-page creation for large content
- Meeting transcript and notes formatting

#### **`NotionServiceFacade`** (300 lines)
- Backward compatibility layer
- Unified interface to all Notion services
- Workspace setup and validation

### **✅ File Upload Service Modularization (Completed)**

#### **Before: Monolithic Structure**
- **`file-upload-service.ts`**: 310 lines with mixed file handling responsibilities
- Multer configuration, file processing, content extraction, file management all in one file
- Complex file lifecycle management and cleanup operations

#### **After: Modular Structure**

#### **`FileUploadConfigService`** (300 lines)
- Multer configuration and middleware setup
- File validation and type checking
- Upload directory management and file naming
- Supported file types and size limits

#### **`FileUploadProcessingService`** (300 lines)
- File processing and database storage
- Multiple file handling and batch operations
- Embedding generation integration
- File validation and error handling

#### **`FileUploadContentService`** (300 lines)
- Text extraction from uploaded files
- PDF and Word document processing
- Content quality estimation
- Fallback text generation for unsupported types

#### **`FileUploadManagementService`** (300 lines)
- File lifecycle management and cleanup
- Orphaned file detection and removal
- Old file cleanup and maintenance
- File repair and statistics

#### **`FileUploadServiceFacade`** (300 lines)
- Backward compatibility layer
- Unified interface to all file upload services
- Comprehensive file processing workflows

## 🚀 Usage Examples

### **Using the Facade (Recommended for existing code)**
```typescript
import { googleService } from './services/google-service';

// All existing code continues to work
const authUrl = googleService.getAuthUrl(redirectUri, integrationId);
const files = await googleService.listFiles(auth, folderId);
```

### **Using Individual Modules (Recommended for new code)**
```typescript
import { GoogleOAuthService, GoogleDriveService } from './services/google';

const oauthService = new GoogleOAuthService();
const driveService = new GoogleDriveService();

await oauthService.initialize();
await driveService.initialize();

const authUrl = oauthService.getAuthUrl(redirectUri, integrationId);
const files = await driveService.listFiles(auth, folderId);
```

### **Service Health Monitoring**
```typescript
import { googleService } from './services/google';

const health = await googleService.getHealthStatus();
console.log('Google services health:', health);
```

## 🧪 Testing

### **Unit Testing Individual Modules**
```typescript
import { GoogleOAuthService } from './services/google';

describe('GoogleOAuthService', () => {
  let service: GoogleOAuthService;

  beforeEach(async () => {
    service = new GoogleOAuthService();
    await service.initialize();
  });

  afterEach(async () => {
    await service.cleanup();
  });

  it('should generate valid auth URLs', () => {
    const result = service.getAuthUrl('http://test.com', 123);
    expect(result.url).toContain('oauth2');
    expect(result.state).toContain('123');
  });
});
```

### **Integration Testing with Facade**
```typescript
import { GoogleServiceFacade } from './services/google';

describe('GoogleServiceFacade', () => {
  let service: GoogleServiceFacade;

  beforeEach(async () => {
    service = new GoogleServiceFacade();
    await service.initialize();
  });

  it('should maintain backward compatibility', async () => {
    // Test that all original methods work
    const authUrl = service.getAuthUrl('http://test.com');
    expect(authUrl).toBeDefined();
  });
});
```

## 📊 Performance Benefits

### **Memory Usage**
- **Reduced Memory Footprint**: Only load needed service modules
- **Lazy Loading**: Services can be loaded on-demand
- **Resource Cleanup**: Proper cleanup prevents memory leaks

### **Development Speed**
- **Faster Compilation**: Smaller files compile faster
- **Parallel Development**: Teams can work on different services simultaneously
- **Easier Debugging**: Issues are isolated to specific service modules

### **Testing Speed**
- **Focused Tests**: Test only the specific functionality being changed
- **Faster Test Runs**: Smaller test suites run faster
- **Better Coverage**: Easier to achieve comprehensive test coverage

## 🔄 Migration Strategy

### **Phase 1: Google Service (Completed)**
- ✅ Broke down 1,938-line `google-service.ts` into 4 focused modules
- ✅ Maintained backward compatibility with facade pattern
- ✅ All existing code continues to work without changes

### **Phase 2: RAG Service (Completed)**
- ✅ Broke down 669-line `rag-service.ts` into 5 focused modules:
  - ✅ `RAGChatService` - Chat session and message management
  - ✅ `RAGAIResponseService` - AI response generation with OpenAI
  - ✅ `RAGContextService` - Context building and source management
  - ✅ `RAGBatchService` - Batch processing and cost optimization
  - ✅ `RAGServiceFacade` - Unified interface for backward compatibility

### **Phase 3: Microsoft Service (Completed)**
- ✅ Broke down 1,214-line `microsoft-service.ts` into 6 focused modules:
  - ✅ `MicrosoftOAuthService` - Microsoft OAuth authentication and token management
  - ✅ `MicrosoftTeamsService` - Teams operations and channel management
  - ✅ `MicrosoftOneDriveService` - OneDrive operations and file management
  - ✅ `MicrosoftSharePointService` - SharePoint site operations and management
  - ✅ `MicrosoftContentService` - Content extraction from Microsoft files
  - ✅ `MicrosoftServiceFacade` - Unified interface for backward compatibility

### **Phase 4: Notion Service (Completed)**
- ✅ Broke down 729-line `notion-service.ts` into 5 focused modules:
  - ✅ `NotionAuthService` - Authentication and client management
  - ✅ `NotionDatabaseService` - Database operations and management
  - ✅ `NotionPageService` - Page operations and management
  - ✅ `NotionContentService` - Content creation, chunking, and management
  - ✅ `NotionServiceFacade` - Unified interface for backward compatibility

### **Phase 5: File Upload Service (Completed)**
- ✅ Broke down 310-line `file-upload-service.ts` into 5 focused modules:
  - ✅ `FileUploadConfigService` - Multer configuration and file validation
  - ✅ `FileUploadProcessingService` - File processing and database storage
  - ✅ `FileUploadContentService` - Text extraction from uploaded files
  - ✅ `FileUploadManagementService` - File lifecycle management and cleanup
  - ✅ `FileUploadServiceFacade` - Unified interface for backward compatibility

### **Phase 6: Future Services**
- Break down `notion-service.ts` into focused modules:
  - `NotionAuthService` - Notion authentication
  - `NotionDatabaseService` - Database operations
  - `NotionPageService` - Page creation and updates

## 🛠️ Development Guidelines

### **Creating New Service Modules**
1. Extend `BaseService` for consistent patterns
2. Implement proper initialization and cleanup
3. Add comprehensive error handling
4. Include health checks
5. Write unit tests for all methods

### **Service Dependencies**
- Use dependency injection where possible
- Declare dependencies in `getDependencies()`
- Initialize dependencies in `onInitialize()`
- Clean up dependencies in `onCleanup()`

### **Error Handling**
- Use `this.handleError()` for consistent error handling
- Log errors with context using `this.log()`
- Validate inputs using built-in validation methods
- Implement retry logic for transient failures

### **Best Practices**
- Keep service classes focused on their domain
- Use consistent naming conventions
- Document all public methods
- Provide comprehensive health checks
- Write tests for all functionality

## 🔍 Debugging

### **Service Health Monitoring**
```typescript
// Check individual service health
const oauthHealth = await googleService.getOAuthService().getHealthStatus();

// Check facade health (includes all sub-services)
const overallHealth = await googleService.getHealthStatus();
```

### **Service Information**
```typescript
// Get service information
const info = googleService.getServiceInfo();
console.log('Service info:', info);

// Get sub-service information
const subServices = info.subServices;
console.log('OAuth service:', subServices.oauth);
```

### **Logging**
All services use structured logging with consistent formatting:
```
[2025-06-05T16:26:38.437Z] [GOOGLEOAUTH] 🚀 Initializing GoogleOAuth service...
[2025-06-05T16:26:38.440Z] [GOOGLEOAUTH] ✅ GoogleOAuth service initialized
```

This modular service system provides a solid foundation for scalable, maintainable service architecture while preserving backward compatibility and enabling future growth.
