# Database Setup Guide for MeetSync with RAG Integration

## Prerequisites

1. **PostgreSQL 12+** installed locally
2. **pgvector extension** for vector operations
3. **Node.js 18+** and npm

## Step 1: Install PostgreSQL and pgvector

### On Windows:
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Install pgvector:
   ```bash
   # Using pre-built binaries (recommended)
   # Download from: https://github.com/pgvector/pgvector/releases
   # Or use WSL/Docker for easier setup
   ```

### On macOS:
```bash
# Install PostgreSQL
brew install postgresql

# Install pgvector
brew install pgvector
```

### On Linux (Ubuntu/Debian):
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Install pgvector
sudo apt install postgresql-14-pgvector
```

## Step 2: Create Database

1. **Start PostgreSQL service**:
   ```bash
   # Windows (if installed as service)
   net start postgresql-x64-14

   # macOS
   brew services start postgresql

   # Linux
   sudo systemctl start postgresql
   ```

2. **Connect to PostgreSQL**:
   ```bash
   # Connect as postgres user
   psql -U postgres
   ```

3. **Create database and user**:
   ```sql
   -- Create database
   CREATE DATABASE meetsync_db;

   -- Create user (optional, for security)
   CREATE USER meetsync_user WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE meetsync_db TO meetsync_user;

   -- Connect to the new database
   \c meetsync_db;

   -- Enable pgvector extension
   CREATE EXTENSION IF NOT EXISTS vector;

   -- Verify installation
   SELECT * FROM pg_extension WHERE extname = 'vector';
   ```

## Step 3: Update Environment Variables

Update your `.env` file with the correct database connection:

```env
# For default postgres user
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/meetsync_db

# Or for custom user
DATABASE_URL=postgresql://meetsync_user:your_secure_password@localhost:5432/meetsync_db
```

## Step 4: Run Migrations

1. **Install dependencies** (if not already done):
   ```bash
   npm install
   ```

2. **Run the initial migration**:
   ```bash
   # Apply existing migration
   npm run db:push
   ```

3. **Apply RAG tables migration**:
   ```bash
   # Connect to your database and run:
   psql -U postgres -d meetsync_db -f migrations/0001_add_rag_tables.sql
   ```

## Step 5: Verify Setup

1. **Check tables were created**:
   ```sql
   \c meetsync_db;
   \dt
   ```

   You should see these tables:
   - `users`
   - `integrations`
   - `sync_logs`
   - `sync_items`
   - `files`
   - `file_chunks` (new)
   - `chat_sessions` (new)
   - `chat_messages` (new)
   - `projects` (new)

2. **Test vector operations**:
   ```sql
   -- Test vector functionality
   SELECT '[1,2,3]'::vector;
   ```

## Step 6: Start the Application

```bash
npm run dev
```

The application should now start with full RAG functionality!

## Troubleshooting

### pgvector not found
- Make sure pgvector is properly installed for your PostgreSQL version
- Check PostgreSQL logs for extension loading errors

### Connection refused
- Verify PostgreSQL is running: `pg_isready`
- Check if the port (5432) is correct
- Verify username/password in DATABASE_URL

### Migration errors
- Make sure you're connected to the correct database
- Check if pgvector extension is enabled
- Verify PostgreSQL user has necessary permissions

### Development Mode (No Database)
If you want to run without PostgreSQL for development:
1. Comment out or remove `DATABASE_URL` from `.env`
2. The app will use in-memory storage (MemStorage)
3. RAG features will work but data won't persist

## Production Considerations

1. **Use connection pooling** for better performance
2. **Set up proper indexes** for vector operations
3. **Configure backup strategy** for your database
4. **Use environment-specific credentials**
5. **Monitor vector index performance**
