import json
from notion_client import Client
from metadata_utils import extract_metadata
from llm_utils import get_meeting_name, get_attendees_csv
from google_drive_utils import extract_summary_paragraph

def get_database_structure(notion, database_id):
    db = notion.databases.retrieve(database_id)
    return db['properties']

def upload_to_notion(title, transcript_text, notes_text, NOTION_TOKEN, DATABASE_ID, notes_structural_elements=None):
    notion = Client(auth=NOTION_TOKEN)
    print(f"\nContent lengths:")
    print(f"Title ({len(title)} chars):", title[:100])
    print(f"Transcript ({len(transcript_text)} chars):", transcript_text[:100])
    print(f"Notes ({len(notes_text)} chars):", notes_text[:100])

    # Extract metadata from filename (for date/time only)
    metadata = extract_metadata(title)

    # Extract summary paragraph from Notes tab structural elements
    summary_para = ''
    if notes_structural_elements:
        summary_para = extract_summary_paragraph(notes_structural_elements)
    if not summary_para:
        summary_para = notes_text[:300]  # fallback: first 300 chars

    # Use LLM to get meeting name and attendees
    try:
        meeting_name = get_meeting_name(summary_para)
        attendees = get_attendees_csv(summary_para)
    except Exception as e:
        print(f"LLM extraction failed: {e}")
        meeting_name = metadata['meeting_name']
        attendees = 'Not specified'

    # Create blocks for the page content
    blocks = [
        {
            'object': 'block',
            'type': 'heading_1',
            'heading_1': {
                'rich_text': [{'type': 'text', 'text': {'content': meeting_name}}]
            }
        },
        {
            'object': 'block',
            'type': 'paragraph',
            'paragraph': {
                'rich_text': [
                    {'type': 'text', 'text': {'content': '📅 ' + metadata['date']}},
                    {'type': 'text', 'text': {'content': ' | '}},
                    {'type': 'text', 'text': {'content': '🕒 ' + metadata['time']}},
                    {'type': 'text', 'text': {'content': ' | '}},
                    {'type': 'text', 'text': {'content': '👥 ' + attendees}}
                ]
            }
        },
        {
            'object': 'block',
            'type': 'divider',
            'divider': {}
        },
        {
            'object': 'block',
            'type': 'heading_2',
            'heading_2': {
                'rich_text': [{'type': 'text', 'text': {'content': 'Notes'}}]
            }
        }
    ]
    for chunk in [notes_text[i:i+2000] for i in range(0, len(notes_text), 2000)]:
        blocks.append({
            'object': 'block',
            'type': 'paragraph',
            'paragraph': {
                'rich_text': [{'type': 'text', 'text': {'content': chunk}}]
            }
        })
    blocks.extend([
        {
            'object': 'block',
            'type': 'heading_2',
            'heading_2': {
                'rich_text': [{'type': 'text', 'text': {'content': 'Transcript'}}]
            }
        }
    ])
    for chunk in [transcript_text[i:i+2000] for i in range(0, len(transcript_text), 2000)]:
        blocks.append({
            'object': 'block',
            'type': 'paragraph',
            'paragraph': {
                'rich_text': [{'type': 'text', 'text': {'content': chunk}}]
            }
        })
    properties = {
        'Name': {
            'title': [{'type': 'text', 'text': {'content': meeting_name}}]
        },
        'Date': {
            'date': {
                'start': metadata['date_iso']
            }
        },
        'Time': {
            'rich_text': [{'type': 'text', 'text': {'content': metadata['time']}}]
        },
        'Attendees': {
            'rich_text': [{'type': 'text', 'text': {'content': attendees}}]
        }
    }
    print("\nCreating Notion page with full content and metadata...")
    page = notion.pages.create(
        parent={'database_id': DATABASE_ID},
        properties=properties,
        children=blocks
    )
    print(f"✅ Created page: {meeting_name}")
    return page 