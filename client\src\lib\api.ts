import { apiRequest } from "./queryClient";

// Integration API functions
export async function getIntegrations() {
  const res = await apiRequest("GET", "/api/integrations");
  return res.json();
}

export async function getIntegration(id: number) {
  const res = await apiRequest("GET", `/api/integrations/${id}`);
  return res.json();
}

export async function createIntegration(data: any) {
  const res = await apiRequest("POST", "/api/integrations", data);
  return res.json();
}

export async function updateIntegration(id: number, data: any) {
  const res = await apiRequest("PUT", `/api/integrations/${id}`, data);
  return res.json();
}

export async function deleteIntegration(id: number) {
  const res = await apiRequest("DELETE", `/api/integrations/${id}`);
  return res.json();
}

export async function getAuthUrl(id: number, redirectUri: string) {
  const res = await apiRequest("POST", `/api/integrations/${id}/auth-url`, { redirectUri });
  return res.json();
}

export async function testConnection(id: number) {
  const res = await apiRequest("POST", `/api/integrations/${id}/test-connection`);
  return res.json();
}

// Schedule API functions
export async function updateSchedule(integrationId: number, schedule: string, enabled: boolean) {
  const res = await apiRequest("POST", "/api/schedules", { integrationId, schedule, enabled });
  return res.json();
}

// Sync API functions
export async function getSyncLogs(params: { integrationId?: number, limit?: number } = {}) {
  const queryParams = new URLSearchParams();
  if (params.integrationId) queryParams.append("integrationId", params.integrationId.toString());
  if (params.limit) queryParams.append("limit", params.limit.toString());
  
  const res = await apiRequest("GET", `/api/sync-logs?${queryParams.toString()}`);
  return res.json();
}

export async function getSyncLog(id: number) {
  const res = await apiRequest("GET", `/api/sync-logs/${id}`);
  return res.json();
}

export async function startSync(integrationId: number) {
  const res = await apiRequest("POST", "/api/sync-now", { integrationId });
  return res.json();
}

export async function getSyncItems(params: { syncLogId?: number, status?: string } = {}) {
  const queryParams = new URLSearchParams();
  if (params.syncLogId) queryParams.append("syncLogId", params.syncLogId.toString());
  if (params.status) queryParams.append("status", params.status);
  
  const res = await apiRequest("GET", `/api/sync-items?${queryParams.toString()}`);
  return res.json();
}

// Files API functions
export async function getFiles(params: { platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string } = {}) {
  const queryParams = new URLSearchParams();
  if (params.platform) queryParams.append("platform", params.platform);
  if (params.userId) queryParams.append("userId", params.userId);
  if (params.limit) queryParams.append("limit", params.limit.toString());
  if (params.offset) queryParams.append("offset", params.offset.toString());
  if (params.folderId) queryParams.append("folderId", params.folderId);
  
  const res = await apiRequest("GET", `/api/files?${queryParams.toString()}`);
  return res.json();
}

export async function getFile(id: number) {
  const res = await apiRequest("GET", `/api/files/${id}`);
  return res.json();
}

export async function searchFiles(params: { query: string, platform?: string, fileType?: string, folderId?: string } = { query: "" }) {
  const queryParams = new URLSearchParams();
  queryParams.append("q", params.query);
  if (params.platform) queryParams.append("platform", params.platform);
  if (params.fileType) queryParams.append("fileType", params.fileType);
  if (params.folderId) queryParams.append("folderId", params.folderId);
  
  const res = await apiRequest("GET", `/api/files/search?${queryParams.toString()}`);
  return res.json();
}

export async function deleteFile(id: number) {
  const res = await apiRequest("DELETE", `/api/files/${id}`);
  return res.json();
}

// Google Drive API functions
export async function getGoogleDriveFolders(id: number) {
  const res = await apiRequest("GET", `/api/integrations/${id}/folders`);
  return res.json();
}

// Microsoft Teams API functions
export async function getTeamsFolders(id: number) {
  const res = await apiRequest("GET", `/api/integrations/${id}/teams-folders`);
  return res.json();
}

export async function getTeamsAuthUrl(id: number, redirectUri?: string) {
  const url = redirectUri 
    ? `/api/integrations/${id}/teams-auth-url?redirectUri=${encodeURIComponent(redirectUri)}`
    : `/api/integrations/${id}/teams-auth-url`;
  const res = await apiRequest("GET", url);
  return res.json();
}
