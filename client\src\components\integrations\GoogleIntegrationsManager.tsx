import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { useToast } from '../../hooks/use-toast';
import { ConnectedAccountsStatus } from './ConnectedAccountsStatus';
import { GoogleDriveCard } from './GoogleDriveCard';
import { GoogleCalendarCard } from './GoogleCalendarCard';
import { GmailCard } from './GmailCard';
import { 
  InfoIcon,
  PlusIcon,
  CheckCircleIcon,
  AlertCircleIcon
} from 'lucide-react';

interface GoogleService {
  type: string;
  name: string;
  description: string;
  integrationId?: number;
  isConnected: boolean;
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
}

interface GoogleAccount {
  email: string;
  displayName?: string;
  isConnected: boolean;
  connectedAt?: string;
  services: GoogleService[];
}

interface GoogleIntegrationsManagerProps {
  className?: string;
}

export function GoogleIntegrationsManager({ className }: GoogleIntegrationsManagerProps) {
  const [googleAccount, setGoogleAccount] = useState<GoogleAccount | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadGoogleIntegrations();
  }, []);

  const loadGoogleIntegrations = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/integrations/google/status');
      if (response.ok) {
        const data = await response.json();
        setGoogleAccount(data.account);
      }
    } catch (error) {
      console.error('Error loading Google integrations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const connectGoogleAccount = async () => {
    setIsConnecting(true);
    try {
      // Create a temporary integration to get OAuth URL
      const createResponse = await fetch('/api/integrations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'google-drive', // We'll use drive as the base, but it will have all scopes
          name: 'Google Account',
          status: 'disconnected',
        }),
      });

      if (createResponse.ok) {
        const createData = await createResponse.json();
        const integrationId = createData.integration.id;

        // Get OAuth URL
        const authResponse = await fetch(`/api/integrations/${integrationId}/auth-url`);
        if (authResponse.ok) {
          const authData = await authResponse.json();
          // Redirect to Google OAuth
          window.location.href = authData.url;
        }
      }
    } catch (error) {
      toast({
        title: "Connection Failed",
        description: "Failed to connect Google account",
        variant: "destructive",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const addGoogleService = async (serviceType: string, serviceName: string) => {
    try {
      const response = await fetch('/api/integrations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: serviceType,
          name: serviceName,
          status: googleAccount?.isConnected ? 'connected' : 'disconnected',
          // If Google account is already connected, use existing credentials
          useExistingGoogleAuth: googleAccount?.isConnected,
        }),
      });

      if (response.ok) {
        toast({
          title: "Service Added",
          description: `${serviceName} has been added to your integrations`,
        });
        await loadGoogleIntegrations(); // Refresh the data
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to add ${serviceName}`,
        variant: "destructive",
      });
    }
  };

  const handleServiceStatusChange = async (serviceType: string, connected: boolean) => {
    // Refresh the integrations data when service status changes
    await loadGoogleIntegrations();
  };

  const availableServices = [
    { type: 'google_drive', name: 'Google Drive', description: 'Sync and search your Google Drive files' },
    { type: 'gmail', name: 'Gmail', description: 'Sync and search your Gmail emails' },
    { type: 'google_calendar', name: 'Google Calendar', description: 'Sync and search your Google Calendar events' },
  ];

  const getConnectedServices = () => {
    return googleAccount?.services?.filter(s => s.isConnected) || [];
  };

  const getAvailableToAdd = () => {
    const connectedTypes = googleAccount?.services?.map(s => s.type) || [];
    return availableServices.filter(service => !connectedTypes.includes(service.type));
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="h-32 bg-gray-200 rounded-lg animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-64 bg-gray-200 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  if (!googleAccount?.isConnected) {
    return (
      <div className={`space-y-6 ${className}`}>
        <ConnectedAccountsStatus />
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-red-100 rounded-lg flex items-center justify-center">
                <div className="h-4 w-4 bg-red-500 rounded-full" />
              </div>
              <span>Google Integrations</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                Connect your Google account once to access Gmail, Google Drive, Google Calendar, and other Google services.
                You'll only need to authenticate once for all Google integrations.
              </AlertDescription>
            </Alert>
            
            <div className="text-center py-8">
              <h3 className="text-lg font-medium mb-2">Connect Your Google Account</h3>
              <p className="text-gray-600 mb-4">
                One connection gives you access to all Google services
              </p>
              <Button 
                onClick={connectGoogleAccount}
                disabled={isConnecting}
                size="lg"
                className="mb-4"
              >
                {isConnecting ? "Connecting..." : "Connect Google Account"}
              </Button>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                {availableServices.map((service) => (
                  <div key={service.type} className="border rounded-lg p-4 bg-gray-50">
                    <h4 className="font-medium">{service.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <ConnectedAccountsStatus />
      
      {/* Account Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-red-100 rounded-lg flex items-center justify-center">
                <div className="h-4 w-4 bg-red-500 rounded-full" />
              </div>
              <span>Google Account</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <CheckCircleIcon className="h-3 w-3 mr-1" />
                Connected
              </Badge>
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{googleAccount.displayName || googleAccount.email}</p>
              {googleAccount.displayName && (
                <p className="text-sm text-gray-500">{googleAccount.email}</p>
              )}
              {googleAccount.connectedAt && (
                <p className="text-xs text-gray-400">
                  Connected {new Date(googleAccount.connectedAt).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">
                {getConnectedServices().length} of {availableServices.length} services connected
              </p>
              <p className="text-xs text-gray-500">
                All services use the same authentication
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Connected Services */}
      {getConnectedServices().length > 0 && (
        <div>
          <h3 className="text-lg font-medium mb-4">Connected Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {googleAccount.services.map((service) => {
              if (!service.isConnected) return null;
              
              switch (service.type) {
                case 'google_drive':
                  return (
                    <GoogleDriveCard
                      key={service.type}
                      integrationId={service.integrationId!}
                      isConnected={service.isConnected}
                      onStatusChange={(connected) => handleServiceStatusChange(service.type, connected)}
                    />
                  );
                case 'gmail':
                  return (
                    <GmailCard
                      key={service.type}
                      integrationId={service.integrationId!}
                      isConnected={service.isConnected}
                      onStatusChange={(connected) => handleServiceStatusChange(service.type, connected)}
                    />
                  );
                case 'google_calendar':
                  return (
                    <GoogleCalendarCard
                      key={service.type}
                      integrationId={service.integrationId!}
                      isConnected={service.isConnected}
                      onStatusChange={(connected) => handleServiceStatusChange(service.type, connected)}
                    />
                  );
                default:
                  return null;
              }
            })}
          </div>
        </div>
      )}

      {/* Available to Add */}
      {getAvailableToAdd().length > 0 && (
        <div>
          <h3 className="text-lg font-medium mb-4">Available Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {getAvailableToAdd().map((service) => (
              <Card key={service.type} className="border-dashed">
                <CardContent className="pt-6">
                  <div className="text-center">
                    <h4 className="font-medium mb-2">{service.name}</h4>
                    <p className="text-sm text-gray-600 mb-4">{service.description}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addGoogleService(service.type, service.name)}
                      className="w-full"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add Service
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 