import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { formatDate } from "@/lib/utils/date";
import { 
  SearchIcon, 
  ExternalLinkIcon, 
  TrashIcon, 
  MailIcon, 
  FilterIcon,
  RefreshCwIcon,
  EyeIcon,
  StarIcon,
  ReplyIcon,
  ForwardIcon
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Email {
  id: number;
  externalId: string;
  platform: string;
  subject: string;
  content: string;
  sender: string;
  recipients: string[];
  cc?: string[];
  bcc?: string[];
  threadId?: string;
  status: string;
  metadata?: any;
  extractedMetadata?: any;
  userId?: string;
  organizationId?: string;
  syncItemId?: string;
  isRead: boolean;
  isStarred: boolean;
  isArchived: boolean;
  labels?: string[];
  createdAt: string;
  updatedAt: string;
  receivedAt: string;
}

interface ApiResponse {
  success: boolean;
  data: Email[];
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
  };
  error?: string;
}

// API functions
const getEmails = async ({ platform, userId, page = 1, pageSize = 100 }: any = {}) => {
  const params = new URLSearchParams();
  if (platform) params.append('platform', platform);
  if (userId) params.append('userId', userId);
  params.append('page', page.toString());
  params.append('pageSize', pageSize.toString());
  
  const response = await fetch(`/api/emails?${params}`);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const result: ApiResponse = await response.json();
  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch emails');
  }
  return { emails: result.data, pagination: result.pagination };
};

const searchEmails = async ({ query, platform, userId }: any) => {
  const response = await fetch('/api/emails/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ query, platform, userId }),
  });
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const result: ApiResponse = await response.json();
  if (!result.success) {
    throw new Error(result.error || 'Failed to search emails');
  }
  return { emails: result.data };
};

export default function EmailsPage() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  // State for filtering and search
  const [searchQuery, setSearchQuery] = useState("");
  const [platformFilter, setPlatformFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  
  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchQuery]);
  
  // Emails query - get all emails when no search query
  const { data: emailsData, isLoading: isLoadingEmails, error: emailsError, refetch } = useQuery({
    queryKey: ['/api/emails', { 
      platform: platformFilter === "all" ? undefined : platformFilter,
    }],
    queryFn: () => getEmails({ 
      platform: platformFilter === "all" ? undefined : platformFilter,
      pageSize: 1000,
    }),
    enabled: !debouncedSearchQuery,
    refetchInterval: 30000,
  });
  
  // Search query - triggered when there's a search term
  const { data: searchData, isLoading: isSearching, error: searchError } = useQuery({
    queryKey: ['/api/emails/search', { 
      query: debouncedSearchQuery, 
      platform: platformFilter === "all" ? undefined : platformFilter,
    }],
    queryFn: () => searchEmails({ 
      query: debouncedSearchQuery,
      platform: platformFilter === "all" ? undefined : platformFilter,
    }),
    enabled: !!debouncedSearchQuery && debouncedSearchQuery.length > 0,
  });
  
  // Get the appropriate data source based on search state
  const allEmails: Email[] = debouncedSearchQuery 
    ? (searchData?.emails || []) 
    : (emailsData?.emails || []);
  
  // Apply client-side filters
  const filteredEmails = React.useMemo(() => {
    let emails = allEmails;
    
    // Apply status filter
    if (statusFilter !== "all") {
      if (statusFilter === "unread") {
        emails = emails.filter(email => !email.isRead);
      } else if (statusFilter === "starred") {
        emails = emails.filter(email => email.isStarred);
      } else if (statusFilter === "archived") {
        emails = emails.filter(email => email.isArchived);
      }
    }
    
    return emails;
  }, [allEmails, statusFilter]);
  
  const totalCount = filteredEmails.length;
  const isLoading = debouncedSearchQuery ? isSearching : isLoadingEmails;
  const error = debouncedSearchQuery ? searchError : emailsError;
  
  // Get unique platforms for filter
  const uniquePlatforms = React.useMemo(() => {
    const platforms = new Set(allEmails.map(email => email.platform));
    return Array.from(platforms);
  }, [allEmails]);
  
  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
  };
  
  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
    setDebouncedSearchQuery("");
  };
  
  // Handle email click to open detail modal
  const handleEmailClick = (email: Email) => {
    setSelectedEmail(email);
    setIsDetailModalOpen(true);
  };
  
  // Get platform icon
  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'gmail':
        return (
          <div className="w-6 h-6 bg-red-500 rounded-sm flex items-center justify-center">
            <MailIcon className="h-3 w-3 text-white" />
          </div>
        );
      case 'outlook':
        return (
          <div className="w-6 h-6 bg-blue-500 rounded-sm flex items-center justify-center">
            <MailIcon className="h-3 w-3 text-white" />
          </div>
        );
      default:
        return (
          <div className="w-6 h-6 bg-gray-500 rounded-sm flex items-center justify-center">
            <MailIcon className="h-3 w-3 text-white" />
          </div>
        );
    }
  };
  
  // Get platform name
  const getPlatformName = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'gmail':
        return 'Gmail';
      case 'outlook':
        return 'Outlook';
      default:
        return platform.charAt(0).toUpperCase() + platform.slice(1);
    }
  };
  
  // Format recipients
  const formatRecipients = (recipients: string[]) => {
    if (recipients.length === 0) return '—';
    if (recipients.length === 1) return recipients[0];
    if (recipients.length <= 3) return recipients.join(', ');
    return `${recipients.slice(0, 2).join(', ')} +${recipients.length - 2} more`;
  };
  
  // Get email preview
  const getEmailPreview = (content: string) => {
    return content.replace(/\n/g, ' ').substring(0, 100) + (content.length > 100 ? '...' : '');
  };

  return (
    <AppLayout title="Emails">
      <div className="space-y-6">
        {/* Search and filters */}
        <Card>
          <CardHeader>
            <CardTitle>Email Management</CardTitle>
            <CardDescription>
              Search and manage emails from your integrated platforms
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search input */}
              <div className="flex-1 relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search emails by subject, sender, or content..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-9"
                />
              </div>
              
              {/* Platform filter */}
              <Select value={platformFilter} onValueChange={setPlatformFilter}>
                <SelectTrigger className="w-[180px]">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Platforms</SelectItem>
                  {uniquePlatforms.map(platform => (
                    <SelectItem key={platform} value={platform}>
                      {getPlatformName(platform)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Status filter */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="unread">Unread</SelectItem>
                  <SelectItem value="starred">Starred</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {debouncedSearchQuery && (
              <div className="mt-2 text-sm text-muted-foreground">
                {isSearching ? "Searching..." : `Found ${totalCount} email(s) matching "${debouncedSearchQuery}"`}
                <Button variant="link" className="h-auto p-0 ml-2" onClick={clearSearch}>
                  Clear search
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Emails table */}
        <Card>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCwIcon className="h-6 w-6 animate-spin mr-2" />
                <span>Loading emails...</span>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-8 text-red-600">
                <span>Error loading emails: {error.message}</span>
              </div>
            ) : filteredEmails.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                <MailIcon className="h-12 w-12 mb-4 text-muted-foreground/50" />
                <h3 className="text-lg font-medium mb-2">No emails found</h3>
                <p className="text-sm">
                  {debouncedSearchQuery || platformFilter !== "all" || statusFilter !== "all"
                    ? "Try adjusting your search criteria or filters" 
                    : "No emails have been synced yet. Connect an email integration and run a sync to see emails here."
                  }
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[400px]">Email</TableHead>
                    <TableHead>Platform</TableHead>
                    <TableHead>Sender</TableHead>
                    <TableHead>Recipients</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Received</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEmails.map((email) => (
                    <TableRow 
                      key={email.id} 
                      className={cn(
                        "hover:bg-muted/50 cursor-pointer",
                        email.status === "deleted" && "opacity-50 bg-muted/30",
                        !email.isRead && "font-medium"
                      )}
                      onClick={() => handleEmailClick(email)}
                    >
                      <TableCell>
                        <div className="flex items-start space-x-3">
                          <div className="flex items-center space-x-2 mt-1">
                            {!email.isRead && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                            {email.isStarred && (
                              <StarIcon className="h-4 w-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-foreground truncate">
                              {email.subject}
                            </p>
                            <p className="text-sm text-muted-foreground truncate">
                              {getEmailPreview(email.content)}
                            </p>
                            {email.labels && email.labels.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {email.labels.slice(0, 3).map((label, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {label}
                                  </Badge>
                                ))}
                                {email.labels.length > 3 && (
                                  <span className="text-xs text-muted-foreground">
                                    +{email.labels.length - 3} more
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getPlatformIcon(email.platform)}
                          <span className="text-sm text-muted-foreground">
                            {getPlatformName(email.platform)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {email.sender}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {formatRecipients(email.recipients)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Badge 
                            variant={email.isRead ? "secondary" : "default"}
                            className={email.isArchived ? "bg-gray-100 text-gray-800" : ""}
                          >
                            {email.isArchived ? "Archived" : email.isRead ? "Read" : "Unread"}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {formatDate(email.receivedAt)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                            <Button variant="ghost" size="sm">
                              •••
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              handleEmailClick(email);
                            }}>
                              <EyeIcon className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                              <ReplyIcon className="h-4 w-4 mr-2" />
                              Reply
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                              <ForwardIcon className="h-4 w-4 mr-2" />
                              Forward
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Email Detail Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          {selectedEmail && (
            <>
              <DialogHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <DialogTitle className="text-xl font-semibold text-left">
                      {selectedEmail.subject}
                    </DialogTitle>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        {getPlatformIcon(selectedEmail.platform)}
                        <span>{getPlatformName(selectedEmail.platform)}</span>
                      </div>
                      <span>•</span>
                      <span>From: {selectedEmail.sender}</span>
                      <span>•</span>
                      <span>{formatDate(selectedEmail.receivedAt)}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {selectedEmail.isStarred && (
                      <StarIcon className="h-5 w-5 text-yellow-500 fill-current" />
                    )}
                    <Badge variant={selectedEmail.isRead ? "secondary" : "default"}>
                      {selectedEmail.isRead ? "Read" : "Unread"}
                    </Badge>
                  </div>
                </div>
              </DialogHeader>
              
              <div className="space-y-4">
                {/* Email metadata */}
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">To: </span>
                    <span className="text-muted-foreground">
                      {selectedEmail.recipients.join(', ')}
                    </span>
                  </div>
                  {selectedEmail.cc && selectedEmail.cc.length > 0 && (
                    <div>
                      <span className="font-medium">CC: </span>
                      <span className="text-muted-foreground">
                        {selectedEmail.cc.join(', ')}
                      </span>
                    </div>
                  )}
                  {selectedEmail.bcc && selectedEmail.bcc.length > 0 && (
                    <div>
                      <span className="font-medium">BCC: </span>
                      <span className="text-muted-foreground">
                        {selectedEmail.bcc.join(', ')}
                      </span>
                    </div>
                  )}
                </div>
                
                {/* Labels */}
                {selectedEmail.labels && selectedEmail.labels.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {selectedEmail.labels.map((label, index) => (
                      <Badge key={index} variant="outline">
                        {label}
                      </Badge>
                    ))}
                  </div>
                )}
                
                {/* Email content */}
                <div className="border-t pt-4">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed">
                    {selectedEmail.content}
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
} 