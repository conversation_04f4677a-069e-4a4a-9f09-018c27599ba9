import { Request, Response } from 'express';
import { fileUploadService } from '../services/file-upload';
import { storage } from '../storage/index.js';

export class FileUploadController {
  
  /**
   * Handle multiple file uploads
   */
  async uploadFiles(req: Request, res: Response) {
    try {
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length === 0) {
        return res.status(400).json({
          message: 'No files uploaded',
          error: 'Please select at least one file to upload'
        });
      }

      console.log(`Processing ${files.length} uploaded files`);

      const results = [];
      const errors = [];

      // Process each file
      for (const file of files) {
        try {
          const fileRecord = await fileUploadService.processUploadedFile(
            file,
            req.body.userId || 'anonymous'
          );
          results.push(fileRecord);
        } catch (error: any) {
          console.error(`Error processing file ${file.originalname}:`, error);
          errors.push({
            filename: file.originalname,
            error: error.message
          });
        }
      }

      return res.json({
        message: `Successfully uploaded ${results.length} of ${files.length} files`,
        files: results,
        errors: errors.length > 0 ? errors : undefined,
        count: results.length
      });

    } catch (error: any) {
      console.error('Error in file upload:', error);
      return res.status(500).json({
        message: 'Failed to upload files',
        error: error.message
      });
    }
  }

  /**
   * Get uploaded files for a user
   */
  async getUploadedFiles(req: Request, res: Response) {
    try {
      const userId = req.query.userId as string;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
      const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;

      const files = await storage.getFiles('uploaded_files', userId, limit, offset);

      return res.json({
        files: files,
        total: files.length,
        limit,
        offset
      });

    } catch (error: any) {
      console.error('Error getting uploaded files:', error);
      return res.status(500).json({
        message: 'Failed to get uploaded files',
        error: error.message
      });
    }
  }

  /**
   * Delete an uploaded file
   */
  async deleteUploadedFile(req: Request, res: Response) {
    try {
      const fileId = parseInt(req.params.id);
      
      if (isNaN(fileId)) {
        return res.status(400).json({
          message: 'Invalid file ID'
        });
      }

      const success = await fileUploadService.deleteUploadedFile(fileId);

      if (!success) {
        return res.status(404).json({
          message: 'File not found or not an uploaded file'
        });
      }

      return res.json({
        message: 'File deleted successfully'
      });

    } catch (error: any) {
      console.error('Error deleting uploaded file:', error);
      return res.status(500).json({
        message: 'Failed to delete file',
        error: error.message
      });
    }
  }

  /**
   * Download an uploaded file
   */
  async downloadFile(req: Request, res: Response) {
    try {
      const fileId = parseInt(req.params.id);
      
      if (isNaN(fileId)) {
        return res.status(400).json({
          message: 'Invalid file ID'
        });
      }

      const file = await storage.getFile(fileId);
      
      if (!file || file.platform !== 'uploaded_files') {
        return res.status(404).json({
          message: 'File not found'
        });
      }

      if (!file.fileUrl) {
        return res.status(404).json({
          message: 'File path not found'
        });
      }

      // Set appropriate headers
      res.setHeader('Content-Disposition', `attachment; filename="${file.fileName}"`);
      res.setHeader('Content-Type', file.mimeType || 'application/octet-stream');

      // Send file
      return res.sendFile(file.fileUrl, { root: '/' });

    } catch (error: any) {
      console.error('Error downloading file:', error);
      return res.status(500).json({
        message: 'Failed to download file',
        error: error.message
      });
    }
  }

  /**
   * Get file upload statistics
   */
  async getUploadStats(req: Request, res: Response) {
    try {
      const userId = req.query.userId as string;
      
      const files = await storage.getFiles('uploaded_files', userId, 1000, 0);
      const allFiles = files;

      const stats = {
        totalFiles: allFiles.length,
        totalSize: allFiles.reduce((sum: number, file: any) => sum + (file.fileSize || 0), 0),
        fileTypes: {} as Record<string, number>,
        recentUploads: allFiles
          .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 5)
      };

      // Count file types
      allFiles.forEach((file: any) => {
        stats.fileTypes[file.fileType] = (stats.fileTypes[file.fileType] || 0) + 1;
      });

      return res.json(stats);

    } catch (error: any) {
      console.error('Error getting upload stats:', error);
      return res.status(500).json({
        message: 'Failed to get upload statistics',
        error: error.message
      });
    }
  }
}

export const fileUploadController = new FileUploadController();
