import fs from 'fs';
import path from 'path';
import mammoth from 'mammoth';
import { BaseService } from "../base/service.interface";

/**
 * File Upload Content Service - handles text extraction from uploaded files
 */
export class FileUploadContentService extends BaseService {
  constructor() {
    super('FileUploadContent', '1.0.0', 'Text extraction from uploaded files');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'File Upload Content service initialized');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      supportedTypes: ['text', 'pdf', 'document', 'spreadsheet', 'presentation'],
      extractionMethods: ['text', 'pdf', 'word', 'fallback'],
    };
  }

  /**
   * Extract text from uploaded file based on type
   */
  async extractTextFromFile(filePath: string, fileType: string): Promise<string> {
    this.ensureInitialized();
    this.validateString(filePath, 'file path');
    this.validateString(fileType, 'file type');

    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    try {
      this.log('info', `Extracting text from ${fileType} file: ${path.basename(filePath)}`);

      switch (fileType) {
        case 'text':
          return await this.extractTextFromTextFile(filePath);
        
        case 'document':
          return await this.extractTextFromDocument(filePath);
        
        case 'spreadsheet':
          return await this.extractTextFromSpreadsheet(filePath);
        
        case 'presentation':
          return await this.extractTextFromPresentation(filePath);
        
        default:
          return await this.extractFallbackText(filePath);
      }
    } catch (error: any) {
      this.log('error', `Error extracting text from ${filePath}`, error);
      return await this.extractFallbackText(filePath);
    }
  }

  /**
   * Extract text from plain text files
   */
  private async extractTextFromTextFile(filePath: string): Promise<string> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      this.log('info', `Extracted ${content.length} characters from text file`);
      return content;
    } catch (error: any) {
      this.log('error', 'Error reading text file:', error);
      return await this.extractFallbackText(filePath);
    }
  }

  /**
   * Extract text from document files (PDF, Word)
   */
  private async extractTextFromDocument(filePath: string): Promise<string> {
    const extension = path.extname(filePath).toLowerCase();

    if (extension === '.pdf') {
      return await this.extractTextFromPDF(filePath);
    } else if (['.doc', '.docx'].includes(extension)) {
      return await this.extractTextFromWord(filePath);
    } else {
      return await this.extractFallbackText(filePath);
    }
  }

  /**
   * Extract text from PDF files using centralized PDF service
   */
  private async extractTextFromPDF(filePath: string): Promise<string> {
    try {
      // Use centralized PDF service
      const { unifiedContentExtractor } = await import('../unified-content-extractor.service');
      
      const buffer = fs.readFileSync(filePath);
      const result = await pdfService.extractTextFromPDF(buffer, path.basename(filePath));
      
      if (result.success && result.extractedText) {
        this.log('info', `Extracted ${result.extractedText.length} characters from PDF`);
        return result.extractedText;
      } else {
        this.log('warn', `PDF extraction failed: ${result.error}`);
        return await this.extractFallbackText(filePath);
      }
    } catch (error: any) {
      this.log('error', 'Error extracting text from PDF:', error);
      return await this.extractFallbackText(filePath);
    }
  }

  /**
   * Extract text from Word documents using centralized Word service
   */
  private async extractTextFromWord(filePath: string): Promise<string> {
    try {
      // Use centralized Word service
      const { wordService } = await import('../word-service');
      
      const buffer = fs.readFileSync(filePath);
      const result = await wordService.extractTextFromWord(buffer, path.basename(filePath));
      
      if (result.success && result.extractedText) {
        this.log('info', `Extracted ${result.extractedText.length} characters from Word document`);
        return result.extractedText;
      } else {
        this.log('warn', `Word extraction failed: ${result.error}`);
        return await this.extractFallbackText(filePath);
      }
    } catch (error: any) {
      this.log('error', 'Error extracting text from Word document:', error);
      return await this.extractFallbackText(filePath);
    }
  }

  /**
   * Extract text from spreadsheet files (placeholder for future implementation)
   */
  private async extractTextFromSpreadsheet(filePath: string): Promise<string> {
    try {
      // TODO: Implement Excel/spreadsheet text extraction
      // For now, return metadata-based description
      const stats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      const extension = path.extname(filePath).toLowerCase();
      
      this.log('info', 'Spreadsheet text extraction not yet implemented, using metadata');
      
      return `Spreadsheet File: ${fileName}
File Type: ${extension.substring(1).toUpperCase()} Spreadsheet
File Size: ${Math.round(stats.size / 1024)}KB
Last Modified: ${stats.mtime.toISOString()}

This spreadsheet contains data tables, formulas, and calculations. 
Content includes structured data that can be searched by filename and metadata.
Full text extraction from spreadsheet cells will be implemented in a future update.`;

    } catch (error: any) {
      this.log('error', 'Error processing spreadsheet file:', error);
      return await this.extractFallbackText(filePath);
    }
  }

  /**
   * Extract text from presentation files (placeholder for future implementation)
   */
  private async extractTextFromPresentation(filePath: string): Promise<string> {
    try {
      // TODO: Implement PowerPoint/presentation text extraction
      // For now, return metadata-based description
      const stats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      const extension = path.extname(filePath).toLowerCase();
      
      this.log('info', 'Presentation text extraction not yet implemented, using metadata');
      
      return `Presentation File: ${fileName}
File Type: ${extension.substring(1).toUpperCase()} Presentation
File Size: ${Math.round(stats.size / 1024)}KB
Last Modified: ${stats.mtime.toISOString()}

This presentation contains slides with text, images, and multimedia content.
Content includes slide titles, bullet points, and speaker notes.
Full text extraction from presentation slides will be implemented in a future update.`;

    } catch (error: any) {
      this.log('error', 'Error processing presentation file:', error);
      return await this.extractFallbackText(filePath);
    }
  }

  /**
   * Extract fallback text when specific extraction fails
   */
  private async extractFallbackText(filePath: string): Promise<string> {
    try {
      const stats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      const extension = path.extname(filePath).toLowerCase();
      const mimeType = this.getMimeTypeFromExtension(extension);
      
      this.log('info', `Using fallback text extraction for: ${fileName}`);
      
      return `File: ${fileName}
File Type: ${extension ? extension.substring(1).toUpperCase() : 'Unknown'}
MIME Type: ${mimeType}
File Size: ${Math.round(stats.size / 1024)}KB
Created: ${stats.birthtime.toISOString()}
Last Modified: ${stats.mtime.toISOString()}
Upload Path: ${filePath}

This file has been uploaded and is searchable by filename and metadata.
Text extraction for this file type may be limited, but the file content is preserved and accessible.
The file can be downloaded and viewed using appropriate applications.`;

    } catch (error: any) {
      this.log('error', 'Error creating fallback text:', error);
      return `File: ${path.basename(filePath)} - Text extraction failed, but file is available for download.`;
    }
  }

  /**
   * Get MIME type from file extension
   */
  private getMimeTypeFromExtension(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.csv': 'text/csv',
      '.rtf': 'application/rtf',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.mp3': 'audio/mpeg',
      '.mp4': 'video/mp4',
      '.zip': 'application/zip',
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Check if file type supports text extraction
   */
  supportsTextExtraction(fileType: string, mimeType?: string): boolean {
    const supportedTypes = ['text', 'document'];
    const supportedMimeTypes = [
      'text/plain',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    return supportedTypes.includes(fileType) ||
           Boolean(mimeType && supportedMimeTypes.includes(mimeType));
  }

  /**
   * Get extraction capabilities for different file types
   */
  getExtractionCapabilities(): {
    supported: string[];
    partial: string[];
    metadata: string[];
  } {
    return {
      supported: [
        'text/plain',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ],
      partial: [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      ],
      metadata: [
        'image/*',
        'audio/*',
        'video/*',
        'application/zip',
        'application/x-rar-compressed',
      ],
    };
  }

  /**
   * Estimate extraction quality for a file
   */
  estimateExtractionQuality(fileType: string, mimeType: string): {
    quality: 'high' | 'medium' | 'low' | 'metadata';
    description: string;
  } {
    if (mimeType === 'text/plain') {
      return {
        quality: 'high',
        description: 'Full text content will be extracted and searchable',
      };
    }

    if (mimeType === 'application/pdf' || mimeType.includes('word')) {
      return {
        quality: 'high',
        description: 'Text content will be extracted from document structure',
      };
    }

    if (mimeType.includes('sheet') || mimeType.includes('presentation')) {
      return {
        quality: 'medium',
        description: 'Metadata and basic structure will be extracted, full content extraction coming soon',
      };
    }

    if (mimeType.startsWith('image/') || mimeType.startsWith('audio/') || mimeType.startsWith('video/')) {
      return {
        quality: 'metadata',
        description: 'File metadata and properties will be searchable, content analysis not available',
      };
    }

    return {
      quality: 'low',
      description: 'Basic file information will be searchable, limited content extraction',
    };
  }
}
