import React from 'react';
import AppLayout from '@/components/layout/AppLayout';
import { GoogleIntegrationsManager } from '@/components/integrations/GoogleIntegrationsManager';

export default function IntegrationsPage() {
  return (
    <AppLayout title="Integrations">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Integrations</h1>
          <p className="text-gray-600 mt-2">
            Connect your accounts to sync data and enable AI-powered conversations
          </p>
        </div>

        <GoogleIntegrationsManager />

        {/* Future sections for other providers */}
        {/* <MicrosoftIntegrationsManager />
        <NotionIntegrationsManager /> */}
      </div>
    </AppLayout>
  );
} 