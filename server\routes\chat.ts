import type { Express } from "express";
import { chatController } from "../controllers/chat";
import { ragService } from "../services/rag";

/**
 * Register chat and messaging routes
 */
export function registerChatRoutes(app: Express) {
  // Chat and RAG Routes
  app.get('/api/chat/sessions', (req, res) => chatController.getSessions(req, res));
  app.post('/api/chat/sessions', (req, res) => chatController.createSession(req, res));
  app.get('/api/chat/sessions/:id', (req, res) => chatController.getSession(req, res));
  app.put('/api/chat/sessions/:id', (req, res) => chatController.updateSession(req, res));
  app.delete('/api/chat/sessions/:id', (req, res) => chatController.deleteSession(req, res));
  app.get('/api/chat/sessions/:id/messages', (req, res) => chatController.getMessages(req, res));
  app.post('/api/chat/sessions/:id/messages', (req, res) => chatController.sendMessage(req, res));
  app.post('/api/chat/sessions/:id/stream', (req, res) => chatController.sendStreamingMessage(req, res));
  app.get('/api/chat/sources', (req, res) => chatController.getAvailableSources(req, res));
  app.post('/api/chat/search', (req, res) => chatController.searchSimilar(req, res));

  // Chat file search endpoint
  app.post('/api/chat/search-files', async (req, res) => {
    try {
      const { description, enabledSources, limit } = req.body;

      if (!description) {
        return res.status(400).json({ message: 'Description is required' });
      }

      const results = await ragService.searchFilesByDescription(
        description,
        enabledSources || [],
        limit || 10
      );

      return res.json({
        results,
        count: results.length,
        description,
        enabledSources: enabledSources || [],
      });
    } catch (error: any) {
      console.error('Error searching files by description:', error);
      return res.status(500).json({
        message: 'Failed to search files by description',
        error: error.message
      });
    }
  });
} 