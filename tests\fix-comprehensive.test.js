// Comprehensive test script to verify all fixes
async function testAllFixes() {
  const baseUrl = 'http://localhost:3000';

  console.log('🔧 COMPREHENSIVE FIX TESTING\n');

  // Wait for server to start
  console.log('⏳ Waiting for server to start...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing server health...');
    const healthResponse = await fetch(`${baseUrl}/api/diagnostic/health`);
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ Server is healthy:', healthData.status);
    } else {
      console.log('❌ Server health check failed');
      return;
    }

    // Test 2: Schema Validation Fix
    console.log('\n2️⃣ Testing schema validation fix...');
    const schemaTestResponse = await fetch(`${baseUrl}/api/integrations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'google-drive',
        name: 'Schema Test Integration',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null // This should now work!
      })
    });

    if (schemaTestResponse.status === 201) {
      console.log('✅ Schema validation fix: WORKING');
      const newIntegration = await schemaTestResponse.json();
      console.log('   Created integration ID:', newIntegration.integration?.id);
    } else {
      console.log('❌ Schema validation fix: FAILED');
      const errorData = await schemaTestResponse.json();
      console.log('   Error:', errorData.message);
    }

    // Test 3: Clear corrupted credentials from integration 1
    console.log('\n3️⃣ Clearing corrupted credentials from integration 1...');
    const clearResponse = await fetch(`${baseUrl}/api/diagnostic/clear-credentials/1`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });

    if (clearResponse.ok) {
      console.log('✅ Credentials cleared successfully');
    } else {
      console.log('❌ Failed to clear credentials');
    }

    // Test 4: Test credential flow (should work now)
    console.log('\n4️⃣ Testing credential flow...');
    const credentialFlowResponse = await fetch(`${baseUrl}/api/diagnostic/test-credential-flow`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });

    if (credentialFlowResponse.ok) {
      const credentialData = await credentialFlowResponse.json();
      if (credentialData.success) {
        console.log('✅ Credential flow test: WORKING');
        console.log('   Token match:', credentialData.testResults?.tokenMatch);
      } else {
        console.log('❌ Credential flow test failed:', credentialData.error);
      }
    } else {
      console.log('❌ Credential flow endpoint error:', credentialFlowResponse.status);
    }

    // Test 5: Basic encryption test
    console.log('\n5️⃣ Testing basic encryption...');
    const encryptResponse = await fetch(`${baseUrl}/api/diagnostic/test-encryption`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ testData: 'Fix verification test' })
    });

    if (encryptResponse.ok) {
      const encryptData = await encryptResponse.json();
      console.log('✅ Basic encryption:', encryptData.success ? 'WORKING' : 'FAILED');
    } else {
      console.log('❌ Encryption test failed');
    }

    console.log('\n🎯 SUMMARY:');
    console.log('- Server should now be working correctly');
    console.log('- Schema validation should accept null syncSchedule');
    console.log('- Double encryption issue should be resolved');
    console.log('- You can now re-authenticate Google integrations');

    console.log('\n📋 NEXT STEPS:');
    console.log('1. Go to http://localhost:5000/debug');
    console.log('2. Test "Clear Credentials ID 1" to reset integration 1');
    console.log('3. Go to main app and re-authenticate Google Drive integration');
    console.log('4. Try accessing Google Drive folders');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAllFixes();