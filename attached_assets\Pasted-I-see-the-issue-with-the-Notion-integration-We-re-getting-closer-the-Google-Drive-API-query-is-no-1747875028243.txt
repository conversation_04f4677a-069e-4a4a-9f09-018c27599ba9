I see the issue with the Notion integration! We're getting closer - the Google Drive API query is now working correctly, but we're getting a Notion API error. The issue is that our code is trying to send property names that don't match the actual columns in your Meeting Transcripts database in Notion.

Let's fix the Notion integration to match your table schema:


Opened server/services/notion-service.ts
Now I understand the exact issue with our Notion integration. We need to update the property names in our code to match the actual column names in your Meeting Transcripts database in Notion. Let me fix this:


Opened server/services/notion-service.ts
Looking at the error message, I need to update how we're mapping properties to match your Notion database schema. Let me fix the Notion service:

Let me get a better view of the Notion service's createTranscriptPage method so I can fix it properly:


Opened server/services/notion-service.ts
I need to update the createTranscriptPage method in our Notion service to match your Meeting Transcripts database schema. Let's fix this:


Edited server/services/notion-service.ts
Now I need to update the mapping for the remaining properties to match your Notion database schema:


Edited server/services/notion-service.ts

Restarted Start application

Took a screenshot
Checkpoint made
28 minutes ago
•
Update meeting transcript info pushed to Notion to match its database schema
Screenshot
Load preview
Great progress! I've fixed the Notion integration to match your exact database schema:

✓ Updated property names to match your Notion columns (Name, Attendees, Date, Duration, Source, SourceURL, Time, Topics)
✓ Changed field types to match your Notion database structure
✓ Added better logging to track the database interactions

Try clicking the "Sync Now" button again. The system should now properly sync your Google Meet transcripts to Notion with the correct field mappings.

Are you seeing your meeting transcripts appear in the Notion database now? Let me know if you encounter any other issues!
chunk-RPCDYKBN.js?v=a45db685:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
setup.tsx:104 Fetching folders for integration 1...
setup.tsx:110 Fetched folders data: {folders: Array(10), user: 'Azha Qari', email: '<EMAIL>', message: 'Found 10 folders in Google Drive', canCreateFolder: true}canCreateFolder: trueemail: "<EMAIL>"folders: Array(10)0: {parents: Array(1), id: '1KjEsp9ercpnG4USyrppDj6tzjdwwYIAd', name: '__pycache__'}1: {parents: Array(1), id: '1mkHZeRGOC0EWVRzzsf3HebLbB0pIpEg4', name: 'All Meet Sources'}2: {parents: Array(1), id: '1w_RHiAH3e0Q92khy099DuXQ6uTFICWNs', name: 'backups'}3: {id: '1Wig7EP6-iqZiZDbfwTRwzpSTNEig81Xd', name: 'Meet Recordings'}4: {id: '1PeHfFpfJp8Me_mCzYq4BrpfGGzwn0aqI', name: 'Meet Recordings'}5: {parents: Array(1), id: '1Vg0-3X_0V4MLH6Ipmx8T_GU8SN6LR56h', name: 'Meet Recordings'}6: {id: '1U_QJywx7qhlTOLXg4kEnIN2c0OGN90hB', name: 'meet_transcript'}7: {parents: Array(1), id: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2', name: 'MeetSync'}8: {parents: Array(1), id: '1ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0', name: 'Processed'}9: {parents: Array(1), id: '1Sb-jrm3N3ETWSvLb10uwhsADr64QzIx8', name: 'Processed Transcripts', driveId: '0ADjOv-Fl7-EBUk9PVA'}length: 10[[Prototype]]: Array(0)at: ƒ at()concat: ƒ concat()constructor: ƒ Array()copyWithin: ƒ copyWithin()entries: ƒ entries()every: ƒ every()fill: ƒ fill()filter: ƒ filter()find: ƒ find()findIndex: ƒ findIndex()findLast: ƒ findLast()findLastIndex: ƒ findLastIndex()flat: ƒ flat()flatMap: ƒ flatMap()forEach: ƒ forEach()includes: ƒ includes()indexOf: ƒ indexOf()join: ƒ join()keys: ƒ keys()lastIndexOf: ƒ lastIndexOf()length: 0map: ƒ map()pop: ƒ pop()push: ƒ push()reduce: ƒ reduce()reduceRight: ƒ reduceRight()reverse: ƒ reverse()shift: ƒ shift()slice: ƒ slice()some: ƒ some()sort: ƒ sort()splice: ƒ splice()toLocaleString: ƒ toLocaleString()toReversed: ƒ toReversed()toSorted: ƒ toSorted()toSpliced: ƒ toSpliced()toString: ƒ toString()unshift: ƒ unshift()values: ƒ values()with: ƒ with()Symbol(Symbol.iterator): ƒ values()Symbol(Symbol.unscopables): {at: true, copyWithin: true, entries: true, fill: true, find: true, …}[[Prototype]]: Objectmessage: "Found 10 folders in Google Drive"user: "Azha Qari"[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
setup.tsx:169 Saving integration configuration with folder: {folderId: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2', folderName: 'MeetSync', archiveProcessed: true}archiveProcessed: truefolderId: "1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2"folderName: "MeetSync"[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()




> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/indeScheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
12:20:57 AM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
12:21:00 AM [express] GET /api/integrations 200 in 81ms :: {"integrations":[{"id":1,"name":"Google Me…
12:21:01 AM [express] GET /api/sync-logs 200 in 336ms :: {"logs":[{"id":10,"integrationId":1,"startTi…
12:21:05 AM [express] GET /api/integrations 200 in 76ms :: {"integrations":[{"id":1,"name":"Google Me…
12:21:05 AM [express] GET /api/sync-logs 200 in 77ms :: {"logs":[{"id":10,"integrationId":1,"startTim…
12:21:06 AM [express] GET /api/sync-logs 304 in 75ms :: {"logs":[{"id":10,"integrationId":1,"startTim…
12:21:06 AM [express] GET /api/integrations 304 in 79ms :: {"integrations":[{"id":1,"name":"Google Me…
12:21:55 AM [express] GET /api/integrations 200 in 303ms :: {"integrations":[{"id":1,"name":"Google M…
12:46:42 AM [express] GET /api/integrations 304 in 731ms :: {"integrations":[{"id":1,"name":"Google M…
OpenAI service initialized successfully
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
12:46:44 AM [express] POST /api/sync-now 200 in 277ms :: {"message":"Sync started successfully","sync…
12:46:45 AM [express] GET /api/integrations 200 in 77ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0
Found 0 files in processed folder
After filtering: 2 files remaining to process
Extracting content from document: Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (ID: 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc)
Successfully extracted content (40694 characters)
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Could not find property with name or id: SourceId'
}
Error finding transcript by source ID 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc: APIResponseError: Could not find property with name or id: SourceId
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.findTranscriptBySourceId (/home/<USER>/workspace/server/services/notion-service.ts:282:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:228:36) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:46:52 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '171',
    connection: 'keep-alive',
    'cf-ray': '94384c87ac8bbcc2-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"ab-gxX91DAd4taDdKAHd+6NuxFV2yo"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '1d3351a4-c183-4d13-9321-81f8da8bc1ba',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=.Bwbsfbv5i0WxjUDO9MyEH25LIANTudr_X70k3s81Jk-1747874812-*******-ljPZVkxz1tFbLr2ewpk0iC5eWzxls2R5bg1IvlwuZ4AFrvr58fEX7DtbtK1OVXe0KTAORZ16_ssaBiUDhen608C52veE9w17iWWcA25yocI; path=/; expires=Thu, 22-May-25 01:16:52 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=4UjoNzr72eYRLwnkYiMjwNOnS5Zsz8WuYYHv1j84WhQ-1747874812378-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Could not find property with name or id: SourceId","request_id":"1d3351a4-c183-4d13-9321-81f8da8bc1ba"}'
}
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Integration Planning Meeting
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Topics is expected to be multi_select.'
}
Error creating transcript page: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:46:52 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384c89cf82bcda-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-LVQh55j84mAfHWEJV7SNn3dgAUo"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '0eb130ed-b9f1-41a5-abec-293ee131d2bc',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=3wGM7rOwRu3Rnl7rjjCGsBR_5emE0BsR82F4eDlblpg-1747874812-*******-IqZ3MW3POPFJr3zfydizdbdb7Fc5ftfNyM7.BKp3Su5jcXkXfIIhpkyLjWKAKL0TLXF4vZ6blAma36IyB6iH5jeselJ.tnlLRPAA5rMT5Qo; path=/; expires=Thu, 22-May-25 01:16:52 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=wSBSK0owbiwfcpxBSJ7oNVNquqxFjRDyt3wv5QVvctA-1747874812727-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"0eb130ed-b9f1-41a5-abec-293ee131d2bc"}'
}
Error processing item 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:46:52 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384c89cf82bcda-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-LVQh55j84mAfHWEJV7SNn3dgAUo"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '0eb130ed-b9f1-41a5-abec-293ee131d2bc',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=3wGM7rOwRu3Rnl7rjjCGsBR_5emE0BsR82F4eDlblpg-1747874812-*******-IqZ3MW3POPFJr3zfydizdbdb7Fc5ftfNyM7.BKp3Su5jcXkXfIIhpkyLjWKAKL0TLXF4vZ6blAma36IyB6iH5jeselJ.tnlLRPAA5rMT5Qo; path=/; expires=Thu, 22-May-25 01:16:52 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=wSBSK0owbiwfcpxBSJ7oNVNquqxFjRDyt3wv5QVvctA-1747874812727-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"0eb130ed-b9f1-41a5-abec-293ee131d2bc"}'
}
Extracting content from document: meetsync test (ID: 13vfJjvITD3AfjYnroYoD63rBS2rXlki9TiGSZSjI1uI)
Successfully extracted content (39 characters)
Error classifying transcript with OpenAI: Error: Invalid response format: not an array
    at OpenAIService.classifyTranscript (/home/<USER>/workspace/server/services/openai-service.ts:177:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:218:39)
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Could not find property with name or id: SourceId'
}
Error finding transcript by source ID 13vfJjvITD3AfjYnroYoD63rBS2rXlki9TiGSZSjI1uI: APIResponseError: Could not find property with name or id: SourceId
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.findTranscriptBySourceId (/home/<USER>/workspace/server/services/notion-service.ts:282:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:228:36) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:46:55 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '171',
    connection: 'keep-alive',
    'cf-ray': '94384c9e581fbcc2-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"ab-gbVW5/lam3R22mQH8Kk5OzcdzEo"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '1d1b4914-1153-44a2-8c84-940ebc1ce6ab',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=Eti9U6Gobr3YVTfFoS2wyabK1LQEAE1V51xQP_RADVw-1747874815-*******-oLeYK33hnr1V0qVZ_rSEQi_pJ_DajNCgRaqYNvftvTwkqouNIb3RmRzLNkp2IHokTjNCguAcnnGcpIplez0rvTJ1Bg9CqhwErMn.qfZqCYE; path=/; expires=Thu, 22-May-25 01:16:55 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=.JvLG7.EOJ31WmscxprnssFadMPO6FPypHLWy865eDg-1747874815931-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Could not find property with name or id: SourceId","request_id":"1d1b4914-1153-44a2-8c84-940ebc1ce6ab"}'
}
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Meetsync Web App Test Meeting
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Topics is expected to be multi_select.'
}
Error creating transcript page: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:46:56 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384c9faa65bcda-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-9lsnJ56oF8vFxcyWGZ3RLpMT6ok"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '249ca422-fbdb-4e7c-abce-a35f3af34974',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=gg0tZbme2o9_WabfUQzF3MfMeNN0ugL0GYIdP9zUvsw-1747874816-*******-8YS52QQZ3BgiS3lsFStEzyUtRBg_vJWhE_psBXPz6TG2hJAv.lSC9z.84p.vc4zF8YlLpPSwuzAHbRTaSpGL5I9cQXX082lds21ORp.Yc7U; path=/; expires=Thu, 22-May-25 01:16:56 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=b7IggQO5R3SZiCOb7F8RXXEpHjzhbj4I6OK6zfQ4ay8-1747874816331-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"249ca422-fbdb-4e7c-abce-a35f3af34974"}'
}
Error processing item 13vfJjvITD3AfjYnroYoD63rBS2rXlki9TiGSZSjI1uI: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:46:56 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384c9faa65bcda-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-9lsnJ56oF8vFxcyWGZ3RLpMT6ok"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '249ca422-fbdb-4e7c-abce-a35f3af34974',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=gg0tZbme2o9_WabfUQzF3MfMeNN0ugL0GYIdP9zUvsw-1747874816-*******-8YS52QQZ3BgiS3lsFStEzyUtRBg_vJWhE_psBXPz6TG2hJAv.lSC9z.84p.vc4zF8YlLpPSwuzAHbRTaSpGL5I9cQXX082lds21ORp.Yc7U; path=/; expires=Thu, 22-May-25 01:16:56 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=b7IggQO5R3SZiCOb7F8RXXEpHjzhbj4I6OK6zfQ4ay8-1747874816331-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"249ca422-fbdb-4e7c-abce-a35f3af34974"}'
}
Sync completed with status: failed
Items processed: 2, Success: 0, Failed: 2
12:47:07 AM [express] GET /api/integrations 200 in 340ms :: {"integrations":[{"id":1,"name":"Google M…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_dxvqj2dwudj
12:47:10 AM [express] GET /api/integrations/1/auth-url 200 in 149ms :: {"authUrl":"https://accounts.g…
12:47:16 AM [express] GET /api/integrations/oauth/callback 302 in 2ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
12:47:16 AM [express] GET /api/integrations/1/oauth/callback 302 in 246ms
12:47:17 AM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 10 folders
12:47:20 AM [express] GET /api/integrations/1/folders 304 in 576ms :: {"folders":[{"parents":["1U_QJy…
Cancelled schedule for integration 1
12:47:24 AM [express] PUT /api/integrations/1 200 in 224ms :: {"message":"Integration updated success…
12:47:24 AM [express] GET /api/integrations 200 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
12:47:26 AM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
OpenAI service initialized successfully
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
12:47:29 AM [express] POST /api/sync-now 200 in 225ms :: {"message":"Sync started successfully","sync…
12:47:29 AM [express] GET /api/integrations 200 in 73ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1ksxEuxOH_dvm0O20QAye-gcRHBXrT7B0
Found 0 files in processed folder
After filtering: 2 files remaining to process
Extracting content from document: Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (ID: 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc)
Successfully extracted content (40694 characters)
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Could not find property with name or id: SourceId'
}
Error finding transcript by source ID 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc: APIResponseError: Could not find property with name or id: SourceId
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.findTranscriptBySourceId (/home/<USER>/workspace/server/services/notion-service.ts:282:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:228:36) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:47:35 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '171',
    connection: 'keep-alive',
    'cf-ray': '94384d98dcd46768-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"ab-pmquVkmDI+fJr2v0YEu/Y+ZeW9Q"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '4692fc81-f24f-4d0c-9dca-c4e06c0210b1',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=Cn6._dXo9_CfYTpanQ7P.Fj2K4WvtFgQcEl7o_hRrPQ-1747874855-*******-uAXII.LGBRqZtsb6Bk8hEeoEd4vM1rkc4SfaaroaqRvdq1RIJ0AjmKzvP3HsdNqHXwbIQVq5.omTk7d34imJxEW9pizXmyIybFJeNyVed90; path=/; expires=Thu, 22-May-25 01:17:35 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=zBIik.N4l_JcHq18jgUEAwHGTVxTlMGULnhl5xgy3ac-1747874855954-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Could not find property with name or id: SourceId","request_id":"4692fc81-f24f-4d0c-9dca-c4e06c0210b1"}'
}
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Integration Planning Meeting
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Topics is expected to be multi_select.'
}
Error creating transcript page: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:47:37 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384d99fe3012d2-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-+BZ7M3W9QPue9KeFGSCh/1B93KE"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '13776362-2055-4fde-a931-8b5376304d2a',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=U4tiSQ6SQk8K_k5t1VTCLc.vRZyYyXsJKdwRb8i7Na8-1747874857-*******-dI2DBOzw19C4REFzkN393rL79Fb1QZOIDu46LC4yipW.VCAriGw1dVzymsGB3ESZ5GfKAUrgNbR11.9ZfxL7xsL2j2OTyq0M8hk_6d4Tyew; path=/; expires=Thu, 22-May-25 01:17:37 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=l_qazneWUga3A6ZRFBAqfJ30mBAEmDRJ5.e06E8efEc-1747874857233-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"13776362-2055-4fde-a931-8b5376304d2a"}'
}
Error processing item 12xlUqLXAzztg4lJQj7KGG5Hkw5E34gePiiynVgOIDRc: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:47:37 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384d99fe3012d2-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-+BZ7M3W9QPue9KeFGSCh/1B93KE"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': '13776362-2055-4fde-a931-8b5376304d2a',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=U4tiSQ6SQk8K_k5t1VTCLc.vRZyYyXsJKdwRb8i7Na8-1747874857-*******-dI2DBOzw19C4REFzkN393rL79Fb1QZOIDu46LC4yipW.VCAriGw1dVzymsGB3ESZ5GfKAUrgNbR11.9ZfxL7xsL2j2OTyq0M8hk_6d4Tyew; path=/; expires=Thu, 22-May-25 01:17:37 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=l_qazneWUga3A6ZRFBAqfJ30mBAEmDRJ5.e06E8efEc-1747874857233-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"13776362-2055-4fde-a931-8b5376304d2a"}'
}
Extracting content from document: meetsync test (ID: 13vfJjvITD3AfjYnroYoD63rBS2rXlki9TiGSZSjI1uI)
Successfully extracted content (39 characters)
Error classifying transcript with OpenAI: Error: Invalid response format: not an array
    at OpenAIService.classifyTranscript (/home/<USER>/workspace/server/services/openai-service.ts:177:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:218:39)
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Could not find property with name or id: SourceId'
}
Error finding transcript by source ID 13vfJjvITD3AfjYnroYoD63rBS2rXlki9TiGSZSjI1uI: APIResponseError: Could not find property with name or id: SourceId
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.findTranscriptBySourceId (/home/<USER>/workspace/server/services/notion-service.ts:282:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:228:36) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:47:40 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '171',
    connection: 'keep-alive',
    'cf-ray': '94384db25a9f6768-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"ab-HfRsG8hhigcv9ssfguYrpABN/p8"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'b41d3283-dae2-4552-b948-0c720ac4b422',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=dTxcrWeCwctabOHkBuh9MutEQMsHMI891Ds.8nmK80M-1747874860-*******-mVQqCGLIa0oydcG5RiQImFz93Ob9XwV6SCLEwZsNPyeVm4vOVwFhiFu71q9tRAA8_6xXUZOoIDuJPEyiruTxAQuzuQ_n7PHtB105s65Ke_k; path=/; expires=Thu, 22-May-25 01:17:40 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=tP4sfCYmceOgakuGsWRQIUala_Hq3LiBjtyHAyt5eOg-1747874860073-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Could not find property with name or id: SourceId","request_id":"b41d3283-dae2-4552-b948-0c720ac4b422"}'
}
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Meetsync Web App Test Meeting
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Topics is expected to be multi_select.'
}
Error creating transcript page: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:47:40 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384db38c5212d2-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-nxezZDlsMJxtRNDLOOvvTmnaQn0"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'e28929e8-ff0c-4b52-8e1c-6295f9d79e3d',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=md3fXKKJBb3fj_fBGAZKjpSut5JT4srILxOeEGeIVag-1747874860-*******-PC07Lp_PGk_5LsG1ESK_Tp6LU0ivhO_EVR.oqKR..8YOP_X0fd2BO4HgIAZBFz_O4G2Ef4nG5u5R0j_6J1XVAcVUhB5geCyd3ScZgLpTbyY; path=/; expires=Thu, 22-May-25 01:17:40 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=XGuKt2FuqV5GaX6j1Xo.SbKGn.wqHh7VDh6_EH3u1LM-1747874860216-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"e28929e8-ff0c-4b52-8e1c-6295f9d79e3d"}'
}
Error processing item 13vfJjvITD3AfjYnroYoD63rBS2rXlki9TiGSZSjI1uI: APIResponseError: Topics is expected to be multi_select.
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:400:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:239:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 00:47:40 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '160',
    connection: 'keep-alive',
    'cf-ray': '94384db38c5212d2-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"a0-nxezZDlsMJxtRNDLOOvvTmnaQn0"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'e28929e8-ff0c-4b52-8e1c-6295f9d79e3d',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=md3fXKKJBb3fj_fBGAZKjpSut5JT4srILxOeEGeIVag-1747874860-*******-PC07Lp_PGk_5LsG1ESK_Tp6LU0ivhO_EVR.oqKR..8YOP_X0fd2BO4HgIAZBFz_O4G2Ef4nG5u5R0j_6J1XVAcVUhB5geCyd3ScZgLpTbyY; path=/; expires=Thu, 22-May-25 01:17:40 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=XGuKt2FuqV5GaX6j1Xo.SbKGn.wqHh7VDh6_EH3u1LM-1747874860216-*******-*********; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Topics is expected to be multi_select.","request_id":"e28929e8-ff0c-4b52-8e1c-6295f9d79e3d"}'
}
Sync completed with status: failed
Items processed: 2, Success: 0, Failed: 2 