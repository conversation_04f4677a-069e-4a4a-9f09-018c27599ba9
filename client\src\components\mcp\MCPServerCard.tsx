import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  Zap,
  Settings,
  RefreshCw
} from 'lucide-react';
import { MCPServer } from './types';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface MCPServerCardProps {
  server: MCPServer;
  onToggle: (serverId: string, enabled: boolean) => void;
  onReconnect: (serverId: string) => void;
  onConfigure: (serverId: string) => void;
  className?: string;
}

export function MCPServerCard({ 
  server, 
  onToggle, 
  onReconnect, 
  onConfigure,
  className = "" 
}: MCPServerCardProps) {
  const getStatusIcon = () => {
    if (!server.config.enabled) {
      return <XCircle className="w-4 h-4 text-gray-400" />;
    }
    
    if (server.status.connected && server.status.healthy) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    
    if (server.status.connected && !server.status.healthy) {
      return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    }
    
    return <XCircle className="w-4 h-4 text-red-500" />;
  };

  const getStatusText = () => {
    if (!server.config.enabled) return 'Disabled';
    if (server.status.connected && server.status.healthy) return 'Connected';
    if (server.status.connected && !server.status.healthy) return 'Unhealthy';
    return 'Disconnected';
  };

  const getStatusColor = () => {
    if (!server.config.enabled) return 'bg-gray-100 text-gray-600';
    if (server.status.connected && server.status.healthy) return 'bg-green-100 text-green-700';
    if (server.status.connected && !server.status.healthy) return 'bg-yellow-100 text-yellow-700';
    return 'bg-red-100 text-red-700';
  };

  const getServerTypeIcon = () => {
    switch (server.type) {
      case 'google-drive':
        return '📁';
      case 'microsoft-teams':
        return '👥';
      case 'file-upload':
        return '📤';
      default:
        return '🔧';
    }
  };

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{getServerTypeIcon()}</div>
            <div>
              <CardTitle className="text-lg font-semibold">{server.name}</CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon()}
                <Badge variant="secondary" className={cn("text-xs", getStatusColor())}>
                  {getStatusText()}
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              checked={server.config.enabled}
              onCheckedChange={(enabled) => onToggle(server.id, enabled)}
              className="data-[state=checked]:bg-green-500"
            />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Server Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <div className="text-gray-500">Tools Available</div>
              <div className="font-semibold flex items-center space-x-1">
                <Zap className="w-3 h-3 text-blue-500" />
                <span>{server.tools.length}</span>
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-gray-500">Response Time</div>
              <div className="font-semibold flex items-center space-x-1">
                <Clock className="w-3 h-3 text-purple-500" />
                <span>
                  {server.status.responseTime ? `${server.status.responseTime}ms` : 'N/A'}
                </span>
              </div>
            </div>
          </div>

          {/* Last Connected */}
          {server.lastConnected && (
            <div className="text-xs text-gray-500">
              Last connected {formatDistanceToNow(server.lastConnected, { addSuffix: true })}
            </div>
          )}

          {/* Error Message */}
          {server.error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-2">
              <div className="text-xs text-red-600 font-medium">Error:</div>
              <div className="text-xs text-red-500 mt-1">{server.error}</div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onReconnect(server.id)}
              disabled={!server.config.enabled}
              className="flex-1"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Reconnect
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onConfigure(server.id)}
              className="flex-1"
            >
              <Settings className="w-3 h-3 mr-1" />
              Configure
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
