import { BaseService } from "../base/service.interface";
import { embeddingService } from "../ai/embedding-service";
import { storage } from "../../storage/index.js";

// Import batch embedding service if available
let batchEmbeddingService: any;
try {
  // Dynamic import to avoid require() issues - batch service is optional
  import("../ai/batch-embedding-service").then(({ batchEmbeddingService: service }) => {
    batchEmbeddingService = service;
  }).catch(() => {
    console.log("Batch embedding service not available, batch processing disabled");
  });
} catch (error) {
  console.log("Batch embedding service not available, batch processing disabled");
}

/**
 * RAG Batch Processing Service - handles batch operations for embeddings and file processing
 */
export class RAGBatchService extends BaseService {
  constructor() {
    super('RAGBatch', '1.0.0', 'RAG batch processing for embeddings and file operations');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'RAG Batch service initialized');
    if (!batchEmbeddingService) {
      this.log('warn', 'Batch embedding service not available - batch processing disabled');
    }
  }

  protected getDependencies(): string[] {
    return ['storage', 'embedding-service'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    try {
      const batchEnabled = this.isBatchProcessingEnabled();
      const threshold = this.getBatchThreshold();
      
      return {
        batchProcessingEnabled: batchEnabled,
        batchServiceAvailable: !!batchEmbeddingService,
        batchThreshold: threshold,
        storageConnected: true,
      };
    } catch (error) {
      return {
        batchProcessingEnabled: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Process multiple files using batch API (50% cheaper, no rate limits)
   */
  async processBatchEmbeddings(fileIds: number[]): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(fileIds, 'file IDs');

    if (!batchEmbeddingService) {
      throw new Error('Batch embedding service not available');
    }

    try {
      this.log('info', `Starting batch processing for ${fileIds.length} files`);

      // Get file contents
      const files = [];
      for (const fileId of fileIds) {
        const file = await storage.getFile(fileId);
        if (file && file.fileContent) {
          files.push({
            fileId,
            content: file.fileContent,
          });
        }
      }

      if (files.length === 0) {
        throw new Error("No files with content found for batch processing");
      }

      // Submit batch job
      const batchId = await batchEmbeddingService.processFilesForEmbeddingsBatch(files);

      this.log('info', `Batch job submitted: ${batchId}`);
      this.log('info', `Check status with: checkBatchStatus("${batchId}")`);

      return batchId;

    } catch (error: any) {
      this.handleError(error, 'batch processing');
    }
  }

  /**
   * Check status of batch processing job
   */
  async checkBatchStatus(batchId: string): Promise<any> {
    this.ensureInitialized();
    this.validateString(batchId, 'batch ID');

    if (!batchEmbeddingService) {
      throw new Error('Batch embedding service not available');
    }

    try {
      return await batchEmbeddingService.checkBatchStatus(batchId);
    } catch (error: any) {
      this.handleError(error, 'checking batch status');
    }
  }

  /**
   * Smart processing: Choose between real-time and batch based on context
   */
  async processFilesSmart(fileIds: number[], options: {
    urgent?: boolean;
    userWaiting?: boolean;
    bulkOperation?: boolean;
  } = {}): Promise<{ method: string; result: any }> {
    this.ensureInitialized();
    this.validateRequired(fileIds, 'file IDs');

    const { urgent = false, userWaiting = false, bulkOperation = false } = options;

    // Decision logic
    const shouldUseBatch = this.shouldUseBatchProcessing(fileIds.length, { urgent, userWaiting, bulkOperation });

    if (shouldUseBatch) {
      this.log('info', `Using BATCH processing for ${fileIds.length} files (background)`);
      const batchId = await this.processBatchEmbeddings(fileIds);
      return {
        method: 'batch',
        result: {
          batchId,
          message: `Files submitted for background processing. Check status with batch ID: ${batchId}`,
          estimatedTime: '10 minutes to 6 hours',
          cost: '50% cheaper than real-time'
        }
      };
    } else {
      this.log('info', `Using REAL-TIME processing for ${fileIds.length} files (immediate)`);
      // Process files one by one with the queue system
      const results = [];
      for (const fileId of fileIds) {
        try {
          const file = await storage.getFile(fileId);
          if (file && file.fileContent) {
            await embeddingService.processFileForEmbeddings(fileId, file.fileContent);
            results.push({ fileId, status: 'completed' });
          }
        } catch (error: any) {
          results.push({ fileId, status: 'failed', error: error.message });
        }
      }
      return {
        method: 'real-time',
        result: {
          processed: results,
          message: 'Files processed immediately',
          estimatedTime: 'completed',
          cost: 'standard rate'
        }
      };
    }
  }

  /**
   * Decision logic for processing method
   */
  private shouldUseBatchProcessing(fileCount: number, options: {
    urgent?: boolean;
    userWaiting?: boolean;
    bulkOperation?: boolean;
  }): boolean {
    const { urgent, userWaiting, bulkOperation } = options;

    // Force real-time if user is waiting
    if (userWaiting || urgent) {
      return false;
    }

    // Force batch for bulk operations
    if (bulkOperation) {
      return true;
    }

    // Use batch for large numbers of files
    const batchThreshold = this.getBatchThreshold();
    if (fileCount >= batchThreshold) {
      return true;
    }

    // Check if batch processing is enabled
    if (!this.isBatchProcessingEnabled()) {
      return false;
    }

    // Default to real-time for small numbers
    return false;
  }

  /**
   * Auto-detect and process files that need embeddings using smart logic
   */
  async processPendingFilesSmart(options: {
    urgent?: boolean;
    userWaiting?: boolean;
  } = {}): Promise<{ method: string; result: any } | null> {
    this.ensureInitialized();

    try {
      // Get all files without embeddings
      const allFiles = await storage.getFiles();
      const filesNeedingEmbeddings = [];

      for (const file of allFiles) {
        const hasEmbeddings = await embeddingService.hasEmbeddings(file.id);
        if (!hasEmbeddings && file.fileContent) {
          filesNeedingEmbeddings.push(file.id);
        }
      }

      if (filesNeedingEmbeddings.length === 0) {
        this.log('info', "No files need embedding processing");
        return null;
      }

      this.log('info', `Found ${filesNeedingEmbeddings.length} files needing embeddings`);

      // Use smart processing
      return await this.processFilesSmart(filesNeedingEmbeddings, {
        ...options,
        bulkOperation: true // This is a bulk operation
      });

    } catch (error: any) {
      this.handleError(error, 'processing pending files');
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  async processPendingFilesBatch(): Promise<string | null> {
    const result = await this.processPendingFilesSmart({ userWaiting: false });
    return result?.method === 'batch' ? result.result.batchId : null;
  }

  /**
   * Get batch processing statistics
   */
  async getBatchStatistics(): Promise<{
    batchProcessingEnabled: boolean;
    batchThreshold: number;
    pendingFiles: number;
    processedFiles: number;
    totalFiles: number;
  }> {
    this.ensureInitialized();

    try {
      const allFiles = await storage.getFiles();
      let pendingFiles = 0;
      let processedFiles = 0;

      for (const file of allFiles) {
        if (file.fileContent) {
          const hasEmbeddings = await embeddingService.hasEmbeddings(file.id);
          if (hasEmbeddings) {
            processedFiles++;
          } else {
            pendingFiles++;
          }
        }
      }

      return {
        batchProcessingEnabled: this.isBatchProcessingEnabled(),
        batchThreshold: this.getBatchThreshold(),
        pendingFiles,
        processedFiles,
        totalFiles: allFiles.length,
      };

    } catch (error: any) {
      this.log('error', 'Error getting batch statistics', error);
      return {
        batchProcessingEnabled: false,
        batchThreshold: 0,
        pendingFiles: 0,
        processedFiles: 0,
        totalFiles: 0,
      };
    }
  }

  /**
   * Check if batch processing is enabled
   */
  private isBatchProcessingEnabled(): boolean {
    return process.env.ENABLE_BATCH_PROCESSING === 'true' && !!batchEmbeddingService;
  }

  /**
   * Get batch processing threshold
   */
  private getBatchThreshold(): number {
    return parseInt(process.env.BATCH_PROCESSING_THRESHOLD || "5");
  }

  /**
   * Get batch processing cost savings
   */
  getBatchCostSavings(): {
    enabled: boolean;
    savingsPercentage: number;
    estimatedDelay: string;
  } {
    return {
      enabled: this.isBatchProcessingEnabled(),
      savingsPercentage: 50, // OpenAI Batch API is 50% cheaper
      estimatedDelay: '10 minutes to 6 hours',
    };
  }

  /**
   * Force process all pending files (admin function)
   */
  async forceProcessAllPending(useBatch: boolean = true): Promise<{ method: string; result: any } | null> {
    this.ensureInitialized();

    try {
      const allFiles = await storage.getFiles();
      const filesNeedingEmbeddings = [];

      for (const file of allFiles) {
        const hasEmbeddings = await embeddingService.hasEmbeddings(file.id);
        if (!hasEmbeddings && file.fileContent) {
          filesNeedingEmbeddings.push(file.id);
        }
      }

      if (filesNeedingEmbeddings.length === 0) {
        this.log('info', "No files need processing");
        return null;
      }

      this.log('info', `Force processing ${filesNeedingEmbeddings.length} files (batch: ${useBatch})`);

      if (useBatch && this.isBatchProcessingEnabled()) {
        const batchId = await this.processBatchEmbeddings(filesNeedingEmbeddings);
        return {
          method: 'batch',
          result: { batchId, fileCount: filesNeedingEmbeddings.length }
        };
      } else {
        // Process all files immediately
        const results = [];
        for (const fileId of filesNeedingEmbeddings) {
          try {
            const file = await storage.getFile(fileId);
            if (file && file.fileContent) {
              await embeddingService.processFileForEmbeddings(fileId, file.fileContent);
              results.push({ fileId, status: 'completed' });
            }
          } catch (error: any) {
            results.push({ fileId, status: 'failed', error: error.message });
          }
        }
        return {
          method: 'real-time',
          result: { processed: results, fileCount: filesNeedingEmbeddings.length }
        };
      }

    } catch (error: any) {
      this.handleError(error, 'force processing all pending files');
    }
  }
}
