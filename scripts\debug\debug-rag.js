// Debug script to check RAG database state
import { storage } from '../../server/storage/index.ts';
import { embeddingService } from '../../server/services/embedding-service.ts';

async function debugRAG() {
  console.log('🔍 Debugging RAG System...\n');

  try {
    // Check files
    console.log('📁 Checking files...');
    const files = await storage.getFiles();
    console.log(`Found ${files.length} files total`);
    
    if (files.length > 0) {
      console.log('Sample files:');
      files.slice(0, 5).forEach(file => {
        console.log(`  - ${file.fileName} (${file.platform}, status: ${file.status})`);
      });
      
      // Check for presentation files specifically
      const presentations = files.filter(file => 
        file.fileName.toLowerCase().includes('neurolink') ||
        file.fileType === 'presentation' ||
        file.fileName.toLowerCase().includes('.pptx')
      );
      
      console.log(`\n📊 Found ${presentations.length} presentation files:`);
      for (const presentation of presentations) {
        console.log(`  - ${presentation.fileName} (ID: ${presentation.id})`);
        console.log(`    Type: ${presentation.fileType}, Platform: ${presentation.platform}`);
        
        const chunks = await storage.getFileChunks(presentation.id);
        console.log(`    Chunks: ${chunks.length}`);
        
        if (chunks.length > 0) {
          console.log(`    Sample chunk: "${chunks[0].content.substring(0, 100)}..."`);
          console.log(`    Has embedding: ${chunks[0].embedding ? 'Yes' : 'No'}`);
        }
        console.log('');
      }
    }

    // Check integrations
    console.log('\n🔗 Checking integrations...');
    const integrations = await storage.getIntegrations();
    console.log(`Found ${integrations.length} integrations`);
    integrations.forEach(integration => {
      console.log(`  - ${integration.name} (${integration.type}, status: ${integration.status})`);
    });

    // Test embedding service
    console.log('\n🤖 Testing embedding service...');
    console.log(`Embedding service initialized: ${embeddingService.isInitialized()}`);
    
    if (embeddingService.isInitialized()) {
      try {
        const testEmbedding = await embeddingService.generateEmbedding('test query');
        console.log(`Test embedding generated: ${testEmbedding.length} dimensions`);
        
        // Test search for presentations specifically
        console.log('\n🔍 Testing vector search for presentations...');
        const searchResults = await embeddingService.searchSimilarChunks('presentation NeuroLink Edge version', [], 10);
        console.log(`Search results: ${searchResults.length} chunks found`);
        
        if (searchResults.length > 0) {
          console.log('Sample result:');
          console.log(`  - File: ${searchResults[0].fileName}`);
          console.log(`  - Content: "${searchResults[0].content.substring(0, 100)}..."`);
          console.log(`  - Similarity: ${searchResults[0].similarity}`);
        }
      } catch (error) {
        console.error('Error testing embedding service:', error.message);
      }
    }

  } catch (error) {
    console.error('Debug error:', error);
  }
}

debugRAG().then(() => {
  console.log('\n✅ Debug complete');
  process.exit(0);
}).catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
