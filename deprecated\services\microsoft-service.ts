// DEPRECATED: This file is being modularized for better maintainability
// New imports should use the modular structure from ./microsoft/
// This file is kept for backward compatibility during the migration

import { Client } from '@microsoft/microsoft-graph-client';
import { AuthenticationProvider } from '@microsoft/microsoft-graph-client';
// import { File, InsertFile } from '../../shared/schema.js'; // Deprecated - using any types

// Import the new modular Microsoft service facade
import { MicrosoftServiceFacade } from "../microsoft/microsoft.facade";

interface MicrosoftCredentials {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
  id_token?: string;
  expires_at?: number;
}

interface TeamsSource {
  id: string;
  name: string;
  type: 'teams_channel' | 'sharepoint_site' | 'onedrive_folder';
  parentId?: string;
  parentName?: string;
}

interface TeamsFileMetadata {
  _sourceType: 'teams_channel' | 'sharepoint_site' | 'onedrive_personal';
  _sourceFolderId: string;
  _teamId?: string;
  _channelId?: string;
  _siteId?: string;
  meetingId?: string;
  meetingType?: 'channel' | 'adhoc' | 'scheduled';
  organizer?: {
    displayName: string;
    email: string;
    id: string;
  };
  webUrl: string;
  driveId: string;
  parentReference?: {
    driveId: string;
    id: string;
    path: string;
  };
  shared?: boolean;
  permissions?: Array<{
    grantedTo?: {
      user?: { displayName: string; email: string; };
      application?: { displayName: string; };
    };
    roles: string[];
  }>;
}

class CustomAuthProvider implements AuthenticationProvider {
  private credentials: MicrosoftCredentials;

  constructor(credentials: MicrosoftCredentials) {
    this.credentials = credentials;
  }

  async getAccessToken(): Promise<string> {
    // Check if token is expired
    if (this.credentials.expires_at && Date.now() >= this.credentials.expires_at) {
      throw new Error('Token expired, refresh required');
    }
    return this.credentials.access_token;
  }
}

/**
 * Microsoft Service for Teams, OneDrive, and SharePoint integration
 * @deprecated Use MicrosoftServiceFacade from ./microsoft/ instead
 */
export class MicrosoftService {
  private clientId: string;
  private clientSecret: string;
  private tenantId: string;
  private redirectUri: string;

  constructor() {
    this.clientId = process.env.MICROSOFT_CLIENT_ID!;
    this.clientSecret = process.env.MICROSOFT_CLIENT_SECRET!;
    this.tenantId = process.env.MICROSOFT_TENANT_ID!;
    this.redirectUri = process.env.MICROSOFT_REDIRECT_URI!;

    if (!this.clientId || !this.clientSecret || !this.tenantId) {
      throw new Error('Microsoft Teams configuration missing. Please check environment variables.');
    }
  }

  /**
   * Get authorization URL for OAuth flow
   */
  getAuthUrl(state?: string): string {
    const baseUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/authorize`;
    const params = new URLSearchParams({
      client_id: this.clientId,
      response_type: 'code',
      redirect_uri: this.redirectUri,
      scope: 'https://graph.microsoft.com/Files.Read.All https://graph.microsoft.com/Sites.Read.All https://graph.microsoft.com/Group.Read.All https://graph.microsoft.com/User.Read https://graph.microsoft.com/Directory.Read.All https://graph.microsoft.com/OnlineMeetings.Read https://graph.microsoft.com/Calendars.Read https://graph.microsoft.com/Chat.Read https://graph.microsoft.com/ChannelMessage.Read.All offline_access',
      response_mode: 'query',
      ...(state && { state })
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code: string): Promise<MicrosoftCredentials> {
    const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
    
    const params = new URLSearchParams({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      code,
      redirect_uri: this.redirectUri,
      grant_type: 'authorization_code'
    });

    try {
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token exchange failed: ${response.status} ${errorData}`);
      }

      const tokenData = await response.json();
      
      // Add expiration timestamp
      const credentials: MicrosoftCredentials = {
        ...tokenData,
        expires_at: Date.now() + (tokenData.expires_in * 1000)
      };

      return credentials;
    } catch (error) {
      console.error('Error exchanging code for token:', error);
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<MicrosoftCredentials> {
    const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
    
    const params = new URLSearchParams({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      refresh_token: refreshToken,
      grant_type: 'refresh_token'
    });

    try {
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token refresh failed: ${response.status} ${errorData}`);
      }

      const tokenData = await response.json();
      
      return {
        ...tokenData,
        expires_at: Date.now() + (tokenData.expires_in * 1000)
      };
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }

  /**
   * Test connection to Microsoft Graph
   */
  async testConnection(credentials: MicrosoftCredentials): Promise<{success: boolean, user?: any, error?: string}> {
    try {
      const client = this.createGraphClient(credentials);
      const user = await client.api('/me').get();
      
      return {
        success: true,
        user: {
          displayName: user.displayName,
          mail: user.mail || user.userPrincipalName,
          id: user.id
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get available sources (Teams, SharePoint sites, OneDrive)
   */
  async getAvailableSources(credentials: MicrosoftCredentials): Promise<{
    teams: any[];
    sites: any[];
    drives: any[];
  }> {
    const client = this.createGraphClient(credentials);
    
    try {
      // Get user's teams
      const teamsResponse = await client.api('/me/joinedTeams').get();
      const teams = teamsResponse.value || [];

      // Get SharePoint sites
      const sitesResponse = await client.api('/sites?search=*').get();
      const sites = sitesResponse.value || [];

      // Get user's drives (OneDrive)
      const drivesResponse = await client.api('/me/drives').get();
      const drives = drivesResponse.value || [];

      return { teams, sites, drives };
    } catch (error) {
      console.error('Error getting available sources:', error);
      throw error;
    }
  }

  /**
   * Get Teams channels for a specific team
   */
  async getTeamsChannels(credentials: MicrosoftCredentials, teamId: string): Promise<any[]> {
    const client = this.createGraphClient(credentials);
    
    try {
      const response = await client.api(`/teams/${teamId}/channels`).get();
      return response.value || [];
    } catch (error) {
      console.error(`Error getting channels for team ${teamId}:`, error);
      throw error;
    }
  }

  /**
   * Sync files from a Teams channel with comprehensive file retrieval
   */
  async syncTeamsChannel(
    credentials: MicrosoftCredentials, 
    teamId: string, 
    channelId: string
  ): Promise<any[]> {
    const client = this.createGraphClient(credentials);
    
    try {
      console.log(`[MICROSOFT] Starting comprehensive sync for Teams channel ${channelId}`);
      
      // Get the channel's files folder
      const folderResponse = await client.api(`/teams/${teamId}/channels/${channelId}/filesFolder`).get();
      const driveId = folderResponse.parentReference.driveId;
      const folderId = folderResponse.id;

      console.log(`[MICROSOFT] Channel files folder - driveId: ${driveId}, folderId: ${folderId}`);

      // Get ALL files recursively from the channel folder
      const allFiles = await this.getAllFilesRecursively(client, driveId, folderId, credentials, teamId, channelId);

      console.log(`[MICROSOFT] Found ${allFiles.length} total files in channel ${channelId}`);

      return allFiles;
    } catch (error) {
      console.error(`[MICROSOFT] Error syncing Teams channel ${teamId}/${channelId}:`, error);
      throw error;
    }
  }

  /**
   * Sync files from OneDrive folder with comprehensive file retrieval
   */
  async syncOneDriveFolder(
    credentials: MicrosoftCredentials, 
    folderId: string = 'root'
  ): Promise<any[]> {
    const client = this.createGraphClient(credentials);
    
    try {
      console.log(`[MICROSOFT] Starting comprehensive sync for OneDrive folder ${folderId}`);
      
      // Get ALL files recursively from the OneDrive folder
      const allFiles = await this.getAllFilesRecursively(client, 'me', folderId, credentials, undefined, undefined, undefined);

      console.log(`[MICROSOFT] Found ${allFiles.length} total files in OneDrive folder ${folderId}`);

      return allFiles;
    } catch (error) {
      console.error(`[MICROSOFT] Error syncing OneDrive folder ${folderId}:`, error);
      throw error;
    }
  }

  /**
   * Create Microsoft Graph client with authentication
   */
  private createGraphClient(credentials: MicrosoftCredentials): Client {
    const authProvider = new CustomAuthProvider(credentials);
    return Client.initWithMiddleware({ authProvider });
  }

  /**
   * Determine file type based on file properties with comprehensive detection
   */
  private determineFileType(item: any): string {
    const name = item.name?.toLowerCase() || '';
    const mimeType = item.file?.mimeType?.toLowerCase() || '';
    const extension = this.getFileExtension(name);

    // Video files (including meeting recordings)
    if (mimeType.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'webm', 'mkv', 'flv'].includes(extension)) {
      // Check if it's specifically a meeting recording
      if (name.includes('recording') || name.includes('meeting recording') || name.includes('teams meeting')) {
        return 'recording'; // Separate type for meeting recordings
      }
      return 'video';
    }

    // Audio files
    if (mimeType.startsWith('audio/') || ['mp3', 'wav', 'm4a', 'wma', 'aac', 'flac', 'ogg'].includes(extension)) {
      return 'audio';
    }

    // Meeting transcripts (only text-based files)
    if ((name.includes('transcript') || name.includes('meeting notes') || name.includes('notes by gemini')) &&
        (mimeType.includes('text') || ['txt', 'vtt', 'srt', 'docx', 'doc'].includes(extension))) {
      return 'transcript';
    }

    // Documents
    if (mimeType.includes('document') || mimeType.includes('text') || 
        ['docx', 'doc', 'pdf', 'txt', 'rtf', 'odt'].includes(extension)) {
      return 'document';
    }

    // Presentations
    if (mimeType.includes('presentation') || 
        ['pptx', 'ppt', 'odp', 'key'].includes(extension)) {
      return 'presentation';
    }

    // Spreadsheets
    if (mimeType.includes('sheet') || 
        ['xlsx', 'xls', 'csv', 'ods', 'numbers'].includes(extension)) {
      return 'spreadsheet';
    }

    // Images
    if (mimeType.startsWith('image/') || 
        ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      return 'image';
    }

    // Archives
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
      return 'archive';
    }

    // Code files
    if (['js', 'ts', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs'].includes(extension)) {
      return 'code';
    }

    // Default based on extension if we don't know the type
    if (extension) {
      return extension;
    }

    return 'unknown';
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
  }

  /**
   * Get all files recursively from a folder, including subfolders
   */
  private async getAllFilesRecursively(
    client: Client, 
    driveId: string, 
    folderId: string, 
    credentials: MicrosoftCredentials,
    teamId?: string, 
    channelId?: string,
    siteId?: string,
    folderPath: string = ''
  ): Promise<any[]> {
    const allFiles: any[] = [];
    
    try {
      console.log(`[MICROSOFT] Scanning folder: ${folderId} at path: ${folderPath || 'root'}`);
      
      // Get items in current folder with expanded properties
      const response = await client.api(`/drives/${driveId}/items/${folderId}/children`)
        .select('id,name,file,folder,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference')
        .expand('thumbnails')
        .get();
      
      const items = response.value || [];
      console.log(`[MICROSOFT] Found ${items.length} items in folder ${folderId}`);
      
      for (const item of items) {
        if (item.file) {
          // This is a file - process it
          console.log(`[MICROSOFT] Processing file: ${item.name} (${item.id})`);
          
          // Check if this is a meeting-related file
          const isMeetingFile = this.determineFileType(item) === 'transcript' || 
                               item.name.toLowerCase().includes('meeting') ||
                               item.name.toLowerCase().includes('recording');
          
          // Extract enhanced metadata for meeting files
          let enhancedMeetingData: any = {};
          if (isMeetingFile) {
            console.log(`[MICROSOFT] Extracting enhanced meeting metadata for: ${item.name}`);
            try {
              const meetingId = this.extractMeetingId(item.name, item);
              enhancedMeetingData = await this.getEnhancedMeetingMetadata(
                credentials, 
                meetingId || undefined, 
                item.name
              );
            } catch (metadataError: any) {
              console.log(`[MICROSOFT] Could not extract enhanced metadata for ${item.name}:`, metadataError.message);
            }
          }
          
          const file = {
            externalId: item.id,
            fileName: item.name, // Use the ACTUAL file name, not folder name
            fileType: this.determineFileType(item),
            platform: 'microsoft_teams',
            sourceUrl: item.webUrl,
            userId: item.createdBy?.user?.id || item.lastModifiedBy?.user?.id || 'unknown',
            lastModified: item.lastModifiedDateTime ? new Date(item.lastModifiedDateTime) : new Date(),
            extractedMetadata: {
              _sourceType: teamId ? 'teams_channel' : siteId ? 'sharepoint_site' : 'onedrive_personal',
              _sourceFolderId: teamId ? channelId : folderId,
              ...(teamId && { _teamId: teamId }),
              ...(channelId && { _channelId: channelId }),
              ...(siteId && { _siteId: siteId }),
              webUrl: item.webUrl,
              driveId: driveId,
              parentReference: item.parentReference,
              folderPath: folderPath,
              // Resolved folder name instead of ID
              folderName: folderPath ? folderPath.split('/').pop() || 'Unknown Folder' : 'Root',
              // Resolved owner name instead of ID
              ownerName: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
              ownerEmail: item.createdBy?.user?.userPrincipalName || item.lastModifiedBy?.user?.userPrincipalName || null,
              fileExtension: this.getFileExtension(item.name),
              mimeType: item.file?.mimeType || 'unknown',
              shared: item.shared !== undefined,
              lastModifiedDateTime: item.lastModifiedDateTime,
              createdDateTime: item.createdDateTime,
              size: item.size || 0,
              createdBy: item.createdBy?.user?.displayName,
              lastModifiedBy: item.lastModifiedBy?.user?.displayName,
              owner: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
              hasContent: true,
              isDownloadable: true,
              isMeetingFile,
              // Enhanced meeting metadata
              ...(enhancedMeetingData.onlineMeeting && {
                meetingSubject: enhancedMeetingData.onlineMeeting.subject,
                meetingStartTime: enhancedMeetingData.onlineMeeting.startDateTime,
                meetingEndTime: enhancedMeetingData.onlineMeeting.endDateTime,
                meetingOrganizer: enhancedMeetingData.onlineMeeting.organizer,
                meetingAttendees: enhancedMeetingData.onlineMeeting.attendees,
                meetingJoinUrl: enhancedMeetingData.onlineMeeting.joinUrl,
                conferenceId: enhancedMeetingData.onlineMeeting.conferenceId
              }),
              ...(enhancedMeetingData.calendarEvent && {
                calendarSubject: enhancedMeetingData.calendarEvent.subject,
                calendarStartTime: enhancedMeetingData.calendarEvent.startDateTime,
                calendarEndTime: enhancedMeetingData.calendarEvent.endDateTime,
                calendarOrganizer: enhancedMeetingData.calendarEvent.organizer,
                calendarAttendees: enhancedMeetingData.calendarEvent.attendees,
                isOnlineMeeting: enhancedMeetingData.calendarEvent.isOnlineMeeting
              }),
              ...(enhancedMeetingData.userTeams && {
                userTeamsContext: enhancedMeetingData.userTeams
              })
            } as TeamsFileMetadata
          };
          
          allFiles.push(file);
        } else if (item.folder) {
          // This is a folder - recurse into it
          const subFolderPath = folderPath ? `${folderPath}/${item.name}` : item.name;
          console.log(`[MICROSOFT] Recursing into subfolder: ${item.name} (${item.id})`);
          
          try {
            const subFiles = await this.getAllFilesRecursively(
              client, 
              driveId, 
              item.id, 
              credentials,
              teamId, 
              channelId, 
              siteId, 
              subFolderPath
            );
            allFiles.push(...subFiles);
          } catch (subFolderError) {
            console.error(`[MICROSOFT] Error scanning subfolder ${item.name}:`, subFolderError);
            // Continue with other folders
          }
        }
      }
      
      return allFiles;
    } catch (error) {
      console.error(`[MICROSOFT] Error getting files from folder ${folderId}:`, error);
      throw error;
    }
  }

  /**
   * Comprehensive sync method to aggregate files from all Microsoft sources
   * Includes Teams channels, SharePoint sites, and OneDrive folders
   */
  async syncAllSources(credentials: MicrosoftCredentials): Promise<{
    files: any[];
    sources: {
      teams: number;
      sharepoint: number;
      onedrive: number;
    };
    summary: {
      total: number;
      byType: Record<string, number>;
      bySource: Record<string, number>;
    };
  }> {
    const client = this.createGraphClient(credentials);
    const allFiles: any[] = [];
    const sources = { teams: 0, sharepoint: 0, onedrive: 0 };
    const byType: Record<string, number> = {};
    const bySource: Record<string, number> = {};

    try {
      console.log('[MICROSOFT] Starting comprehensive sync of all sources...');

      // 1. Get all available sources
      const availableSources = await this.getAvailableSources(credentials);
      console.log('[MICROSOFT] Available sources:', {
        teams: availableSources.teams.length,
        sites: availableSources.sites.length,
        drives: availableSources.drives.length
      });

      // 2. Sync Teams channels
      for (const team of availableSources.teams) {
        try {
          console.log(`[MICROSOFT] Syncing team: ${team.displayName} (${team.id})`);
          const channels = await this.getTeamsChannels(credentials, team.id);
          
          for (const channel of channels) {
            try {
              console.log(`[MICROSOFT] Syncing channel: ${channel.displayName} (${channel.id})`);
              const channelFiles = await this.syncTeamsChannel(credentials, team.id, channel.id);
              
              // Add team and channel context to metadata
              channelFiles.forEach(file => {
                const currentMetadata = file.extractedMetadata as TeamsFileMetadata;
                file.extractedMetadata = {
                  ...currentMetadata,
                  _teamName: team.displayName,
                  _channelName: channel.displayName,
                  _sourceContext: `Team: ${team.displayName} / Channel: ${channel.displayName}`
                } as TeamsFileMetadata;
              });
              
              allFiles.push(...channelFiles);
              sources.teams += channelFiles.length;
              
              // Count by type
              channelFiles.forEach(file => {
                byType[file.fileType] = (byType[file.fileType] || 0) + 1;
                bySource['teams_channel'] = (bySource['teams_channel'] || 0) + 1;
              });
              
              console.log(`[MICROSOFT] Found ${channelFiles.length} files in channel ${channel.displayName}`);
            } catch (channelError) {
              console.error(`[MICROSOFT] Error syncing channel ${channel.displayName}:`, channelError);
              // Continue with other channels
            }
          }
        } catch (teamError) {
          console.error(`[MICROSOFT] Error syncing team ${team.displayName}:`, teamError);
          // Continue with other teams
        }
      }

      // 3. Sync SharePoint sites
      for (const site of availableSources.sites.slice(0, 10)) { // Limit to first 10 sites to avoid overwhelming
        try {
          console.log(`[MICROSOFT] Syncing SharePoint site: ${site.displayName || site.name} (${site.id})`);
          const siteFiles = await this.syncSharePointSite(credentials, site.id);
          
          // Add site context to metadata
          siteFiles.forEach(file => {
            const currentMetadata = file.extractedMetadata as TeamsFileMetadata;
            file.extractedMetadata = {
              ...currentMetadata,
              _siteName: site.displayName || site.name,
              _sourceContext: `SharePoint Site: ${site.displayName || site.name}`
            } as TeamsFileMetadata;
          });
          
          allFiles.push(...siteFiles);
          sources.sharepoint += siteFiles.length;
          
          // Count by type
          siteFiles.forEach(file => {
            byType[file.fileType] = (byType[file.fileType] || 0) + 1;
            bySource['sharepoint_site'] = (bySource['sharepoint_site'] || 0) + 1;
          });
          
          console.log(`[MICROSOFT] Found ${siteFiles.length} files in SharePoint site ${site.displayName || site.name}`);
        } catch (siteError) {
          console.error(`[MICROSOFT] Error syncing SharePoint site ${site.displayName || site.name}:`, siteError);
          // Continue with other sites
        }
      }

      // 4. Sync OneDrive folders
      for (const drive of availableSources.drives) {
        try {
          console.log(`[MICROSOFT] Syncing OneDrive: ${drive.name} (${drive.id})`);
          const driveFiles = await this.getAllFilesRecursively(client, drive.id, 'root', credentials, undefined, undefined, undefined);
          
          // Add drive context to metadata
          driveFiles.forEach(file => {
            const currentMetadata = file.extractedMetadata as TeamsFileMetadata;
            file.extractedMetadata = {
              ...currentMetadata,
              _driveName: drive.name,
              _sourceContext: `OneDrive: ${drive.name}`
            } as TeamsFileMetadata;
          });
          
          allFiles.push(...driveFiles);
          sources.onedrive += driveFiles.length;
          
          // Count by type
          driveFiles.forEach(file => {
            byType[file.fileType] = (byType[file.fileType] || 0) + 1;
            bySource['onedrive_personal'] = (bySource['onedrive_personal'] || 0) + 1;
          });
          
          console.log(`[MICROSOFT] Found ${driveFiles.length} files in OneDrive ${drive.name}`);
        } catch (driveError) {
          console.error(`[MICROSOFT] Error syncing OneDrive ${drive.name}:`, driveError);
          // Continue with other drives
        }
      }

      const summary = {
        total: allFiles.length,
        byType,
        bySource
      };

      console.log('[MICROSOFT] Comprehensive sync completed:', {
        totalFiles: allFiles.length,
        sources,
        summary
      });

      return {
        files: allFiles,
        sources,
        summary
      };

    } catch (error) {
      console.error('[MICROSOFT] Error in comprehensive sync:', error);
      throw error;
    }
  }

  /**
   * Sync files from a SharePoint site with comprehensive file retrieval
   */
  async syncSharePointSite(
    credentials: MicrosoftCredentials, 
    siteId: string
  ): Promise<any[]> {
    const client = this.createGraphClient(credentials);
    
    try {
      console.log(`[MICROSOFT] Starting comprehensive sync for SharePoint site ${siteId}`);
      
      // Get the default document library for the site
      const drivesResponse = await client.api(`/sites/${siteId}/drives`).get();
      const drives = drivesResponse.value || [];
      
      const allFiles: any[] = [];
      
      for (const drive of drives) {
        try {
          console.log(`[MICROSOFT] Syncing SharePoint drive: ${drive.name} (${drive.id})`);
          
          // Get ALL files recursively from the drive root
          const driveFiles = await this.getAllFilesRecursively(client, drive.id, 'root', credentials, undefined, undefined, siteId);
          
          // Add drive context to metadata
          driveFiles.forEach(file => {
            const currentMetadata = file.extractedMetadata as TeamsFileMetadata;
            file.extractedMetadata = {
              ...currentMetadata,
              _driveName: drive.name,
              _driveId: drive.id,
              _sourceContext: `SharePoint Drive: ${drive.name}`
            } as TeamsFileMetadata;
          });
          
          allFiles.push(...driveFiles);
          console.log(`[MICROSOFT] Found ${driveFiles.length} files in drive ${drive.name}`);
        } catch (driveError) {
          console.error(`[MICROSOFT] Error syncing drive ${drive.name} in site ${siteId}:`, driveError);
          // Continue with other drives
        }
      }

      console.log(`[MICROSOFT] Found ${allFiles.length} total files in SharePoint site ${siteId}`);
      return allFiles;
    } catch (error) {
      console.error(`[MICROSOFT] Error syncing SharePoint site ${siteId}:`, error);
      throw error;
    }
  }

  /**
   * Get folders structure for Teams/OneDrive/SharePoint selection
   * Similar to Google Drive folder listing but for Microsoft sources
   */
  async getFoldersForSelection(credentials: MicrosoftCredentials): Promise<{
    user: {
      displayName: string;
      email: string;
      id: string;
    };
    sources: Array<{
      id: string;
      name: string;
      type: 'teams_channel' | 'onedrive_folder' | 'sharepoint_site';
      parentId?: string;
      parentName?: string;
      webUrl?: string;
      description?: string;
      itemCount?: number;
    }>;
  }> {
    const client = this.createGraphClient(credentials);
    
    try {
      // Get user info
      const userResponse = await client.api('/me').get();
      const user = {
        displayName: userResponse.displayName,
        email: userResponse.mail || userResponse.userPrincipalName,
        id: userResponse.id
      };

      const allSources: Array<{
        id: string;
        name: string;
        type: 'teams_channel' | 'onedrive_folder' | 'sharepoint_site';
        parentId?: string;
        parentName?: string;
        webUrl?: string;
        description?: string;
        itemCount?: number;
      }> = [];

      // 1. Get OneDrive folders
      try {
        const oneDriveResponse = await client.api('/me/drive/root/children').get();
        const oneDriveItems = oneDriveResponse.value || [];
        
        for (const item of oneDriveItems) {
          if (item.folder) { // Only folders
            // Get item count for the folder
            let itemCount = 0;
            try {
              const childrenResponse = await client.api(`/me/drive/items/${item.id}/children`).get();
              itemCount = childrenResponse.value?.length || 0;
            } catch (error) {
              // Continue if we can't get children count
            }

            allSources.push({
              id: item.id,
              name: item.name,
              type: 'onedrive_folder',
              webUrl: item.webUrl,
              description: `OneDrive folder`,
              itemCount
            });
          }
        }
      } catch (error) {
        console.error('Error fetching OneDrive folders:', error);
        // Continue without OneDrive folders
      }

      // 2. Get Teams and their channels
      try {
        const teamsResponse = await client.api('/me/joinedTeams').get();
        const teams = teamsResponse.value || [];
        
        for (const team of teams) {
          try {
            const channelsResponse = await client.api(`/teams/${team.id}/channels`).get();
            const channels = channelsResponse.value || [];
            
            for (const channel of channels) {
              // Get file count for the channel
              let itemCount = 0;
              try {
                const folderResponse = await client.api(`/teams/${team.id}/channels/${channel.id}/filesFolder`).get();
                const driveId = folderResponse.parentReference.driveId;
                const folderId = folderResponse.id;
                const filesResponse = await client.api(`/drives/${driveId}/items/${folderId}/children`).get();
                itemCount = filesResponse.value?.length || 0;
              } catch (error) {
                // Continue if we can't get file count
              }

              allSources.push({
                id: channel.id,
                name: `${channel.displayName}`,
                type: 'teams_channel',
                parentId: team.id,
                parentName: team.displayName,
                webUrl: channel.webUrl,
                description: `Team: ${team.displayName}`,
                itemCount
              });
            }
          } catch (error) {
            console.error(`Error fetching channels for team ${team.displayName}:`, error);
            // Continue with other teams
          }
        }
      } catch (teamsError: any) {
        console.log(`[MICROSOFT] Could not get user teams:`, teamsError.message);
        // Continue without Teams
      }

      // 3. Get SharePoint sites (limit to first 10 for performance)
      try {
        const sitesResponse = await client.api('/sites?search=*').get();
        const sites = sitesResponse.value?.slice(0, 10) || [];
        
        for (const site of sites) {
          try {
            // Get drives for this site
            const drivesResponse = await client.api(`/sites/${site.id}/drives`).get();
            const drives = drivesResponse.value || [];
            
            for (const drive of drives) {
              // Get item count for the drive
              let itemCount = 0;
              try {
                const filesResponse = await client.api(`/drives/${drive.id}/root/children`).get();
                itemCount = filesResponse.value?.length || 0;
              } catch (error) {
                // Continue if we can't get item count
              }

              allSources.push({
                id: drive.id,
                name: `${drive.name}`,
                type: 'sharepoint_site',
                parentId: site.id,
                parentName: site.displayName || site.name,
                webUrl: drive.webUrl,
                description: `SharePoint: ${site.displayName || site.name}`,
                itemCount
              });
            }
          } catch (error) {
            console.error(`Error fetching drives for site ${site.displayName || site.name}:`, error);
            // Continue with other sites
          }
        }
      } catch (error) {
        console.error('Error fetching SharePoint sites:', error);
        // Continue without SharePoint sites
      }

      return {
        user,
        sources: allSources
      };
    } catch (error) {
      console.error('Error getting folders for selection:', error);
      throw error;
    }
  }

  /**
   * Extract meeting ID from filename or metadata
   */
  private extractMeetingId(fileName: string, metadata?: any): string | null {
    // Common patterns for Teams meeting files
    const patterns = [
      /Meeting ID[:\s]+([A-Za-z0-9\-_]+)/i,
      /meetingId[=:\s]+([A-Za-z0-9\-_]+)/i,
      /_(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})/,  // ISO date pattern
      /([A-Za-z0-9]{8}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{12})/  // UUID pattern
    ];

    for (const pattern of patterns) {
      const match = fileName.match(pattern);
      if (match) {
        return match[1];
      }
    }

    // Check metadata if available
    if (metadata?.meetingId) {
      return metadata.meetingId;
    }

    return null;
  }

  /**
   * Get rich meeting metadata including attendees and organizer info
   */
  async getEnhancedMeetingMetadata(credentials: MicrosoftCredentials, meetingId?: string, fileName?: string): Promise<any> {
    const client = this.createGraphClient(credentials);
    
    try {
      console.log(`[MICROSOFT] Retrieving enhanced meeting metadata for: ${fileName || meetingId}`);
      
      let meetingData: any = {};

      // If we have a meeting ID, get the online meeting details
      if (meetingId) {
        try {
          const onlineMeeting = await client.api(`/me/onlineMeetings/${meetingId}`).get();
          meetingData.onlineMeeting = {
            subject: onlineMeeting.subject,
            startDateTime: onlineMeeting.startDateTime,
            endDateTime: onlineMeeting.endDateTime,
            organizer: onlineMeeting.participants?.organizers?.[0],
            attendees: onlineMeeting.participants?.attendees || [],
            joinUrl: onlineMeeting.joinUrl,
            conferenceId: onlineMeeting.audioConferencing?.conferenceId,
            lobbyBypassSettings: onlineMeeting.lobbyBypassSettings
          };
        } catch (meetingError: any) {
          console.log(`[MICROSOFT] Could not get online meeting details for ${meetingId}:`, meetingError.message);
        }
      }

      // Try to find related calendar events by searching recent events
      if (fileName) {
        try {
          const now = new Date();
          const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
          
          const events = await client.api('/me/events')
            .filter(`start/dateTime ge '${thirtyDaysAgo.toISOString()}' and start/dateTime le '${now.toISOString()}'`)
            .select('subject,start,end,organizer,attendees,onlineMeeting,isOnlineMeeting')
            .top(50)
            .get();

          // Find matching event by subject similarity or meeting details
          for (const event of events.value || []) {
            if (this.isRelatedMeetingEvent(fileName, event)) {
              meetingData.calendarEvent = {
                subject: event.subject,
                startDateTime: event.start?.dateTime,
                endDateTime: event.end?.dateTime,
                organizer: event.organizer,
                attendees: event.attendees || [],
                isOnlineMeeting: event.isOnlineMeeting,
                onlineMeetingJoinUrl: event.onlineMeeting?.joinUrl
              };
              break;
            }
          }
        } catch (eventsError: any) {
          console.log(`[MICROSOFT] Could not search calendar events:`, eventsError.message);
        }
      }

      // Get user's Teams and channels for context
      try {
        const teams = await client.api('/me/joinedTeams').select('id,displayName').get();
        meetingData.userTeams = teams.value || [];
      } catch (teamsError: any) {
        console.log(`[MICROSOFT] Could not get user teams:`, teamsError.message);
      }

      return meetingData;
    } catch (error) {
      console.error(`[MICROSOFT] Error getting enhanced meeting metadata:`, error);
      return {};
    }
  }

  /**
   * Check if a calendar event is related to the meeting file
   */
  private isRelatedMeetingEvent(fileName: string, event: any): boolean {
    const fileNameLower = fileName.toLowerCase();
    const subject = event.subject?.toLowerCase() || '';
    
    // Check for direct subject match
    if (subject && fileNameLower.includes(subject)) {
      return true;
    }

    // Check if it's an online meeting
    if (event.isOnlineMeeting) {
      // Extract date from filename and compare
      const dateMatch = fileName.match(/(\d{4})-?(\d{2})-?(\d{2})/);
      if (dateMatch && event.start?.dateTime) {
        const fileDate = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
        const eventDate = event.start.dateTime.split('T')[0];
        if (fileDate === eventDate) {
          return true;
        }
      }

      // Check for time patterns
      const timeMatch = fileName.match(/(\d{2}):?(\d{2})/);
      if (timeMatch && event.start?.dateTime) {
        const fileTime = `${timeMatch[1]}:${timeMatch[2]}`;
        const eventTime = new Date(event.start.dateTime).toTimeString().substring(0, 5);
        if (fileTime === eventTime) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Get chat history for a Teams channel or meeting
   */
  async getChatHistory(credentials: MicrosoftCredentials, teamId?: string, channelId?: string, chatId?: string): Promise<any[]> {
    const client = this.createGraphClient(credentials);
    
    try {
      let messages: any[] = [];

      if (teamId && channelId) {
        // Get channel messages
        const response = await client.api(`/teams/${teamId}/channels/${channelId}/messages`)
          .top(50)
          .select('id,body,from,createdDateTime,attachments,mentions')
          .get();
        messages = response.value || [];
      } else if (chatId) {
        // Get chat messages
        const response = await client.api(`/chats/${chatId}/messages`)
          .top(50)
          .select('id,body,from,createdDateTime,attachments,mentions')
          .get();
        messages = response.value || [];
      }

      return messages.map(msg => ({
        id: msg.id,
        content: msg.body?.content,
        contentType: msg.body?.contentType,
        sender: msg.from?.user?.displayName,
        senderEmail: msg.from?.user?.userPrincipalName,
        timestamp: msg.createdDateTime,
        attachments: msg.attachments || [],
        mentions: msg.mentions || []
      }));
    } catch (error) {
      console.error(`[MICROSOFT] Error getting chat history:`, error);
      return [];
    }
  }

  /**
   * Download file content and extract text for PDFs and Word documents
   */
  async downloadAndExtractFileContent(
    credentials: MicrosoftCredentials,
    driveId: string,
    fileId: string,
    fileName: string,
    mimeType?: string
  ): Promise<string | null> {
    const client = this.createGraphClient(credentials);

    try {
      // Import the centralized services
      const { pdfService } = await import('../pdf-service.js');
      const { wordService } = await import('../word-service.js');

      // Check if it's a PDF file
      const { PDFService } = await import('../pdf-service.js');
      const { WordService } = await import('../word-service.js');
      const isPdf = PDFService.isPDFFile(fileName, mimeType);

      // Check if it's a Word document
      const isWord = WordService.isWordFile(fileName, mimeType);

      if (!isPdf && !isWord) {
        console.log(`[MICROSOFT] Skipping content extraction for unsupported file type: ${fileName}`);
        return null;
      }
      
      const fileType = isPdf ? 'PDF' : 'Word document';
      console.log(`[MICROSOFT] Downloading ${fileType} content for: ${fileName}`);
      console.log(`[MICROSOFT] Using driveId: ${driveId}, fileId: ${fileId}`);

      // Download the file content with proper response type
      const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
        .responseType('arraybuffer' as any)
        .get();

      console.log(`[MICROSOFT] Download response type: ${typeof downloadResponse}`);
      console.log(`[MICROSOFT] Download response constructor: ${downloadResponse?.constructor?.name}`);

      if (!downloadResponse) {
        console.log(`[MICROSOFT] No content received for file: ${fileName}`);
        return null;
      }

      // Convert response to buffer
      let buffer: Buffer;

      if (Buffer.isBuffer(downloadResponse)) {
        console.log(`[MICROSOFT] Response is already a Buffer`);
        buffer = downloadResponse;
      } else if (downloadResponse instanceof ArrayBuffer) {
        console.log(`[MICROSOFT] Response is ArrayBuffer, converting to Buffer`);
        buffer = Buffer.from(downloadResponse);
      } else if (downloadResponse instanceof Uint8Array) {
        console.log(`[MICROSOFT] Response is Uint8Array, converting to Buffer`);
        buffer = Buffer.from(downloadResponse);
      } else if (typeof downloadResponse === 'string') {
        console.log(`[MICROSOFT] Response is string, converting to Buffer`);
        buffer = Buffer.from(downloadResponse, 'binary');
      } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
        console.log(`[MICROSOFT] Response has buffer property, using that`);
        buffer = Buffer.from(downloadResponse.buffer);
      } else {
        console.log(`[MICROSOFT] Unexpected response type for file: ${fileName}`);
        console.log(`[MICROSOFT] Response details:`, {
          type: typeof downloadResponse,
          constructor: downloadResponse?.constructor?.name,
          hasBuffer: !!(downloadResponse && typeof downloadResponse === 'object' && 'buffer' in downloadResponse),
          keys: downloadResponse && typeof downloadResponse === 'object' ? Object.keys(downloadResponse).slice(0, 5) : []
        });
        return null;
      }

      console.log(`[MICROSOFT] Downloaded ${buffer.length} bytes for: ${fileName}`);

      // Extract text based on file type
      let extractionResult;

      if (isPdf) {
        // Use centralized PDF service to extract text
        extractionResult = await pdfService.extractTextFromPDF(buffer, fileName);
      } else if (isWord) {
        // Use centralized Word service to extract text
        extractionResult = await wordService.extractTextFromWord(buffer, fileName);
      }

      if (!extractionResult || !extractionResult.success || !extractionResult.extractedText) {
        console.log(`[MICROSOFT] ${fileType} text extraction failed: ${extractionResult?.error || 'Unknown error'}`);
        return null;
      }

      console.log(`[MICROSOFT] Successfully extracted ${extractionResult.extractedText.length} characters from ${fileType}: ${fileName}`);
      return extractionResult.extractedText;
      
    } catch (error: any) {
      console.error(`[MICROSOFT] Error downloading/extracting content for ${fileName}:`, error.message);
      console.error(`[MICROSOFT] Full error:`, error);
      return null;
    }
  }
}

// DEPRECATED: Use the new modular Microsoft service instead
// This is kept for backward compatibility during migration

// Use the new modular Microsoft service facade
export const microsoftService = new MicrosoftServiceFacade();

// Legacy Microsoft service class (kept for reference during migration)
export const legacyMicrosoftService = new MicrosoftService();