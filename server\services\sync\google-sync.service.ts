import { storage } from "../../storage/index.js";
import { googleService } from "../platform-integrations/google";
import { getFileProcessor } from './processors';
import { BaseService } from "../base/service.interface";

/**
 * Google Drive sync service for reference-based synchronization
 */
export class GoogleSyncService {
  /**
   * Run Google Drive sync for an integration
   * @param integration Integration to sync
   * @param syncLogId ID of the sync log
   */
  async runSync(integration: any, syncLogId: number): Promise<void> {
    let itemsProcessed = 0;
    let itemsSuccess = 0;
    let itemsFailed = 0;
    let itemsSkipped = 0;

    try {
      console.log(`Starting incremental reference-based Google Drive sync for integration ${integration.id}`);

      // Get the source config - support both single folder (legacy) and multiple folders (new)
      const sourceConfig = integration.sourceConfig || {};
      let sourceFolderIds: string[] = [];

      if (sourceConfig.driveIds && Array.isArray(sourceConfig.driveIds)) {
        // New multi-folder configuration
        sourceFolderIds = sourceConfig.driveIds;
      } else if (sourceConfig.driveId) {
        // Legacy single folder configuration
        sourceFolderIds = [sourceConfig.driveId];
      } else {
        // Default to root if no configuration
        sourceFolderIds = ['root'];
      }

      console.log(`Scanning Google Drive folders: ${sourceFolderIds.join(', ')}`);

      // Get Google auth client
      const auth = await googleService.getAuthorizedClient(integration.credentials);

      // Get ALL files from all selected Google Drive folders
      let allFiles: any[] = [];
      for (const folderId of sourceFolderIds) {
        console.log(`Scanning folder: ${folderId}`);
        const folderFiles = await googleService.listAllFiles(auth, folderId, true);
        console.log(`Found ${folderFiles.length} files in folder ${folderId}`);

        // Add folder source info to each file for tracking
        const filesWithSource = folderFiles.map(file => ({
          ...file,
          _sourceFolderId: folderId
        }));

        allFiles = allFiles.concat(filesWithSource);
      }

      console.log(`Found ${allFiles.length} total files in Google Drive`);

      // Get existing files for this platform
      const existingFiles = await storage.getFilesForIntegration(integration.id, 'google_drive');
      console.log(`Found ${existingFiles.length} existing files in database`);

      // Create a map of existing files by externalId for quick lookup
      const existingFileMap = new Map<string, any>();
      for (const file of existingFiles) {
        existingFileMap.set(file.externalId, file);
      }

      // Track which external IDs we've seen in this sync
      const foundExternalIds = new Set<string>();

      // Update sync log with total files count
      await storage.updateSyncLog(syncLogId, {
        details: {
          totalFiles: allFiles.length,
          existingFiles: existingFiles.length,
          scanMode: 'incremental_reference_based',
          sourceFolderIds: sourceFolderIds,
        },
      });

      // Process each file for metadata extraction and reference storage
      for (const file of allFiles) {
        itemsProcessed++;
        foundExternalIds.add(file.id || '');

        // Minimal delay between files (sequential processing is very safe)
        if (itemsProcessed > 1) {
          console.log('Waiting 200ms before processing next file...');
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        try {
          // Check if we already have this file in our database
          const existingFile = existingFileMap.get(file.id || '');

          // Extract comprehensive metadata
          const fileMetadata = googleService.extractFileMetadata(file);

          if (!fileMetadata) {
            console.warn(`Could not extract metadata for file: ${file.name} (${file.id})`);
            itemsFailed++;
            continue;
          }

          // Process the file and handle vectorization using the new modular processor
          const processor = await getFileProcessor('google_drive');
          const result = await processor.processFile(
            { ...file, metadata: fileMetadata }, // Combine file data with metadata for the new interface
            existingFile,
            integration
          );

          if (result.success) {
            itemsSuccess++;
            
            // Create a successful sync item for tracking
            await storage.createSyncItem({
              type: fileMetadata.fileType,
              title: fileMetadata.fileName,
              status: "success",
              integrationId: integration.id,
              syncLogId,
              externalId: fileMetadata.externalId,
              sourceUrl: fileMetadata.sourceUrl,
              destinationUrl: null, // We're not creating Notion pages anymore
              metadata: {
                platform: 'google_drive',
                fileId: result.savedFile?.id,
                referenceSync: true,
                incremental: true,
                wasUpdated: !!existingFile,
              },
            });
          } else {
            itemsFailed++;
            
            // Create a failed sync item for tracking
            await storage.createSyncItem({
              type: "unknown",
              title: file.name || "Unknown file",
              status: "failed",
              integrationId: integration.id,
              syncLogId,
              externalId: file.id || "",
              sourceUrl: file.webViewLink || "",
              error: result.error || "Unknown error occurred",
              metadata: {
                platform: 'google_drive',
                referenceSync: true,
                incremental: true,
              },
            });
          }

          if (result.skipped) {
            itemsSkipped++;
          }

        } catch (error) {
          console.error(`Error processing Google file ${file.name} (${file.id}):`, error);
          itemsFailed++;
        }

        // Update sync log progress periodically
        if (itemsProcessed % 50 === 0) {
          await storage.updateSyncLog(syncLogId, {
            itemsProcessed,
            itemsSuccess,
            itemsFailed,
          });
        }
      }

      // Mark files as deleted if they're no longer in the source
      let deletedCount = 0;
      const missingExternalIds: string[] = [];

      for (const existingFile of existingFiles) {
        if (!foundExternalIds.has(existingFile.externalId) && existingFile.status === 'active') {
          missingExternalIds.push(existingFile.externalId);
          deletedCount++;
        }
      }

      if (missingExternalIds.length > 0) {
        console.log(`Marking ${missingExternalIds.length} files as deleted for platform: google_drive`);
        await storage.markFilesAsDeleted(missingExternalIds, 'google_drive');
        console.log(`Marked ${deletedCount} files as deleted (no longer in source)`);
      }

      // Final sync log update
      const syncStatus = itemsFailed === 0 ? "success" : itemsSuccess > 0 ? "partial" : "failed";

      await storage.updateSyncLog(syncLogId, {
        status: syncStatus,
        endTime: new Date(),
        itemsProcessed,
        itemsSuccess,
        itemsFailed,
        details: {
          totalFiles: allFiles.length,
          existingFiles: existingFiles.length,
          newFiles: itemsSuccess - (itemsProcessed - allFiles.length),
          updatedFiles: itemsProcessed - allFiles.length,
          skippedFiles: itemsSkipped,
          deletedFiles: deletedCount,
          scanMode: 'incremental_reference_based',
          sourceFolderIds: sourceFolderIds,
          completedAt: new Date().toISOString(),
        },
      });

      // Update integration status and lastSyncAt
      await storage.updateIntegrationStatus(
        integration.id,
        syncStatus === "failed" ? "error" : "connected"
      );

      // Update lastSyncAt timestamp for successful or partial syncs
      if (syncStatus !== "failed") {
        await storage.updateIntegrationLastSync(integration.id, new Date());
      }

      console.log(`Incremental sync completed. Processed: ${itemsProcessed}, Success: ${itemsSuccess}, Failed: ${itemsFailed}, Skipped: ${itemsSkipped}, Deleted: ${deletedCount}`);

    } catch (error: any) {
      console.error(`Error in incremental reference-based Google Drive sync:`, error);

      // Update sync log with error
      await storage.updateSyncLog(syncLogId, {
        status: "failed",
        endTime: new Date(),
        itemsProcessed,
        itemsSuccess,
        itemsFailed,
        error: error.message || "Unknown error occurred",
      });

      // Update integration status
      await storage.updateIntegrationStatus(integration.id, "error");

      throw error;
    }
  }


}

export const googleSyncService = new GoogleSyncService(); 