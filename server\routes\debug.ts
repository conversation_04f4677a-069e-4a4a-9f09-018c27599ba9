import type { Express } from "express";
import { storage } from "../storage/index.js";
import { simpleEmbeddingService } from "../services/ai/simple-embedding-service.js";
import { unifiedContentExtractor } from "../services/ai/unified-content-extractor.service.js";

/**
 * Register debug and diagnostic routes for troubleshooting the RAG system
 */
export function registerDebugRoutes(app: Express) {
  
  // Debug endpoint to check file processing status
  app.get('/api/debug/files-status', async (req, res) => {
    try {
      console.log('🔍 Debug: Checking file processing status...');
      
      // Get all files from all platforms
      const allFiles = await storage.getFiles();
      
      // Group files by platform
      const filesByPlatform: Record<string, any[]> = {};
      const embeddingStatus: Record<string, any> = {};
      
      for (const file of allFiles) {
        const platform = file.platform || 'unknown';
        if (!filesByPlatform[platform]) {
          filesByPlatform[platform] = [];
        }
        filesByPlatform[platform].push(file);
        
        // Check if file has embeddings
        const hasEmbeddings = await storage.getFileChunks(file.id);
        embeddingStatus[file.id] = {
          fileName: file.fileName,
          platform: file.platform,
          fileType: file.fileType,
          hasEmbeddings: hasEmbeddings.length > 0,
          chunkCount: hasEmbeddings.length,
          hasFileContent: !!(file.fileContent && file.fileContent.length > 0),
          fileContentLength: file.fileContent?.length || 0,
          status: file.status,
          lastModified: file.lastModified,
          createdAt: file.createdAt
        };
      }
      
      // Calculate statistics
      const totalFiles = allFiles.length;
      const filesWithEmbeddings = Object.values(embeddingStatus).filter(f => f.hasEmbeddings).length;
      const filesWithContent = Object.values(embeddingStatus).filter(f => f.hasFileContent).length;
      
      const platformStats = Object.entries(filesByPlatform).map(([platform, files]) => ({
        platform,
        totalFiles: files.length,
        filesWithEmbeddings: files.filter(f => embeddingStatus[f.id]?.hasEmbeddings).length,
        filesWithContent: files.filter(f => embeddingStatus[f.id]?.hasFileContent).length,
        avgContentLength: files
          .filter(f => embeddingStatus[f.id]?.hasFileContent)
          .reduce((sum, f) => sum + (embeddingStatus[f.id]?.fileContentLength || 0), 0) / 
          Math.max(1, files.filter(f => embeddingStatus[f.id]?.hasFileContent).length)
      }));
      
      res.json({
        summary: {
          totalFiles,
          filesWithEmbeddings,
          filesWithContent,
          embeddingCoverage: totalFiles > 0 ? (filesWithEmbeddings / totalFiles * 100).toFixed(1) + '%' : '0%',
          contentCoverage: totalFiles > 0 ? (filesWithContent / totalFiles * 100).toFixed(1) + '%' : '0%'
        },
        platformBreakdown: platformStats,
        embeddingServiceStatus: {
          initialized: simpleEmbeddingService.isInitialized(),
          available: !!simpleEmbeddingService
        },
        contentExtractorStatus: {
          available: !!unifiedContentExtractor,
          supportedTypes: ['pdf', 'document', 'text', 'csv', 'transcript']
        },
        detailedFiles: embeddingStatus
      });
      
    } catch (error: any) {
      console.error('Error in files status debug endpoint:', error);
      res.status(500).json({
        error: 'Failed to get file status',
        message: error.message
      });
    }
  });

  // Debug endpoint to test content extraction for a specific file
  app.post('/api/debug/test-extraction/:fileId', async (req, res) => {
    try {
      const fileId = parseInt(req.params.fileId);
      const { forceReprocess } = req.body;
      
      console.log(`🔍 Debug: Testing content extraction for file ${fileId}...`);
      
      // Get file info
      const file = await storage.getFile(fileId);
      if (!file) {
        return res.status(404).json({ error: 'File not found' });
      }
      
      // Create platform metadata
      const platformMetadata = {
        platform: file.platform,
        fileName: file.fileName,
        fileType: file.fileType,
        mimeType: file.mimeType || 'application/octet-stream',
        sourceUrl: file.sourceUrl || undefined,
        lastModified: file.lastModified || new Date(),
        owner: file.userId || 'Unknown',
        folderPath: 'Debug Test',
        sourceContext: `${file.platform} Debug Test`
      };
      
      // Test content extraction
      const startTime = Date.now();
      const contentResult = await unifiedContentExtractor.extractContent(platformMetadata);
      const extractionTime = Date.now() - startTime;
      
      let embeddingResult = null;
      if (contentResult.success && contentResult.content && (forceReprocess || !file.fileContent)) {
        try {
          // Update file with extracted content
          await storage.updateFile(fileId, { fileContent: contentResult.content });
          
          // Generate embeddings if requested
          if (forceReprocess) {
            // First delete existing chunks
            const existingChunks = await storage.getFileChunks(fileId);
            if (existingChunks.length > 0) {
              await storage.deleteFileChunks(fileId);
            }
            
            // Generate new embeddings
            if (simpleEmbeddingService.isInitialized()) {
              await simpleEmbeddingService.processFileForEmbeddings(fileId, contentResult.content);
              embeddingResult = { success: true, message: 'Embeddings regenerated' };
            } else {
              embeddingResult = { success: false, message: 'Embedding service not initialized' };
            }
          }
        } catch (updateError) {
          embeddingResult = { success: false, error: (updateError as any).message };
        }
      }
      
      // Get updated chunk count
      const chunks = await storage.getFileChunks(fileId);
      
      res.json({
        file: {
          id: file.id,
          fileName: file.fileName,
          platform: file.platform,
          fileType: file.fileType,
          mimeType: file.mimeType
        },
        extraction: {
          success: contentResult.success,
          method: contentResult.metadata?.extractionMethod,
          quality: contentResult.metadata?.quality,
          contentLength: contentResult.content?.length || 0,
          wordCount: contentResult.metadata?.wordCount,
          confidence: contentResult.metadata?.confidence,
          warnings: contentResult.metadata?.warnings,
          error: contentResult.error,
          timeTaken: extractionTime
        },
        embeddings: embeddingResult,
        chunks: {
          count: chunks.length,
          details: chunks.map(c => ({
            id: c.id,
            chunkIndex: c.chunkIndex,
            contentLength: c.content.length,
            tokenCount: c.tokenCount
          }))
        },
        contentPreview: contentResult.content?.substring(0, 500) + (contentResult.content && contentResult.content.length > 500 ? '...' : '')
      });
      
    } catch (error: any) {
      console.error(`Error testing extraction for file ${req.params.fileId}:`, error);
      res.status(500).json({
        error: 'Failed to test content extraction',
        message: error.message
      });
    }
  });

  // Debug endpoint to test RAG search
  app.post('/api/debug/test-rag-search', async (req, res) => {
    try {
      const { query, enabledSources = [], limit = 5 } = req.body;
      
      if (!query) {
        return res.status(400).json({ error: 'Query is required' });
      }
      
      console.log(`🔍 Debug: Testing RAG search for query: "${query}"`);
      
      const startTime = Date.now();
      
      // Test embedding search
      let searchResults = [];
      if (simpleEmbeddingService.isInitialized()) {
        try {
          searchResults = await simpleEmbeddingService.searchSimilarChunks(
            query,
            enabledSources,
            limit
          );
        } catch (searchError) {
          console.error('RAG search error:', searchError);
        }
      }
      
      const searchTime = Date.now() - startTime;
      
      // Get available sources
      const availableSources = [];
      try {
        const { ragService } = await import('../services/rag');
        const sources = await ragService.getAvailableSources();
        availableSources.push(...sources);
      } catch (sourceError) {
        console.error('Error getting sources:', sourceError);
      }
      
      res.json({
        query,
        enabledSources,
        searchResults: searchResults.map(chunk => ({
          id: chunk.id,
          fileId: chunk.fileId,
          fileName: chunk.fileName,
          platform: chunk.platform,
          similarity: chunk.similarity,
          contentPreview: chunk.content.substring(0, 200) + (chunk.content.length > 200 ? '...' : ''),
          contentLength: chunk.content.length
        })),
        stats: {
          resultsFound: searchResults.length,
          searchTime,
          embeddingServiceAvailable: simpleEmbeddingService.isInitialized(),
        },
        availableSources: availableSources.map(source => ({
          id: source.id,
          name: source.name,
          platform: source.platform,
          connected: source.connected,
          fileCount: source.fileCount
        }))
      });
      
    } catch (error: any) {
      console.error('Error testing RAG search:', error);
      res.status(500).json({
        error: 'Failed to test RAG search',
        message: error.message
      });
    }
  });
}
