import fs from 'fs';
import path from 'path';
import { BaseService } from "../base/service.interface";
import { storage } from "../../storage/index.js";

/**
 * File Upload Processing Service - handles file processing and database storage
 */
export class FileUploadProcessingService extends BaseService {
  constructor() {
    super('FileUploadProcessing', '1.0.0', 'File upload processing and database storage');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'File Upload Processing service initialized');
  }

  protected getDependencies(): string[] {
    return ['storage', 'FileUploadConfig'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      storageConnected: true,
      supportedOperations: ['process', 'store', 'validate'],
    };
  }

  /**
   * Process uploaded file and store in database
   */
  async processUploadedFile(
    file: Express.Multer.File,
    userId?: string
  ): Promise<{
    success: boolean;
    fileId?: number;
    error?: string;
    warnings?: string[];
  }> {
    this.ensureInitialized();
    this.validateRequired(file, 'uploaded file');

    try {
      this.log('info', `Processing uploaded file: ${file.originalname}`);

      // Determine file type
      const fileType = this.determineFileType(file);

      // Create file record in database
      const fileRecord = await storage.createFile({
        externalId: `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        fileName: file.originalname,
        fileType,
        mimeType: file.mimetype,
        fileSize: file.size,
        platform: 'uploaded_files',
        fileUrl: file.path,
        downloadUrl: file.path,
        userId: userId || 'anonymous',
        status: 'active',
        extractedMetadata: {
          uploadedAt: new Date().toISOString(),
          originalPath: file.path,
          uploadedBy: userId || 'anonymous',
          fileExtension: path.extname(file.originalname).toLowerCase(),
          category: this.getFileCategory(file.mimetype),
        }
      });

      this.log('info', `File record created with ID: ${fileRecord.id}`);

      // Process file for embeddings if it contains text
      const warnings: string[] = [];
      if (this.shouldProcessForEmbeddings(fileType, file.mimetype)) {
        try {
          await this.processFileForEmbeddings(fileRecord.id, file.path, fileType);
        } catch (embeddingError: any) {
          this.log('error', `Error processing file ${fileRecord.id} for embeddings:`, embeddingError);
          warnings.push('File uploaded successfully but embedding generation failed');
        }
      } else {
        warnings.push('File type does not support text extraction - searchable by metadata only');
      }

      return {
        success: true,
        fileId: fileRecord.id,
        warnings: warnings.length > 0 ? warnings : undefined,
      };

    } catch (error: any) {
      this.log('error', 'Error processing uploaded file', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Process multiple uploaded files
   */
  async processMultipleFiles(
    files: Express.Multer.File[],
    userId?: string
  ): Promise<{
    success: boolean;
    results: Array<{
      filename: string;
      fileId?: number;
      success: boolean;
      error?: string;
      warnings?: string[];
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }> {
    this.ensureInitialized();
    this.validateRequired(files, 'uploaded files');

    const results = [];
    let successful = 0;
    let failed = 0;

    for (const file of files) {
      try {
        const result = await this.processUploadedFile(file, userId);
        
        results.push({
          filename: file.originalname,
          fileId: result.fileId,
          success: result.success,
          error: result.error,
          warnings: result.warnings,
        });

        if (result.success) {
          successful++;
        } else {
          failed++;
        }
      } catch (error: any) {
        results.push({
          filename: file.originalname,
          success: false,
          error: error.message,
        });
        failed++;
      }
    }

    return {
      success: failed === 0,
      results,
      summary: {
        total: files.length,
        successful,
        failed,
      },
    };
  }

  /**
   * Process file for embeddings using unified content extractor
   */
  private async processFileForEmbeddings(
    fileId: number,
    filePath: string,
    fileType: string
  ): Promise<void> {
    try {
      // Import services dynamically to avoid circular dependencies
      const { simpleEmbeddingService } = await import('../ai/simple-embedding-service.js');
      const { unifiedContentExtractor } = await import('../ai/unified-content-extractor.service');

      if (!simpleEmbeddingService.isInitialized()) {
        this.log('warn', 'Embedding service not initialized, skipping embedding generation');
        return;
      }

      // Get file info for metadata
      const fileRecord = await storage.getFile(fileId);
      if (!fileRecord) {
        throw new Error(`File record not found for ID ${fileId}`);
      }

             // Create platform metadata for unified extractor
       const platformMetadata = {
         platform: 'uploaded_files',
         fileName: fileRecord.fileName,
         fileType: fileType,
         mimeType: fileRecord.mimeType || 'application/octet-stream',
         sourceUrl: fileRecord.sourceUrl || undefined,
         lastModified: fileRecord.lastModified || new Date(),
         owner: fileRecord.userId || 'Unknown',
         folderPath: 'Uploaded Files',
         sourceContext: 'User Upload'
       };

      // Extract content using unified extractor
      const contentResult = await unifiedContentExtractor.extractContent(
        platformMetadata,
        undefined, // Let it read from filePath
        filePath
      );

      if (contentResult.success && contentResult.content) {
        // Store the extracted content in the file record
        await storage.updateFile(fileId, { fileContent: contentResult.content });

        // Generate embeddings using simple embedding service
        await simpleEmbeddingService.processFileForEmbeddings(fileId, contentResult.content);
        this.log('info', `Generated embeddings for file ${fileId} using ${contentResult.metadata?.extractionMethod}`);
      } else {
        this.log('warn', `Content extraction failed for file ${fileId}: ${contentResult.error}`);
      }

    } catch (error: any) {
      this.log('error', `Error processing file ${fileId} for embeddings:`, error);
      throw error;
    }
  }

  /**
   * Determine file type from uploaded file (matches unified content extractor logic)
   */
  private determineFileType(file: Express.Multer.File): string {
    const mimeType = file.mimetype.toLowerCase();
    const extension = path.extname(file.originalname).toLowerCase();

    // PDF files (highest priority)
    if (extension === '.pdf' || mimeType.includes('pdf')) {
      return 'document';
    }

    // CSV files (before general text classification)
    if (extension === '.csv' || mimeType.includes('csv')) {
      return 'spreadsheet';
    }

    // Office documents
    if (mimeType.includes('word') || mimeType.includes('document') ||
        ['.doc', '.docx', '.rtf', '.odt'].includes(extension)) {
      return 'document';
    }

    // Spreadsheets
    if (mimeType.includes('sheet') || mimeType.includes('excel') ||
        ['.xls', '.xlsx'].includes(extension)) {
      return 'spreadsheet';
    }

    // Presentations
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint') ||
        ['.ppt', '.pptx'].includes(extension)) {
      return 'presentation';
    }

    // Code files
    if (['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.css',
         '.scss', '.less', '.sql', '.sh', '.html', '.xml', '.json', '.yaml', '.yml'].includes(extension)) {
      return 'code';
    }

    // Markdown files
    if (['.md', '.markdown', '.mdx'].includes(extension)) {
      return 'text';
    }

    // Text files (after specific types)
    if (mimeType.includes('text') || ['.txt', '.log'].includes(extension)) {
      return 'text';
    }

    // Media files
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('video/')) return 'video';

    // Archives
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z') ||
        ['.zip', '.rar', '.7z'].includes(extension)) {
      return 'archive';
    }

    return 'other';
  }

  /**
   * Get file category for metadata (simplified to match file type)
   */
  private getFileCategory(mimeType: string): string {
    // Use the same logic as determineFileType for consistency
    const dummyFile = { mimetype: mimeType, originalname: 'dummy.ext' } as Express.Multer.File;
    return this.determineFileType(dummyFile);
  }

  /**
   * Check if file should be processed for embeddings
   */
  private shouldProcessForEmbeddings(fileType: string, mimeType: string): boolean {
    const textBasedTypes = ['text', 'code', 'document', 'spreadsheet', 'presentation'];
    const nonTextTypes = ['image', 'audio', 'video', 'archive', 'other'];

    return textBasedTypes.includes(fileType) && !nonTextTypes.includes(fileType);
  }

  /**
   * Get file processing statistics
   */
  async getProcessingStats(): Promise<{
    totalFiles: number;
    filesByType: Record<string, number>;
    filesByStatus: Record<string, number>;
    totalSize: number;
    averageSize: number;
  }> {
    this.ensureInitialized();

    try {
      const files = await storage.getFiles('uploaded_files');
      
      const filesByType: Record<string, number> = {};
      const filesByStatus: Record<string, number> = {};
      let totalSize = 0;

      for (const file of files) {
        // Count by type
        const type = file.fileType || 'unknown';
        filesByType[type] = (filesByType[type] || 0) + 1;

        // Count by status
        const status = file.status || 'unknown';
        filesByStatus[status] = (filesByStatus[status] || 0) + 1;

        // Sum size
        totalSize += file.fileSize || 0;
      }

      return {
        totalFiles: files.length,
        filesByType,
        filesByStatus,
        totalSize,
        averageSize: files.length > 0 ? Math.round(totalSize / files.length) : 0,
      };

    } catch (error: any) {
      this.log('error', 'Error getting processing stats', error);
      return {
        totalFiles: 0,
        filesByType: {},
        filesByStatus: {},
        totalSize: 0,
        averageSize: 0,
      };
    }
  }

  /**
   * Validate uploaded file
   */
  validateUploadedFile(file: Express.Multer.File): {
    isValid: boolean;
    error?: string;
    warnings?: string[];
  } {
    const warnings: string[] = [];

    // Check if file exists
    if (!fs.existsSync(file.path)) {
      return {
        isValid: false,
        error: 'Uploaded file not found on disk',
      };
    }

    // Check file size consistency
    const actualSize = fs.statSync(file.path).size;
    if (actualSize !== file.size) {
      warnings.push(`File size mismatch: expected ${file.size}, actual ${actualSize}`);
    }

    // Check for empty files
    if (actualSize === 0) {
      return {
        isValid: false,
        error: 'File is empty',
      };
    }

    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  /**
   * Get uploaded file info
   */
  async getUploadedFileInfo(fileId: number): Promise<any> {
    this.ensureInitialized();
    this.validateNumber(fileId, 'file ID', 1);

    try {
      const file = await storage.getFile(fileId);
      
      if (!file || file.platform !== 'uploaded_files') {
        return null;
      }

      // Check if physical file exists
      const physicalFileExists = file.fileUrl ? fs.existsSync(file.fileUrl) : false;

      return {
        ...file,
        physicalFileExists,
        uploadedAt: file.extractedMetadata?.uploadedAt,
        category: file.extractedMetadata?.category,
      };

    } catch (error: any) {
      this.log('error', `Error getting uploaded file info for ${fileId}`, error);
      return null;
    }
  }
}
