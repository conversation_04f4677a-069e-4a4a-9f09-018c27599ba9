import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  UserIcon, 
  ChevronDownIcon, 
  ChevronUpIcon,
  CheckCircleIcon,
  XCircleIcon,
  MailIcon,
  FolderIcon,
  CalendarIcon,
  ShieldCheckIcon,
  AlertCircleIcon,
  ExternalLinkIcon
} from 'lucide-react';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';
import { IntegrationIcon } from '@/components/ui/integration-icons';

interface ConnectedAccount {
  provider: string; // "google", "microsoft", "notion"
  email: string;
  displayName?: string;
  avatar?: string;
  connectedAt: string;
  lastUsed?: string;
  services: {
    name: string; // "Gmail", "Google Drive", "Google Calendar"
    type: string; // "gmail", "google_drive", "google_calendar" 
    status: 'connected' | 'error' | 'syncing';
    integrationId: number;
    lastSyncAt?: string;
    itemCount?: number;
  }[];
  scopes: string[];
  isActive: boolean;
}

interface ConnectedAccountsStatusProps {
  className?: string;
}

export function ConnectedAccountsStatus({ className }: ConnectedAccountsStatusProps) {
  const [accounts, setAccounts] = useState<ConnectedAccount[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadConnectedAccounts();
  }, []);

  const loadConnectedAccounts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/integrations/connected-accounts');
      if (response.ok) {
        const data = await response.json();
        setAccounts(data.accounts || []);
      }
    } catch (error) {
      console.error('Error loading connected accounts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'google':
        return (
          <div className="h-6 w-6 bg-red-100 rounded-full flex items-center justify-center">
            <div className="h-3 w-3 bg-red-500 rounded-full" />
          </div>
        );
      case 'microsoft':
        return (
          <div className="h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center">
            <div className="h-3 w-3 bg-blue-500 rounded-full" />
          </div>
        );
      case 'notion':
        return (
          <div className="h-6 w-6 bg-gray-100 rounded-full flex items-center justify-center">
            <div className="h-3 w-3 bg-gray-500 rounded-full" />
          </div>
        );
      default:
        return <UserIcon className="h-6 w-6 text-gray-400" />;
    }
  };

  const getServiceIcon = (serviceType: string) => {
    return <IntegrationIcon type={serviceType} size={16} />;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <CheckCircleIcon className="h-3 w-3 mr-1" />
            Connected
          </Badge>
        );
      case 'syncing':
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <div className="h-3 w-3 mr-1 animate-spin border border-blue-600 border-t-transparent rounded-full" />
            Syncing
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="destructive">
            <XCircleIcon className="h-3 w-3 mr-1" />
            Error
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <AlertCircleIcon className="h-3 w-3 mr-1" />
            Unknown
          </Badge>
        );
    }
  };

  const handleDisconnectAccount = async (provider: string, email: string) => {
    try {
      const response = await fetch('/api/integrations/disconnect-account', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider, email }),
      });
      
      if (response.ok) {
        await loadConnectedAccounts(); // Refresh the list
      }
    } catch (error) {
      console.error('Error disconnecting account:', error);
    }
  };

  const activeAccounts = accounts.filter(account => account.isActive);
  const totalServices = accounts.reduce((sum, account) => sum + account.services.length, 0);
  const connectedServices = accounts.reduce(
    (sum, account) => sum + account.services.filter(s => s.status === 'connected').length, 
    0
  );

  if (isLoading) {
    return (
      <Card className={`${className}`}>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-3">
            <div className="h-6 w-6 bg-gray-200 rounded-full animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse flex-1" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <div className="cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <ShieldCheckIcon className="h-5 w-5 text-green-600" />
                  <div>
                    <CardTitle className="text-sm font-medium">
                      Connected Accounts
                    </CardTitle>
                    <p className="text-xs text-gray-500">
                      {activeAccounts.length} account{activeAccounts.length !== 1 ? 's' : ''}, {connectedServices} service{connectedServices !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {activeAccounts.slice(0, 3).map((account, index) => (
                    <div key={index} className="relative">
                      {getProviderIcon(account.provider)}
                    </div>
                  ))}
                  {activeAccounts.length > 3 && (
                    <div className="h-6 w-6 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xs text-gray-600">+{activeAccounts.length - 3}</span>
                    </div>
                  )}
                  {isExpanded ? (
                    <ChevronUpIcon className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
            </CardHeader>
          </div>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="pt-0">
            {activeAccounts.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-gray-500 text-sm">No connected accounts</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => window.location.href = '/integrations/new/setup'}
                >
                  Connect an Account
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {activeAccounts.map((account, accountIndex) => (
                  <div key={accountIndex} className="border rounded-lg p-3 bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {getProviderIcon(account.provider)}
                        <div>
                          <div className="font-medium text-sm">
                            {account.displayName || account.email}
                          </div>
                          {account.displayName && (
                            <div className="text-xs text-gray-500">{account.email}</div>
                          )}
                          <div className="text-xs text-gray-400">
                            Connected {new Date(account.connectedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnectAccount(account.provider, account.email)}
                        className="text-xs"
                      >
                        Disconnect
                      </Button>
                    </div>
                    
                    {/* Services */}
                    <div className="space-y-2">
                      <h4 className="text-xs font-medium text-gray-700">Services</h4>
                      <div className="grid grid-cols-1 gap-2">
                        {account.services.map((service, serviceIndex) => (
                          <div key={serviceIndex} className="flex items-center justify-between p-2 bg-white rounded border">
                            <div className="flex items-center space-x-2">
                              {getServiceIcon(service.type)}
                              <div>
                                <div className="text-sm font-medium">{service.name}</div>
                                {service.lastSyncAt && (
                                  <div className="text-xs text-gray-500">
                                    Last sync: {new Date(service.lastSyncAt).toLocaleString()}
                                  </div>
                                )}
                                {service.itemCount !== undefined && (
                                  <div className="text-xs text-gray-500">
                                    {service.itemCount.toLocaleString()} items
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {getStatusBadge(service.status)}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.location.href = `/integrations/${service.integrationId}/setup`}
                              >
                                <ExternalLinkIcon className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Scopes */}
                    {account.scopes && account.scopes.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <h4 className="text-xs font-medium text-gray-700 mb-2">Permissions</h4>
                        <div className="flex flex-wrap gap-1">
                          {account.scopes.slice(0, 5).map((scope, scopeIndex) => (
                            <Badge key={scopeIndex} variant="outline" className="text-xs">
                              {scope.split('.').pop()?.replace('readonly', 'read')}
                            </Badge>
                          ))}
                          {account.scopes.length > 5 && (
                            <Badge variant="outline" className="text-xs">
                              +{account.scopes.length - 5} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
} 