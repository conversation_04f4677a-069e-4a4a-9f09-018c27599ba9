import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  UserIcon, 
  TrashIcon, 
  PlusIcon, 
  CalendarIcon, 
  MailIcon, 
  HardDriveIcon,
  Loader2Icon,
  ExternalLinkIcon
} from 'lucide-react';

interface ConnectedAccount {
  provider: string;
  email: string;
  displayName: string;
  connectedAt: string;
  lastUsed: string | null;
  services: Array<{
    name: string;
    type: string;
    status: string;
    integrationId: number;
    lastSyncAt: string | null;
    itemCount?: number;
  }>;
  scopes: string[];
  isActive: boolean;
}

interface AccountManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const getProviderIcon = (provider: string) => {
  switch (provider) {
    case 'google':
      return 'https://developers.google.com/identity/images/g-logo.png';
    case 'microsoft':
      return 'https://upload.wikimedia.org/wikipedia/commons/4/44/Microsoft_logo.svg';
    default:
      return 'https://via.placeholder.com/32x32?text=?';
  }
};

const getServiceIcon = (type: string) => {
  switch (type) {
    case 'gmail':
      return <MailIcon className="h-4 w-4" />;
    case 'google_calendar':
    case 'google-calendar':
      return <CalendarIcon className="h-4 w-4" />;
    case 'google_drive':
    case 'google-drive':
      return <HardDriveIcon className="h-4 w-4" />;
    default:
      return <UserIcon className="h-4 w-4" />;
  }
};

export default function AccountManager({ isOpen, onClose }: AccountManagerProps) {
  const [selectedAccountToDelete, setSelectedAccountToDelete] = useState<ConnectedAccount | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch connected accounts
  const { data: accountsData, isLoading } = useQuery({
    queryKey: ['/api/accounts'],
    queryFn: async () => {
      const response = await fetch('/api/accounts');
      if (!response.ok) throw new Error('Failed to fetch accounts');
      return response.json();
    },
    enabled: isOpen,
  });

  // Disconnect account mutation
  const disconnectMutation = useMutation({
    mutationFn: async (account: ConnectedAccount) => {
      const response = await fetch('/api/accounts/disconnect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          provider: account.provider, 
          email: account.email 
        }),
      });
      if (!response.ok) throw new Error('Failed to disconnect account');
      return response.json();
    },
    onSuccess: (data, account) => {
      toast({
        title: 'Account disconnected',
        description: `Successfully disconnected ${account.email} and ${data.disconnectedServices} services.`,
      });
      queryClient.invalidateQueries({ queryKey: ['/api/accounts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      setShowDeleteDialog(false);
      setSelectedAccountToDelete(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to disconnect account',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Add new account (trigger OAuth)
  const addAccountMutation = useMutation({
    mutationFn: async (provider: string) => {
      // This will trigger OAuth flow
      window.location.href = `/api/integrations/oauth/connect?provider=${provider}`;
    },
  });

  const handleDisconnectAccount = (account: ConnectedAccount) => {
    setSelectedAccountToDelete(account);
    setShowDeleteDialog(true);
  };

  const confirmDisconnect = () => {
    if (selectedAccountToDelete) {
      disconnectMutation.mutate(selectedAccountToDelete);
    }
  };

  const accounts: ConnectedAccount[] = accountsData?.accounts || [];

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UserIcon className="h-5 w-5" />
              Connected Accounts
            </DialogTitle>
            <DialogDescription>
              Manage your connected accounts and their integrations. You can add new accounts or remove existing ones.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Add New Account Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Add New Account</CardTitle>
                <CardDescription>
                  Connect a new account to enable more integrations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-3">
                  <Button
                    onClick={() => addAccountMutation.mutate('google')}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <img 
                      src={getProviderIcon('google')} 
                      alt="Google" 
                      className="h-4 w-4" 
                    />
                    Connect Google Account
                    <ExternalLinkIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={() => addAccountMutation.mutate('microsoft')}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <img 
                      src={getProviderIcon('microsoft')} 
                      alt="Microsoft" 
                      className="h-4 w-4" 
                    />
                    Connect Microsoft Account
                    <ExternalLinkIcon className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Connected Accounts List */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Your Connected Accounts</h3>
              
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2Icon className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading accounts...</span>
                </div>
              ) : accounts.length === 0 ? (
                <Card>
                  <CardContent className="py-8">
                    <div className="text-center text-muted-foreground">
                      <UserIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No connected accounts yet</p>
                      <p className="text-sm">Add your first account above to get started</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {accounts.map((account, index) => (
                    <Card key={`${account.provider}-${account.email}`} className="relative">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <img 
                              src={getProviderIcon(account.provider)} 
                              alt={account.provider} 
                              className="h-8 w-8" 
                            />
                            <div>
                              <CardTitle className="text-base">
                                {account.displayName || account.email}
                              </CardTitle>
                              <CardDescription className="flex items-center gap-2">
                                {account.email}
                                <Badge variant={account.isActive ? 'default' : 'secondary'}>
                                  {account.isActive ? 'Active' : 'Inactive'}
                                </Badge>
                              </CardDescription>
                            </div>
                          </div>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDisconnectAccount(account)}
                            disabled={disconnectMutation.isPending}
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Connected</span>
                            <span>{new Date(account.connectedAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Last Used</span>
                            <span>
                              {account.lastUsed 
                                ? new Date(account.lastUsed).toLocaleDateString()
                                : 'Never'
                              }
                            </span>
                          </div>
                          
                          {/* Connected Services */}
                          <div>
                            <h4 className="text-sm font-medium mb-2">Connected Services</h4>
                            <div className="flex flex-wrap gap-2">
                              {account.services.map((service, serviceIndex) => (
                                <Badge 
                                  key={`${service.integrationId}-${serviceIndex}`}
                                  variant="outline" 
                                  className="flex items-center gap-1"
                                >
                                  {getServiceIcon(service.type)}
                                  {service.name}
                                  <span className={`w-2 h-2 rounded-full ${
                                    service.status === 'connected' ? 'bg-green-500' : 'bg-red-500'
                                  }`} />
                                </Badge>
                              ))}
                              {account.services.length === 0 && (
                                <span className="text-sm text-muted-foreground">
                                  No services connected
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Disconnect Account</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to disconnect <strong>{selectedAccountToDelete?.email}</strong>?
              <br /><br />
              This will remove all integrations for this account and revoke access permissions. 
              Synced files will be preserved but no longer updated.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={disconnectMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDisconnect}
              disabled={disconnectMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {disconnectMutation.isPending ? (
                <>
                  <Loader2Icon className="h-4 w-4 animate-spin mr-2" />
                  Disconnecting...
                </>
              ) : (
                'Disconnect Account'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 