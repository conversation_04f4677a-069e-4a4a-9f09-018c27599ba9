import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  CalendarIcon, 
  MailIcon, 
  HardDriveIcon,
  SettingsIcon,
  RefreshCwIcon,
  BarChart3Icon,
  FileIcon,
  ClockIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  ExternalLinkIcon
} from 'lucide-react';
import { format } from 'date-fns';
import { IntegrationIcon } from '@/components/ui/integration-icons';

interface Integration {
  id: number;
  name: string;
  type: string;
  status: string;
  lastSyncAt: string | null;
  nextSyncAt: string | null;
  sourceConfig: any;
  destinationConfig: any;
}

interface IntegrationDetailsProps {
  isOpen: boolean;
  onClose: () => void;
  integration: Integration | null;
  onConfigure: (id: number) => void;
  onSync: (id: number) => void;
}

const getIntegrationIcon = (type: string) => {
  return <IntegrationIcon type={type} size={24} />;
};

const getIntegrationDescription = (type: string) => {
  switch (type) {
    case 'gmail':
      return 'Access and search your Gmail emails with AI-powered insights. Sync email conversations, attachments, and metadata for comprehensive document analysis.';
    case 'google_calendar':
    case 'google-calendar':
      return 'Sync your Google Calendar events and meetings. Enable AI to understand your schedule context and provide time-aware insights.';
    case 'google_drive':
    case 'google-drive':
      return 'Connect your Google Drive to access files, documents, and folders. Enable AI-powered search and analysis across all your stored content.';
    default:
      return 'Integration for syncing and analyzing your data with AI-powered insights.';
  }
};

const getIntegrationFeatures = (type: string) => {
  switch (type) {
    case 'gmail':
      return [
        'Email thread analysis',
        'Attachment extraction',
        'Smart categorization',
        'Sender insights',
        'Full-text search',
        'Privacy protection'
      ];
    case 'google_calendar':
    case 'google-calendar':
      return [
        'Event synchronization',
        'Meeting detection',
        'Schedule analysis',
        'Attendee insights',
        'Time blocking',
        'Smart reminders'
      ];
    case 'google_drive':
    case 'google-drive':
      return [
        'Document sync',
        'Folder organization',
        'File type support',
        'Version tracking',
        'Content extraction',
        'Collaborative insights'
      ];
    default:
      return ['Data synchronization', 'AI analysis', 'Smart search'];
  }
};

const getIntegrationStats = (type: string) => {
  // Mock stats - in real implementation, this would come from the API
  switch (type) {
    case 'gmail':
      return {
        totalItems: '2,847 emails',
        lastWeek: '143 new emails',
        categories: '12 conversations',
        storage: '1.2 GB processed'
      };
    case 'google_calendar':
    case 'google-calendar':
      return {
        totalItems: '284 events',
        lastWeek: '12 new events',
        categories: '8 meetings',
        storage: '45 MB processed'
      };
    case 'google_drive':
    case 'google-drive':
      return {
        totalItems: '1,394 files',
        lastWeek: '28 new files',
        categories: '156 documents',
        storage: '4.7 GB processed'
      };
    default:
      return {
        totalItems: 'N/A',
        lastWeek: 'N/A',
        categories: 'N/A',
        storage: 'N/A'
      };
  }
};

export default function IntegrationDetails({ 
  isOpen, 
  onClose, 
  integration, 
  onConfigure, 
  onSync 
}: IntegrationDetailsProps) {
  const [activeTab, setActiveTab] = useState('overview');

  if (!integration) return null;

  const stats = getIntegrationStats(integration.type);
  const features = getIntegrationFeatures(integration.type);
  const isConnected = integration.status === 'connected' || integration.status === 'configured';
  const isSyncing = integration.status === 'syncing';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50 text-blue-600">
              {getIntegrationIcon(integration.type)}
            </div>
            <div>
              <DialogTitle className="text-xl">{integration.name}</DialogTitle>
              <DialogDescription className="flex items-center gap-2 mt-1">
                <Badge variant={isConnected ? 'default' : 'secondary'}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </Badge>
                {isSyncing && (
                  <Badge variant="outline" className="animate-pulse">
                    <RefreshCwIcon className="h-3 w-3 mr-1 animate-spin" />
                    Syncing
                  </Badge>
                )}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>About This Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  {getIntegrationDescription(integration.type)}
                </p>
                
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircleIcon className="h-4 w-4 text-green-500" />
                      {feature}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sync Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Last Sync</div>
                    <div className="font-medium">
                      {integration.lastSyncAt 
                        ? format(new Date(integration.lastSyncAt), "MMM d, yyyy 'at' h:mm a")
                        : 'Never synced'
                      }
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Next Sync</div>
                    <div className="font-medium">
                      {integration.nextSyncAt 
                        ? format(new Date(integration.nextSyncAt), "MMM d, yyyy 'at' h:mm a")
                        : 'Not scheduled'
                      }
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    onClick={() => onSync(integration.id)} 
                    disabled={isSyncing}
                    className="flex items-center gap-2"
                  >
                    <RefreshCwIcon className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
                    {isSyncing ? 'Syncing...' : 'Sync Now'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => onConfigure(integration.id)}
                    className="flex items-center gap-2"
                  >
                    <SettingsIcon className="h-4 w-4" />
                    Configure
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalItems}</div>
                  <div className="text-sm text-muted-foreground">Total Items</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">{stats.lastWeek}</div>
                  <div className="text-sm text-muted-foreground">Last Week</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-purple-600">{stats.categories}</div>
                  <div className="text-sm text-muted-foreground">Categories</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-orange-600">{stats.storage}</div>
                  <div className="text-sm text-muted-foreground">Data Processed</div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3Icon className="h-5 w-5" />
                  Activity Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                    <span className="text-sm">Sync Success Rate</span>
                    <Badge variant="default">98.5%</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                    <span className="text-sm">Average Sync Time</span>
                    <Badge variant="outline">2.3 seconds</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                    <span className="text-sm">Last Error</span>
                    <Badge variant="secondary">None</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Integration Settings</CardTitle>
                <CardDescription>
                  Manage how this integration syncs and processes your data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">Auto Sync</div>
                    <div className="text-sm text-muted-foreground">Automatically sync new data</div>
                  </div>
                  <Badge variant="default">Enabled</Badge>
                </div>
                
                <div className="flex justify-between items-center p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">AI Processing</div>
                    <div className="text-sm text-muted-foreground">Enable AI analysis of content</div>
                  </div>
                  <Badge variant="default">Enabled</Badge>
                </div>
                
                <div className="flex justify-between items-center p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">Sync Schedule</div>
                    <div className="text-sm text-muted-foreground">How often to check for updates</div>
                  </div>
                  <Badge variant="outline">Every hour</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">Danger Zone</CardTitle>
                <CardDescription>
                  Irreversible actions for this integration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="destructive" className="flex items-center gap-2">
                  <AlertCircleIcon className="h-4 w-4" />
                  Delete Integration
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button onClick={() => onConfigure(integration.id)} className="flex items-center gap-2">
            <ExternalLinkIcon className="h-4 w-4" />
            Open Configuration
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 