import { Request, Response } from 'express';
import { BaseIntegrationController } from '../base/base-integration.controller.js';
import { googleService } from '../../../services/platform-integrations/google/index.js';
import { oauth2Storage } from '../../../storage/features/oauth2.storage';
import { sessionUtils } from '../../../core/middleware/session.middleware.js';

/**
 * Controller for Google OAuth operations
 * Handles Google Drive and Google Meet authentication flows
 */
export class GoogleOAuthController extends BaseIntegrationController {
  constructor() {
    super();
    // Bind all methods to preserve 'this' context
    this.getAuthUrl = this.getAuthUrl.bind(this);
    this.handleOAuthCallback = this.handleOAuthCallback.bind(this);
  }

  /**
   * Generate Google OAuth authorization URL
   */
  async getAuthUrl(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      // Check if integration exists
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Validate that this is a Google integration
      const isGoogleIntegration = this.validateIntegrationType(integration, [
        'google_drive', 'google-drive', 'google_meet', 'google-meet', 'google_chat', 'google-chat', 'gmail', 'google_calendar', 'google-calendar'
      ]);

      if (!isGoogleIntegration) {
        return res.status(400).json({
          message: `This endpoint is only for Google integrations. Received: ${integration.type}`
        });
      }

      // Get the redirect URI from the query parameters
      const redirectUri = req.query.redirectUri as string;
      if (!redirectUri) {
        return res.status(400).json({ message: 'Redirect URI is required' });
      }

      try {
        this.logAction(`Processing Google OAuth for integration type: ${integration.type}`);

        // Generate Google OAuth URL with integration ID included
        const result = googleService.getAuthUrl(redirectUri, id!);
        const authUrl = result.url;
        const oauthState = result.state;

        // Store the state temporarily in the integration config
        await this.storeOAuthState(id!, oauthState, redirectUri);

        this.logAction('Google OAuth URL generated successfully');

        return this.successResponse(res, { authUrl });
      } catch (error: any) {
        this.logError("Error generating Google OAuth URL:", error);
        return res.status(500).json({
          message: 'Failed to generate Google authorization URL. Please check your Google API credentials.'
        });
      }
    } catch (error: any) {
      return this.handleError(res, error, `Failed to generate auth URL for integration ${req.params.id}`);
    }
  }

  /**
   * Handle Google OAuth callback and store tokens
   */
  async handleOAuthCallback(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      this.logAction('Received Google OAuth callback', { id: req.params.id, parsedId: id });

      if (isNaN(id)) {
        this.logError('Invalid integration ID:', req.params.id);
        return res.status(400).json({ message: 'Invalid integration ID' });
      }

      const integration = await this.getIntegrationById(id);
      if (!integration) {
        this.logError('Integration not found for id:', id);
        const storage = await this.getStorage();
        const allIntegrations = await storage.getIntegrations();
        this.logError('All integrations in DB:', allIntegrations.map((i: any) => i.id));
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Get code and state from query parameters
      const { code, state } = req.query;
      if (!code) {
        return res.status(400).json({ message: 'Authorization code is missing' });
      }

      // Validate OAuth state
      if (!this.validateOAuthState(integration, state as string)) {
        return res.status(400).json({ message: 'Invalid state parameter' });
      }

      // Validate that this is a Google integration
      const isGoogleIntegration = this.validateIntegrationType(integration, [
        'google_drive', 'google-drive', 'google_meet', 'google-meet', 'google_chat', 'google-chat', 'gmail', 'google_calendar', 'google-calendar'
      ]);

      if (!isGoogleIntegration) {
        this.logError(`Processing non-Google integration type`, new Error(`Invalid type: ${integration.type}`));
        return res.status(400).json({
          message: `This endpoint is only for Google integrations. Received: ${integration.type}`
        });
      }

      this.logAction(`Processing Google OAuth callback for integration type: ${integration.type}`);

      try {
        // Get the redirect URI from the integration config
        const redirectUri = this.getRedirectUri(integration);
        if (!redirectUri) {
          return res.status(400).json({ message: 'Missing redirect URI in integration config' });
        }

        // Get tokens from Google
        this.logAction('Exchanging authorization code for tokens');
        const tokens = await googleService.getTokensFromCode(code as string, redirectUri);

        // Store tokens in the token store for service access
        this.logAction('Storing tokens securely in token store');
        const userId = sessionUtils.getUserId(req) || 'anonymous'; // Use default user ID for now
        await oauth2Storage.storeTokens(userId, 'google', tokens);

        // Also store credentials in the integration record (required for folder access)
        this.logAction('Storing credentials in integration record');
        const credentialsData = JSON.stringify(tokens);
        await this.updateIntegrationCredentials(id, credentialsData);

        this.logAction('Google OAuth callback completed successfully');

        // Redirect to the frontend
        return this.redirectToSuccess(res, id);
      } catch (error: any) {
        this.logError('Error processing Google OAuth tokens:', error);
        return this.redirectToError(res, id, error.message);
      }
    } catch (error: any) {
      this.logError(`Error handling Google OAuth callback for integration ${req.params.id}:`, error);

      // Redirect to the frontend with error
      const integrationId = parseInt(req.params.id, 10);
      if (!isNaN(integrationId)) {
        return this.redirectToError(res, integrationId, error.message);
      } else {
        return res.status(400).json({ message: 'Invalid integration ID' });
      }
    }
  }
}

export const googleOAuthController = new GoogleOAuthController(); 