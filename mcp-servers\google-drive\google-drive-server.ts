import { BaseMCPServer } from '../base-mcp.server';
import { googleService } from '../../server/services/platform-integrations/google';
import { logger } from '../../server/core';
import { listDrive<PERSON><PERSON><PERSON><PERSON><PERSON>, searchDrive<PERSON><PERSON><PERSON><PERSON><PERSON>, getFileContentHandler, create<PERSON>ile<PERSON><PERSON><PERSON> } from './google-drive-tools';

/**
 * SDK-Enhanced Google Drive MCP Server
 * 
 * This server uses the full MCP SDK to provide Google Drive operations including:
 * - Tools with Zod validation
 * - Dynamic resources with templates
 * - Prompt templates for common operations
 * - Automatic protocol handling
 */
export class GoogleDriveMCPServer extends BaseMCPServer {
  constructor() {
    super('google-drive', 'SDK-enhanced Google Drive file operations and folder management', '2.0.0');
  }

  protected async initializeServer(): Promise<void> {
    // Initialize Google service if needed
    if (googleService && typeof googleService.initialize === 'function') {
      await googleService.initialize();
    } else {
      logger.warn('Google service not available - MCP server will have limited functionality');
    }
  }

  protected async registerTools(): Promise<void> {
    this.addTool(
      {
        name: 'list_drive_files',
        description: 'List files and folders in Google Drive with optional filtering',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for authentication' },
            folderId: { type: 'string', description: 'Optional folder ID to list contents of' },
            query: { type: 'string', description: 'Optional search query' },
            maxResults: { type: 'number', description: 'Maximum number of results (default: 50)' }
          },
          required: ['userId']
        },
      },
      listDriveFilesHandler,
      'file-management'
    );

    this.addTool(
      {
        name: 'search_drive_files',
        description: 'Search for files in Google Drive using advanced queries',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for authentication' },
            searchQuery: { type: 'string', description: 'Search query (supports Google Drive search syntax)' },
            fileType: { type: 'string', description: 'Optional file type filter (pdf, doc, sheet, etc.)' },
            maxResults: { type: 'number', description: 'Maximum number of results (default: 20)' }
          },
          required: ['userId', 'searchQuery']
        },
      },
      searchDriveFilesHandler,
      'search'
    );

    this.addTool(
      {
        name: 'get_file_content',
        description: 'Get the content of a specific Google Drive file',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for authentication' },
            fileId: { type: 'string', description: 'Google Drive file ID' },
            format: { type: 'string', description: 'Export format for Google Docs (optional)' }
          },
          required: ['userId', 'fileId']
        },
      },
      getFileContentHandler,
      'file-access'
    );

    this.addTool(
      {
        name: 'create_file',
        description: 'Create a new file in Google Drive',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string', description: 'User ID for authentication' },
            fileName: { type: 'string', description: 'Name of the file to create' },
            content: { type: 'string', description: 'Content of the file' },
            mimeType: { type: 'string', description: 'MIME type of the file (default: text/plain)' },
            folderId: { type: 'string', description: 'Parent folder ID (optional)' }
          },
          required: ['userId', 'fileName', 'content']
        },
      },
      createFileHandler,
      'file-creation'
    );
  }

  protected async registerResources(): Promise<void> {
    // Dynamic file resource with template
    this.addResource(
      {
        uri: 'gdrive://file/{fileId}',
        name: 'google-drive-file',
        description: 'Access Google Drive file content by ID',
        mimeType: 'application/json',
        serverName: this.name,
        template: 'gdrive://file/{fileId}'
      },
      async (uri: string) => {
        // Extract fileId from URI
        const fileId = uri.split('/').pop() || 'unknown';

        return {
          contents: [{
            uri,
            mimeType: 'application/json',
            text: JSON.stringify({
              message: `File content for ${fileId} would be loaded here`,
              fileId,
              note: 'This requires user authentication context'
            }, null, 2)
          }]
        };
      }
    );

    // Drive structure resource
    this.addResource(
      {
        uri: 'gdrive://structure',
        name: 'google-drive-structure',
        description: 'Google Drive folder structure and organization',
        mimeType: 'application/json',
        serverName: this.name
      },
      async (uri: string) => {
        return {
          contents: [{
            uri,
            mimeType: 'application/json',
            text: JSON.stringify({
              message: 'Google Drive structure overview',
              folders: ['Documents', 'Spreadsheets', 'Presentations', 'Images'],
              note: 'Use list_drive_files tool for actual file listing'
            }, null, 2)
          }]
        };
      }
    );
  }

  protected async registerPrompts(): Promise<void> {
    // File search prompt template
    this.addPrompt(
      {
        name: 'search-drive-files',
        description: 'Generate a search query for Google Drive files',
        arguments: [
          {
            name: 'topic',
            description: 'The topic or subject to search for',
            required: true
          },
          {
            name: 'fileType',
            description: 'Preferred file type (optional)',
            required: false
          }
        ],
        serverName: this.name
      },
      ({ topic, fileType }) => ({
        messages: [{
          role: 'user',
          content: {
            type: 'text',
            text: `Search Google Drive for files related to "${topic}"${fileType ? ` of type ${fileType}` : ''}. Use the search_drive_files tool with appropriate search parameters.`
          }
        }]
      })
    );

    // File organization prompt
    this.addPrompt(
      {
        name: 'organize-drive-files',
        description: 'Get suggestions for organizing Google Drive files',
        arguments: [
          {
            name: 'currentStructure',
            description: 'Description of current file organization',
            required: true
          }
        ],
        serverName: this.name
      },
      ({ currentStructure }) => ({
        messages: [{
          role: 'user',
          content: {
            type: 'text',
            text: `Based on this Google Drive structure: "${currentStructure}", suggest improvements for better file organization. Consider folder hierarchy, naming conventions, and file categorization.`
          }
        }]
      })
    );
  }
}
