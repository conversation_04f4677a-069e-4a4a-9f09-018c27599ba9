# Backend (Express)

Generated on: 2025-05-26T19:05:18.596Z

## Key Files

### server/index.ts
Size: 2.22 KB

```ts
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  // Seed test data in development mode
  if (app.get("env") === "development") {
    try {
      const { seedTestData } = await import("./seed-data");
      await seedTestData();
    } catch (error) {
      console.error('Failed to seed test data:', error);
    }
  }

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();

```

### server/routes.ts
Size: 2.88 KB

```ts
import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { integrationController } from "./controllers/integration/index.js";
import { syncController } from "./controllers/sync";
import { schedulerService } from "./services/scheduler-service";
import { registerTestRoutes } from "./routes/test";
import { registerDebugRoutes } from "./routes/diagnostic";

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize the scheduler
  try {
    await schedulerService.initialize();
  } catch (error) {
    console.error('Failed to initialize scheduler:', error);
    console.log('Continuing without scheduler - manual sync will still work');
  }

  // Integration Routes
  app.get('/api/integrations', integrationController.getIntegrations);
  app.get('/api/integrations/:id', integrationController.getIntegration);
  app.post('/api/integrations', integrationController.createIntegration);
  app.put('/api/integrations/:id', integrationController.updateIntegration);
  app.delete('/api/integrations/:id', integrationController.deleteIntegration);
  
  // OAuth Routes
  app.get('/api/integrations/:id/auth-url', integrationController.getAuthUrl);
  app.get('/api/integrations/:id/oauth/callback', integrationController.handleOAuthCallback);
  
  // Global OAuth callback endpoint (for Google Cloud Console redirect URI configuration)
  app.get('/api/integrations/oauth/callback', (req, res) => {
    // Extract integration ID from state parameter
    const state = req.query.state as string;
    const stateData = state ? state.split('_') : [];
    const integrationId = stateData.length > 0 ? stateData[0] : null;
    
    if (integrationId) {
      // Redirect to the integration-specific callback
      const url = `/api/integrations/${integrationId}/oauth/callback${req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : ''}`;
      res.redirect(url);
    } else {
      res.status(400).json({ message: 'Invalid state parameter' });
    }
  });
  app.post('/api/integrations/:id/test-connection', integrationController.testConnection);
  app.get('/api/integrations/:id/folders', integrationController.getGoogleDriveFolders);
  
  // Schedule Routes
  app.post('/api/schedules', integrationController.updateSchedule);
  
  // Sync Routes
  app.get('/api/sync-logs', (req, res) => syncController.getSyncLogs(req, res));
  app.get('/api/sync-logs/:id', (req, res) => syncController.getSyncLog(req, res));
  app.post('/api/sync-now', (req, res) => syncController.syncNow(req, res));
  app.get('/api/sync-items', (req, res) => syncController.getSyncItems(req, res));

  // Test Routes (for debugging)
  registerTestRoutes(app);
  
  // Diagnostic Routes (for comprehensive debugging)
  registerDebugRoutes(app);

  const httpServer = createServer(app);

  return httpServer;
}

```

### server/db.ts
Size: 0.69 KB

```ts
import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";
import dotenv from "dotenv";
dotenv.config();

// Only configure Neon and create database connection if DATABASE_URL is provided
let pool: Pool | null = null;
let db: any = null;

if (process.env.DATABASE_URL) {
  neonConfig.webSocketConstructor = ws;
  pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = drizzle(pool, { schema });
  console.log('Database connection initialized with Neon');
} else {
  console.log('DATABASE_URL not set - using in-memory storage for development');
}

export { pool, db };
```

### drizzle.config.ts
Size: 0.32 KB

```ts
import { defineConfig } from "drizzle-kit";

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL, ensure the database is provisioned");
}

export default defineConfig({
  out: "./migrations",
  schema: "./shared/schema.ts",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
});

```

## Directory Structure

### server\controllers/
- integration.ts (30.12 KB)
- sync.ts (19.02 KB)

### server/
- db.ts (0.69 KB)
- index.ts (2.22 KB)
- routes.ts (2.88 KB)
- seed-data.ts (2.63 KB)
- setup-notion.ts (14.35 KB)
- storage.ts (12.90 KB)
- utils.ts (4.10 KB)
- vite.ts (2.33 KB)

### server/routes/
- system.ts (System health and diagnostic routes)
- integrations.ts (OAuth and integration management)
- sync.ts (Sync schedules and logs)
- chat.ts (Chat sessions and messages)
- files.ts (File management and search)
- rag.ts (RAG search functionality)
- upload.ts (File upload services)
- legacy.ts (Legacy compatibility routes)
- test.ts (Test routes for debugging)
- diagnostic.ts (Comprehensive diagnostic routes)
- index.ts (Route registration functions)
- README.md (Route organization documentation)

### server\scripts/
- setup-google-integration.ts (2.58 KB)

### server\services/
- crypto-service.ts (4.58 KB)
- google-service.ts (27.55 KB)
- notion-service.ts (23.94 KB)
- openai-service.ts (8.12 KB)
- scheduler-service.ts (3.48 KB)
- websocket-service.ts (5.00 KB)

## File Contents

### server\db.ts

```ts
import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";
import dotenv from "dotenv";
dotenv.config();

// Only configure Neon and create database connection if DATABASE_URL is provided
let pool: Pool | null = null;
let db: any = null;

if (process.env.DATABASE_URL) {
  neonConfig.webSocketConstructor = ws;
  pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = drizzle(pool, { schema });
  console.log('Database connection initialized with Neon');
} else {
  console.log('DATABASE_URL not set - using in-memory storage for development');
}

export { pool, db };
```

### server\index.ts

```ts
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  // Seed test data in development mode
  if (app.get("env") === "development") {
    try {
      const { seedTestData } = await import("./seed-data");
      await seedTestData();
    } catch (error) {
      console.error('Failed to seed test data:', error);
    }
  }

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();

```

### server\routes.ts

```ts
import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { integrationController } from "./controllers/integration/index.js";
import { syncController } from "./controllers/sync";
import { schedulerService } from "./services/scheduler-service";
  import { registerTestRoutes } from "./routes/test";
  import { registerDebugRoutes } from "./routes/diagnostic";

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize the scheduler
  try {
    await schedulerService.initialize();
  } catch (error) {
    console.error('Failed to initialize scheduler:', error);
    console.log('Continuing without scheduler - manual sync will still work');
  }

  // Integration Routes
  app.get('/api/integrations', integrationController.getIntegrations);
  app.get('/api/integrations/:id', integrationController.getIntegration);
  app.post('/api/integrations', integrationController.createIntegration);
  app.put('/api/integrations/:id', integrationController.updateIntegration);
  app.delete('/api/integrations/:id', integrationController.deleteIntegration);
  
  // OAuth Routes
  app.get('/api/integrations/:id/auth-url', integrationController.getAuthUrl);
  app.get('/api/integrations/:id/oauth/callback', integrationController.handleOAuthCallback);
  
  // Global OAuth callback endpoint (for Google Cloud Console redirect URI configuration)
  app.get('/api/integrations/oauth/callback', (req, res) => {
    // Extract integration ID from state parameter
    const state = req.query.state as string;
    const stateData = state ? state.split('_') : [];
    const integrationId = stateData.length > 0 ? stateData[0] : null;
    
    if (integrationId) {
      // Redirect to the integration-specific callback
      const url = `/api/integrations/${integrationId}/oauth/callback${req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : ''}`;
      res.redirect(url);
    } else {
      res.status(400).json({ message: 'Invalid state parameter' });
    }
  });
  app.post('/api/integrations/:id/test-connection', integrationController.testConnection);
  app.get('/api/integrations/:id/folders', integrationController.getGoogleDriveFolders);
  
  // Schedule Routes
  app.post('/api/schedules', integrationController.updateSchedule);
  
  // Sync Routes
  app.get('/api/sync-logs', (req, res) => syncController.getSyncLogs(req, res));
  app.get('/api/sync-logs/:id', (req, res) => syncController.getSyncLog(req, res));
  app.post('/api/sync-now', (req, res) => syncController.syncNow(req, res));
  app.get('/api/sync-items', (req, res) => syncController.getSyncItems(req, res));

  // Test Routes (for debugging)
  registerTestRoutes(app);
  
  // Diagnostic Routes (for comprehensive debugging)
  registerDiagnosticRoutes(app);

  const httpServer = createServer(app);

  return httpServer;
}

```

### server\scripts\setup-google-integration.ts

```ts
/**
 * This script sets up a Google integration with default configuration settings
 * for Google Meet transcripts synchronization.
 */

import { storage } from '../storage';

/**
 * Create a Google Meet integration with default settings
 */
async function setupGoogleIntegration() {
  try {
    console.log('Setting up Google Meet integration...');
    
    // Check if integration already exists
    const existingIntegrations = await storage.getIntegrationsByType('google_drive');
    
    if (existingIntegrations.length > 0) {
      console.log('Google Meet integration already exists, skipping setup');
      return;
    }
    
    // Create a new integration for Google Meet
    const integration = await storage.createIntegration({
      type: 'google_drive',
      name: 'Google Drive Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        // Default configuration settings
        syncEnabled: true,
        processingEnabled: true,
      },
      sourceConfig: {
        // Google Drive folder will be selected by the user during setup
        folderId: '',
        includeSubfolders: false,
        filenamePattern: '*.doc',
      },
      destinationConfig: {
        // Notion database ID will be discovered or created during sync
        databaseId: '',
        createNewPages: true,
        updateExisting: true,
      },
      syncFilters: {
        // Default sync filter settings
        skipProcessed: true,
        dateRange: {
          start: null, // No start date limit
          end: null,   // No end date limit
        },
        fileTypes: ['document'],
      },
      syncSchedule: null, // No default schedule
      isLlmEnabled: true, // Enable LLM for metadata extraction by default
      syncStatus: 'idle',
    });
    
    console.log(`Created Google Drive integration with ID: ${integration.id}`);
    
    // Display next steps
    console.log('\nNext steps:');
    console.log('1. Configure Google OAuth credentials in the application');
    console.log('2. Sync your Google Drive documents to Notion');
  } catch (error) {
    console.error('Error setting up Google integration:', error);
  }
}

```

### server\seed-data.ts

```ts
import { storage } from "./storage";

async function seedTestData() {
  try {
    console.log('🌱 Seeding test data...');

    // Check if we already have integrations
    const existingIntegrations = await storage.getIntegrations();
    
    if (existingIntegrations.length > 0) {
      console.log(`✅ Found ${existingIntegrations.length} existing integrations. Skipping seed.`);
      return;
    }

    // Create Google Drive integration
    const googleDriveIntegration = await storage.createIntegration({
      type: 'google_drive',
      name: 'Google Drive Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        description: 'Sync documents from Google Drive'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    // Create Microsoft Teams integration
    const teamsIntegration = await storage.createIntegration({
      type: 'microsoft_teams',
      name: 'Microsoft Teams Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        description: 'Sync meeting transcripts from Microsoft Teams'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    // Create Slack integration
    const slackIntegration = await storage.createIntegration({
      type: 'slack',
      name: 'Slack Integration',
      status: 'disconnected',
      credentials: null,
      config: {
        description: 'Sync conversation logs from Slack channels'
      },
      sourceConfig: {},
      destinationConfig: {},
      isLlmEnabled: true,
      syncFilters: {},
      syncSchedule: null,
      syncStatus: 'idle'
    });

    console.log('✅ Successfully seeded test data:');
    console.log(`   - Google Drive Integration (ID: ${googleDriveIntegration.id})`);
    console.log(`   - Microsoft Teams Integration (ID: ${teamsIntegration.id})`);
    console.log(`   - Slack Integration (ID: ${slackIntegration.id})`);
  } catch (error) {
    console.error('Error seeding test data:', error);
  }
}

```

### server\services\crypto-service.ts

```ts
import crypto from 'crypto';
import dotenv from "dotenv";
dotenv.config();

/**
 * Service for encrypting and decrypting sensitive data
 */
class CryptoService {
  private algorithm = 'aes-256-cbc';
  private key: Buffer;
  private ivLength = 16;

  constructor() {
    // Get encryption key from environment
    const encryptionKey = process.env.ENCRYPTION_KEY;
    
    if (!encryptionKey) {
      console.warn('ENCRYPTION_KEY not found in environment variables. Using a default key for development. DO NOT USE IN PRODUCTION!');
      // Default key for development - 32 bytes (256 bits)
      this.key = crypto.scryptSync('default-development-key-do-not-use-in-production', 'salt', 32);
    } else {
      // Create a fixed-length key using the provided encryption key
      this.key = crypto.scryptSync(encryptionKey, 'salt', 32);
    }
  }

  /**
   * Encrypt a string value
   * @param text String to encrypt
   * @returns Encrypted string in format: iv:encryptedData (base64)
   */
  async encrypt(text: string): Promise<string> {
    try {
      console.log('[CRYPTO] Encrypting data, text length:', text.length);
      
      // Generate a random initialization vector
      const iv = crypto.randomBytes(this.ivLength);
      
      // Create cipher
      const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);
      
      // Encrypt the text
      let encrypted = cipher.update(text, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      // Return IV and encrypted data as a single string (IV:encrypted)
      const result = `${iv.toString('base64')}:${encrypted}`;
      console.log('[CRYPTO] Encryption successful, result length:', result.length);
      
      return result;
    } catch (error) {
      console.error('[CRYPTO] Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt an encrypted string
   * @param encryptedText String in format: iv:encryptedData (base64)
   * @returns Decrypted str
... (truncated)
```

### server\services\openai-service.ts

```ts
import OpenAI from "openai";

class OpenAIService {
  private openai!: OpenAI;
  private initialized: boolean = false;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn(
        "OPENAI_API_KEY not found in environment. AI features will be disabled.",
      );
      return;
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.initialized = true;
    console.log("OpenAI service initialized successfully");
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Execute OpenAI API call with retry logic and exponential backoff
   */
  private async executeWithRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[OpenAI] Attempt ${attempt}/${maxRetries}`);
        return await apiCall();
      } catch (error: any) {
        if (error.status === 429 && attempt < maxRetries) {
          // Rate limit error - wait before retrying
          const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
          console.log(`[OpenAI] Rate limit hit, waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // If it's the last attempt or not a rate limit error, throw the error
        throw error;
      }
    }
    
    throw new Error('Max retries exceeded');
  }

  /**
   * Extract meeting name from transcript content
   */
  async extractMeetingName(content: string): Promise<string> {
    if (!this.initialized) {
      return "Untitled Meeting";
    }

    try {
      // Take first 3000 characters for context (to stay within token limits)
      const contextText = content.substring(0, 3000);

      const response = await this.openai.chat.completions.create
... (truncated)
```

### server\services\scheduler-service.ts

```ts
import { scheduleJob, Job, RecurrenceRule, RecurrenceSpecDateRange } from 'node-schedule';
import { syncController } from '../controllers/sync';

/**
 * Service for scheduling and managing recurring sync jobs
 */
class SchedulerService {
  private jobs: Map<number, Job> = new Map();
  
  /**
   * Initialize the scheduler service
   * Loads all scheduled integrations from the database and schedules them
   */
  async initialize(): Promise<void> {
    try {
      // Get all integrations with schedules from storage
      const { storage } = await import('../storage');
      const integrations = await storage.getIntegrations();
      
      let scheduledCount = 0;
      
      // Schedule each integration with a valid schedule
      for (const integration of integrations) {
        if (integration.syncSchedule) {
          this.scheduleIntegrationSync(integration.id, integration.syncSchedule);
          scheduledCount++;
        }
      }
      
      console.log(`Initialized scheduler with ${scheduledCount} jobs`);
    } catch (error) {
      console.error('Error initializing scheduler:', error);
    }
  }
  
  /**
   * Schedule an integration to sync on a recurring basis
   * @param integrationId ID of the integration to sync
   * @param cronExpression Cron expression for the schedule
   */
  scheduleIntegrationSync(integrationId: number, cronExpression: string): void {
    try {
      // Cancel any existing job for this integration
      this.cancelSchedule(integrationId);
      
      // Create a new job
      const job = scheduleJob(cronExpression, async () => {
        console.log(`Running scheduled sync for integration ${integrationId}`);
        
        try {
          // Run the sync operation
          await syncController.startSync(integrationId);
        } catch (error) {
          console.error(`Error in scheduled sync for integration ${integrationId}:`, error);
        }
      });
      
      // Store the job
      this.jobs.set(integrationId, job);
    
... (truncated)
```

### server\services\websocket-service.ts

```ts
import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';

interface WebSocketMessage {
  type: string;
  data: any;
}

interface IntegrationStatusUpdate {
  integrationId: number;
  status: string;
  message?: string;
  syncLogId?: number;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Set<WebSocket> = new Set();

  /**
   * Initialize WebSocket server
   */
  initialize(server: Server) {
    this.wss = new WebSocketServer({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', (ws: WebSocket, request) => {
      console.log('[WebSocket] New client connected from:', request.socket.remoteAddress);
      
      // Add client to our set
      this.clients.add(ws);

      // Send welcome message
      this.sendToClient(ws, {
        type: 'connection',
        data: { message: 'Connected to MeetSync WebSocket server' }
      });

      // Handle client messages
      ws.on('message', (data: Buffer) => {
        try {
          const message = JSON.parse(data.toString()) as WebSocketMessage;
          this.handleClientMessage(ws, message);
        } catch (error) {
          console.error('[WebSocket] Error parsing client message:', error);
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        console.log('[WebSocket] Client disconnected');
        this.clients.delete(ws);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error('[WebSocket] Client error:', error);
        this.clients.delete(ws);
      });
    });

    console.log('[WebSocket] Server initialized on /ws endpoint');
  }

  /**
   * Handle incoming messages from clients
   */
  private handleClientMessage(ws: WebSocket, message: WebSocketMessage) {
    console.log('[WebSocket] Received message:', message);

    switch (message.type) {
      case 'ping':
        this.sendToClient(ws, {
          type: 'pong',
          data: { message: 'Pong' }
        });
        break;
      case 'status-update':
        const update = message.data as IntegrationStatusUpdate;
        this.handleStatusUpdate(update);
        break;
      default:
        console.error('[WebSocket] Unknown message type:', message.type);
    }
  }

  private sendToClient(ws: WebSocket, message: WebSocketMessage) {
    ws.send(JSON.stringify(message));
  }

  private handleStatusUpdate(update: IntegrationStatusUpdate) {
    // Handle status update
  }
}

```

