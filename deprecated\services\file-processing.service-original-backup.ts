import { storage } from "../../storage/index.js";
import { openaiService } from "../openai-service";
import { simpleEmbeddingService as embeddingService } from "../simple-embedding-service.js";

// TODO: Move to base/types after modularization
interface ProcessResult {
  success: boolean;
  skipped: boolean;
  error?: string;
  savedFile?: any;
}

// TODO: Move to base/interfaces after modularization
interface IFileProcessor {
  processFile(fileData: any, existingFile: any, integration: any): Promise<ProcessResult>;
  canProcess(platform: string, fileType?: string): boolean;
}

// TODO: Move to base/types after modularization
interface PlatformMetadata {
  platform: string;
  sourceType: string;
  sourceContext: string;
  folderPath?: string;
  owner?: string;
  lastModified: Date;
}

// Import the new modular processors
import { ProcessorFactory, getFileProcessor } from './processors';

/**
 * File processing service for handling metadata extraction and vectorization
 * Now uses modular processors for different platforms
 */
export class FileProcessingService {
  /**
   * Process a Google Drive file
   * @deprecated Use processFile with platform parameter instead
   */
  async processGoogleFile(
    fileData: any,  // This is now the processed metadata with proper Date objects
    fileMetadata: any,
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    // Delegate to the new modular processor
    // Combine fileData and fileMetadata for the new interface
    const combinedData = { ...fileData, ...fileMetadata, originalFile: fileData };
    const processor = await getFileProcessor('google_drive');
    return processor.processFile(combinedData, existingFile, integration);
  }

  /**
   * Process a file using the appropriate platform processor
   */
  async processFile(
    platform: string,
    fileData: any,
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    try {
      const processor = await getFileProcessor(platform);
      return await processor.processFile(fileData, existingFile, integration);
    } catch (error: any) {
      console.error(`[FILE-PROCESSING] Error getting processor for platform ${platform}:`, error);
      return {
        success: false,
        skipped: false,
        error: `No processor available for platform: ${platform}`
      };
    }
  }

  /**
   * Check if a processor is available for a platform
   */
  hasProcessorForPlatform(platform: string): boolean {
    try {
      // Using dynamic import would be async, so for now just return false
      // This method should be refactored to be async if needed
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get all available platforms
   */
  getAvailablePlatforms(): string[] {
    try {
      // Using dynamic import would be async, so for now just return empty array
      // This method should be refactored to be async if needed
      return [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Legacy method - Process a Google Drive file (original implementation for backward compatibility)
   */
  async processGoogleFileLegacy(
    fileData: any,  // This is now the processed metadata with proper Date objects
    fileMetadata: any,
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    try {
      // Check if file has changed (only if it exists)
      let needsProcessing = true;
      let needsAIProcessing = false;

      if (existingFile) {
        // Compare modification times
        const existingModified = existingFile.lastModified ? new Date(existingFile.lastModified) : new Date(0);
        const sourceModified = fileMetadata.lastModified || new Date(); // lastModified is already a Date object
        
        if (sourceModified <= existingModified && existingFile.status === 'active') {
          // File hasn't changed, skip processing
          console.log(`Skipping unchanged file: ${fileMetadata.fileName}`);
          return { success: true, skipped: true };
        } else {
          console.log(`File changed, will reprocess: ${fileMetadata.fileName}`);
          needsAIProcessing = true;
        }
      } else {
        // New file, needs full processing
        console.log(`New file detected: ${fileMetadata.fileName}`);
        needsAIProcessing = true;
      }

      // Skip AI processing if file hasn't changed
      if (!needsProcessing) {
        // Just update status to active if it was deleted before
        if (existingFile && existingFile.status !== 'active') {
          await storage.updateFileStatus(existingFile.id, 'active');
          console.log(`Reactivated file: ${fileMetadata.fileName}`);
        }
        return { success: true, skipped: true };
      }

      // Enhanced metadata for meeting transcripts using AI (only if needed)
      if (needsAIProcessing && fileMetadata.fileType === 'transcript' && integration.isLlmEnabled && openaiService.isInitialized()) {
        try {
          console.log(`Extracting AI metadata for transcript: ${fileData.name}`);

          // For transcripts, we still extract some content for AI analysis
          // but we don't store the full content, just enhanced metadata
          const { GoogleServiceFacade } = await import('../google/google.facade.js');
          const googleService = new GoogleServiceFacade();
          await googleService.initialize();
          const auth = await googleService.getAuthorizedClient(integration.credentials);
          const { transcriptText } = await googleService.extractDocContent(auth, fileData.id || "");

          if (transcriptText) {
            const aiMetadata = await openaiService.extractMetadata(
              transcriptText.substring(0, 6000), // Limit content for AI analysis
              fileData.name || "Unknown File",
            );

            // Merge AI metadata into our file metadata
            fileMetadata.extractedMetadata = {
              ...fileMetadata.extractedMetadata,
              aiExtracted: {
                attendees: aiMetadata.attendees,
                topics: aiMetadata.topics,
                meetingTitle: aiMetadata.title,
                date: aiMetadata.date,
                time: aiMetadata.time,
                summary: aiMetadata.summary,
              }
            };
          }
        } catch (aiError) {
          console.error(`Error extracting AI metadata for ${fileData.name}:`, aiError);
          // Continue without AI metadata
        }
      }

      let savedFile: any;

      if (existingFile) {
        // Update existing file with latest metadata
        savedFile = await storage.updateFile(existingFile.id, {
          fileName: fileMetadata.fileName,
          lastModified: fileMetadata.lastModified,
          extractedMetadata: fileMetadata.extractedMetadata,
          isShared: fileMetadata.isShared,
          sharedWith: fileMetadata.sharedWith,
          status: 'active', // Ensure it's marked as active
          updatedAt: new Date(),
        });
        
        console.log(`Updated file reference: ${fileMetadata.fileName}`);
      } else {
        // Create new file reference
        // Handle large file sizes that exceed PostgreSQL integer limits
        const safeFileSize = fileMetadata.fileSize && fileMetadata.fileSize > 2147483647
          ? 2147483647 // Max PostgreSQL integer value
          : fileMetadata.fileSize;

        savedFile = await storage.createFile({
          externalId: fileMetadata.externalId,
          fileName: fileMetadata.fileName,
          fileType: fileMetadata.fileType,
          mimeType: fileMetadata.mimeType,
          fileSize: safeFileSize,
          platform: fileMetadata.platform,
          sourceUrl: fileMetadata.sourceUrl,
          downloadUrl: fileMetadata.downloadUrl,
          thumbnailUrl: fileMetadata.thumbnailUrl,
          userId: fileMetadata.userId,
          lastModified: fileMetadata.lastModified,
          isShared: fileMetadata.isShared,
          sharedWith: fileMetadata.sharedWith,
          extractedMetadata: fileMetadata.extractedMetadata as any,
          status: 'active',
        });
        
        console.log(`Created new file reference: ${fileMetadata.fileName}`);
      }

      // Generate embeddings for RAG functionality (if enabled and content available)
      if (needsAIProcessing && embeddingService.isInitialized()) {
        await this.processAdvancedEmbeddings(savedFile, fileMetadata, fileData, integration);
      }

      return { success: true, skipped: false, savedFile };

    } catch (error: any) {
      console.error(`Error processing Google file ${fileMetadata.fileName}:`, error);
      return { success: false, skipped: false, error: error.message };
    }
  }

  /**
   * Process a Microsoft Teams file
   * @deprecated Use processFile with platform parameter instead
   */
  async processMicrosoftFile(
    file: any,
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    // Delegate to the new modular processor
    const processor = await getFileProcessor('microsoft_teams');
    return processor.processFile(file, existingFile, integration);
  }

  /**
   * Legacy method - Process a Microsoft Teams file (original implementation for backward compatibility)
   */
  async processMicrosoftFileLegacy(
    file: any,
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    try {
      // Check if file has changed
      let needsProcessing = true;
      let needsAIProcessing = false;

      if (existingFile) {
        const existingModified = existingFile.lastModified ? new Date(existingFile.lastModified) : new Date(0);
        const sourceModified = file.lastModified ? new Date(file.lastModified) : new Date();
        
        if (sourceModified <= existingModified && existingFile.status === 'active') {
          console.log(`Skipping unchanged file: ${file.fileName}`);
          return { success: true, skipped: true };
        } else {
          console.log(`File changed, will reprocess: ${file.fileName}`);
          needsAIProcessing = true;
        }
      } else {
        // New file, needs full processing
        console.log(`New file detected: ${file.fileName}`);
        needsAIProcessing = true;
      }

      // Enhanced metadata for meeting transcripts using AI (only if needed)
      if (needsAIProcessing && file.fileType === 'transcript' && integration.isLlmEnabled && openaiService.isInitialized()) {
        try {
          console.log(`Extracting enhanced AI metadata for Microsoft Teams file: ${file.fileName}`);
          
          // For Microsoft Teams, we now have enhanced metadata from Graph API
          // Pass this to the OpenAI service for better extraction
          const aiMetadata = await openaiService.extractMetadata(
            '', // We don't have the file content, but metadata is more accurate now
            file.fileName,
            file.extractedMetadata // Pass the enhanced Teams metadata
          );
          
          // Merge AI metadata with existing Teams metadata
          file.extractedMetadata = {
            ...file.extractedMetadata,
            aiExtractedTitle: aiMetadata.title,
            aiExtractedAttendees: aiMetadata.attendees,
            aiExtractedTopics: aiMetadata.topics,
            aiExtractedDate: aiMetadata.date,
            aiExtractedTime: aiMetadata.time,
            aiProcessed: true,
            aiProcessedAt: new Date().toISOString(),
            enhancedWithGraphAPI: !!(file.extractedMetadata as any)?.meetingAttendees || !!(file.extractedMetadata as any)?.calendarAttendees
          };
          
          console.log(`Enhanced AI metadata extracted for ${file.fileName}:`, {
            title: aiMetadata.title,
            attendeeCount: aiMetadata.attendees.length,
            topics: aiMetadata.topics,
            hasGraphAPIData: !!(file.extractedMetadata as any)?.meetingAttendees || !!(file.extractedMetadata as any)?.calendarAttendees
          });
        } catch (aiError) {
          console.error(`Error extracting AI metadata for ${file.fileName}:`, aiError);
          // Continue without AI metadata but mark as attempted
          file.extractedMetadata = {
            ...file.extractedMetadata,
            aiProcessed: false,
            aiError: (aiError as any)?.message || 'Unknown AI processing error',
            aiProcessedAt: new Date().toISOString()
          };
        }
      }

      let savedFile: any;

      if (existingFile) {
        savedFile = await storage.updateFile(existingFile.id, {
          fileName: file.fileName,
          lastModified: file.lastModified ? new Date(file.lastModified) : new Date(),
          extractedMetadata: file.extractedMetadata,
          mimeType: file.mimeType,
          fileSize: file.fileSize && file.fileSize > 2147483647 ? 2147483647 : file.fileSize,
          downloadUrl: file.downloadUrl,
          thumbnailUrl: file.thumbnailUrl,
          isShared: file.isShared,
          sharedWith: file.sharedWith,
          status: 'active',
          updatedAt: new Date(),
        });
        console.log(`Updated file reference: ${file.fileName}`);
      } else {
        // Handle large file sizes that exceed PostgreSQL integer limits
        const safeFileSize = file.fileSize && file.fileSize > 2147483647
          ? 2147483647 // Max PostgreSQL integer value
          : file.fileSize;

        savedFile = await storage.createFile({
          externalId: file.externalId,
          fileName: file.fileName,
          fileType: file.fileType,
          mimeType: file.mimeType,
          fileSize: safeFileSize,
          platform: file.platform,
          sourceUrl: file.sourceUrl,
          downloadUrl: file.downloadUrl,
          thumbnailUrl: file.thumbnailUrl,
          userId: file.userId,
          lastModified: file.lastModified ? new Date(file.lastModified) : new Date(),
          isShared: file.isShared,
          sharedWith: file.sharedWith,
          extractedMetadata: file.extractedMetadata as any,
          status: 'active',
        });
        console.log(`Created new file reference: ${file.fileName}`);
      }

      // Generate embeddings for RAG functionality (if enabled and content available)
      if (needsAIProcessing && embeddingService.isInitialized()) {
        await this.processTeamsEmbeddings(savedFile, file, integration);
      }

      return { success: true, skipped: false, savedFile };

    } catch (error: any) {
      console.error(`Error processing Microsoft file ${file.fileName}:`, error);
      return { success: false, skipped: false, error: error.message };
    }
  }

  /**
   * Process advanced embeddings with comprehensive content extraction (matching original logic)
   */
  private async processAdvancedEmbeddings(savedFile: any, fileMetadata: any, originalFile: any, integration: any): Promise<void> {
    try {
      // Check if file already has embeddings
      const hasEmbeddings = await embeddingService.hasEmbeddings(savedFile.id);

      if (!hasEmbeddings) {
        console.log(`Generating embeddings for file: ${fileMetadata.fileName}`);

        // Extract ALL possible content for comprehensive vectorization
        let contentToVectorize = "";

        try {
          // 1. Always include file metadata (name, description, etc.)
          const metadataContent = [
            `File: ${fileMetadata.fileName}`,
            `Type: ${fileMetadata.fileType}`,
            `Platform: ${fileMetadata.platform || 'google_drive'}`,
          ];

          if (fileMetadata.extractedMetadata) {
            const metadata = fileMetadata.extractedMetadata;
            if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
            if (metadata.title && metadata.title !== fileMetadata.fileName) {
              metadataContent.push(`Title: ${metadata.title}`);
            }
            if (metadata.topics && Array.isArray(metadata.topics)) {
              metadataContent.push(`Topics: ${metadata.topics.join(', ')}`);
            }
            if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
          }

          contentToVectorize = metadataContent.join('\n') + '\n\n';

          // 2. Comprehensive content extraction with specialized document processing
          try {
            console.log(`[SYNC] Extracting content for ${fileMetadata.fileName}...`);

            let contentExtracted = false;

            // First, try specialized document processing for supported file types
            const { pdfService } = await import('../pdf-service.js');
            const { wordService } = await import('../word-service.js');
            const { GoogleServiceFacade } = await import('../google/google.facade.js');
            const googleService = new GoogleServiceFacade();
            await googleService.initialize();

            // Check if this is a PDF file
            if ((pdfService as any).isPDFFile && (pdfService as any).isPDFFile(fileMetadata.fileName, originalFile.mimeType)) {
              try {
                console.log(`[GOOGLE] Processing PDF: ${fileMetadata.fileName}`);
                const auth = await googleService.getAuthorizedClient(integration.credentials);
                const pdfBuffer = await googleService.downloadPDFContent(auth, originalFile.id || "", fileMetadata.fileName);

                if (pdfBuffer) {
                  const platformMetadata = {
                    platform: 'google_drive' as const,
                    sourceType: 'google_drive_folder',
                    sourceContext: `Google Drive`,
                    folderPath: originalFile.parents && originalFile.parents.length > 0 ? `Folder ID: ${originalFile.parents[0]}` : 'Root',
                    owner: fileMetadata.extractedMetadata?.owner || 'Unknown',
                    lastModified: fileMetadata.lastModified || new Date()
                  };

                  const pdfResult = await pdfService.processPDFFile(
                    pdfBuffer,
                    fileMetadata.fileName,
                    platformMetadata,
                    integration.isLlmEnabled && (await import('../openai-service.js')).openaiService.isInitialized()
                  );

                  if (pdfResult.success && pdfResult.vectorizationContent) {
                    console.log(`[GOOGLE] Successfully processed PDF: ${fileMetadata.fileName}`);
                    console.log(`[GOOGLE] PDF content length: ${pdfResult.vectorizationContent.length} characters`);

                    // Use the comprehensive vectorization content from PDF service
                    contentToVectorize = pdfResult.vectorizationContent;
                    contentExtracted = true;
                  } else {
                    console.log(`[GOOGLE] PDF processing failed for ${fileMetadata.fileName}: ${pdfResult.error}`);
                  }
                } else {
                  console.log(`[GOOGLE] Could not download PDF content for: ${fileMetadata.fileName}`);
                }
              } catch (pdfError: any) {
                console.error(`[GOOGLE] Error processing PDF ${fileMetadata.fileName}:`, pdfError.message);
                // Continue to fallback methods
              }
            }
            // Check if this is a Word document
            else if ((wordService as any).isWordFile && (wordService as any).isWordFile(fileMetadata.fileName, originalFile.mimeType)) {
              try {
                console.log(`[GOOGLE] Processing Word document: ${fileMetadata.fileName}`);
                const auth = await googleService.getAuthorizedClient(integration.credentials);
                const wordBuffer = await googleService.downloadWordContent(auth, originalFile.id || "", fileMetadata.fileName);

                if (wordBuffer) {
                  const platformMetadata = {
                    platform: 'google_drive' as const,
                    sourceType: 'google_drive_folder',
                    sourceContext: `Google Drive`,
                    folderPath: originalFile.parents && originalFile.parents.length > 0 ? `Folder ID: ${originalFile.parents[0]}` : 'Root',
                    owner: fileMetadata.extractedMetadata?.owner || 'Unknown',
                    lastModified: fileMetadata.lastModified || new Date()
                  };

                  const wordResult = await wordService.processWordFile(
                    wordBuffer,
                    fileMetadata.fileName,
                    platformMetadata,
                    integration.isLlmEnabled && (await import('../openai-service.js')).openaiService.isInitialized()
                  );

                  if (wordResult.success && wordResult.vectorizationContent) {
                    console.log(`[GOOGLE] Successfully processed Word document: ${fileMetadata.fileName}`);
                    console.log(`[GOOGLE] Word content length: ${wordResult.vectorizationContent.length} characters`);

                    // Use the comprehensive vectorization content from Word service
                    contentToVectorize = wordResult.vectorizationContent;
                    contentExtracted = true;
                  } else {
                    console.log(`[GOOGLE] Word processing failed for ${fileMetadata.fileName}: ${wordResult.error}`);
                  }
                } else {
                  console.log(`[GOOGLE] Could not download Word content for: ${fileMetadata.fileName}`);
                }
              } catch (wordError: any) {
                console.error(`[GOOGLE] Error processing Word document ${fileMetadata.fileName}:`, wordError.message);
                // Continue to fallback methods
              }
            }

            // If specialized processing didn't work, try universal content extraction
            if (!contentExtracted) {
              try {
                // Try the universal extractFileContent method as fallback
                const auth = await googleService.getAuthorizedClient(integration.credentials);
                const fileContent = await googleService.extractFileContent(auth, originalFile);
                if (fileContent && fileContent.trim().length > 0) {
                  contentToVectorize += `Content:\n${fileContent}`;
                  contentExtracted = true;
                  console.log(`[GOOGLE] Universal extraction: ${fileContent.length} characters from ${fileMetadata.fileName}`);
                }
              } catch (universalError: any) {
                console.log(`[GOOGLE] Universal extraction failed for ${fileMetadata.fileName}: ${universalError.message}`);

                // Final fallback: try Google Docs API for document types
                try {
                  const auth = await googleService.getAuthorizedClient(integration.credentials);
                  const { transcriptText } = await googleService.extractDocContent(auth, originalFile.id || "");
                  if (transcriptText && transcriptText.trim().length > 0) {
                    contentToVectorize += `Content:\n${transcriptText}`;
                    contentExtracted = true;
                    console.log(`[GOOGLE] Docs API extraction: ${transcriptText.length} characters from ${fileMetadata.fileName}`);
                  }
                } catch (docsError: any) {
                  console.log(`[GOOGLE] Docs API extraction failed for ${fileMetadata.fileName}: ${docsError.message}`);
                }
              }
            }

            if (!contentExtracted) {
              console.log(`[GOOGLE] No content extracted for ${fileMetadata.fileName}, using metadata-only vectorization`);
            }

          } catch (contentError: any) {
            console.error(`[GOOGLE] Content extraction error for ${fileMetadata.fileName}:`, contentError.message);
            console.log(`[GOOGLE] Falling back to metadata-only vectorization for ${fileMetadata.fileName}`);
          }

          // 3. For files we can't extract content from, create meaningful searchable text
          if (contentToVectorize.trim().length < 100) {
            // Add file path and folder information for better searchability
            if (originalFile.parents && originalFile.parents.length > 0) {
              contentToVectorize += `\nLocation: Folder ID ${originalFile.parents[0]}`;
            }

            // Add MIME type information
            if (originalFile.mimeType) {
              contentToVectorize += `\nMIME Type: ${originalFile.mimeType}`;

              // Add human-readable file type descriptions
              const typeDescriptions: { [key: string]: string } = {
                'application/vnd.google-apps.document': 'Google Document - text document',
                'application/vnd.google-apps.spreadsheet': 'Google Spreadsheet - data and calculations',
                'application/vnd.google-apps.presentation': 'Google Presentation - slides and presentations',
                'application/vnd.google-apps.folder': 'Google Drive Folder - contains other files',
                'application/pdf': 'PDF Document - portable document format',
                'image/jpeg': 'JPEG Image - photo or picture',
                'image/png': 'PNG Image - photo or picture',
                'video/mp4': 'MP4 Video - video file',
                'audio/mpeg': 'MP3 Audio - audio file',
                'text/plain': 'Text File - plain text document',
              };

              const description = typeDescriptions[originalFile.mimeType];
              if (description) {
                contentToVectorize += `\nFile Description: ${description}`;
              }
            }

            // Add creation/modification dates for temporal search
            if (originalFile.createdTime) {
              contentToVectorize += `\nCreated: ${new Date(originalFile.createdTime).toLocaleDateString()}`;
            }
            if (originalFile.modifiedTime) {
              contentToVectorize += `\nModified: ${new Date(originalFile.modifiedTime).toLocaleDateString()}`;
            }
          }

          // Always generate embeddings if we have any content
          if (contentToVectorize.trim().length > 10) {
            console.log(`Vectorizing ${contentToVectorize.length} characters for ${fileMetadata.fileName}`);

            // Generate embeddings sequentially (await to prevent parallel processing)
            try {
              await embeddingService.processFileForEmbeddings(savedFile.id, contentToVectorize);
              console.log(`✅ Successfully generated embeddings for: ${fileMetadata.fileName}`);
            } catch (embeddingError) {
              console.error(`❌ Error generating embeddings for ${fileMetadata.fileName}:`, embeddingError);
            }
          } else {
            console.log(`⚠️ Minimal content for ${fileMetadata.fileName}, but still vectorizing metadata`);
            // Even with minimal content, vectorize the filename and type
            const minimalContent = `File: ${fileMetadata.fileName}\nType: ${fileMetadata.fileType}`;
            try {
              await embeddingService.processFileForEmbeddings(savedFile.id, minimalContent);
            } catch (embeddingError) {
              console.error(`❌ Error generating minimal embeddings for ${fileMetadata.fileName}:`, embeddingError);
            }
          }

        } catch (vectorizationError) {
          console.error(`❌ Error in vectorization process for ${fileMetadata.fileName}:`, vectorizationError);
          // Still try to create minimal embeddings
          const fallbackContent = `File: ${fileMetadata.fileName}\nType: ${fileMetadata.fileType}`;
          try {
            await embeddingService.processFileForEmbeddings(savedFile.id, fallbackContent);
          } catch (fallbackError) {
            console.error(`❌ Complete vectorization failure for ${fileMetadata.fileName}:`, fallbackError);
          }
        }
      } else {
        console.log(`✅ Embeddings already exist for: ${fileMetadata.fileName}`);
      }
    } catch (embeddingError) {
      console.error(`Error processing embeddings for ${fileMetadata.fileName}:`, embeddingError);
      // Continue with sync even if embedding generation fails
    }
  }

  /**
   * Process embeddings for a file (simple version for Microsoft files)
   */
  private async processEmbeddings(savedFile: any, fileData: any, fileName: string): Promise<void> {
    if (!embeddingService.isInitialized()) {
      return;
    }

    try {
      const hasEmbeddings = await embeddingService.hasEmbeddings(savedFile.id);

      if (!hasEmbeddings) {
        console.log(`Generating embeddings for file: ${fileName}`);

        // Build content for vectorization
        let contentToVectorize = "";

        const metadataContent = [
          `File: ${fileName}`,
          `Type: ${fileData.fileType || savedFile.fileType}`,
          `Platform: ${fileData.platform || savedFile.platform}`,
        ];

        if (fileData.extractedMetadata || savedFile.extractedMetadata) {
          const metadata = fileData.extractedMetadata || savedFile.extractedMetadata;
          
          if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
          if (metadata.title && metadata.title !== fileName) {
            metadataContent.push(`Title: ${metadata.title}`);
          }
          if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
        }

        contentToVectorize = metadataContent.join('\n');

        if (fileData.content) {
          contentToVectorize += `\n\nContent:\n${fileData.content}`;
        }

        if (contentToVectorize.trim()) {
          await embeddingService.processFileForEmbeddings(savedFile.id, contentToVectorize);
          console.log(`✅ Generated embeddings for ${fileName} (${contentToVectorize.length} chars)`);
        } else {
          console.log(`⚠️ No content to vectorize for ${fileName}`);
        }
      }
    } catch (error) {
      console.error(`Error processing embeddings for ${fileName}:`, error);
    }
  }

  /**
   * Process comprehensive Teams embeddings with specialized document processing (matching original logic)
   */
  private async processTeamsEmbeddings(savedFile: any, file: any, integration: any): Promise<void> {
    if (!embeddingService.isInitialized()) {
      return;
    }

    try {
      // Check if file already has embeddings
      const hasEmbeddings = await embeddingService.hasEmbeddings(savedFile.id);

      if (!hasEmbeddings) {
        console.log(`Generating embeddings for Teams file: ${file.fileName}`);

        // Build comprehensive content for vectorization
        let contentToVectorize = "";

        try {
          // 1. Always include file metadata (name, description, etc.)
          const metadataContent = [
            `File: ${file.fileName}`,
            `Type: ${file.fileType}`,
            `Platform: microsoft_teams`,
          ];

          if (file.extractedMetadata) {
            const metadata = file.extractedMetadata as any;
            
            // Add general metadata fields
            if (metadata.description) metadataContent.push(`Description: ${metadata.description}`);
            if (metadata.title && metadata.title !== file.fileName) {
              metadataContent.push(`Title: ${metadata.title}`);
            }
            if (metadata.summary) metadataContent.push(`Summary: ${metadata.summary}`);
            
            // Add Teams-specific metadata
            if (metadata._sourceType) {
              metadataContent.push(`Source Type: ${metadata._sourceType}`);
            }
            if (metadata._sourceContext) {
              metadataContent.push(`Source: ${metadata._sourceContext}`);
            }
            if (metadata._teamName) {
              metadataContent.push(`Team: ${metadata._teamName}`);
            }
            if (metadata._channelName) {
              metadataContent.push(`Channel: ${metadata._channelName}`);
            }
            if (metadata._siteName) {
              metadataContent.push(`SharePoint Site: ${metadata._siteName}`);
            }
            if (metadata._driveName) {
              metadataContent.push(`Drive: ${metadata._driveName}`);
            }
            
            // Add meeting-specific metadata
            if (metadata.meetingSubject) {
              metadataContent.push(`Meeting Subject: ${metadata.meetingSubject}`);
            }
            if (metadata.calendarSubject) {
              metadataContent.push(`Calendar Subject: ${metadata.calendarSubject}`);
            }
            if (metadata.meetingAttendees && Array.isArray(metadata.meetingAttendees)) {
              const attendeeNames = metadata.meetingAttendees
                .map((a: any) => a.displayName || a.email)
                .filter(Boolean)
                .join(', ');
              if (attendeeNames) {
                metadataContent.push(`Meeting Attendees: ${attendeeNames}`);
              }
            }
            if (metadata.calendarAttendees && Array.isArray(metadata.calendarAttendees)) {
              const attendeeNames = metadata.calendarAttendees
                .map((a: any) => a.emailAddress?.name || a.emailAddress?.address)
                .filter(Boolean)
                .join(', ');
              if (attendeeNames) {
                metadataContent.push(`Calendar Attendees: ${attendeeNames}`);
              }
            }
            if (metadata.organizer) {
              metadataContent.push(`Organizer: ${metadata.organizer.displayName || metadata.organizer.email}`);
            }
            if (metadata.meetingOrganizer) {
              metadataContent.push(`Meeting Organizer: ${metadata.meetingOrganizer.displayName || metadata.meetingOrganizer.email}`);
            }
            if (metadata.calendarOrganizer) {
              metadataContent.push(`Calendar Organizer: ${metadata.calendarOrganizer.displayName || metadata.calendarOrganizer.email}`);
            }
            
            // Add AI-extracted metadata if available
            if (metadata.aiExtractedTitle) {
              metadataContent.push(`AI Title: ${metadata.aiExtractedTitle}`);
            }
            if (metadata.aiExtractedAttendees && Array.isArray(metadata.aiExtractedAttendees)) {
              metadataContent.push(`AI Attendees: ${metadata.aiExtractedAttendees.join(', ')}`);
            }
            if (metadata.aiExtractedTopics && Array.isArray(metadata.aiExtractedTopics)) {
              metadataContent.push(`AI Topics: ${metadata.aiExtractedTopics.join(', ')}`);
            }
            if (metadata.aiExtractedDate) {
              metadataContent.push(`AI Date: ${metadata.aiExtractedDate}`);
            }
            if (metadata.aiExtractedTime) {
              metadataContent.push(`AI Time: ${metadata.aiExtractedTime}`);
            }
          }

          contentToVectorize = metadataContent.join('\n') + '\n\n';

          // 2. Try to extract content for PDFs and Word documents
          const { pdfService } = await import('../pdf-service.js');
          const { wordService } = await import('../word-service.js');
          const { microsoftService } = await import('../microsoft');
          const { cryptoService } = await import('../crypto-service.js');

          const isPdf = (pdfService as any).isPDFFile && (pdfService as any).isPDFFile(file.fileName, (file.extractedMetadata as any)?.mimeType);
          const isWord = (wordService as any).isWordFile && (wordService as any).isWordFile(file.fileName, (file.extractedMetadata as any)?.mimeType);

          if (file.fileType === 'document' && (isPdf || isWord)) {
            try {
              const fileType = isPdf ? 'PDF' : 'Word document';
              console.log(`[TEAMS] Attempting ${fileType} content extraction for: ${file.fileName}`);

              // Get drive ID and file ID from metadata
              const metadata = file.extractedMetadata as any;
              const driveId = metadata?.driveId;
              const fileId = file.externalId;

              if (driveId && fileId) {
                // Download the file buffer first
                const credentials = JSON.parse(await cryptoService.decrypt(integration.credentials));
                const client = (microsoftService as any).createGraphClient(credentials);
                const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
                  .responseType('arraybuffer' as any)
                  .get();

                if (downloadResponse) {
                  // Convert response to buffer (same logic as microsoft-service.ts)
                  let buffer: Buffer;
                  if (Buffer.isBuffer(downloadResponse)) {
                    buffer = downloadResponse;
                  } else if (downloadResponse instanceof ArrayBuffer) {
                    buffer = Buffer.from(downloadResponse);
                  } else if (downloadResponse instanceof Uint8Array) {
                    buffer = Buffer.from(downloadResponse);
                  } else if (typeof downloadResponse === 'string') {
                    buffer = Buffer.from(downloadResponse, 'binary');
                  } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
                    buffer = Buffer.from(downloadResponse.buffer);
                  } else {
                    throw new Error('Unexpected response type');
                  }

                  // Build platform metadata for document service
                  const platformMetadata = {
                    platform: 'microsoft_teams' as const,
                    sourceType: metadata._sourceType || 'teams_file',
                    sourceContext: metadata._sourceContext || `Microsoft Teams file`,
                    folderPath: metadata.folderPath || metadata._sourceFolderId || 'Unknown',
                    owner: metadata.owner || metadata.ownerName || 'Unknown',
                    lastModified: file.lastModified || new Date()
                  };

                  // Process document using appropriate centralized service
                  let documentResult;
                  if (isPdf) {
                    documentResult = await pdfService.processPDFFile(
                      buffer,
                      file.fileName,
                      platformMetadata,
                      integration.isLlmEnabled && openaiService.isInitialized()
                    );
                  } else if (isWord) {
                    documentResult = await wordService.processWordFile(
                      buffer,
                      file.fileName,
                      platformMetadata,
                      integration.isLlmEnabled && openaiService.isInitialized()
                    );
                  }

                  if (documentResult && documentResult.success && documentResult.vectorizationContent) {
                    console.log(`[TEAMS] Successfully processed ${fileType} using centralized service: ${file.fileName}`);
                    console.log(`[TEAMS] ${fileType} content length: ${documentResult.vectorizationContent.length} characters`);

                    // Use the comprehensive vectorization content from document service
                    contentToVectorize = documentResult.vectorizationContent;

                    // Update file metadata with document-extracted metadata
                    if (documentResult.metadata) {
                      file.extractedMetadata = {
                        ...file.extractedMetadata,
                        [isPdf ? 'pdfMetadata' : 'wordMetadata']: documentResult.metadata,
                        hasExtractedContent: true,
                        contentType: isPdf ? 'pdf' : 'word'
                      };
                    }
                  } else {
                    console.log(`[TEAMS] Centralized ${fileType} processing failed for ${file.fileName}: ${documentResult?.error || 'Unknown error'}`);
                    // Continue with metadata-only vectorization
                  }
                }
              } else {
                console.log(`[TEAMS] Missing driveId or fileId for ${fileType} extraction: ${file.fileName}`);
              }
            } catch (documentError: any) {
              const fileType = isPdf ? 'PDF' : 'Word document';
              console.error(`[TEAMS] Error extracting ${fileType} content for ${file.fileName}:`, documentError.message);
              // Continue with metadata-only vectorization
            }
          }

          // 3. Add file type descriptions and context
          if (file.fileType === 'transcript') {
            contentToVectorize += `File Description: Microsoft Teams meeting transcript or recording file\n`;
          } else if (file.fileType === 'document') {
            contentToVectorize += `File Description: Microsoft Teams shared document\n`;
          } else {
            contentToVectorize += `File Description: Microsoft Teams file - ${file.fileType}\n`;
          }

          // 4. Add temporal context
          if (file.lastModified) {
            contentToVectorize += `Last Modified: ${new Date(file.lastModified).toLocaleDateString()}\n`;
          }
          if (file.createdAt) {
            contentToVectorize += `Created: ${new Date(file.createdAt).toLocaleDateString()}\n`;
          }

          // 5. Add meeting time context if available
          if (file.extractedMetadata) {
            const metadata = file.extractedMetadata as any;
            if (metadata.meetingStartTime) {
              contentToVectorize += `Meeting Start: ${new Date(metadata.meetingStartTime).toLocaleString()}\n`;
            }
            if (metadata.calendarStartTime) {
              contentToVectorize += `Calendar Start: ${new Date(metadata.calendarStartTime).toLocaleString()}\n`;
            }
          }

          // Always generate embeddings if we have any content
          if (contentToVectorize.trim().length > 10) {
            console.log(`Vectorizing ${contentToVectorize.length} characters for Teams file: ${file.fileName}`);

            // Generate embeddings sequentially (await to prevent parallel processing)
            try {
              await embeddingService.processFileForEmbeddings(savedFile.id, contentToVectorize);
              console.log(`✅ Successfully generated embeddings for Teams file: ${file.fileName}`);
            } catch (embeddingError) {
              console.error(`❌ Error generating embeddings for Teams file ${file.fileName}:`, embeddingError);
            }
          } else {
            console.log(`⚠️ Minimal content for Teams file ${file.fileName}, using fallback vectorization`);
            // Even with minimal content, vectorize the filename and type
            const minimalContent = `File: ${file.fileName}\nType: ${file.fileType}\nPlatform: microsoft_teams`;
            try {
              await embeddingService.processFileForEmbeddings(savedFile.id, minimalContent);
            } catch (embeddingError) {
              console.error(`❌ Error generating minimal embeddings for Teams file ${file.fileName}:`, embeddingError);
            }
          }

        } catch (vectorizationError: any) {
          console.error(`❌ Error in vectorization process for Teams file ${file.fileName}:`, vectorizationError);
          // Still try to create minimal embeddings
          const fallbackContent = `File: ${file.fileName}\nType: ${file.fileType}\nPlatform: microsoft_teams`;
          try {
            await embeddingService.processFileForEmbeddings(savedFile.id, fallbackContent);
          } catch (fallbackError) {
            console.error(`❌ Complete vectorization failure for Teams file ${file.fileName}:`, fallbackError);
          }
        }
      } else {
        console.log(`✅ Embeddings already exist for Teams file: ${file.fileName}`);
      }
    } catch (embeddingError) {
      console.error(`Error processing embeddings for Teams file ${file.fileName}:`, embeddingError);
      // Continue with sync even if embedding generation fails
    }
  }
}

export const fileProcessingService = new FileProcessingService();