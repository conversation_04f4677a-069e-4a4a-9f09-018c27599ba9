import { Client } from '@microsoft/microsoft-graph-client';
import { AuthenticationProvider } from '@microsoft/microsoft-graph-client';
import { BaseService } from "../../base/service.interface";

export interface MicrosoftCredentials {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
  id_token?: string;
  expires_at?: number;
}

/**
 * Custom authentication provider for Microsoft Graph
 */
class CustomAuthProvider implements AuthenticationProvider {
  private credentials: MicrosoftCredentials;

  constructor(credentials: MicrosoftCredentials) {
    this.credentials = credentials;
  }

  async getAccessToken(): Promise<string> {
    // Check if token is expired
    if (this.credentials.expires_at && Date.now() >= this.credentials.expires_at) {
      throw new Error('Token expired, refresh required');
    }
    return this.credentials.access_token;
  }
}

/**
 * Microsoft OAuth Service - handles authentication and token management
 */
export class MicrosoftOAuthService extends BaseService {
  private clientId!: string; // Will be initialized in onInitialize
  private clientSecret!: string; // Will be initialized in onInitialize
  private tenantId!: string; // Will be initialized in onInitialize
  private redirectUri!: string; // Will be initialized in onInitialize

  constructor() {
    super('MicrosoftOAuth', '1.0.0', 'Microsoft OAuth authentication and token management');
  }

  protected async onInitialize(): Promise<void> {
    this.validateEnvironment(['MICROSOFT_CLIENT_ID', 'MICROSOFT_CLIENT_SECRET', 'MICROSOFT_TENANT_ID']);
    
    this.clientId = process.env.MICROSOFT_CLIENT_ID!;
    this.clientSecret = process.env.MICROSOFT_CLIENT_SECRET!;
    this.tenantId = process.env.MICROSOFT_TENANT_ID!;
    this.redirectUri = process.env.MICROSOFT_REDIRECT_URI!;

    this.log('info', 'Microsoft OAuth service initialized');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    const hasCredentials = !!(this.clientId && this.clientSecret && this.tenantId);
    return {
      hasCredentials,
      tenantId: this.tenantId,
      redirectUri: this.redirectUri,
    };
  }

  /**
   * Get authorization URL for OAuth flow
   */
  getAuthUrl(state?: string): string {
    this.ensureInitialized();

    const baseUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/authorize`;
    const params = new URLSearchParams({
      client_id: this.clientId,
      response_type: 'code',
      redirect_uri: this.redirectUri,
      scope: this.getRequiredScopes().join(' '),
      response_mode: 'query',
      ...(state && { state })
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code: string): Promise<MicrosoftCredentials> {
    this.ensureInitialized();
    this.validateString(code, 'authorization code');

    const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
    
    const params = new URLSearchParams({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      code,
      redirect_uri: this.redirectUri,
      grant_type: 'authorization_code'
    });

    try {
      this.log('info', 'Exchanging authorization code for tokens...');
      
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token exchange failed: ${response.status} ${errorData}`);
      }

      const tokenData = await response.json();
      
      // Add expiration timestamp
      const credentials: MicrosoftCredentials = {
        ...tokenData,
        expires_at: Date.now() + (tokenData.expires_in * 1000)
      };

      this.log('info', 'Successfully obtained tokens from Microsoft');
      return credentials;

    } catch (error: any) {
      this.handleError(error, 'token exchange');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<MicrosoftCredentials> {
    this.ensureInitialized();
    this.validateString(refreshToken, 'refresh token');

    const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
    
    const params = new URLSearchParams({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      refresh_token: refreshToken,
      grant_type: 'refresh_token'
    });

    try {
      this.log('info', 'Refreshing access token...');
      
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token refresh failed: ${response.status} ${errorData}`);
      }

      const tokenData = await response.json();
      
      const credentials: MicrosoftCredentials = {
        ...tokenData,
        expires_at: Date.now() + (tokenData.expires_in * 1000)
      };

      this.log('info', 'Successfully refreshed Microsoft tokens');
      return credentials;

    } catch (error: any) {
      this.handleError(error, 'token refresh');
    }
  }

  /**
   * Test connection to Microsoft Graph
   */
  async testConnection(credentials: MicrosoftCredentials): Promise<{
    success: boolean;
    user?: any;
    error?: string;
  }> {
    this.ensureInitialized();
    this.validateRequired(credentials, 'credentials');

    try {
      const client = this.createGraphClient(credentials);
      const user = await client.api('/me').get();
      
      return {
        success: true,
        user: {
          displayName: user.displayName,
          mail: user.mail || user.userPrincipalName,
          id: user.id
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create Microsoft Graph client with authentication
   */
  createGraphClient(credentials: MicrosoftCredentials): Client {
    this.ensureInitialized();
    this.validateRequired(credentials, 'credentials');

    const authProvider = new CustomAuthProvider(credentials);
    return Client.initWithMiddleware({ authProvider });
  }

  /**
   * Validate Microsoft credentials
   */
  validateCredentials(credentials: any): boolean {
    return !!(
      credentials &&
      credentials.access_token &&
      typeof credentials.access_token === 'string' &&
      credentials.token_type &&
      credentials.expires_in
    );
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(credentials: MicrosoftCredentials): boolean {
    if (!credentials.expires_at) {
      return true;
    }

    // Consider token expired 5 minutes before actual expiry
    return credentials.expires_at <= Date.now() + 5 * 60 * 1000;
  }

  /**
   * Get required scopes for Microsoft Graph access
   */
  getRequiredScopes(): string[] {
    return [
      'https://graph.microsoft.com/Files.Read.All',
      'https://graph.microsoft.com/Sites.Read.All',
      'https://graph.microsoft.com/Group.Read.All',
      'https://graph.microsoft.com/User.Read',
      'https://graph.microsoft.com/Directory.Read.All',
      'https://graph.microsoft.com/OnlineMeetings.Read',
      'https://graph.microsoft.com/Calendars.Read',
      'https://graph.microsoft.com/Chat.Read',
      'https://graph.microsoft.com/ChannelMessage.Read.All',
      'offline_access'
    ];
  }

  /**
   * Get tenant information
   */
  getTenantInfo(): {
    tenantId: string;
    clientId: string;
    redirectUri: string;
  } {
    this.ensureInitialized();
    
    return {
      tenantId: this.tenantId,
      clientId: this.clientId,
      redirectUri: this.redirectUri,
    };
  }

  /**
   * Get user information from credentials
   */
  async getUserInfo(credentials: MicrosoftCredentials): Promise<any> {
    this.ensureInitialized();
    this.validateRequired(credentials, 'credentials');

    try {
      const client = this.createGraphClient(credentials);
      const user = await client.api('/me')
        .select('id,displayName,mail,userPrincipalName,jobTitle,department,companyName')
        .get();
      
      return {
        id: user.id,
        displayName: user.displayName,
        email: user.mail || user.userPrincipalName,
        jobTitle: user.jobTitle,
        department: user.department,
        company: user.companyName,
      };
    } catch (error: any) {
      this.handleError(error, 'getting user info');
    }
  }
}
