-- =============================================================================
-- MeetSync Development PostgreSQL Initialization Script
-- =============================================================================

-- Include all production extensions
\i /docker-entrypoint-initdb.d/01-init.sql

-- Development-specific configurations
-- Enable more detailed logging for development
SET log_statement = 'all';
SET log_duration = on;
SET log_min_duration_statement = 0;

-- Create development helper functions
CREATE OR REPLACE FUNCTION reset_sequences()
RETURNS void AS $$
DECLARE
    rec RECORD;
BEGIN
    FOR rec IN
        SELECT sequence_name
        FROM information_schema.sequences
        WHERE sequence_schema = 'public'
    LOOP
        EXECUTE 'ALTER SEQUENCE ' || rec.sequence_name || ' RESTART WITH 1';
    END LOOP;
    RAISE NOTICE 'All sequences reset to 1';
END;
$$ LANGUAGE plpgsql;

-- Function to truncate all tables (useful for testing)
CREATE OR REPLACE FUNCTION truncate_all_tables()
RETURNS void AS $$
DECLARE
    rec RECORD;
BEGIN
    FOR rec IN
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    LOOP
        EXECUTE 'TRUNCATE TABLE ' || rec.table_name || ' CASCADE';
    END LOOP;
    RAISE NOTICE 'All tables truncated';
END;
$$ LANGUAGE plpgsql;

-- Development logging
DO $$
BEGIN
    RAISE NOTICE 'MeetSync Development PostgreSQL initialization completed';
    RAISE NOTICE 'Additional development functions available:';
    RAISE NOTICE '  - reset_sequences(): Reset all sequence counters to 1';
    RAISE NOTICE '  - truncate_all_tables(): Clear all table data';
    RAISE NOTICE 'Enhanced logging enabled for development';
END $$; 