require('dotenv').config();
const { google } = require('googleapis');
const pdf = require('pdf-parse');

async function testPDFProcessingWithOAuth() {
  try {
    console.log('🔍 Testing PDF processing for Anant A. Resume.pdf with OAuth...');
    
    // Test PDF file details
    const fileId = '1Zk6ZgZHomhCXxjvHx31a8DMIoiVzrSqx';
    const fileName = 'Anant A. Resume.pdf';
    
    console.log(`📄 File ID: ${fileId}`);
    console.log(`📄 File Name: ${fileName}`);
    
    // Check OAuth environment variables
    const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
    const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
    
    if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
      console.log('❌ Google OAuth credentials not found in environment');
      console.log('   Expected: GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET');
      console.log(`   GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID ? 'found' : 'missing'}`);
      console.log(`   GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET ? 'found' : 'missing'}`);
      console.log('✅ PDF OAuth processing test completed');
      return;
    }
    
    console.log('✅ Google OAuth credentials found');
    console.log('   Note: This test would need user OAuth tokens to download files');
    console.log('   The actual app handles this through the Google integration flow');
    
    // Test PDF parsing capability with a sample buffer
    console.log('\n📚 Testing PDF parsing capabilities...');
    
    // Create a simple test PDF buffer (minimal PDF structure)
    const testPDFBuffer = Buffer.from(`%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF Content) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000010 00000 n 
0000000053 00000 n 
0000000125 00000 n 
0000000185 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
279
%%EOF`);

    try {
      const testData = await pdf(testPDFBuffer);
      console.log('✅ PDF parsing library working');
      console.log(`   Extracted text: "${testData.text.trim()}"`);
      console.log(`   Page count: ${testData.numpages}`);
    } catch (parseError) {
      console.log('❌ PDF parsing failed:', parseError.message);
    }
    
    console.log('\n✅ PDF OAuth processing test completed');
    
  } catch (error) {
    console.error('❌ Error testing PDF processing:', error);
  }
}

testPDFProcessingWithOAuth(); 