import { BaseService } from "../base/service.interface";
import { storage } from "../../storage/index.js";
import type { InsertChatMessage, InsertChatSession } from "../../../shared/index.js";

/**
 * RAG Chat Service - handles chat sessions and message management
 */
export class RAGChatService extends BaseService {
  constructor() {
    super('RAGChat', '1.0.0', 'RAG chat session and message management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'RAG Chat service initialized');
  }

  protected getDependencies(): string[] {
    return ['storage'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    try {
      // Test storage connectivity
      const sessions = await storage.getChatSessions(undefined, 1);
      return {
        storageConnected: true,
        canAccessSessions: true,
        sessionCount: sessions.length,
      };
    } catch (error) {
      return {
        storageConnected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create a new chat session
   */
  async createChatSession(
    title?: string,
    enabledSources?: string[],
    userId?: string
  ): Promise<any> {
    this.ensureInitialized();

    try {
      const sessionData: InsertChatSession = {
        title: title || "New Chat",
        enabledSources: enabledSources || [],
        userId: userId || "anonymous",
      };

      const session = await storage.createChatSession(sessionData);
      this.log('info', `Created chat session ${session.id}`);
      return session;
    } catch (error: any) {
      this.handleError(error, 'creating chat session');
    }
  }

  /**
   * Get chat sessions for a user
   */
  async getChatSessions(userId?: string, limit: number = 20): Promise<any[]> {
    this.ensureInitialized();
    this.validateNumber(limit, 'limit', 1, 100);

    try {
      return await storage.getChatSessions(userId, limit);
    } catch (error: any) {
      this.log('error', 'Error getting chat sessions', error);
      return [];
    }
  }

  /**
   * Get messages for a chat session
   */
  async getChatMessages(sessionId: number, limit: number = 50): Promise<any[]> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);
    this.validateNumber(limit, 'limit', 1, 200);

    try {
      return await storage.getChatMessages(sessionId, limit);
    } catch (error: any) {
      this.log('error', 'Error getting chat messages', error);
      return [];
    }
  }

  /**
   * Store a user message
   */
  async storeUserMessage(
    sessionId: number,
    content: string,
    enabledSources: string[] = []
  ): Promise<any> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);
    this.validateString(content, 'message content');

    try {
      const userMessage: InsertChatMessage = {
        sessionId,
        role: "user",
        content,
        sourcesUsed: enabledSources,
        tokenCount: this.estimateTokenCount(content),
      };

      return await storage.createChatMessage(userMessage);
    } catch (error: any) {
      this.handleError(error, 'storing user message');
    }
  }

  /**
   * Store an AI assistant message
   */
  async storeAIMessage(
    sessionId: number,
    content: string,
    enabledSources: string[] = [],
    relevantChunks: any[] = [],
    model: string = "gpt-4.1-nano-2025-04-14"
  ): Promise<any> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);
    this.validateString(content, 'message content');

    try {
      const aiMessage: InsertChatMessage = {
        sessionId,
        role: "assistant",
        content,
        sourcesUsed: enabledSources,
        relevantChunks: relevantChunks.map(chunk => ({
          id: chunk.id,
          fileId: chunk.fileId,
          fileName: chunk.fileName,
          similarity: chunk.similarity,
        })),
        tokenCount: this.estimateTokenCount(content),
        model,
      };

      return await storage.createChatMessage(aiMessage);
    } catch (error: any) {
      this.handleError(error, 'storing AI message');
    }
  }

  /**
   * Delete a chat session and all its messages
   */
  async deleteChatSession(sessionId: number): Promise<boolean> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);

    try {
      const result = await storage.deleteChatSession(sessionId);
      this.log('info', `Deleted chat session ${sessionId}: ${result ? 'success' : 'failed'}`);
      return result;
    } catch (error: any) {
      this.log('error', 'Error deleting chat session', error);
      return false;
    }
  }

  /**
   * Update chat session (e.g., change enabled sources)
   */
  async updateChatSession(
    sessionId: number,
    updates: { title?: string; enabledSources?: string[] }
  ): Promise<any> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);

    try {
      return await storage.updateChatSession(sessionId, updates);
    } catch (error: any) {
      this.handleError(error, 'updating chat session');
    }
  }

  /**
   * Get chat history for context (excluding system messages)
   */
  async getChatHistory(sessionId: number, messageCount: number = 8): Promise<any[]> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);
    this.validateNumber(messageCount, 'message count', 1, 50);

    try {
      const chatHistory = await storage.getChatMessages(sessionId, messageCount + 2); // Get a few extra in case of system messages
      
      // Filter out system messages and return the requested count
      return chatHistory
        .filter(msg => msg.role !== "system")
        .slice(-messageCount) // Last N messages for context
        .map(msg => ({
          role: msg.role,
          content: msg.content,
        }));
    } catch (error: any) {
      this.log('error', 'Error getting chat history', error);
      return [];
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(sessionId: number): Promise<{
    messageCount: number;
    userMessages: number;
    assistantMessages: number;
    totalTokens: number;
    lastActivity: Date | null;
  }> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);

    try {
      const messages = await storage.getChatMessages(sessionId);
      
      const stats = {
        messageCount: messages.length,
        userMessages: messages.filter(m => m.role === 'user').length,
        assistantMessages: messages.filter(m => m.role === 'assistant').length,
        totalTokens: messages.reduce((sum, m) => sum + (m.tokenCount || 0), 0),
        lastActivity: messages.length > 0 ? new Date(messages[messages.length - 1].createdAt) : null,
      };

      return stats;
    } catch (error: any) {
      this.log('error', 'Error getting session stats', error);
      return {
        messageCount: 0,
        userMessages: 0,
        assistantMessages: 0,
        totalTokens: 0,
        lastActivity: null,
      };
    }
  }

  /**
   * Estimate token count for a text (rough approximation)
   */
  private estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Validate session exists
   */
  async validateSession(sessionId: number): Promise<boolean> {
    this.ensureInitialized();
    this.validateNumber(sessionId, 'session ID', 1);

    try {
      const sessions = await storage.getChatSessions(undefined, 1000); // Get many sessions to check
      return sessions.some(session => session.id === sessionId);
    } catch (error: any) {
      this.log('error', 'Error validating session', error);
      return false;
    }
  }

  /**
   * Clean up old sessions (utility method)
   */
  async cleanupOldSessions(daysOld: number = 30): Promise<number> {
    this.ensureInitialized();
    this.validateNumber(daysOld, 'days old', 1);

    try {
      const sessions = await storage.getChatSessions();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      let deletedCount = 0;
      for (const session of sessions) {
        const sessionDate = new Date(session.updatedAt);
        if (sessionDate < cutoffDate) {
          const deleted = await this.deleteChatSession(session.id);
          if (deleted) deletedCount++;
        }
      }

      this.log('info', `Cleaned up ${deletedCount} old sessions (older than ${daysOld} days)`);
      return deletedCount;
    } catch (error: any) {
      this.log('error', 'Error cleaning up old sessions', error);
      return 0;
    }
  }
}
