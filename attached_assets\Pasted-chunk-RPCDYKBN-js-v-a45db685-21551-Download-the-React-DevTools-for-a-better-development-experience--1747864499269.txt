chunk-RPCDYKBN.js?v=a45db685:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
setup.tsx:104 Fetching folders for integration 1...
setup.tsx:110 Fetched folders data: ObjectcanCreateFolder: trueemail: "azha<PERSON><EMAIL>"folders: Array(60)0: {parents: Array(1), id: '15WC2peFXdh7J8FFs8Vb0WhDRcXGbtMix', name: '<PERSON><PERSON>'}1: {parents: Arra<PERSON>(1), id: '1kusm3bnAY8S_BegYcd5rmRAEp5B-Lf4r', name: 'AlgoEx<PERSON>'}2: {parents: Arra<PERSON>(1), id: '14rI_4ZJIHz7_FCuAGvrdcUaLwp6DskUr', name: 'APPH 1040'}3: {parents: A<PERSON><PERSON>(1), id: '1e8LDixNOURfzNc3NnhucehUgoRHaLSL0', name: 'APPH 1040'}4: {parents: <PERSON><PERSON><PERSON>(1), id: '1kNwQwnpfpJlTVez_mi8Z4fE77lm4MMkU', name: 'APPH 1040'}5: {parents: Array(1), id: '1i1KQOJAsJBPV6Kw2bJI2zjvhlM_6xozg', name: 'ARBC 1501'}6: {parents: Array(1), id: '1czMcCXL_qNgazXeqQA6zNJuCKSpmJNP2', name: 'Calc 3'}7: {id: '0ByMUTQ5Gdt2mdTZtWDE5VkFWLWc', name: 'Calc Parties 2017'}8: {parents: Array(1), id: '1M_W9HTFlkSWz1PWi4kuqxBPX-DMCw9Cb', name: 'Centennial Apartment'}9: {parents: Array(1), id: '0B6DVoRoVOqauflVXVEtGYnllM0t5RllVWlhmRmJueWltSGF0aHpxMHNuMEpRWXhzS2syLVU', name: 'Classroom'}10: {id: '0B07Dlflcs1UefkFWTUlQaG5GN1p2ejkyT0cxZFBKV09zWDJnWk5Hakt3VE5PcmVxTGs3OHM', name: 'CS'}11: {parents: Array(1), id: '18SeMO9GtJR-YsHXQGt4wYAZbm0rN-aqc', name: 'CS 1301'}12: {parents: Array(1), id: '1Zeh6OqPMY-BXbka6giR8UcWEs4BJVf3r', name: 'CS 1331'}13: {parents: Array(1), id: '1lLe7yVRCkGEav8R9T3nukyhT8oF1ASEb', name: 'CS 1332'}14: {parents: Array(1), id: '1HH4bPaTapbHS-eGU5o6IygfnPchOObMi', name: 'CS 2050'}15: {parents: Array(1), id: '1tGw6m3nuTJKZ6V9n9r0qtsY_96eP9OSg', name: 'CS 2261'}16: {parents: Array(1), id: '1D8pGxcmu7BD42M4o10G_XDcFatNVqEpv', name: 'CS 2340'}17: {parents: Array(1), id: '1l5mF5PgC14IDVlPnmRkq4e6C8iUWDqs5', name: 'CS 3001'}18: {parents: Array(1), id: '1I2RyO0uZPJNv_39mmTnLfBDaLKOpEcQi', name: 'CS 3001'}19: {parents: Array(1), id: '1TH3x4nEb59s39rGFxDEplU7jFpT5hpVA', name: 'CS 3311'}20: {parents: Array(1), id: '1ISQLjIpCPA80XUE3alwPIEnyn1uaMYqZ', name: 'CS 3451'}21: {parents: Array(1), id: '1O43ljzMj8_qmk0X5nCjeh5FBW6KHT23o', name: 'CS 3451'}22: {parents: Array(1), id: '1d5R6Cqmk55GcZ71sDhvXmbW-0f-QB2Gi', name: 'CS 3873'}23: {id: '1hMJmo0YEyaYUN1npvt9RfOUnZyxTH-NF', name: 'CS 3873 Project'}24: {parents: Array(1), id: '1LJHX_sxHpXlt1WokqahgdwowT1T9MzOg', name: 'CS 4460'}25: {parents: Array(1), id: '1H1ggqC2PR1Lj5OXzmXdBEVjm0GQK__Q8', name: 'CS 4460'}26: {parents: Array(1), id: '1j9sNBzF_isXymifOLzlhp_7SEfZCmzHT', name: 'CS 4590'}27: {parents: Array(1), id: '1wz3yPwTXHumj_TdUpzk3Xp0ql7JeKLMM', name: 'CS 4590'}28: {parents: Array(1), id: '1PyQS6Uj2PxAsRRJLqHJRty2eqiNaVdm-', name: 'EAS'}29: {parents: Array(1), id: '1_Dgjn1p76y-JBrKtqreSxB6WhCRVYg-j', name: 'EAS 1601'}30: {parents: Array(1), id: '1QHEvL7OK5At2rjj8xS33ElORgR-S8SIC', name: 'ECON 2105'}31: {parents: Array(1), id: '1hHcE2ej3YyWWBVyd2wfGIRnOrPfJtY80', name: 'ENGL 1101'}32: {parents: Array(1), id: '1ir51qZZnGy3kAPSwOldIBWTgs-sERTmw', name: 'ENGL 1102'}33: {id: '1f0O5mmgWDIiGFrFPuXLI6f2RNibIKEql', name: 'Getting Started'}34: {id: '1bHCQI00rb3C97IV6osw1gZR0Zr4F73D3', name: 'GRAD 2k19'}35: {parents: Array(1), id: '13dGJPctMzIDssXumg21ZMu_xQzG13iaP', name: 'GT 1000'}36: {id: '0B_uJsdsot1tjLVFkRlhfVW1Gelk', name: 'HOPE Soccer Tournament'}37: {parents: Array(1), id: '14rF4UsFugL1uCIWuldTFL-mK3X8PFwzl', name: 'ID 2202'}38: {parents: Array(1), id: '1v7qhLpwVoqkpmWS3GpHdKvcIeanKWDE6', name: 'ISYE 3770'}39: {parents: Array(1), id: '15J4vkMm_-FQ5xFzKJ74Yiw990FdJnEQB', name: 'MATH 1551'}40: {parents: Array(1), id: '1MFDjDRdThtiRF4lq9Ig35XB5z9dYGG9c', name: 'MATH 3012'}41: {parents: Array(1), id: '14MZbhxGgmTwxfx8t4UZNhUoJDhyjJxJN', name: 'Past Courses'}42: {parents: Array(1), id: '1tjhN8u3rS8u47whrb2Tq1J5c7a3aQ9Tw', name: 'Physics'}43: {parents: Array(1), id: '19kMetxdPOUOx8gzxv5MFuXX5uGJaFhpW', name: 'POL 1101'}44: {id: '1YenioxP3QPJv963ZjoW7uq4eEx8jE2tK', name: 'Portfolio Examples'}45: {parents: Array(1), id: '1wopEZrVfS0cxkHosl01WgmG72rhx9KOK', name: 'PSYC 1101'}46: {parents: Array(1), id: '1_JYy5Hgjm-LdeTWT7RbWE2UWX11J_QbE', name: 'PSYC 2210'}47: {parents: Array(1), id: '1LJ66UnqyWrblvnCRGEo7uvT3WjxvmwXS', name: 'PSYC 2210'}48: {parents: Array(1), id: '1B3178dIDGwbuifLtyICpjE5IbwdeP4ww', name: 'PSYC 2760'}49: {parents: Array(1), id: '1SzQc48QZQlAr_aGF1C5oshwFx0m-ugcG', name: 'PSYC 2760'}50: {id: '1pDYRexmCFiDeYCUj6ZLe_edBuJdxblGy', name: 'psyc2012 '}51: {parents: Array(1), id: '19x3uEeR2PFyjhb3uHrEw76qT4wDScf2gmlmsmG7WXTxSHARM2D6qjQKMg7LTZW2_3AoSkUmg', name: 'SAT Prep 1'}52: {parents: Array(1), id: '1EYAn6WEz3Fhrh1WBlTg5QooeTj85oZJ4', name: 'Sources'}53: {parents: Array(1), id: '1rT_JsJMaVs2jF_mkr2KGG6ycftsGm5zE', name: 'Statistics'}54: {parents: Array(1), id: '1ZmseUBzYfm8h5RgQA3_vDZA1oukkZuqC', name: 'Tutoring (Euclidian Education)'}55: {parents: Array(1), id: '1lm1ke5HkTu7GZjdTIpLMMG3CjeXbvlQe', name: 'U.S. History'}56: {id: '1clSZzQK8idsuA2LIC-PhCEd5O4sTH6iV', name: 'UX Peer Review'}57: {id: '1FqBmtUd2Quh8eg2rDeFerBGYtA0XGBLl', name: 'Vision Statement Peer Review'}58: {parents: Array(1), id: '14-K3rUo53YOmgBfWySp3QuZbyQUMSNYu', name: 'Web Environment Resources'}59: {parents: Array(1), id: '10nRQZSE4nmqj5HHXjPz0Vd0TwLG2i6h-', name: 'Workout'}length: 60[[Prototype]]: Array(0)message: "Found 60 folders in Google Drive"user: "Azha Qari"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "15WC2peFXdh7J8FFs8Vb0WhDRcXGbtMix"name: "Aditi"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "1kusm3bnAY8S_BegYcd5rmRAEp5B-Lf4r"name: "AlgoExpert"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "14rI_4ZJIHz7_FCuAGvrdcUaLwp6DskUr"name: "APPH 1040"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "1M_W9HTFlkSWz1PWi4kuqxBPX-DMCw9Cb"name: "Centennial Apartment"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "0B6DVoRoVOqauflVXVEtGYnllM0t5RllVWlhmRmJueWltSGF0aHpxMHNuMEpRWXhzS2syLVU"name: "Classroom"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "1lLe7yVRCkGEav8R9T3nukyhT8oF1ASEb"name: "CS 1332"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "0B_uJsdsot1tjLVFkRlhfVW1Gelk"name: "HOPE Soccer Tournament"[[Prototype]]: Object
setup.tsx:246 Testing connection with folder: Objectid: "14rF4UsFugL1uCIWuldTFL-mK3X8PFwzl"name: "ID 2202"[[Prototype]]: Object
setup.tsx:169 Saving integration configuration with folder: ObjectarchiveProcessed: truefolderId: "14rF4UsFugL1uCIWuldTFL-mK3X8PFwzl"folderName: "ID 2202"[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
