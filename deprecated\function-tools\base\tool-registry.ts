import { 
  FunctionTool, 
  Tool<PERSON>ategory, 
  ToolRegistration, 
  IToolCategory,
  ToolValidator 
} from './function-tool.interface';

/**
 * Central registry for all function tools
 * Manages tool registration, discovery, and categorization
 */
export class ToolRegistry {
  private static instance: ToolRegistry;
  private tools: Map<string, ToolRegistration> = new Map();
  private categories: Map<ToolCategory, IToolCategory> = new Map();
  private isInitialized: boolean = false;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry();
    }
    return ToolRegistry.instance;
  }

  /**
   * Initialize the registry with tool categories
   */
  initialize(categories: IToolCategory[]): void {
    if (this.isInitialized) {
      console.warn('[ToolRegistry] Already initialized, skipping...');
      return;
    }

    console.log('[ToolRegistry] Initializing tool registry...');

    // Register categories
    categories.forEach(category => {
      this.registerCategory(category);
    });

    // Initialize all categories
    categories.forEach(category => {
      category.initialize();
    });

    this.isInitialized = true;
    console.log(`[ToolRegistry] Initialized with ${this.tools.size} tools across ${this.categories.size} categories`);
  }

  /**
   * Register a tool category
   */
  registerCategory(category: IToolCategory): void {
    const categoryType = category.getCategory();
    
    if (this.categories.has(categoryType)) {
      console.warn(`[ToolRegistry] Category '${categoryType}' already registered, skipping...`);
      return;
    }

    this.categories.set(categoryType, category);
    console.log(`[ToolRegistry] Registered category: ${categoryType}`);
  }

  /**
   * Register a tool with metadata
   */
  registerTool(registration: ToolRegistration): void {
    const { tool, category, isDynamic = false, platform, priority = 0 } = registration;

    // Validate tool structure
    if (!ToolValidator.validateTool(tool)) {
      throw new Error(`Invalid tool structure for '${(tool as any)?.name || 'unknown'}'`);
    }

    // Check for duplicate tool names
    if (this.tools.has(tool.name)) {
      console.warn(`[ToolRegistry] Tool '${tool.name}' already registered, overwriting...`);
    }

    // Register the tool
    this.tools.set(tool.name, {
      tool,
      category,
      isDynamic,
      platform,
      priority
    });

    console.log(`[ToolRegistry] Registered tool '${tool.name}' in category '${category}'${platform ? ` for platform '${platform}'` : ''}`);
  }

  /**
   * Get a tool by name
   */
  getTool(name: string): FunctionTool | undefined {
    const registration = this.tools.get(name);
    return registration?.tool;
  }

  /**
   * Get tool registration metadata
   */
  getToolRegistration(name: string): ToolRegistration | undefined {
    return this.tools.get(name);
  }

  /**
   * Get all tools
   */
  getAllTools(): FunctionTool[] {
    return Array.from(this.tools.values()).map(reg => reg.tool);
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category: ToolCategory): FunctionTool[] {
    return Array.from(this.tools.values())
      .filter(reg => reg.category === category)
      .map(reg => reg.tool);
  }

  /**
   * Get tools by platform
   */
  getToolsByPlatform(platform: string): FunctionTool[] {
    return Array.from(this.tools.values())
      .filter(reg => reg.platform === platform)
      .map(reg => reg.tool);
  }

  /**
   * Get dynamic tools only
   */
  getDynamicTools(): FunctionTool[] {
    return Array.from(this.tools.values())
      .filter(reg => reg.isDynamic)
      .map(reg => reg.tool);
  }

  /**
   * Get static tools only
   */
  getStaticTools(): FunctionTool[] {
    return Array.from(this.tools.values())
      .filter(reg => !reg.isDynamic)
      .map(reg => reg.tool);
  }

  /**
   * Check if a tool exists
   */
  hasTool(name: string): boolean {
    return this.tools.has(name);
  }

  /**
   * Get all tool names
   */
  getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  /**
   * Get tool names by category
   */
  getToolNamesByCategory(category: ToolCategory): string[] {
    return Array.from(this.tools.values())
      .filter(reg => reg.category === category)
      .map(reg => reg.tool.name);
  }

  /**
   * Get available categories
   */
  getCategories(): ToolCategory[] {
    return Array.from(this.categories.keys());
  }

  /**
   * Get category instance
   */
  getCategory(category: ToolCategory): IToolCategory | undefined {
    return this.categories.get(category);
  }

  /**
   * Execute a tool by name
   */
  async executeTool(name: string, parameters: any): Promise<string> {
    const registration = this.tools.get(name);
    if (!registration) {
      throw new Error(`Tool '${name}' not found in registry`);
    }

    // Validate parameters
    if (!ToolValidator.validateParameters(registration.tool, parameters)) {
      throw new Error(`Invalid parameters for tool '${name}'`);
    }

    // Find the appropriate category and execute
    const categoryInstance = this.categories.get(registration.category);
    if (!categoryInstance) {
      throw new Error(`Category '${registration.category}' not found for tool '${name}'`);
    }

    return categoryInstance.executeTool(name, parameters);
  }

  /**
   * Get tools formatted for OpenAI function calling
   */
  getToolsForOpenAI(): any[] {
    return this.getAllTools().map(tool => ({
      type: "function",
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  /**
   * Get tool categories with their tools for reporting
   */
  getToolCategoriesReport(): Record<string, string[]> {
    const report: Record<string, string[]> = {};
    
    for (const category of this.getCategories()) {
      const categoryName = category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      report[categoryName] = this.getToolNamesByCategory(category);
    }
    
    return report;
  }

  /**
   * Clear all tools (useful for testing)
   */
  clear(): void {
    this.tools.clear();
    this.categories.clear();
    this.isInitialized = false;
    console.log('[ToolRegistry] Registry cleared');
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    totalTools: number;
    dynamicTools: number;
    staticTools: number;
    categories: number;
    toolsByCategory: Record<string, number>;
  } {
    const stats = {
      totalTools: this.tools.size,
      dynamicTools: this.getDynamicTools().length,
      staticTools: this.getStaticTools().length,
      categories: this.categories.size,
      toolsByCategory: {} as Record<string, number>
    };

    // Count tools by category
    for (const category of this.getCategories()) {
      stats.toolsByCategory[category] = this.getToolsByCategory(category).length;
    }

    return stats;
  }

  /**
   * Remove a tool from the registry
   */
  removeTool(name: string): boolean {
    const removed = this.tools.delete(name);
    if (removed) {
      console.log(`[ToolRegistry] Removed tool '${name}'`);
    }
    return removed;
  }

  /**
   * Update tool registration
   */
  updateTool(name: string, updates: Partial<ToolRegistration>): boolean {
    const existing = this.tools.get(name);
    if (!existing) {
      return false;
    }

    const updated = { ...existing, ...updates };
    
    // If tool is being updated, validate it
    if (updates.tool) {
      if (!ToolValidator.validateTool(updates.tool)) {
        throw new Error(`Invalid tool structure for update of '${name}'`);
      }
    }

    this.tools.set(name, updated);
    console.log(`[ToolRegistry] Updated tool '${name}'`);
    return true;
  }
} 