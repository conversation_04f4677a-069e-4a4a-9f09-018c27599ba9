import { Client } from "@notionhq/client";
import { BaseService } from "../../base/service.interface";

/**
 * Notion Database Service - handles database operations and management
 */
export class NotionDatabaseService extends BaseService {
  constructor() {
    super('NotionDatabase', '1.0.0', 'Notion database operations and management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Notion Database service initialized');
  }

  protected getDependencies(): string[] {
    return ['NotionAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      supportedOperations: ['create', 'find', 'query', 'update'],
      databaseTypes: ['meeting_transcripts', 'custom'],
    };
  }

  /**
   * Find an existing Meeting Transcripts database in a Notion page
   */
  async findTranscriptsDatabase(client: Client, pageId: string): Promise<string | null> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');

    try {
      this.log('info', `Searching for Meeting Transcripts database in page: ${pageId}`);
      
      const response = await client.blocks.children.list({
        block_id: pageId,
      });
      
      // Look for child database blocks
      const databaseBlocks = response.results.filter(
        (block: any) => block.type === 'child_database'
      );
      
      this.log('info', `Found ${databaseBlocks.length} database blocks in page`);
      
      // Check each database to find one named "Meeting Transcripts"
      for (const block of databaseBlocks) {
        try {
          const databaseInfo = await client.databases.retrieve({
            database_id: block.id,
          });
          
          // Extract database title
          let title = '';
          if ((databaseInfo as any).title && (databaseInfo as any).title.length > 0) {
            title = (databaseInfo as any).title
              .map((titlePart: any) => titlePart.plain_text)
              .join('');
          }
          
          this.log('info', `Found database: "${title}" (ID: ${block.id})`);
          
          if (title.toLowerCase().includes('meeting') || 
              title.toLowerCase().includes('transcript')) {
            this.log('info', `Found existing Meeting Transcripts database: ${block.id}`);
            return block.id;
          }
        } catch (dbError) {
          this.log('error', `Error checking database ${block.id}`, dbError);
          continue;
        }
      }
      
      this.log('info', 'No Meeting Transcripts database found');
      return null;
    } catch (error: any) {
      this.log('error', 'Error finding Meeting Transcripts database', error);
      return null;
    }
  }

  /**
   * Create a Meeting Transcripts database in a Notion page
   */
  async createTranscriptsDatabase(client: Client, pageId: string): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');

    try {
      this.log('info', `Creating Meeting Transcripts database in page: ${pageId}`);
      
      // Create the database with the same schema as setup-notion.ts
      const response = await client.databases.create({
        parent: {
          type: 'page_id',
          page_id: pageId,
        },
        title: [
          {
            type: 'text',
            text: {
              content: 'Meeting Transcripts',
            },
          },
        ],
        properties: {
          Name: {
            title: {},
          },
          Date: {
            date: {},
          },
          Source: {
            select: {
              options: [
                { name: 'Google Meet', color: 'blue' },
                { name: 'Google Chat', color: 'green' },
                { name: 'Microsoft Teams', color: 'purple' },
                { name: 'Zoom', color: 'orange' },
                { name: 'Slack', color: 'pink' },
                { name: 'Other', color: 'gray' },
              ],
            },
          },
          Attendees: {
            rich_text: {},
          },
          Topics: {
            multi_select: {
              options: [],
            },
          },
          Duration: {
            rich_text: {},
          },
          SourceURL: {
            url: {},
          },
          Time: {
            rich_text: {},
          },
          "AI summary": {
            rich_text: {},
          },
        },
      });
      
      this.log('info', `Created Meeting Transcripts database: ${response.id}`);
      return response.id;
    } catch (error: any) {
      this.handleError(error, 'creating Meeting Transcripts database');
    }
  }

  /**
   * Find or create a Meeting Transcripts database in a Notion page
   */
  async findOrCreateTranscriptsDatabase(client: Client, pageId: string): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(pageId, 'page ID');

    // First try to find existing database
    const existingDatabaseId = await this.findTranscriptsDatabase(client, pageId);
    if (existingDatabaseId) {
      return existingDatabaseId;
    }
    
    // If not found, create a new one
    return await this.createTranscriptsDatabase(client, pageId);
  }

  /**
   * Get database information
   */
  async getDatabaseInfo(client: Client, databaseId: string): Promise<any> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');

    try {
      const database = await client.databases.retrieve({
        database_id: databaseId,
      });

      return {
        id: database.id,
        title: (database as any).title?.map((t: any) => t.plain_text).join('') || 'Untitled',
        properties: database.properties,
        created_time: (database as any).created_time,
        last_edited_time: (database as any).last_edited_time,
        url: (database as any).url,
      };
    } catch (error: any) {
      this.handleError(error, `getting database info for ${databaseId}`);
    }
  }

  /**
   * Query database pages
   */
  async queryDatabase(
    client: Client,
    databaseId: string,
    filter?: any,
    sorts?: any[],
    startCursor?: string,
    pageSize: number = 100
  ): Promise<{
    results: any[];
    nextCursor?: string;
    hasMore: boolean;
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');
    this.validateNumber(pageSize, 'page size', 1, 100);

    try {
      const response = await client.databases.query({
        database_id: databaseId,
        filter,
        sorts,
        start_cursor: startCursor,
        page_size: pageSize,
      });

      return {
        results: response.results,
        nextCursor: response.next_cursor || undefined,
        hasMore: response.has_more,
      };
    } catch (error: any) {
      this.handleError(error, `querying database ${databaseId}`);
    }
  }

  /**
   * Search for pages in database by title
   */
  async searchDatabaseByTitle(
    client: Client,
    databaseId: string,
    searchTerm: string
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');
    this.validateString(searchTerm, 'search term');

    try {
      const filter = {
        property: 'Name',
        title: {
          contains: searchTerm,
        },
      };

      const result = await this.queryDatabase(client, databaseId, filter);
      return result.results;
    } catch (error: any) {
      this.log('error', `Error searching database by title: ${searchTerm}`, error);
      return [];
    }
  }

  /**
   * Get all pages from database (with pagination)
   */
  async getAllDatabasePages(client: Client, databaseId: string): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');

    try {
      const allPages: any[] = [];
      let hasMore = true;
      let startCursor: string | undefined;

      while (hasMore) {
        const result = await this.queryDatabase(
          client,
          databaseId,
          undefined,
          undefined,
          startCursor,
          100
        );

        allPages.push(...result.results);
        hasMore = result.hasMore;
        startCursor = result.nextCursor;
      }

      this.log('info', `Retrieved ${allPages.length} pages from database ${databaseId}`);
      return allPages;
    } catch (error: any) {
      this.handleError(error, `getting all pages from database ${databaseId}`);
    }
  }

  /**
   * Update database properties
   */
  async updateDatabase(
    client: Client,
    databaseId: string,
    updates: {
      title?: any[];
      properties?: any;
    }
  ): Promise<any> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');
    this.validateRequired(updates, 'updates');

    try {
      const response = await client.databases.update({
        database_id: databaseId,
        ...updates,
      });

      this.log('info', `Updated database ${databaseId}`);
      return response;
    } catch (error: any) {
      this.handleError(error, `updating database ${databaseId}`);
    }
  }

  /**
   * Get database schema information
   */
  getDatabaseSchema(): {
    name: string;
    properties: Record<string, any>;
  } {
    return {
      name: 'Meeting Transcripts',
      properties: {
        Name: { type: 'title', description: 'Meeting title' },
        Date: { type: 'date', description: 'Meeting date' },
        Source: { 
          type: 'select', 
          description: 'Meeting source platform',
          options: ['Google Meet', 'Google Chat', 'Microsoft Teams', 'Zoom', 'Slack', 'Other']
        },
        Attendees: { type: 'rich_text', description: 'Meeting attendees' },
        Topics: { type: 'multi_select', description: 'Meeting topics' },
        Duration: { type: 'rich_text', description: 'Meeting duration' },
        SourceURL: { type: 'url', description: 'Source URL' },
        Time: { type: 'rich_text', description: 'Meeting time' },
        'AI summary': { type: 'rich_text', description: 'AI-generated summary' },
      },
    };
  }

  /**
   * Validate database structure
   */
  async validateDatabaseStructure(client: Client, databaseId: string): Promise<{
    isValid: boolean;
    missingProperties: string[];
    extraProperties: string[];
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Notion client');
    this.validateString(databaseId, 'database ID');

    try {
      const database = await client.databases.retrieve({
        database_id: databaseId,
      });

      const expectedSchema = this.getDatabaseSchema();
      const actualProperties = Object.keys(database.properties);
      const expectedProperties = Object.keys(expectedSchema.properties);

      const missingProperties = expectedProperties.filter(
        prop => !actualProperties.includes(prop)
      );

      const extraProperties = actualProperties.filter(
        prop => !expectedProperties.includes(prop)
      );

      return {
        isValid: missingProperties.length === 0,
        missingProperties,
        extraProperties,
      };
    } catch (error: any) {
      this.log('error', `Error validating database structure for ${databaseId}`, error);
      return {
        isValid: false,
        missingProperties: [],
        extraProperties: [],
      };
    }
  }
}
