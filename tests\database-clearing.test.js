#!/usr/bin/env node

/**
 * Test script to verify database clearing functionality
 */

const BASE_URL = 'http://localhost:8080';

async function testDatabaseClearing() {
  console.log('🧪 Testing Database Clearing Functionality...\n');

  try {
    // Test 1: Check if server is running
    console.log('1. Testing server health...');
    const healthResponse = await fetch(`${BASE_URL}/api/health`);
    if (!healthResponse.ok) {
      throw new Error('Server health check failed');
    }
    const healthData = await healthResponse.json();
    console.log('✅ Server is healthy:', healthData.status);

    // Test 2: Check initial state after clearing
    console.log('\n2. Checking initial state after database clearing...');
    const integrationsResponse = await fetch(`${BASE_URL}/api/integrations`);
    if (!integrationsResponse.ok) {
      throw new Error('Failed to fetch integrations');
    }
    const integrationsData = await integrationsResponse.json();
    console.log(`✅ Found ${integrationsData.integrations.length} integrations (should be 3 fresh ones)`);

    // Verify these are fresh integrations with expected names
    const expectedIntegrations = ['Google Drive Integration', 'Microsoft Teams Integration', 'Slack Integration'];
    const actualNames = integrationsData.integrations.map(i => i.name);
    
    for (const expectedName of expectedIntegrations) {
      if (actualNames.includes(expectedName)) {
        console.log(`✅ Found expected integration: ${expectedName}`);
      } else {
        console.log(`❌ Missing expected integration: ${expectedName}`);
      }
    }

    // Test 3: Check that all integrations have fresh IDs and are disconnected
    console.log('\n3. Verifying fresh integration state...');
    for (const integration of integrationsData.integrations) {
      if (integration.status === 'disconnected') {
        console.log(`✅ Integration "${integration.name}" is in fresh disconnected state (ID: ${integration.id})`);
      } else {
        console.log(`⚠️  Integration "${integration.name}" has unexpected status: ${integration.status}`);
      }
    }

    // Test 4: Check chat sessions (should be empty after clearing)
    console.log('\n4. Checking chat sessions state...');
    const sessionsResponse = await fetch(`${BASE_URL}/api/chat/sessions`);
    if (sessionsResponse.ok) {
      const sessionsData = await sessionsResponse.json();
      if (sessionsData.sessions && sessionsData.sessions.length === 0) {
        console.log('✅ Chat sessions are empty as expected after clearing');
      } else {
        console.log(`⚠️  Found ${sessionsData.sessions?.length || 0} chat sessions (expected 0)`);
      }
    } else {
      console.log('⚠️  Could not fetch chat sessions');
    }

    // Test 5: Check sources endpoint
    console.log('\n5. Testing chat sources endpoint...');
    const sourcesResponse = await fetch(`${BASE_URL}/api/chat/sources`);
    if (!sourcesResponse.ok) {
      throw new Error('Failed to fetch chat sources');
    }
    const sourcesData = await sourcesResponse.json();
    console.log(`✅ Found ${sourcesData.sources.length} chat sources available`);

    // Test 6: Create a test integration to verify functionality still works
    console.log('\n6. Testing integration creation after clearing...');
    const createResponse = await fetch(`${BASE_URL}/api/integrations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'google-drive',
        name: 'Test Integration After Clearing',
        status: 'disconnected',
        config: {},
        sourceConfig: {},
        destinationConfig: {},
        isLlmEnabled: true,
        syncFilters: {},
        syncSchedule: null,
      }),
    });

    if (!createResponse.ok) {
      const errorData = await createResponse.json();
      throw new Error(`Failed to create test integration: ${errorData.message || createResponse.statusText}`);
    }

    const createData = await createResponse.json();
    console.log('✅ Successfully created test integration after clearing:', createData.integration.name, `(ID: ${createData.integration.id})`);

    console.log('\n🎉 All database clearing tests passed!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Server health check');
    console.log('- ✅ Fresh integrations after clearing');
    console.log('- ✅ All integrations in disconnected state');
    console.log('- ✅ Chat sessions cleared');
    console.log('- ✅ Sources endpoint working');
    console.log('- ✅ Integration creation still functional');
    console.log('\n🧹 Database clearing functionality is working correctly!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testDatabaseClearing();
