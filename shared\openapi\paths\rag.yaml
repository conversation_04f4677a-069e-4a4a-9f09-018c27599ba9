RagSearch:
  get:
    tags:
      - RAG
    summary: RAG semantic search
    description: Perform semantic search using RAG functionality
    operationId: ragSearch
    parameters:
      - $ref: '../components/parameters.yaml#/SearchQuery'
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/SearchThreshold'
    responses:
      '200':
        $ref: '../components/responses.yaml#/RagSearchResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

RagStatus:
  get:
    tags:
      - RAG
    summary: Get RAG status
    description: Get the status of the RAG system and indexing
    operationId: getRagStatus
    responses:
      '200':
        description: RAG status retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                status:
                  type: object
                  properties:
                    totalFiles:
                      type: integer
                    processedFiles:
                      type: integer
                    totalChunks:
                      type: integer
                    indexedChunks:
                      type: integer
                    lastUpdate:
                      type: string
                      format: date-time
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 