import { Client } from "@notionhq/client";
import { BaseService } from "../../base/service.interface";

/**
 * Notion Authentication Service - handles Notion API authentication and client management
 */
export class NotionAuthService extends BaseService {
  private client: Client | null = null;
  private isAuthenticated = false;

  constructor() {
    super('NotionAuth', '1.0.0', 'Notion API authentication and client management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Notion Auth service initialized');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      isAuthenticated: this.isAuthenticated,
      hasClient: !!this.client,
      clientReady: this.isReady(),
    };
  }

  /**
   * Initialize Notion client with API key
   */
  async initializeWithApiKey(apiKey: string): Promise<void> {
    this.ensureInitialized();
    this.validateString(apiKey, 'API key');

    try {
      this.log('info', 'Initializing Notion client with API key...');
      
      this.client = new Client({ auth: apiKey });
      
      // Test the connection by getting user info
      const response = await this.client.users.me({});
      
      if (response && response.id) {
        this.isAuthenticated = true;
        this.log('info', 'Notion client initialized successfully');
        this.log('info', `Connected as user: ${response.name || response.id}`);
      } else {
        throw new Error("Failed to validate Notion API connection");
      }
    } catch (error: any) {
      this.isAuthenticated = false;
      this.client = null;
      this.handleError(error, 'initializing Notion client');
    }
  }

  /**
   * Initialize from encrypted API key
   */
  async initializeFromEncrypted(encryptedApiKey: string): Promise<void> {
    this.ensureInitialized();
    this.validateString(encryptedApiKey, 'encrypted API key');

    try {
      this.log('info', 'Initializing Notion client from encrypted API key...');
      
      const { cryptoService } = await import("../../core/crypto-service");
      const apiKey = await cryptoService.decrypt(encryptedApiKey);
      
      await this.initializeWithApiKey(apiKey);
    } catch (error: any) {
      this.isAuthenticated = false;
      this.client = null;
      this.handleError(error, 'initializing Notion client from encrypted key');
    }
  }

  /**
   * Check if the client is ready for use
   */
  isReady(): boolean {
    return this.isAuthenticated && this.client !== null;
  }

  /**
   * Get the Notion client instance
   */
  getClient(): Client {
    if (!this.isReady()) {
      throw new Error("Notion client not initialized. Call initialize() first.");
    }
    return this.client!;
  }

  /**
   * Test the current connection
   */
  async testConnection(): Promise<{
    success: boolean;
    user?: any;
    error?: string;
  }> {
    if (!this.isReady()) {
      return {
        success: false,
        error: 'Notion client not initialized',
      };
    }

    try {
      const user = await this.client!.users.me({});
      return {
        success: true,
        user: {
          id: user.id,
          name: user.name,
          type: user.type,
          avatar_url: user.avatar_url,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<any> {
    this.ensureInitialized();
    
    if (!this.isReady()) {
      throw new Error("Notion client not initialized");
    }

    try {
      const user = await this.client!.users.me({});
      return {
        id: user.id,
        name: user.name,
        type: user.type,
        avatar_url: user.avatar_url,
        person: (user as any).person,
        bot: (user as any).bot,
      };
    } catch (error: any) {
      this.handleError(error, 'getting current user');
    }
  }

  /**
   * List all users (if bot has permission)
   */
  async listUsers(): Promise<any[]> {
    this.ensureInitialized();
    
    if (!this.isReady()) {
      throw new Error("Notion client not initialized");
    }

    try {
      const response = await this.client!.users.list({});
      return response.results || [];
    } catch (error: any) {
      this.log('error', 'Error listing users', error);
      return [];
    }
  }

  /**
   * Get user by ID
   */
  async getUser(userId: string): Promise<any> {
    this.ensureInitialized();
    this.validateString(userId, 'user ID');
    
    if (!this.isReady()) {
      throw new Error("Notion client not initialized");
    }

    try {
      return await this.client!.users.retrieve({ user_id: userId });
    } catch (error: any) {
      this.handleError(error, `getting user ${userId}`);
    }
  }

  /**
   * Validate API key format
   */
  validateApiKey(apiKey: string): boolean {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }

    // Notion API keys typically start with 'secret_' and are 50+ characters
    return apiKey.startsWith('secret_') && apiKey.length >= 50;
  }

  /**
   * Get API key requirements
   */
  getApiKeyRequirements(): {
    format: string;
    minLength: number;
    prefix: string;
    example: string;
  } {
    return {
      format: 'Notion Integration Token',
      minLength: 50,
      prefix: 'secret_',
      example: 'secret_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    };
  }

  /**
   * Reset authentication state
   */
  reset(): void {
    this.client = null;
    this.isAuthenticated = false;
    this.log('info', 'Notion authentication reset');
  }

  /**
   * Cleanup resources
   */
  protected async onCleanup(): Promise<void> {
    this.reset();
    this.log('info', 'Notion Auth service cleaned up');
  }

  /**
   * Get authentication status
   */
  getAuthStatus(): {
    isAuthenticated: boolean;
    hasClient: boolean;
    isReady: boolean;
  } {
    return {
      isAuthenticated: this.isAuthenticated,
      hasClient: !!this.client,
      isReady: this.isReady(),
    };
  }

  /**
   * Refresh connection (re-test current credentials)
   */
  async refreshConnection(): Promise<boolean> {
    if (!this.client) {
      return false;
    }

    try {
      const response = await this.client.users.me({});
      this.isAuthenticated = !!(response && response.id);
      
      if (this.isAuthenticated) {
        this.log('info', 'Notion connection refreshed successfully');
      } else {
        this.log('warn', 'Notion connection refresh failed');
      }
      
      return this.isAuthenticated;
    } catch (error: any) {
      this.isAuthenticated = false;
      this.log('error', 'Error refreshing Notion connection', error);
      return false;
    }
  }

  /**
   * Get client configuration
   */
  getClientConfig(): {
    hasAuth: boolean;
    version: string;
    userAgent: string;
  } {
    if (!this.client) {
      return {
        hasAuth: false,
        version: 'unknown',
        userAgent: 'unknown',
      };
    }

    return {
      hasAuth: true,
      version: '2022-06-28', // Notion API version
      userAgent: 'notion-sdk-js',
    };
  }
}
