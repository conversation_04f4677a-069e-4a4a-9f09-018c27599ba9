import { ReactNode } from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface MetricsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: ReactNode;
  trend?: {
    value: number;
    label: string;
    type: "increase" | "decrease" | "neutral";
  };
  badge?: {
    text: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  children?: ReactNode;
  className?: string;
  onClick?: () => void;
}

export function MetricsCard({
  title,
  value,
  description,
  icon,
  trend,
  badge,
  children,
  className,
  onClick
}: MetricsCardProps) {
  const getTrendIcon = () => {
    switch (trend?.type) {
      case "increase":
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case "decrease":
        return <TrendingDown className="h-3 w-3 text-red-500" />;
      default:
        return <Minus className="h-3 w-3 text-muted-foreground" />;
    }
  };

  const getTrendColor = () => {
    switch (trend?.type) {
      case "increase":
        return "text-green-600";
      case "decrease":
        return "text-red-600";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <Card 
      className={cn(
        "hover:shadow-lg transition-all duration-200 cursor-pointer border-2 hover:border-primary/20",
        onClick && "hover:scale-[1.02]",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          {badge && (
            <Badge variant={badge.variant || "secondary"} className="text-xs">
              {badge.text}
            </Badge>
          )}
        </div>
        {icon && (
          <div className="p-2 bg-primary/10 rounded-lg">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="text-3xl font-bold tracking-tight">
            {value}
          </div>
          
          {(description || trend) && (
            <div className="flex items-center justify-between">
              {description && (
                <CardDescription className="text-xs">
                  {description}
                </CardDescription>
              )}
              
              {trend && (
                <div className={cn(
                  "flex items-center space-x-1 text-xs font-medium",
                  getTrendColor()
                )}>
                  {getTrendIcon()}
                  <span>{trend.value}%</span>
                  <span className="text-muted-foreground">{trend.label}</span>
                </div>
              )}
            </div>
          )}
          
          {children}
        </div>
      </CardContent>
    </Card>
  );
}

interface StatsGridProps {
  stats: Array<{
    title: string;
    value: string | number;
    description?: string;
    icon?: ReactNode;
    trend?: {
      value: number;
      label: string;
      type: "increase" | "decrease" | "neutral";
    };
    badge?: {
      text: string;
      variant?: "default" | "secondary" | "destructive" | "outline";
    };
    onClick?: () => void;
  }>;
  columns?: number;
  className?: string;
}

export function StatsGrid({ stats, columns = 3, className }: StatsGridProps) {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  };

  return (
    <div className={cn(
      "grid gap-6",
      gridCols[columns as keyof typeof gridCols] || gridCols[3],
      className
    )}>
      {stats.map((stat, index) => (
        <MetricsCard
          key={index}
          title={stat.title}
          value={stat.value}
          description={stat.description}
          icon={stat.icon}
          trend={stat.trend}
          badge={stat.badge}
          onClick={stat.onClick}
        />
      ))}
    </div>
  );
} 