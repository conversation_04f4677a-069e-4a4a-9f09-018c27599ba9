import { cryptoService } from "../core/crypto-service";

/**
 * Microsoft Teams Service
 * Handles authentication and API calls to Microsoft Graph API for Teams integration
 */
class TeamsService {
  /**
   * Generate an authorization URL for Microsoft OAuth flow
   * @param redirectUri Original redirect URI after OAuth flow (not used - we use global callback)
   * @param integrationId The ID of the integration being connected
   * @returns Object containing the auth URL and state token
   */
  getAuthUrl(
    redirectUri: string,
    integrationId?: number,
  ): { url: string; state: string } {
    // Get the integration ID from the redirect URI if not provided
    if (!integrationId && redirectUri) {
      const matches = redirectUri.match(/\/api\/integrations\/(\d+)\/oauth/);
      if (matches && matches[1]) {
        integrationId = parseInt(matches[1], 10);
      }
    }

    console.log(`[TEAMS] Generating OAuth URL for integration ID: ${integrationId}`);

    // Get Microsoft OAuth credentials from environment
    const clientId = process.env.MICROSOFT_CLIENT_ID;
    const tenantId = process.env.MICROSOFT_TENANT_ID;

    if (!clientId || !tenantId) {
      throw new Error("Microsoft OAuth credentials not configured");
    }

    // Always use the global callback endpoint as configured in Azure AD
    const globalRedirectUri = process.env.MICROSOFT_REDIRECT_URI ||
      `${process.env.SERVER_URL || "http://localhost:8080"}/api/integrations/teams/oauth/callback`;

    console.log(`[TEAMS] Using global redirect URI: ${globalRedirectUri}`);

    // Microsoft Graph API scopes for Teams access
    const scopes = [
      "https://graph.microsoft.com/Chat.Read",
      "https://graph.microsoft.com/ChannelMessage.Read.All",
      "https://graph.microsoft.com/Team.ReadBasic.All",
      "https://graph.microsoft.com/Channel.ReadBasic.All",
      "https://graph.microsoft.com/User.Read",
      "offline_access"  // For refresh tokens
    ];

    // Generate a state token that includes the integration ID
    const randomPart = Math.random().toString(36).substring(2, 15);
    const state = `${integrationId || 0}_${randomPart}`;

    console.log(`[TEAMS] Generated OAuth state token: ${state}`);

    // Construct Microsoft OAuth URL
    const baseUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize`;
    const params = new URLSearchParams({
      client_id: clientId,
      response_type: 'code',
      redirect_uri: globalRedirectUri,
      scope: scopes.join(' '),
      state: state,
      response_mode: 'query',
      prompt: 'consent', // Force consent to ensure we get refresh token
    });

    const authUrl = `${baseUrl}?${params.toString()}`;

    return {
      url: authUrl,
      state,
    };
  }

  /**
   * Exchange authorization code for tokens using Microsoft OAuth
   * @param code Authorization code from OAuth flow
   * @param redirectUri The redirect URI used in the auth request (not used)
   */
  async getTokensFromCode(code: string, redirectUri?: string): Promise<any> {
    // Get Microsoft OAuth credentials from environment
    const clientId = process.env.MICROSOFT_CLIENT_ID;
    const clientSecret = process.env.MICROSOFT_CLIENT_SECRET;
    const tenantId = process.env.MICROSOFT_TENANT_ID;

    if (!clientId || !clientSecret || !tenantId) {
      throw new Error("Microsoft OAuth credentials not configured");
    }

    // Always use the global callback endpoint as configured in Azure AD
    const globalRedirectUri = process.env.MICROSOFT_REDIRECT_URI ||
      `${process.env.SERVER_URL || "http://localhost:5000"}/api/integrations/oauth/callback`;

    console.log(`[TEAMS] Using global redirect URI for token exchange: ${globalRedirectUri}`);

    // Prepare token request
    const tokenUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`;
    const params = new URLSearchParams({
      client_id: clientId,
      client_secret: clientSecret,
      code: code,
      redirect_uri: globalRedirectUri,
      grant_type: 'authorization_code',
    });

    // Exchange the code for tokens
    try {
      console.log("[TEAMS] Exchanging authorization code for tokens...");

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("[TEAMS] Token exchange failed:", response.status, errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const tokens = await response.json();

      if (tokens.error) {
        console.error("[TEAMS] OAuth error:", tokens);
        throw new Error(`OAuth error: ${tokens.error_description || tokens.error}`);
      }

      console.log("[TEAMS] Successfully obtained tokens from Microsoft");
      console.log("[TEAMS] Token type:", tokens.token_type);
      console.log("[TEAMS] Scope:", tokens.scope);
      console.log("[TEAMS] Has refresh token:", !!tokens.refresh_token);

      return tokens;
    } catch (error: any) {
      console.error("[TEAMS] Error exchanging code for tokens:", error);
      throw new Error(
        `Failed to exchange authorization code for tokens: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * Get an authorized HTTP client using encrypted credentials
   * @param encryptedCredentials Encrypted credentials from storage
   */
  async getAuthorizedClient(encryptedCredentials: string): Promise<{
    accessToken: string;
    headers: Record<string, string>;
  }> {
    try {
      console.log('[TEAMS] Getting authorized client...');
      console.log('[TEAMS] Encrypted credentials length:', encryptedCredentials.length);

      // Validate the encrypted format first
      if (!cryptoService.validateEncryptedFormat(encryptedCredentials)) {
        console.error('[TEAMS] Invalid encrypted credentials format');
        throw new Error('Invalid encrypted credentials format - expected "iv:encryptedData"');
      }

      // Decrypt the credentials
      console.log('[TEAMS] Decrypting credentials...');
      const credentialsJson = await cryptoService.decrypt(encryptedCredentials);
      console.log('[TEAMS] Decryption successful, JSON length:', credentialsJson.length);

      // Parse the JSON
      let credentials: any;
      try {
        credentials = JSON.parse(credentialsJson);
        console.log('[TEAMS] JSON parsing successful');
        console.log('[TEAMS] Credentials keys:', Object.keys(credentials));
      } catch (parseError: any) {
        console.error('[TEAMS] JSON parsing failed:', parseError.message);
        throw new Error(`Failed to parse credentials JSON: ${parseError.message}`);
      }

      // Validate required fields
      if (!credentials.access_token) {
        console.error('[TEAMS] Missing access_token in credentials');
        throw new Error('Invalid credentials: missing access_token');
      }

      // Check if access token is expired and refresh if needed
      if (this.isTokenExpired(credentials)) {
        console.log('[TEAMS] Token is expired, refreshing...');
        credentials = await this.refreshAccessToken(credentials);
        console.log('[TEAMS] Token refreshed successfully');
      } else {
        console.log('[TEAMS] Token is still valid');
      }

      // Return authorized headers for API calls
      return {
        accessToken: credentials.access_token,
        headers: {
          'Authorization': `Bearer ${credentials.access_token}`,
          'Content-Type': 'application/json',
        },
      };
    } catch (error: any) {
      console.error("[TEAMS] Error getting authorized client:", error);
      throw error;
    }
  }

  /**
   * Check if the access token is expired
   * @param credentials OAuth2 credentials
   */
  private isTokenExpired(credentials: any): boolean {
    if (!credentials.expires_in && !credentials.expires_on) {
      return true;
    }

    // Calculate expiry time
    let expiryTime: number;
    if (credentials.expires_on) {
      // expires_on is Unix timestamp
      expiryTime = credentials.expires_on * 1000;
    } else if (credentials.expires_in) {
      // expires_in is seconds from when token was issued
      // Assume token was issued recently if no timestamp available
      const issuedAt = credentials.issued_at || Date.now();
      expiryTime = issuedAt + (credentials.expires_in * 1000);
    } else {
      return true;
    }

    // Consider token expired 5 minutes before actual expiry
    return expiryTime <= Date.now() + 5 * 60 * 1000;
  }

  /**
   * Refresh the access token using the refresh token
   * @param credentials Current credentials object
   */
  private async refreshAccessToken(credentials: any): Promise<any> {
    if (!credentials.refresh_token) {
      throw new Error('No refresh token available');
    }

    const clientId = process.env.MICROSOFT_CLIENT_ID;
    const clientSecret = process.env.MICROSOFT_CLIENT_SECRET;
    const tenantId = process.env.MICROSOFT_TENANT_ID;

    if (!clientId || !clientSecret || !tenantId) {
      throw new Error("Microsoft OAuth credentials not configured");
    }

    const tokenUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`;
    const params = new URLSearchParams({
      client_id: clientId,
      client_secret: clientSecret,
      refresh_token: credentials.refresh_token,
      grant_type: 'refresh_token',
    });

    try {
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("[TEAMS] Token refresh failed:", response.status, errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const newTokens = await response.json();

      if (newTokens.error) {
        console.error("[TEAMS] Refresh token error:", newTokens);
        throw new Error(`Refresh token error: ${newTokens.error_description || newTokens.error}`);
      }

      // Update credentials with new tokens, keeping the refresh token if not provided
      const updatedCredentials = {
        ...credentials,
        access_token: newTokens.access_token,
        expires_in: newTokens.expires_in,
        expires_on: Math.floor(Date.now() / 1000) + newTokens.expires_in,
        issued_at: Date.now(),
      };

      // Use new refresh token if provided, otherwise keep the old one
      if (newTokens.refresh_token) {
        updatedCredentials.refresh_token = newTokens.refresh_token;
      }

      return updatedCredentials;
    } catch (error: any) {
      console.error("[TEAMS] Error refreshing token:", error);
      throw new Error(`Failed to refresh access token: ${error.message}`);
    }
  }

  /**
   * Test the Teams connection by making a simple API call
   * @param encryptedCredentials Encrypted credentials from storage
   */
  async testConnection(encryptedCredentials: string): Promise<{
    success: boolean;
    userInfo?: any;
    error?: string;
  }> {
    try {
      console.log('[TEAMS] Testing connection...');

      const client = await this.getAuthorizedClient(encryptedCredentials);

      // Test API call - get user info
      const response = await fetch('https://graph.microsoft.com/v1.0/me', {
        headers: client.headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[TEAMS] Test connection failed:', response.status, errorText);
        return {
          success: false,
          error: `API call failed: HTTP ${response.status}`,
        };
      }

      const userInfo = await response.json();
      console.log('[TEAMS] Connection test successful, user:', userInfo.displayName);

      return {
        success: true,
        userInfo: {
          displayName: userInfo.displayName,
          email: userInfo.mail || userInfo.userPrincipalName,
          id: userInfo.id,
        },
      };
    } catch (error: any) {
      console.error('[TEAMS] Connection test failed:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

export const teamsService = new TeamsService();