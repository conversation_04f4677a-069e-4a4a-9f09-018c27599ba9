import crypto from 'crypto';

/**
 * Cryptographic utility functions
 */

// Generate a random string
export const generateRandomString = (length: number): string => {
  return crypto.randomBytes(length).toString('hex');
};

// Generate a secure random token
export const generateSecureToken = (length = 32): string => {
  return crypto.randomBytes(length).toString('base64url');
};

// Hash a password using bcrypt-like approach
export const hashPassword = async (password: string): Promise<string> => {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
};

// Verify a password against a hash
export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  const [salt, hash] = hashedPassword.split(':');
  const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return hash === verifyHash;
};

// Encrypt data using AES-256-GCM
export const encrypt = (text: string, key: string): string => {
  const algorithm = 'aes-256-gcm';
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
};

// Decrypt data using AES-256-GCM
export const decrypt = (encryptedData: string, key: string): string => {
  const algorithm = 'aes-256-gcm';
  const [ivHex, authTagHex, encrypted] = encryptedData.split(':');
  
  const iv = Buffer.from(ivHex, 'hex');
  const authTag = Buffer.from(authTagHex, 'hex');
  
  const decipher = crypto.createDecipher(algorithm, key);
  decipher.setAuthTag(authTag);
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

// Simple encryption for non-sensitive data
export const simpleEncrypt = (text: string, key: string): string => {
  const algorithm = 'aes-256-cbc';
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return `${iv.toString('hex')}:${encrypted}`;
};

// Simple decryption for non-sensitive data
export const simpleDecrypt = (encryptedData: string, key: string): string => {
  const algorithm = 'aes-256-cbc';
  const [ivHex, encrypted] = encryptedData.split(':');
  
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipher(algorithm, key);
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

// Generate a hash for data integrity
export const generateHash = (data: string, algorithm = 'sha256'): string => {
  return crypto.createHash(algorithm).update(data).digest('hex');
};

// Generate HMAC for data authentication
export const generateHMAC = (data: string, key: string, algorithm = 'sha256'): string => {
  return crypto.createHmac(algorithm, key).update(data).digest('hex');
};

// Verify HMAC
export const verifyHMAC = (data: string, key: string, hmac: string, algorithm = 'sha256'): boolean => {
  const expectedHmac = generateHMAC(data, key, algorithm);
  return crypto.timingSafeEqual(Buffer.from(hmac, 'hex'), Buffer.from(expectedHmac, 'hex'));
};

// Generate UUID v4
export const generateUUID = (): string => {
  return crypto.randomUUID();
};

// Validate encrypted format
export const validateEncryptedFormat = (data: string): boolean => {
  const parts = data.split(':');
  return parts.length >= 2 && parts.every(part => /^[a-f0-9]+$/i.test(part));
};

// Secure compare for timing attack prevention
export const secureCompare = (a: string, b: string): boolean => {
  if (a.length !== b.length) return false;
  return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b));
};
