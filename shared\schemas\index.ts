// Re-export all schemas and types
export * from './auth.schema';
export * from './integration.schema';
export * from './file.schema';
export * from './chat.schema';
export * from './project.schema';
export * from './email.schema';
export * from './oauth.schema';

// Re-export validation schemas from types
export {
  googleOAuthSchema,
  microsoftOAuthSchema,
  integrationConfigSchema,
  scheduleUpdateSchema,
  syncNowSchema,
  createChatSessionSchema,
  sendChatMessageSchema,
  chatSearchSchema,
  createProjectSchema,
  updateProjectSchema,
} from '../types';

// Note: Individual schema exports are already available through the wildcard exports above
// No need for duplicate named exports
