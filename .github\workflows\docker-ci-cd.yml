name: Docker CI/CD Pipeline

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # =============================================================================
  # Code Quality and Testing
  # =============================================================================
  test:
    name: Test & Code Quality
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: pgvector/pgvector:pg16
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: meetsync_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linter
        run: npm run lint || true
      
      - name: Run type check
        run: npm run check
      
      - name: Run tests
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://test:test@localhost:5432/meetsync_test
          REDIS_HOST: localhost
          REDIS_PORT: 6379
          SESSION_SECRET: test-session-secret
          ENCRYPTION_KEY: test-encryption-key
        run: |
          # Run database migrations
          npm run migrate
          # Run tests (when available)
          # npm run test
          echo "Tests would run here"
      
      - name: Build application
        run: npm run build
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/
            node_modules/
          retention-days: 1

  # =============================================================================
  # Security Scanning
  # =============================================================================
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
      
      - name: Audit npm dependencies
        run: npm audit --audit-level moderate
        continue-on-error: true

  # =============================================================================
  # Docker Build and Push
  # =============================================================================
  build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, security]
    
    strategy:
      matrix:
        environment: [development, production]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch,suffix=-${{ matrix.environment }}
            type=ref,event=pr,suffix=-${{ matrix.environment }}
            type=semver,pattern={{version}},suffix=-${{ matrix.environment }}
            type=semver,pattern={{major}}.{{minor}},suffix=-${{ matrix.environment }}
            type=raw,value=latest,suffix=-${{ matrix.environment }},enable={{is_default_branch}}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.environment == 'development' && 'Dockerfile.dev' || 'Dockerfile' }}
          target: ${{ matrix.environment }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64
      
      - name: Run Trivy vulnerability scanner on image
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}-${{ matrix.environment }}
          format: 'sarif'
          output: 'trivy-image-results.sarif'
      
      - name: Upload Trivy image scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-image-results.sarif'

  # =============================================================================
  # Deploy to Development
  # =============================================================================
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Deploy to development server
        run: |
          echo "Deploying to development environment..."
          # This would typically involve:
          # 1. SSH to development server
          # 2. Pull latest images
          # 3. Update docker-compose files
          # 4. Run health checks
          # 5. Send notifications
          
          # Example deployment command:
          # ssh ${{ secrets.DEV_SSH_HOST }} "cd /opt/meetsync && \
          #   docker-compose -f docker-compose.dev.yml pull && \
          #   docker-compose -f docker-compose.dev.yml up -d && \
          #   docker-compose -f docker-compose.dev.yml exec app npm run migrate"
      
      - name: Run deployment health check
        run: |
          echo "Running health checks..."
          # curl -f http://dev.meetsync.com/api/health || exit 1
      
      - name: Send deployment notification
        if: always()
        run: |
          echo "Sending deployment notification..."
          # Send to Slack, Teams, or email

  # =============================================================================
  # Deploy to Staging
  # =============================================================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/staging'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Deploy to staging server
        run: |
          echo "Deploying to staging environment..."
          # Similar to dev deployment but with staging configs
      
      - name: Run comprehensive tests
        run: |
          echo "Running staging tests..."
          # Run integration tests, E2E tests, performance tests
      
      - name: Send staging notification
        if: always()
        run: |
          echo "Sending staging notification..."

  # =============================================================================
  # Deploy to Production
  # =============================================================================
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Create backup before deployment
        run: |
          echo "Creating production backup..."
          # Trigger backup script on production server
      
      - name: Deploy to production with blue-green strategy
        run: |
          echo "Deploying to production..."
          # Blue-green deployment:
          # 1. Deploy to "green" environment
          # 2. Run health checks
          # 3. Switch traffic from "blue" to "green"
          # 4. Keep "blue" as rollback option
      
      - name: Run production health checks
        run: |
          echo "Running production health checks..."
          # Comprehensive health checks
          # Performance monitoring
          # Error rate monitoring
      
      - name: Send production notification
        if: always()
        run: |
          echo "Sending production notification..."

  # =============================================================================
  # Database Migration
  # =============================================================================
  migrate:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'
    
    strategy:
      matrix:
        environment: 
          - ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Run database migrations
        env:
          DATABASE_URL: ${{ secrets[format('{0}_DATABASE_URL', matrix.environment)] }}
        run: |
          echo "Running migrations for ${{ matrix.environment }}..."
          # docker run --rm \
          #   -e DATABASE_URL=$DATABASE_URL \
          #   ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest-production \
          #   npm run migrate

  # =============================================================================
  # Monitoring and Alerting
  # =============================================================================
  monitor:
    name: Setup Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-staging, deploy-prod]
    if: always()
    
    steps:
      - name: Update monitoring dashboards
        run: |
          echo "Updating monitoring dashboards..."
          # Update Grafana dashboards
          # Configure alerts
          # Set up log aggregation
      
      - name: Send deployment summary
        run: |
          echo "Sending deployment summary..."
          # Comprehensive deployment report
          # Include test results, security scan results, deployment status

  # =============================================================================
  # Cleanup
  # =============================================================================
  cleanup:
    name: Cleanup Old Images
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-staging, deploy-prod]
    if: always()
    
    steps:
      - name: Delete old container images
        uses: actions/delete-package-versions@v5
        with:
          package-name: ${{ env.IMAGE_NAME }}
          package-type: 'container'
          min-versions-to-keep: 10
          delete-only-untagged-versions: true 