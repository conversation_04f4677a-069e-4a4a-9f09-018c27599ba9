import { BaseService } from "../../base/service.interface";
import { NotionAuthService } from "./auth.service";
import { NotionDatabaseService } from "./database.service";
import { NotionPageService } from "./page.service";
import { NotionContentService } from "./content.service";

/**
 * Notion Service Facade - provides backward compatibility with the original NotionService
 * while using the new modular Notion services underneath
 */
export class NotionServiceFacade extends BaseService {
  private authService: NotionAuthService;
  private databaseService: NotionDatabaseService;
  private pageService: NotionPageService;
  private contentService: NotionContentService;

  constructor() {
    super('NotionServiceFacade', '1.0.0', 'Unified Notion services facade for backward compatibility');
    
    // Initialize modular services
    this.authService = new NotionAuthService();
    this.databaseService = new NotionDatabaseService();
    this.pageService = new NotionPageService();
    this.contentService = new NotionContentService();
  }

  protected async onInitialize(): Promise<void> {
    // Initialize sub-services that don't require API key
    await this.databaseService.initialize();
    await this.pageService.initialize();
    await this.contentService.initialize();

    this.log('info', 'Notion Service Facade initialized with sub-services (auth service requires API key)');
  }

  protected getDependencies(): string[] {
    return [];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    const authHealth = await this.authService.getHealthStatus();
    const databaseHealth = await this.databaseService.getHealthStatus();
    const pageHealth = await this.pageService.getHealthStatus();
    const contentHealth = await this.contentService.getHealthStatus();

    return {
      auth: authHealth,
      database: databaseHealth,
      page: pageHealth,
      content: contentHealth,
      allServicesHealthy: authHealth.healthy && databaseHealth.healthy && pageHealth.healthy && contentHealth.healthy,
    };
  }

  protected async onCleanup(): Promise<void> {
    await Promise.all([
      this.authService.cleanup?.(),
      this.databaseService.cleanup?.(),
      this.pageService.cleanup?.(),
      this.contentService.cleanup?.(),
    ]);
  }

  // Authentication methods - delegate to NotionAuthService
  async initializeWithApiKey(apiKey: string): Promise<void> {
    await super.initialize(); // Initialize the base service first
    return this.authService.initializeWithApiKey(apiKey);
  }

  async initializeFromEncrypted(encryptedApiKey: string): Promise<void> {
    this.ensureInitialized();
    return this.authService.initializeFromEncrypted(encryptedApiKey);
  }

  isReady(): boolean {
    return this.authService.isReady();
  }

  async testConnection(): Promise<{
    success: boolean;
    user?: any;
    error?: string;
  }> {
    this.ensureInitialized();
    return this.authService.testConnection();
  }

  // Page methods - delegate to NotionPageService
  async getPage(pageId: string): Promise<any> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.pageService.getPage(client, pageId);
  }

  // Database methods - delegate to NotionDatabaseService
  async findTranscriptsDatabase(pageId: string): Promise<string | null> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.databaseService.findTranscriptsDatabase(client, pageId);
  }

  async createTranscriptsDatabase(pageId: string): Promise<string> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.databaseService.createTranscriptsDatabase(client, pageId);
  }

  async findOrCreateTranscriptsDatabase(pageId: string): Promise<string> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.databaseService.findOrCreateTranscriptsDatabase(client, pageId);
  }

  // Content methods - delegate to NotionContentService
  async createTranscriptPage(databaseId: string, data: any): Promise<string> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.contentService.createTranscriptPage(client, databaseId, data);
  }

  // Comprehensive methods that use multiple services
  async setupNotionWorkspace(pageId: string, transcriptData: any): Promise<{
    databaseId: string;
    pageId: string;
    success: boolean;
  }> {
    this.ensureInitialized();

    try {
      this.log('info', 'Setting up Notion workspace...');

      // 1. Find or create the transcripts database
      const databaseId = await this.findOrCreateTranscriptsDatabase(pageId);
      this.log('info', `Database ready: ${databaseId}`);

      // 2. Create the transcript page
      const transcriptPageId = await this.createTranscriptPage(databaseId, transcriptData);
      this.log('info', `Transcript page created: ${transcriptPageId}`);

      return {
        databaseId,
        pageId: transcriptPageId,
        success: true,
      };
    } catch (error: any) {
      this.handleError(error, 'setting up Notion workspace');
    }
  }

  async getWorkspaceInfo(): Promise<{
    user: any;
    isReady: boolean;
    capabilities: string[];
  }> {
    this.ensureInitialized();

    try {
      const user = await this.authService.getCurrentUser();
      const isReady = this.authService.isReady();

      return {
        user,
        isReady,
        capabilities: [
          'create_pages',
          'create_databases',
          'manage_content',
          'search_pages',
          'update_properties',
        ],
      };
    } catch (error: any) {
      this.handleError(error, 'getting workspace info');
    }
  }

  async searchWorkspace(query: string, filter?: any): Promise<any[]> {
    this.ensureInitialized();
    const client = this.authService.getClient();

    try {
      const result = await this.pageService.searchPages(client, query, filter);
      return result.results;
    } catch (error: any) {
      this.log('error', `Error searching workspace with query: ${query}`, error);
      return [];
    }
  }

  async getDatabasePages(databaseId: string): Promise<any[]> {
    this.ensureInitialized();
    const client = this.authService.getClient();

    try {
      const result = await this.databaseService.getAllDatabasePages(client, databaseId);
      return result;
    } catch (error: any) {
      this.log('error', `Error getting pages from database ${databaseId}`, error);
      return [];
    }
  }

  async validateWorkspace(pageId: string): Promise<{
    pageExists: boolean;
    hasDatabase: boolean;
    databaseId?: string;
    isValid: boolean;
  }> {
    this.ensureInitialized();
    const client = this.authService.getClient();

    try {
      // Check if page exists
      const pageExists = await this.pageService.pageExists(client, pageId);
      
      if (!pageExists) {
        return {
          pageExists: false,
          hasDatabase: false,
          isValid: false,
        };
      }

      // Check for existing database
      const databaseId = await this.databaseService.findTranscriptsDatabase(client, pageId);
      const hasDatabase = !!databaseId;

      return {
        pageExists: true,
        hasDatabase,
        databaseId: databaseId || undefined,
        isValid: pageExists,
      };
    } catch (error: any) {
      this.log('error', `Error validating workspace for page ${pageId}`, error);
      return {
        pageExists: false,
        hasDatabase: false,
        isValid: false,
      };
    }
  }

  // Utility methods
  extractPageIdFromUrl(url: string): string | null {
    return this.pageService.extractPageIdFromUrl(url);
  }

  getPageUrl(pageId: string): string {
    return this.pageService.getPageUrl(pageId);
  }

  chunkText(text: string, maxChunkLength: number = 1950): string[] {
    return this.contentService.chunkText(text, maxChunkLength);
  }

  // Direct access to sub-services (for advanced usage)
  getAuthService(): NotionAuthService {
    return this.authService;
  }

  getDatabaseService(): NotionDatabaseService {
    return this.databaseService;
  }

  getPageService(): NotionPageService {
    return this.pageService;
  }

  getContentService(): NotionContentService {
    return this.contentService;
  }

  // Service information
  getServiceInfo() {
    return {
      ...super.getServiceInfo(),
      subServices: {
        auth: this.authService.getServiceInfo(),
        database: this.databaseService.getServiceInfo(),
        page: this.pageService.getServiceInfo(),
        content: this.contentService.getServiceInfo(),
      },
    };
  }

  // Additional utility methods for comprehensive compatibility
  async getCurrentUser(): Promise<any> {
    this.ensureInitialized();
    return this.authService.getCurrentUser();
  }

  async listUsers(): Promise<any[]> {
    this.ensureInitialized();
    return this.authService.listUsers();
  }

  async getDatabaseInfo(databaseId: string): Promise<any> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.databaseService.getDatabaseInfo(client, databaseId);
  }

  async getPageMetadata(pageId: string): Promise<any> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.pageService.getPageMetadata(client, pageId);
  }

  async validateDatabaseStructure(databaseId: string): Promise<any> {
    this.ensureInitialized();
    const client = this.authService.getClient();
    return this.databaseService.validateDatabaseStructure(client, databaseId);
  }

  getAuthStatus(): any {
    return this.authService.getAuthStatus();
  }

  getDatabaseSchema(): any {
    return this.databaseService.getDatabaseSchema();
  }

  validateApiKey(apiKey: string): boolean {
    return this.authService.validateApiKey(apiKey);
  }

  getApiKeyRequirements(): any {
    return this.authService.getApiKeyRequirements();
  }

  reset(): void {
    this.authService.reset();
  }

  async refreshConnection(): Promise<boolean> {
    return this.authService.refreshConnection();
  }
}
