#!/usr/bin/env ts-node

import { storage } from '../server/storage/index.js';
import { embeddingService } from '../server/services/ai/embedding-service.js';
import { ragService } from '../server/services/rag/index.js';

async function testEmailDataSource() {
  console.log('🧪 Testing Email Data Source Integration...\n');

  try {
    // Initialize services
    await embeddingService.initialize();
    console.log('✅ Embedding service initialized');

    // Check if we have emails in the database
    const allEmails = await storage.getEmails();
    console.log(`📧 Found ${allEmails.length} emails in the database`);

    if (allEmails.length === 0) {
      console.log('❌ No emails found. Please run the seed-dummy-emails script first.');
      return;
    }

    // Test getting available sources (should include emails)
    const availableSources = await ragService.getAvailableSources();
    console.log('\n📊 Available data sources:');
    availableSources.forEach(source => {
      console.log(`  - ${source.name} (${source.type}) - ${source.connected ? '✅ Connected' : '❌ Disconnected'}`);
    });

    const emailSource = availableSources.find(source => source.type === 'emails');
    if (!emailSource) {
      console.log('❌ Email source not found in available sources');
      return;
    }
    console.log('✅ Email source found in available sources');

    // Test searching emails using RAG
    console.log('\n🔍 Testing email search with RAG...');
    const searchQuery = 'project status update';
    const enabledSources = ['emails'];

    try {
      const searchResults = await ragService.searchSimilar(searchQuery, enabledSources, 5);
      console.log(`📋 Found ${searchResults.length} relevant chunks for query: "${searchQuery}"`);
      
      searchResults.forEach((result, index) => {
        console.log(`\n  ${index + 1}. Similarity: ${result.similarity?.toFixed(3) || 'N/A'}`);
        console.log(`     Content: ${result.content.substring(0, 100)}...`);
        console.log(`     Platform: ${result.platform}`);
        console.log(`     File/Email: ${result.fileName || 'Unknown'}`);
      });

    } catch (error) {
      console.log('❌ Error searching emails:', error);
      return;
    }

    // Test chat integration
    console.log('\n💬 Testing chat integration...');
    try {
      const chatResponse = await ragService.processQuery(
        searchQuery,
        'Test user query about project status',
        enabledSources,
        'test-session'
      );
      
      console.log('✅ Chat integration successful');
      console.log(`📝 Response length: ${chatResponse.length} characters`);
      console.log(`🔗 First 200 chars: ${chatResponse.substring(0, 200)}...`);
    } catch (error) {
      console.log('❌ Error with chat integration:', error);
    }

    console.log('\n✅ Email data source integration test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testEmailDataSource().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { testEmailDataSource }; 