> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Scheduled integration 1 with cron: 0 9 * * * (next run: Fri May 23 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
5:16:53 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
5:16:56 PM [express] GET /api/sync-logs 200 in 92ms :: {"logs":[{"id":23,"integrationId":1,"startTim…
5:16:56 PM [express] GET /api/integrations 304 in 327ms :: {"integrations":[{"id":1,"name":"Google M…
5:16:59 PM [express] GET /api/sync-logs 304 in 79ms :: {"logs":[{"id":23,"integrationId":1,"startTim…
5:16:59 PM [express] GET /api/integrations 304 in 83ms :: {"integrations":[{"id":1,"name":"Google Me…
5:17:00 PM [express] GET /api/sync-logs 200 in 77ms :: {"logs":[{"id":23,"integrationId":1,"startTim…
5:17:00 PM [express] GET /api/integrations 304 in 314ms :: {"integrations":[{"id":1,"name":"Google M…
5:17:50 PM [express] GET /api/integrations 200 in 358ms :: {"integrations":[{"id":1,"name":"Google M…
5:20:29 PM [express] GET /api/integrations 304 in 7104ms :: {"integrations":[{"id":1,"name":"Google …
OpenAI service initialized successfully
5:20:38 PM [express] POST /api/sync-now 200 in 290ms :: {"message":"Sync started successfully","sync…
Using source folder ID: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2 for integration 1
5:20:39 PM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
Notion service initialized successfully
Google Drive query: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2' in parents and trashed = false and (mimeType = 'application/vnd.google-apps.document' or mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType = 'text/plain')
Filtering out files that are in the processed folder: 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Found 0 files in processed folder
After filtering: 3 files remaining to process
Extracting content from document: Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini (ID: 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk)
Successfully extracted content (40694 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Strategic Initiatives and Technical Development Meeting
Created Notion page for "Strategic Initiatives and Technical Development Meeting": 1fbd68e5-c2ad-81f4-ac3f-d2870cc46daa
Splitting summary of 383 chars into 1 chunks
Created chunk with 1950 characters (max: 2000)
Created chunk with 1943 characters (max: 2000)
Created chunk with 1880 characters (max: 2000)
Created chunk with 1815 characters (max: 2000)
Created chunk with 1826 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1892 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1876 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1950 characters (max: 2000)
Created chunk with 1866 characters (max: 2000)
Created chunk with 1871 characters (max: 2000)
Created chunk with 1873 characters (max: 2000)
Created chunk with 1835 characters (max: 2000)
Created chunk with 1791 characters (max: 2000)
Created chunk with 1894 characters (max: 2000)
Created chunk with 1763 characters (max: 2000)
Created chunk with 1935 characters (max: 2000)
Created chunk with 1931 characters (max: 2000)
Created chunk with 928 characters (max: 2000)
Created chunk with 75 characters (max: 2000)
Added content blocks to page 1fbd68e5-c2ad-81f4-ac3f-d2870cc46daa
Moved file 1nPmG2t5iJhSxy7isVB_R8Ziqm6clUxkxayMDfraUnOk to processed folder 1T6u2dBhsbwUlFheJQHcEH0aJzpU4h9_9
Successfully processed Copy of Meeting started 2025/05/21 09:32 EDT - Notes by Gemini and added to Notion database
Extracting content from document: Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini (ID: 16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E)
Successfully extracted content (35496 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Development of an Integrated Meeting Transcription and Platform Connectivity Tool
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Invalid select option, commas not allowed: Use of AI tools like Replet, Contexter, and Manis'
}
Error creating transcript page: APIResponseError: Invalid select option, commas not allowed: Use of AI tools like Replet, Contexter, and Manis
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:426:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:20:52 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '214',
    connection: 'keep-alive',
    'cf-ray': '943dfc98f8e0dd1e-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"d6-v+pwpL1IkzeetQpwXFppTd6vIQ0"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'f1a8da60-a09e-42b3-ae93-e7f605995245',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=iLhgD9EjBjAeRH17nIthUkBBo6ZQjPbeUnIaVvz0uZI-**********-1.0.1.1-7jNC3HuHHNHsITqRcfhFTz9DcTgDINT1FhdwT6Guxvj3cyjdtnAF1Z7Ycxv3bHg1EsARUUFv5fhPHIC2pfoym73WRS6YJBfCyV6IVI.jDF8; path=/; expires=Thu, 22-May-25 17:50:52 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=LgQmqxBaqetK5wUg6mXbvgQW.HOW9hRlI4JUHhBJtZc-**********764-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Invalid select option, commas not allowed: Use of AI tools like Replet, Contexter, and Manis","request_id":"f1a8da60-a09e-42b3-ae93-e7f605995245"}'
}
Error processing item Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini (16BP-hdyL5P4NwOKTpv__a0ZNM0jJR-QAffKP0E-Rl0E): APIResponseError: Invalid select option, commas not allowed: Use of AI tools like Replet, Contexter, and Manis
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:426:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:20:52 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '214',
    connection: 'keep-alive',
    'cf-ray': '943dfc98f8e0dd1e-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"d6-v+pwpL1IkzeetQpwXFppTd6vIQ0"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'f1a8da60-a09e-42b3-ae93-e7f605995245',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=iLhgD9EjBjAeRH17nIthUkBBo6ZQjPbeUnIaVvz0uZI-**********-1.0.1.1-7jNC3HuHHNHsITqRcfhFTz9DcTgDINT1FhdwT6Guxvj3cyjdtnAF1Z7Ycxv3bHg1EsARUUFv5fhPHIC2pfoym73WRS6YJBfCyV6IVI.jDF8; path=/; expires=Thu, 22-May-25 17:50:52 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=LgQmqxBaqetK5wUg6mXbvgQW.HOW9hRlI4JUHhBJtZc-**********764-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Invalid select option, commas not allowed: Use of AI tools like Replet, Contexter, and Manis","request_id":"f1a8da60-a09e-42b3-ae93-e7f605995245"}'
}
File Copy of Meeting started 2025/05/20 09:37 EDT - Notes by Gemini will remain in source folder for retry in next sync
Extracting content from document: Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (ID: 16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ)
Successfully extracted content (24949 characters)
Skipping source ID lookup since SourceId property doesn't exist in Notion schema
Creating page in Notion database 1fad68e5-c2ad-81e7-828d-e589081246ad with title: Automating Meeting Notes and Integration Workflow
@notionhq/client warn: request fail {
  code: 'validation_error',
  message: 'Invalid select option, commas not allowed: Integration of Google Drive, Notion, and transcription services'
}
Error creating transcript page: APIResponseError: Invalid select option, commas not allowed: Integration of Google Drive, Notion, and transcription services
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:426:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:20:57 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '228',
    connection: 'keep-alive',
    'cf-ray': '943dfcb3392bbd49-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"e4-Hb4kXr9xTwOxLRJuL+2bRcBO3aI"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'c20aada2-65f7-4b65-9e5d-db116f21cadd',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=pT3e8rwQcEJPiwLFh5mgguWE6PwtGy96NUlXiUbl6vs-1747934457-1.0.1.1-my_UoRTFuBtBB10L01jKaiLM4Po.O9Up5ICn.LK4if.alqU0dGKyqRcC8KF1xXYth_oP77ppIUx_i6GHBKbT_4wv88cnuVih52NZT838iMk; path=/; expires=Thu, 22-May-25 17:50:57 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=9mloaf1QJPtG8ntuS42..4oUenetiOWWkU_AZpfiiTA-1747934457089-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Invalid select option, commas not allowed: Integration of Google Drive, Notion, and transcription services","request_id":"c20aada2-65f7-4b65-9e5d-db116f21cadd"}'
}
Error processing item Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini (16gaLaHDBYERJUO47KzRX3j989XXVAHWPMsd1vQ5AlnQ): APIResponseError: Invalid select option, commas not allowed: Integration of Google Drive, Notion, and transcription services
    at buildRequestError (/home/<USER>/workspace/node_modules/@notionhq/client/src/errors.ts:240:12)
    at Client.request (/home/<USER>/workspace/node_modules/@notionhq/client/src/Client.ts:223:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NotionService.createTranscriptPage (/home/<USER>/workspace/server/services/notion-service.ts:426:24)
    at async SyncController.runSync (/home/<USER>/workspace/server/controllers/sync.ts:259:30) {
  code: 'validation_error',
  status: 400,
  headers: Headers {
    date: 'Thu, 22 May 2025 17:20:57 GMT',
    'content-type': 'application/json; charset=utf-8',
    'content-length': '228',
    connection: 'keep-alive',
    'cf-ray': '943dfcb3392bbd49-ATL',
    'cf-cache-status': 'DYNAMIC',
    etag: 'W/"e4-Hb4kXr9xTwOxLRJuL+2bRcBO3aI"',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Accept-Encoding',
    'content-security-policy': "default-src 'none'",
    'referrer-policy': 'strict-origin-when-cross-origin',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-notion-request-id': 'c20aada2-65f7-4b65-9e5d-db116f21cadd',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'set-cookie': '__cf_bm=pT3e8rwQcEJPiwLFh5mgguWE6PwtGy96NUlXiUbl6vs-1747934457-1.0.1.1-my_UoRTFuBtBB10L01jKaiLM4Po.O9Up5ICn.LK4if.alqU0dGKyqRcC8KF1xXYth_oP77ppIUx_i6GHBKbT_4wv88cnuVih52NZT838iMk; path=/; expires=Thu, 22-May-25 17:50:57 GMT; domain=.notion.com; HttpOnly; Secure; SameSite=None, _cfuvid=9mloaf1QJPtG8ntuS42..4oUenetiOWWkU_AZpfiiTA-1747934457089-*******-604800000; path=/; domain=.notion.com; HttpOnly; Secure; SameSite=None',
    server: 'cloudflare',
    'alt-svc': 'h3=":443"; ma=86400'
  },
  body: '{"object":"error","status":400,"code":"validation_error","message":"Invalid select option, commas not allowed: Integration of Google Drive, Notion, and transcription services","request_id":"c20aada2-65f7-4b65-9e5d-db116f21cadd"}'
}
File Copy of Meeting started 2025/05/19 12:20 EDT - Notes by Gemini will remain in source folder for retry in next sync
Sync completed with status: partial
Items processed: 3, Success: 1, Failed: 2

why did 2 fail