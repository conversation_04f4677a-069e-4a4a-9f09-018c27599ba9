import { 
  syncLogs, 
  syncItems, 
  type SyncLog, 
  type InsertSyncLog,
  type SyncItem,
  type InsertSyncItem 
} from "../../../shared/index.js";
import { eq, desc, and } from "drizzle-orm";
import { getDatabase } from "../../core/config/database.config";

/**
 * Sync storage operations (logs and items)
 */
export class SyncStorage {
  private db: any;
  private memorySyncLogs: Map<number, SyncLog>;
  private memorySyncItems: Map<number, SyncItem>;
  private syncLogIdCounter: number;
  private syncItemIdCounter: number;
  private useDatabase: boolean;

  constructor(useDatabase = true) {
    this.useDatabase = useDatabase && !!process.env.DATABASE_URL;
    this.memorySyncLogs = new Map();
    this.memorySyncItems = new Map();
    this.syncLogIdCounter = 1;
    this.syncItemIdCounter = 1;
    // Database connection will be initialized lazily when first needed
  }

  /**
   * Ensure database connection is available
   */
  private ensureDatabase(): void {
    if (this.useDatabase && !this.db) {
      try {
        this.db = getDatabase();
      } catch (error) {
        // Silently fall back to memory storage
        this.useDatabase = false;
      }
    }
  }

  // Sync Log methods
  async getSyncLogs(integrationId?: number, limit?: number): Promise<SyncLog[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(syncLogs);

      if (integrationId) {
        baseQuery = baseQuery.where(eq(syncLogs.integrationId, integrationId)) as any;
      }

      baseQuery = baseQuery.orderBy(desc(syncLogs.startTime)) as any;

      if (limit) {
        baseQuery = baseQuery.limit(limit) as any;
      }

      return await baseQuery;
    } else {
      let logs = Array.from(this.memorySyncLogs.values());

      if (integrationId) {
        logs = logs.filter(log => log.integrationId === integrationId);
      }

      // Sort by start time descending (newest first)
      logs.sort((a, b) =>
        new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
      );

      if (limit) {
        logs = logs.slice(0, limit);
      }

      return logs;
    }
  }

  async getSyncLog(id: number): Promise<SyncLog | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(syncLogs).where(eq(syncLogs.id, id));
      return result[0];
    } else {
      return this.memorySyncLogs.get(id);
    }
  }

  async createSyncLog(log: InsertSyncLog): Promise<SyncLog> {
    this.validateId(log.integrationId);
    this.validateString(log.status, 'status');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(syncLogs).values(log).returning();
      return result[0];
    } else {
      const id = this.syncLogIdCounter++;
      const syncLog: SyncLog = { 
        ...log, 
        id, 
        endTime: null,
        startTime: log.startTime || new Date(),
        itemsProcessed: log.itemsProcessed || 0,
        itemsSuccess: log.itemsSuccess || 0,
        itemsFailed: log.itemsFailed || 0,
        details: log.details || null,
        error: log.error || null,
      };
      this.memorySyncLogs.set(id, syncLog);
      return syncLog;
    }
  }

  async updateSyncLog(id: number, data: Partial<SyncLog>): Promise<SyncLog | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.update(syncLogs)
        .set(data)
        .where(eq(syncLogs.id, id))
        .returning();
      return result[0];
    } else {
      const log = this.memorySyncLogs.get(id);
      if (!log) return undefined;

      const updated: SyncLog = {
        ...log,
        ...data,
      };

      this.memorySyncLogs.set(id, updated);
      return updated;
    }
  }

  // Sync Item methods
  async getSyncItems(syncLogId?: number, status?: string): Promise<SyncItem[]> {
    this.ensureDatabase();

    if (this.useDatabase) {
      let baseQuery = this.db.select().from(syncItems);

      const conditions = [];
      if (syncLogId) conditions.push(eq(syncItems.syncLogId, syncLogId));
      if (status) conditions.push(eq(syncItems.status, status));

      if (conditions.length > 0) {
        baseQuery = baseQuery.where(and(...conditions)) as any;
      }

      return await (baseQuery.orderBy(desc(syncItems.createdAt)) as any);
    } else {
      let items = Array.from(this.memorySyncItems.values());

      if (syncLogId) {
        items = items.filter(item => item.syncLogId === syncLogId);
      }

      if (status) {
        items = items.filter(item => item.status === status);
      }

      // Sort by created date descending
      items.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      return items;
    }
  }

  async getSyncItem(id: number): Promise<SyncItem | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(syncItems).where(eq(syncItems.id, id));
      return result[0];
    } else {
      return this.memorySyncItems.get(id);
    }
  }

  async getSyncItemByExternalId(externalId: string, integrationId: number): Promise<SyncItem | undefined> {
    this.validateString(externalId, 'externalId');
    this.validateId(integrationId);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.select().from(syncItems)
        .where(and(eq(syncItems.externalId, externalId), eq(syncItems.integrationId, integrationId)));
      return result[0];
    } else {
      return Array.from(this.memorySyncItems.values()).find(
        (item) => item.externalId === externalId && item.integrationId === integrationId
      );
    }
  }

  async createSyncItem(item: InsertSyncItem): Promise<SyncItem> {
    this.validateId(item.integrationId);
    this.validateString(item.externalId, 'externalId');
    this.validateString(item.title, 'title');
    this.validateString(item.type, 'type');
    this.validateString(item.status, 'status');
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.insert(syncItems).values(item).returning();
      return result[0];
    } else {
      const id = this.syncItemIdCounter++;
      const syncItem: SyncItem = {
        ...item,
        id,
        processedAt: null,
        createdAt: new Date(),
        error: item.error || null,
        syncLogId: item.syncLogId || null,
        sourceUrl: item.sourceUrl || null,
        destinationUrl: item.destinationUrl || null,
        metadata: item.metadata || null,
      };

      this.memorySyncItems.set(id, syncItem);
      return syncItem;
    }
  }

  async updateSyncItem(id: number, data: Partial<SyncItem>): Promise<SyncItem | undefined> {
    this.validateId(id);
    this.ensureDatabase();

    if (this.useDatabase) {
      const result = await this.db.update(syncItems)
        .set(data)
        .where(eq(syncItems.id, id))
        .returning();
      return result[0];
    } else {
      const item = this.memorySyncItems.get(id);
      if (!item) return undefined;

      const updated: SyncItem = {
        ...item,
        ...data,
      };

      this.memorySyncItems.set(id, updated);
      return updated;
    }
  }

  async clearAllSyncData(): Promise<void> {
    this.ensureDatabase();

    if (this.useDatabase) {
      await this.db.delete(syncItems);
      await this.db.delete(syncLogs);
    } else {
      this.memorySyncItems.clear();
      this.memorySyncLogs.clear();
      this.syncLogIdCounter = 1;
      this.syncItemIdCounter = 1;
    }
  }

  // Utility methods
  private validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid ID provided');
    }
  }

  private validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  // Get storage info
  getStorageInfo() {
    return {
      type: this.useDatabase ? 'database' : 'memory',
      syncLogCount: this.useDatabase ? 'unknown' : this.memorySyncLogs.size,
      syncItemCount: this.useDatabase ? 'unknown' : this.memorySyncItems.size,
    };
  }
}
