{"name": "gpt-unify", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon --exec tsx server/index.ts", "dev:client": "vite", "dev:local": "concurrently \"npm run dev:server:local\" \"vite\"", "dev:server:local": "cross-env NODE_ENV=development DATABASE_URL=postgresql://postgres:password@localhost:5432/meetsync_dev REDIS_URL=redis://localhost:6379 nodemon --exec tsx server/index.ts", "build": "npm run build:client && npm run build:server && npm run build:mcp", "build:client": "vite build", "build:server": "tsc -p tsconfig.build.json", "build:mcp": "cd mcp-servers && npm run build", "start": "node dist/server/index.js", "check": "tsc", "db:push": "drizzle-kit push", "migrate": "cross-env NODE_ENV=development drizzle-kit push", "db:status": "tsx scripts/check-db-setup.js", "db:reset": "node server/scripts/run-reset.js", "debug:teams-vectorization": "tsx scripts/debug/debug-teams-vectorization.js", "test:mcp-complete": "node server/services/mcp/test-complete-implementation.js", "kill-server": "node scripts/kill-server.js", "docker:build": "docker build -t meetsync .", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans", "docker:shell": "docker-compose exec meetsync-app sh", "docker:db": "docker-compose exec postgres psql -U meetsync -d meetsync", "docker:migrate": "docker-compose exec meetsync-app npm run migrate", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@azure/msal-node": "^3.5.3", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@microsoft/microsoft-graph-client": "^3.0.7", "@modelcontextprotocol/sdk": "^1.12.1", "@neondatabase/serverless": "^0.10.4", "@notionhq/client": "^3.1.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.60.5", "@types/pg": "^8.15.2", "ahooks": "^3.8.5", "ai": "^3.4.33", "antd": "^5.25.4", "antd-style": "^3.7.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "contextr": "^1.0.17", "cron": "^4.3.0", "cron-parser": "^5.2.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "fast-deep-equal": "^3.1.3", "file-type": "^21.0.0", "framer-motion": "^11.13.1", "google-auth-library": "^9.15.1", "googleapis": "^149.0.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "jszip": "^3.10.1", "lucide-react": "^0.453.0", "mammoth": "^1.9.1", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.1", "nanoid": "^4.0.2", "next-themes": "^0.4.6", "node-cron": "^4.0.6", "node-fetch": "^3.3.2", "node-pptx-parser": "^1.0.1", "node-schedule": "^2.1.1", "node-xlsx": "^0.24.0", "officeparser": "^5.1.1", "openai": "^4.102.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdf2json": "^3.1.6", "pdf2pic": "^2.2.4", "pdfjs-dist": "^4.0.379", "pg": "^8.16.0", "postgres": "^3.4.7", "pptx-parser": "^1.1.7-beta.9", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "redis": "^4.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "webvtt-parser": "^2.2.0", "wouter": "^3.3.5", "ws": "^8.18.0", "xlsx": "^0.18.5", "zod": "^3.24.2", "zod-validation-error": "^3.4.0", "zustand": "^4.5.7"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.3", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/bcryptjs": "^2.4.6", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.17", "@types/dotenv": "^6.1.1", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "20.16.11", "@types/node-schedule": "^2.1.7", "@types/node-xlsx": "^0.15.3", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.1", "esbuild": "^0.25.0", "nodemon": "^3.1.10", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^6.3.5"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}