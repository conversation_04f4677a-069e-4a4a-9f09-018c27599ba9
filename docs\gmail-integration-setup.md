# Gmail Integration Setup Guide

This guide will help you set up Gmail integration to replace dummy email data with real Gmail emails in your MeetSync application.

## Overview

The Gmail integration allows MeetSync to:
- ✅ Fetch emails from your Gmail account via Google APIs
- ✅ Process and vectorize email content for RAG
- ✅ Enable email-based conversations in the chat interface
- ✅ Provide semantic search across your email content
- ✅ Automatically sync new emails periodically

## Prerequisites

1. **Google Cloud Console Setup**: You need a Google Cloud Project with Gmail API enabled
2. **OAuth Credentials**: OAuth 2.0 client credentials configured for your domain
3. **Gmail Account**: A Gmail account to connect and sync emails from

## Step 1: Google Cloud Configuration

### 1.1 Enable Gmail API

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to **APIs & Services > Library**
4. Search for "Gmail API" and enable it
5. Also ensure these APIs are enabled:
   - Google Drive API (already configured)
   - Google OAuth2 API

### 1.2 Update OAuth Scopes

In your OAuth consent screen, make sure you have these scopes:
```
https://www.googleapis.com/auth/gmail.readonly
https://www.googleapis.com/auth/gmail.metadata
https://www.googleapis.com/auth/drive.readonly
https://www.googleapis.com/auth/userinfo.email
https://www.googleapis.com/auth/userinfo.profile
```

### 1.3 Update Redirect URIs

Add your callback URI to the authorized redirect URIs:
```
http://localhost:8080/api/integrations/oauth/callback
https://yourdomain.com/api/integrations/oauth/callback
```

## Step 2: Environment Configuration

Your `.env` file should already have these configured:
```env
# Google OAuth (already configured)
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret

# These should already be in your .env
OPENAI_API_KEY=your_openai_key
DATABASE_URL=your_database_url
```

## Step 3: Database Setup

The email tables are already configured in your schema. Make sure you've run the migrations:

```bash
npm run migrate
```

The following tables are used for Gmail integration:
- `emails` - Stores email metadata and content
- `email_chunks` - Stores vectorized email chunks for RAG

## Step 4: Integration Setup

### 4.1 Create Google Integration

1. Start your application:
   ```bash
   npm run dev
   ```

2. Navigate to the integrations page in your web interface
3. Create a new **Google Drive** integration (this will also work for Gmail)
4. Follow the OAuth flow to connect your Google account

### 4.2 Test Gmail Access

After setting up the integration, you can test Gmail access:

```bash
# Test the Gmail integration
node -e "
const { gmailService } = require('./dist/server/services/platform-integrations/google/gmail.service.js');
console.log('Gmail service loaded successfully');
"
```

## Step 5: Sync Gmail Emails

### 5.1 Via Frontend

1. Go to your integrations page
2. Find your Google integration
3. Use the Gmail management interface to:
   - Test Gmail connection
   - Sync recent emails (last 7 days)
   - Perform full email sync

### 5.2 Via API

You can also sync emails via API calls:

```bash
# Get integration ID from your database or web interface
INTEGRATION_ID=1

# Test Gmail connection
curl -X POST "http://localhost:8080/api/integrations/${INTEGRATION_ID}/gmail/test"

# Sync recent emails (last 7 days)
curl -X POST "http://localhost:8080/api/integrations/${INTEGRATION_ID}/gmail/sync-recent"

# Sync all emails (can take time for large inboxes)
curl -X POST "http://localhost:8080/api/integrations/${INTEGRATION_ID}/gmail/sync-all"

# Get email statistics
curl "http://localhost:8080/api/integrations/${INTEGRATION_ID}/gmail/stats"
```

### 5.3 Via Test Script

Run the Gmail integration test script:

```bash
npx tsx scripts/test-gmail-integration.ts
```

## Step 6: Verify Chat Integration

### 6.1 Check Available Sources

1. Open the chat interface
2. Check that "Emails" appears as an available data source
3. Enable the "Emails" source for your chat session

### 6.2 Test Email-Based Conversations

Try these sample queries in the chat:
- "What are the main topics in my recent emails?"
- "Show me emails about project updates"
- "Find emails from [specific sender]"
- "What meetings are mentioned in my emails?"

## Step 7: Remove Dummy Data (Optional)

Once Gmail integration is working, you can remove the dummy email data:

```bash
# Connect to your database
psql $DATABASE_URL

# Remove dummy emails (be careful!)
DELETE FROM email_chunks WHERE email_id IN (
  SELECT id FROM emails WHERE platform = 'gmail' AND sender LIKE '%@example.com'
);
DELETE FROM emails WHERE platform = 'gmail' AND sender LIKE '%@example.com';

# Or clear all emails and re-sync from Gmail
# DELETE FROM email_chunks;
# DELETE FROM emails;
```

## Troubleshooting

### Common Issues

1. **"Gmail API not enabled"**
   - Ensure Gmail API is enabled in Google Cloud Console
   - Wait a few minutes after enabling the API

2. **"Insufficient permissions"**
   - Check that Gmail scopes are included in your OAuth consent screen
   - Re-authorize the integration to get updated permissions

3. **"Rate limit exceeded"**
   - Gmail API has rate limits. The sync process includes delays to avoid this
   - For large inboxes, use incremental sync instead of full sync

4. **"No emails found"**
   - Check that the integration is properly connected
   - Verify the user account has emails
   - Check the query filters (we exclude chat messages by default)

### Debug Commands

```bash
# Check email count in database
npx tsx -e "
import { storage } from './server/storage/index.js';
const emails = await storage.getEmails();
console.log('Total emails:', emails.length);
console.log('Gmail emails:', emails.filter(e => e.platform === 'gmail').length);
"

# Test RAG search with emails
npx tsx scripts/test-email-data-source.ts

# Check embedding service status
npx tsx -e "
import { embeddingService } from './server/services/ai/embedding-service.js';
await embeddingService.initialize();
console.log('Embedding service initialized:', embeddingService.isInitialized());
"
```

## Security Considerations

1. **Scope Minimization**: The integration only requests read-only access to Gmail
2. **Data Storage**: Email content is stored in your local database and vectorized for search
3. **Token Management**: OAuth tokens are securely stored and refreshed automatically
4. **Rate Limiting**: API calls are rate-limited to respect Gmail's quotas

## Performance Tips

1. **Incremental Sync**: Use "Sync Recent" for daily updates instead of full sync
2. **Batch Processing**: Emails are processed in batches to avoid memory issues
3. **Chunk Size**: Email content is chunked for efficient vectorization
4. **Background Processing**: Full syncs run in the background to avoid blocking the UI

## Next Steps

Once Gmail integration is working:
1. Set up periodic email syncing (can be done via cron jobs)
2. Customize email processing (sentiment analysis, topic extraction)
3. Add email threading and conversation analysis
4. Implement email-based task extraction and calendar integration

For questions or issues, check the application logs or create an issue in the repository. 