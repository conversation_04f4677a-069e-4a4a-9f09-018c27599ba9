import React from 'react';
import { GoogleServiceCard } from './GoogleServiceCard';
import { 
  FolderIcon, 
  RefreshCwIcon, 
  DownloadIcon,
  FileTextIcon,
  ImageIcon,
  FileIcon
} from 'lucide-react';
import { useToast } from '../../hooks/use-toast';

interface GoogleDriveCardProps {
  integrationId: number;
  isConnected: boolean;
  onStatusChange?: (connected: boolean) => void;
  onConnect?: () => Promise<void>;
}

export function GoogleDriveCard({
  integrationId,
  isConnected,
  onStatusChange,
  onConnect,
}: GoogleDriveCardProps) {
  const { toast } = useToast();

  const syncRecentFiles = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/sync-recent`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Google Drive Sync Started",
          description: "Syncing recent files from your Google Drive",
        });
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to sync Google Drive files",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sync Google Drive files",
        variant: "destructive",
      });
    }
  };

  const syncAllFiles = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/sync-all`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Full Google Drive Sync Started",
          description: "This may take several minutes for large drives",
        });
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to start full Google Drive sync",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start full Google Drive sync",
        variant: "destructive",
      });
    }
  };

  const manageFolders = async () => {
    // Navigate to folder management or open folder selection modal
    window.location.href = `/integrations/${integrationId}/setup?step=2`;
  };

  const actions = [
    {
      label: "Sync Recent",
      action: syncRecentFiles,
      variant: "default" as const,
      icon: <RefreshCwIcon className="h-3 w-3" />,
    },
    {
      label: "Sync All",
      action: syncAllFiles,
      variant: "outline" as const,
      icon: <DownloadIcon className="h-3 w-3" />,
    },
    {
      label: "Manage Folders",
      action: manageFolders,
      variant: "outline" as const,
      icon: <FolderIcon className="h-3 w-3" />,
    },
  ];

  const renderStats = (stats: any) => (
    <div className="grid grid-cols-2 gap-4 text-sm">
      {stats.totalFiles !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.totalFiles.toLocaleString()}</div>
          <div className="text-gray-500">Total Files</div>
        </div>
      )}
      {stats.recentFiles !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.recentFiles.toLocaleString()}</div>
          <div className="text-gray-500">Recent Files</div>
        </div>
      )}
      {stats.documentsCount !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg flex items-center justify-center space-x-1">
            <FileTextIcon className="h-4 w-4 text-blue-500" />
            <span>{stats.documentsCount.toLocaleString()}</span>
          </div>
          <div className="text-gray-500">Documents</div>
        </div>
      )}
      {stats.imagesCount !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg flex items-center justify-center space-x-1">
            <ImageIcon className="h-4 w-4 text-green-500" />
            <span>{stats.imagesCount.toLocaleString()}</span>
          </div>
          <div className="text-gray-500">Images</div>
        </div>
      )}
      {stats.foldersCount !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg flex items-center justify-center space-x-1">
            <FolderIcon className="h-4 w-4 text-yellow-500" />
            <span>{stats.foldersCount.toLocaleString()}</span>
          </div>
          <div className="text-gray-500">Folders</div>
        </div>
      )}
      {stats.storageUsed !== undefined && (
        <div className="text-center">
          <div className="font-semibold text-lg">{stats.storageUsed}</div>
          <div className="text-gray-500">Storage Used</div>
        </div>
      )}
    </div>
  );

  const renderProfile = (profile: any) => (
    <div className="flex items-center space-x-3">
      <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
        <FolderIcon className="h-4 w-4 text-blue-600" />
      </div>
      <div>
        <div className="font-medium">{profile.user?.displayName || profile.user?.emailAddress}</div>
        {profile.user?.displayName && profile.user?.emailAddress && (
          <div className="text-sm text-gray-500">{profile.user.emailAddress}</div>
        )}
        {profile.storageQuota && (
          <div className="text-xs text-gray-400">
            {profile.storageQuota.usage} / {profile.storageQuota.limit} used
          </div>
        )}
      </div>
    </div>
  );

  return (
    <GoogleServiceCard
      serviceName="Google Drive"
      serviceType="google_drive"
      icon={
        <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <FolderIcon className="h-6 w-6 text-blue-600" />
        </div>
      }
      description="Sync and search your Google Drive files"
      integrationId={integrationId}
      isConnected={isConnected}
      onStatusChange={onStatusChange}
      onConnect={onConnect}
      endpoints={{
        profile: "folders",
        stats: "stats",
        test: "test-connection",
      }}
      actions={actions}
      renderStats={renderStats}
      renderProfile={renderProfile}
    />
  );
} 