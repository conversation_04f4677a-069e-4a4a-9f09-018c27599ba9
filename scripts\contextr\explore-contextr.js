import fs from 'fs';
import path from 'path';

async function exploreProject() {
  console.log('🔍 Exploring MeetSync project with contextr...\n');

  try {
    // Dynamic import for CommonJS module
    const { FileContextBuilder } = await import('contextr');

    // First, let's get an overview of the project structure
    const overviewBuilder = new FileContextBuilder({
      includeDirs: ['client/src', 'server', 'shared'],
      includeFiles: ['package.json', 'README.md', 'tsconfig.json', 'vite.config.ts', 'tailwind.config.ts'],
      exclude: ['node_modules/**', '.git/**', '**/*.log', '**/*.png', '**/*.jpg', '**/*.ico'],
      listOnlyPatterns: ['**/*.png', '**/*.jpg', '**/*.ico', '**/*.svg']
    });

    console.log('📁 Building project overview...');
    const overviewResult = await overviewBuilder.build('console');
    
    // Save the overview to a file
    fs.writeFileSync('contextr-overview.txt', overviewResult.output);
    console.log('✅ Project overview saved to contextr-overview.txt\n');

    // Now let's look specifically at the client structure
    const clientBuilder = new FileContextBuilder({
      includeDirs: ['client/src'],
      exclude: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts'],
      includeFiles: ['client/package.json', 'client/src/main.tsx', 'client/src/App.tsx']
    });

    console.log('⚛️ Building client context...');
    const clientResult = await clientBuilder.build('console');
    
    // Save the client context to a file
    fs.writeFileSync('contextr-client.txt', clientResult.output);
    console.log('✅ Client context saved to contextr-client.txt\n');

    // Now let's look specifically at the server structure
    const serverBuilder = new FileContextBuilder({
      includeDirs: ['server'],
      exclude: ['**/*.test.ts', '**/*.test.js', '**/*.spec.ts'],
      includeFiles: ['server/package.json']
    });

    console.log('🖥️ Building server context...');
    const serverResult = await serverBuilder.build('console');
    
    // Save the server context to a file
    fs.writeFileSync('contextr-server.txt', serverResult.output);
    console.log('✅ Server context saved to contextr-server.txt\n');

    console.log('🎉 Contextr exploration complete! Files generated:');
    console.log('  - contextr-overview.txt: Full project overview');
    console.log('  - contextr-client.txt: Client-side code context');
    console.log('  - contextr-server.txt: Server-side code context');

  } catch (error) {
    console.error('❌ Error exploring project:', error.message);
    if (error.stack) {
      console.error(error.stack);
    }
  }
}

exploreProject(); 