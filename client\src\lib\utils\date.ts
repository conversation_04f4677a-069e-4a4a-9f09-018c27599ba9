import { format, parseISO, formatDistanceToNow } from 'date-fns';

export function formatDate(dateString: string | null | undefined): string {
  if (!dateString) return 'Never';
  
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : new Date(dateString);
    return format(date, 'MMM d, yyyy - h:mm a');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
}

export function formatRelativeTime(dateString: string | null | undefined): string {
  if (!dateString) return 'Never';
  
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return 'Invalid date';
  }
}

export function getCronDescription(cronExpression: string): string {
  // Simple cron expression interpreter
  if (!cronExpression) return 'Not scheduled';
  
  // Handle common patterns
  if (cronExpression === '0 * * * *') return 'Every hour';
  if (cronExpression === '0 0 * * *') return 'Daily at midnight';
  if (cronExpression === '0 9 * * *') return 'Daily at 9:00 AM';
  if (cronExpression === '0 0 * * 1') return 'Weekly on Monday at midnight';
  if (cronExpression.startsWith('*/')) {
    const minutes = cronExpression.split(' ')[0].replace('*/', '');
    return `Every ${minutes} minutes`;
  }
  
  return cronExpression;
}
