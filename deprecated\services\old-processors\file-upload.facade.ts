import { BaseService } from "../base/service.interface";
import { FileUploadConfigService } from "./config.service";
import { FileUploadProcessingService } from "./processing.service";
// Removed FileUploadContentService import - now using unified content extractor
import { FileUploadManagementService } from "./management.service";

/**
 * File Upload Service Facade - provides backward compatibility with the original FileUploadService
 * while using the new modular file upload services underneath
 */
export class FileUploadServiceFacade extends BaseService {
  private configService: FileUploadConfigService;
  private processingService: FileUploadProcessingService;
  private managementService: FileUploadManagementService;

  constructor() {
    super('FileUploadServiceFacade', '1.0.0', 'Unified file upload services facade for backward compatibility');
    
    // Initialize modular services
    this.configService = new FileUploadConfigService();
    this.processingService = new FileUploadProcessingService();
    this.managementService = new FileUploadManagementService();
  }

  protected async onInitialize(): Promise<void> {
    // Initialize all sub-services
    await this.configService.initialize();
    await this.processingService.initialize();
    await this.managementService.initialize();
    
    this.log('info', 'File Upload Service Facade initialized with all sub-services');
  }

  protected getDependencies(): string[] {
    return ['storage'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    const configHealth = await this.configService.getHealthStatus();
    const processingHealth = await this.processingService.getHealthStatus();
    const managementHealth = await this.managementService.getHealthStatus();

    return {
      config: configHealth,
      processing: processingHealth,
      management: managementHealth,
      allServicesHealthy: configHealth.healthy && processingHealth.healthy && managementHealth.healthy,
    };
  }

  protected async onCleanup(): Promise<void> {
    await Promise.all([
      this.configService.cleanup?.(),
      this.processingService.cleanup?.(),
      this.contentService.cleanup?.(),
      this.managementService.cleanup?.(),
    ]);
  }

  // Configuration methods - delegate to FileUploadConfigService
  getUploadMiddleware() {
    this.ensureInitialized();
    return this.configService.getUploadMiddleware();
  }

  getSupportedFileTypes(): string[] {
    return this.configService.getSupportedFileTypes();
  }

  getFileTypeCategory(mimeType: string): string {
    return this.configService.getFileTypeCategory(mimeType);
  }

  validateFile(file: Express.Multer.File) {
    return this.configService.validateFile(file);
  }

  getUploadDirectory(): string {
    return this.configService.getUploadDirectory();
  }

  getMaxFileSize(): number {
    return this.configService.getMaxFileSize();
  }

  getMaxFiles(): number {
    return this.configService.getMaxFiles();
  }

  // Processing methods - delegate to FileUploadProcessingService
  async processUploadedFile(file: Express.Multer.File, userId?: string) {
    this.ensureInitialized();
    return this.processingService.processUploadedFile(file, userId);
  }

  async processMultipleFiles(files: Express.Multer.File[], userId?: string) {
    this.ensureInitialized();
    return this.processingService.processMultipleFiles(files, userId);
  }

  async getProcessingStats() {
    this.ensureInitialized();
    return this.processingService.getProcessingStats();
  }

  async getUploadedFileInfo(fileId: number) {
    this.ensureInitialized();
    return this.processingService.getUploadedFileInfo(fileId);
  }

  // Content methods - delegate to FileUploadContentService
  async extractTextFromFile(filePath: string, fileType: string): Promise<string> {
    this.ensureInitialized();
    return this.contentService.extractTextFromFile(filePath, fileType);
  }

  supportsTextExtraction(fileType: string, mimeType?: string): boolean {
    return this.contentService.supportsTextExtraction(fileType, mimeType);
  }

  getExtractionCapabilities() {
    return this.contentService.getExtractionCapabilities();
  }

  estimateExtractionQuality(fileType: string, mimeType: string) {
    return this.contentService.estimateExtractionQuality(fileType, mimeType);
  }

  // Management methods - delegate to FileUploadManagementService
  async deleteUploadedFile(fileId: number) {
    this.ensureInitialized();
    return this.managementService.deleteUploadedFile(fileId);
  }

  async deleteMultipleFiles(fileIds: number[]) {
    this.ensureInitialized();
    return this.managementService.deleteMultipleFiles(fileIds);
  }

  async cleanupOrphanedFiles(uploadDir: string) {
    this.ensureInitialized();
    return this.managementService.cleanupOrphanedFiles(uploadDir);
  }

  async cleanupOldFiles(daysOld: number = 30) {
    this.ensureInitialized();
    return this.managementService.cleanupOldFiles(daysOld);
  }

  async getManagementStats() {
    this.ensureInitialized();
    return this.managementService.getManagementStats();
  }

  async repairFileRecords() {
    this.ensureInitialized();
    return this.managementService.repairFileRecords();
  }

  // Comprehensive methods that use multiple services
  async uploadAndProcessFiles(
    files: Express.Multer.File[],
    userId?: string
  ): Promise<{
    success: boolean;
    results: Array<{
      filename: string;
      fileId?: number;
      success: boolean;
      error?: string;
      warnings?: string[];
      extractionQuality?: any;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
      totalSize: number;
    };
  }> {
    this.ensureInitialized();

    try {
      const results = [];
      let successful = 0;
      let failed = 0;
      let totalSize = 0;

      for (const file of files) {
        try {
          // Validate file
          const validation = this.configService.validateFile(file);
          if (!validation.isValid) {
            results.push({
              filename: file.originalname,
              success: false,
              error: validation.error,
            });
            failed++;
            continue;
          }

          // Process file
          const processResult = await this.processingService.processUploadedFile(file, userId);
          
          // Get extraction quality estimate
          const fileType = this.configService.getFileTypeCategory(file.mimetype);
          const extractionQuality = this.contentService.estimateExtractionQuality(fileType, file.mimetype);

          results.push({
            filename: file.originalname,
            fileId: processResult.fileId,
            success: processResult.success,
            error: processResult.error,
            warnings: [...(validation.warnings || []), ...(processResult.warnings || [])],
            extractionQuality,
          });

          if (processResult.success) {
            successful++;
            totalSize += file.size;
          } else {
            failed++;
          }

        } catch (error: any) {
          results.push({
            filename: file.originalname,
            success: false,
            error: error.message,
          });
          failed++;
        }
      }

      return {
        success: failed === 0,
        results,
        summary: {
          total: files.length,
          successful,
          failed,
          totalSize,
        },
      };

    } catch (error: any) {
      this.handleError(error, 'uploading and processing files');
    }
  }

  async getComprehensiveStats(): Promise<{
    upload: any;
    processing: any;
    management: any;
    health: any;
  }> {
    this.ensureInitialized();

    try {
      const [uploadStats, processingStats, managementStats, healthStats] = await Promise.all([
        this.configService.getUploadStats(),
        this.processingService.getProcessingStats(),
        this.managementService.getManagementStats(),
        this.checkHealth(),
      ]);

      return {
        upload: uploadStats,
        processing: processingStats,
        management: managementStats,
        health: healthStats,
      };

    } catch (error: any) {
      this.handleError(error, 'getting comprehensive stats');
    }
  }

  async performMaintenance(options: {
    cleanupOldFiles?: boolean;
    cleanupOrphaned?: boolean;
    repairRecords?: boolean;
    daysOld?: number;
  } = {}): Promise<{
    success: boolean;
    results: {
      oldFilesCleanup?: any;
      orphanedCleanup?: any;
      recordRepair?: any;
    };
    summary: string;
  }> {
    this.ensureInitialized();

    try {
      const results: any = {};
      const actions = [];

      if (options.cleanupOldFiles) {
        results.oldFilesCleanup = await this.managementService.cleanupOldFiles(options.daysOld || 30);
        actions.push(`Cleaned up ${results.oldFilesCleanup.deletedCount} old files`);
      }

      if (options.cleanupOrphaned) {
        const uploadDir = this.configService.getUploadDirectory();
        results.orphanedCleanup = await this.managementService.cleanupOrphanedFiles(uploadDir);
        actions.push(`Cleaned up ${results.orphanedCleanup.deletedCount} orphaned files`);
      }

      if (options.repairRecords) {
        results.recordRepair = await this.managementService.repairFileRecords();
        actions.push(`Repaired ${results.recordRepair.repairedCount} file records`);
      }

      const summary = actions.length > 0 ? actions.join(', ') : 'No maintenance actions performed';

      return {
        success: true,
        results,
        summary,
      };

    } catch (error: any) {
      this.handleError(error, 'performing maintenance');
    }
  }

  // Direct access to sub-services (for advanced usage)
  getConfigService(): FileUploadConfigService {
    return this.configService;
  }

  getProcessingService(): FileUploadProcessingService {
    return this.processingService;
  }

  getContentService(): FileUploadContentService {
    return this.contentService;
  }

  getManagementService(): FileUploadManagementService {
    return this.managementService;
  }

  // Service information
  getServiceInfo() {
    return {
      ...super.getServiceInfo(),
      subServices: {
        config: this.configService.getServiceInfo(),
        processing: this.processingService.getServiceInfo(),
        content: this.contentService.getServiceInfo(),
        management: this.managementService.getServiceInfo(),
      },
    };
  }
}
