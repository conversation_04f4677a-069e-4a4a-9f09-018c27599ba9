// File-related types
export type FileType = "transcript" | "doc" | "ppt" | "pdf" | "video" | "audio" | "image" | "text" | "spreadsheet";
export type FilePlatform = "google_drive" | "microsoft_teams" | "zoom" | "slack" | "uploaded_files";
export type FileStatus = "active" | "deleted" | "inactive" | "processing";

export interface File {
  id: number;
  externalId: string;
  fileName: string;
  fileType: string;
  mimeType: string | null;
  fileSize: number | null;
  platform: string;
  fileContent: string | null;
  fileUrl: string | null;
  downloadUrl: string | null;
  thumbnailUrl: string | null;
  parentFolder: string | null;
  tags: string[] | null;
  extractedMetadata: Record<string, any> | null;
  sourceUrl: string | null;
  userId: string | null;
  organizationId: string | null;
  syncItemId: number | null;
  notionPageId: string | null;
  lastModified: Date | null;
  isShared: boolean | null;
  sharedWith: string[] | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InsertFile {
  externalId: string;
  fileName: string;
  fileType: string;
  mimeType?: string | null;
  fileSize?: number | null;
  platform: string;
  fileContent?: string | null;
  fileUrl?: string | null;
  downloadUrl?: string | null;
  thumbnailUrl?: string | null;
  parentFolder?: string | null;
  tags?: string[] | null;
  extractedMetadata?: Record<string, any> | null;
  sourceUrl?: string | null;
  userId?: string | null;
  organizationId?: string | null;
  syncItemId?: number | null;
  notionPageId?: string | null;
  lastModified?: Date | null;
  isShared?: boolean | null;
  sharedWith?: string[] | null;
  status?: string;
}

// File chunk types for RAG
export interface FileChunk {
  id: number;
  fileId: number;
  chunkIndex: number;
  content: string;
  embedding: number[] | null;
  tokenCount: number | null;
  metadata: Record<string, any> | null;
  createdAt: Date;
}

export interface InsertFileChunk {
  fileId: number;
  chunkIndex: number;
  content: string;
  embedding?: number[] | null;
  tokenCount?: number | null;
  metadata?: Record<string, any> | null;
}

// File processing types
export interface FileProcessingResult {
  success: boolean;
  fileId: number;
  chunksCreated: number;
  error?: string;
}

export interface FileUploadResult {
  success: boolean;
  file?: File;
  error?: string;
}

// File search types
export interface FileSearchOptions {
  query?: string;
  platform?: string;
  fileType?: string;
  folderId?: string;
  userId?: string;
  limit?: number;
  offset?: number;
}

export interface FileSearchResult {
  files: File[];
  total: number;
  hasMore: boolean;
}

// File metadata extraction types
export interface ExtractedMetadata {
  title?: string;
  author?: string;
  createdDate?: string;
  modifiedDate?: string;
  description?: string;
  topics?: string[];
  attendees?: string[];
  meetingDate?: string;
  duration?: number;
  language?: string;
  wordCount?: number;
  pageCount?: number;
  [key: string]: any;
}
