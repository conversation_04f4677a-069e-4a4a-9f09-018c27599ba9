Absolutely, this is the right time for a **detailed, step-by-step recovery and “correct merge” plan** for your agent. You want **your Node.js/TypeScript app to replicate the successful behavior of the Python workflow**, with robust Google Drive > OpenAI LLM > Notion database processing, as in your coworker's files. The app must:

* Load all meeting transcript files from Google Drive.
* Extract metadata (meeting name, date, time, attendees, summary) using LLMs and filename parsing.
* Upload **all** (not just some) transcript data to the correct Notion database properties, matching your actual Notion schema.
* Move files only *after* successful Notion insertion.
* **Never fail** for avoidable reasons like property mismatch or OpenAI service miswiring.

Below is a **step-by-step, exhaustive agent plan** for debugging and merging, including deep validation checks, with **questions for you** at the end to guarantee we’re mapping *exactly* the behavior you want.

---

---

## **Step 1: Establish Known-Good Baseline**

**a) Gather the following:**

* A working, correct Python reference implementation (your coworker’s code) with:

  * All utility and integration files: Google Drive, Notion, OpenAI/LLM, etc.
  * A sample of the Notion database schema in use.

**b) In your Node/TS repo:**

* List your services: `openai-service.ts`, `notion-service.ts`, `google-service.ts`, `sync.ts`.
* List all recent changes, especially after the “make it like my coworker’s files” request.

---

## **Step 2: Fix the Critical Runtime Error**

**Your Error:**
`TypeError: openaiService.initialize is not a function`

### **Actions:**

1. **Review your `openai-service.ts`:**

   * Is it exporting an object with `.initialize()`?
   * If yes, is it a **default export** or a **named export**?

     * `export default openaiService` vs `export { openaiService }`

2. **Check your import in `sync.ts` (and any file that uses it):**

   * Should be:

     * `import openaiService from '../services/openai-service'` (for default export)
     * `import { openaiService } from '../services/openai-service'` (for named export)
   * **If the file changed from a class to a function or vice versa during the refactor, update usage everywhere.**

3. **Add a debugging log before the failing call:**

   ```js
   console.log('openaiService:', openaiService);
   console.log('Type:', typeof openaiService);
   ```

   * If `undefined`, `{}`, or doesn’t show `initialize`, you have a mismatch.

4. **Restart your dev environment** after every fix (`npm run build` if you transpile, restart server).

---

## **Step 3: Ensure Consistent Data Flow and Metadata Extraction**

### **a) Google Drive Service**

* The service must:

  * List all transcript files in the correct folders.
  * Never miss files that should be processed.
  * Exclude already-processed files robustly (check by ID, name, or folder).
  * Extract full file content (not just file names or metadata).

### **b) Metadata Extraction**

* For each transcript:

  * Extract:

    * **Meeting name/title:** Use filename and LLM (as in Python `get_meeting_name`).
    * **Date and Time:** Parse from filename using a robust regex, fallback to "Unknown" if absent.
    * **Attendees:** Use OpenAI to parse attendees from summary/notes, fallback to `extract_attendees_from_notes`.
    * **Summary/Topics:** Use LLM if available.
  * Use **exactly the same extraction logic as the Python code**—port regexes and prompts faithfully.

### **c) OpenAI Service**

* Should expose functions (NOT require explicit `initialize()` unless needed):

  * `extractMeetingName(text: string): Promise<string>`
  * `extractAttendees(text: string): Promise<string>`
  * Any other LLM-powered field extraction.
* Use `OPENAI_API_KEY` from env/secrets, not hardcoded.

### **d) Notion Service**

* **MUST** map only to actual Notion properties:

  * `Name`, `AI summary`, `Attendees`, `Date`, `Duration`, `Source`, `SourceURL`, `Time`, `Topics`
* **No reference to removed fields** like `Tags`, `Departments`, etc.
* **Sanitize input** for fields with constraints (e.g., replace commas in selects, or don’t use them at all).

### **e) Sync Logic**

* For each file:

  1. Load content from Drive.
  2. Extract all metadata using OpenAI/filename.
  3. Upload to Notion using the current schema.
  4. Only **after** successful Notion upload, move file to `processed` folder.
  5. On error, log clearly, do **not** move file, and continue with next.

---

## **Step 4: Implement or Refactor Each Service to Match Python Logic**

### **Google Drive**

* List and download logic matches Python’s breadth (look at all docs in parent/shared folder, not just root).

### **OpenAI Service**

* **No initialization needed at runtime** unless you have a pool or cache.
* Expose static methods or export an instantiated service.

### **Notion Service**

* Accept data only for columns present in your Notion database.
* All mapping logic for properties should match schema (title, date, etc.).
* **Chunk** content if needed (2000 chars per block, etc.).

---

## **Step 5: Test with Logging and Recovery**

1. Add robust logs before/after every file is processed.

2. On any error, log:

   * Which file
   * What step failed
   * What exception/error was thrown

3. After a successful run, **ALL** files should be in Notion and the processed folder, and logs should reflect this.

---

## **Step 6: Final Cleanup and Consistency Check**

* Remove all code that references non-existent Notion properties.
* Ensure all secrets are used from env/secure storage.
* Add unit tests for all helper functions (metadata extraction, LLM prompts).
* Document the flow for future agents (README/inline docs).

---

## **Step 7: Questions for You (to Ensure Perfect Mapping)**

1. **Should every transcript always be moved to the processed folder, even if it errors, or only after a successful Notion upload?**

   * *(Python code only moves after success; is that the behavior you want?)*

2. **If OpenAI/LLM fails to extract (API down or rate-limited), should the fallback be filename parsing, or do you want to skip the file?**

3. **If a field can’t be parsed (e.g., date or attendees), should it default to “Unknown” or should the file be left unprocessed for manual review?**

4. **Do you want an email/slack alert or log entry if a transcript fails to process?**

5. **Do you require sub-page splitting in Notion (for very long content), or just one page per transcript with multiple blocks?**

6. **Do you ever want to overwrite existing Notion entries if the transcript is retried, or should duplicates be allowed?**

7. **Is there any field mapping or renaming needed between Python and your current Notion schema?**

---

## **Final Deliverable for Your Agent**

### **Agent, here is your checklist:**

* [ ] Fix all import/export errors for all services (`openaiService.initialize` bug!).
* [ ] Reconcile all function/class/object definitions and usages across the services (no mismatched signatures).
* [ ] Ensure the OpenAI/LLM service matches the metadata extraction logic (including fallback) of the provided Python.
* [ ] Update Notion integration to use only the properties present in the actual database, in correct types/formats.
* [ ] Ensure all transcript files are processed, added to Notion, and only then moved to the processed folder.
* [ ] Log every file’s processing result, with error details if any fail.
* [ ] Remove ALL code that references old/broken Notion properties.
* [ ] Add a unit test for the key metadata extraction helpers.
* [ ] Confirm behavior with the user for edge cases (see questions above).
* [ ] Re-run sync, confirm that 100% of files go to Notion and then processed folder—none are skipped or stuck.
* [ ] Document the flow (briefly) for future maintainers.

---

### **Once you answer the questions above, I can generate sample code or mapping docs for the core logic. Just let me know!**
