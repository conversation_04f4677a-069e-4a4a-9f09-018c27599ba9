[media pointer="file-service://file-11xGWy6948nHgQb2jcFNGr"]
are you sure this is the case? right now im not seeing any part of one of my transcripts (the long one). view the image i attached of my previous implementation of this functionality that uses python scripts to accomplish. 


files from prev script implementation:

import os
import json
from flask import Flask
from google_drive_utils import get_drive_service, extract_doc_content, mark_processed
from notion_utils import upload_to_notion
from googleapiclient.discovery import build
from google.oauth2 import service_account

# Load config
if not os.path.exists('config.json'):
    print("Error: config.json not found!")
    exit(1)
with open('config.json') as f:
    cfg = json.load(f)
NOTION_TOKEN = cfg['notion_token']
DATABASE_ID  = cfg['database_id']
SERVICE_ACCOUNT_FILE = 'service_account.json'
SCOPES = ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/documents.readonly']

app = Flask(__name__)

def main():
    try:
        svc = get_drive_service()
        print("Successfully created Drive service")
        # Initialize the Docs API service using the same service account credentials
        creds = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        docs_service = build('docs', 'v1', credentials=creds)
        print("Successfully created Docs service")
        # 1) find the Shared Drive
        print("Looking for Shared Drive…")
        drives_result = svc.drives().list(
            q="name='Shared Meeting Transcripts Drive'"
        ).execute()
        drives = drives_result.get('drives', [])
        print(f"Found {len(drives)} drives matching the query")
        if not drives:
            print("No shared drive named 'Shared Meeting Transcripts Drive' found")
            print("Available drives:")
            all_drives = svc.drives().list().execute().get('drives', [])
            for drive in all_drives:
                print(f"- {drive.get('name', 'Unknown')} ({drive.get('id', 'No ID')})")
            return
        drive_id = drives[0]['id']
        print(f"Using shared drive ID: {drive_id}")

        # Get the Processed Transcripts folder ID
        processed_folder_resp = svc.files().list(
            q=f"name='Processed Transcripts' and mimeType='application/vnd.google-apps.folder' and '{drive_id}' in parents",
            fields='files(id)',
            includeItemsFromAllDrives=True,
            supportsAllDrives=True
        ).execute()
        processed_folder_id = None
        processed_folders = processed_folder_resp.get('files', [])
        if processed_folders:
            processed_folder_id = processed_folders[0]['id']
            print(f"Found Processed Transcripts folder with ID: {processed_folder_id}")

        page_token = None
        while True:
            # 2) list all Google Docs in that shared drive, excluding those in Processed Transcripts folder
            query = "mimeType='application/vnd.google-apps.document'"
            if processed_folder_id:
                query += f" and not '{processed_folder_id}' in parents"
            resp = svc.files().list(
                q=query,
                corpora='drive',
                driveId=drive_id,
                includeItemsFromAllDrives=True,
                supportsAllDrives=True,
                fields='nextPageToken, files(id,name,parents)',
                pageToken=page_token
            ).execute()
            files = resp.get('files', [])
            print("Found", len(files), "docs in shared drive")
            for f in files:
                print("Processing:", f['name'], f['id'])
                # Extract content using Docs API
                doc = docs_service.documents().get(documentId=f['id'], includeTabsContent=True).execute()
                notes_structural_elements = None
                for tab in doc.get("tabs", []):
                    tab_title = tab["tabProperties"].get("title", "").lower()
                    if "notes" in tab_title:
                        notes_structural_elements = tab["documentTab"]["body"]["content"]
                        break
                notes_text, transcript_text = extract_doc_content(docs_service, f['id'])
                print(f"Found notes ({len(notes_text)} chars) and transcript ({len(transcript_text)} chars)")
                # 4) push to Notion
                upload_to_notion(f['name'], transcript_text, notes_text, NOTION_TOKEN, DATABASE_ID, notes_structural_elements=notes_structural_elements)
                # 5) archive
                file_parents = f.get('parents',[None])[0]
                print("Moving to archive folder…")
                mark_processed(svc, f['id'], file_parents, drive_id)
                print("✅ uploaded", f['name'])
            page_token = resp.get('nextPageToken')
            if not page_token:
                break
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        return

@app.route('/')
def index():
    return "Meet transcript-to-Notion service is running."

@app.route('/run')
def run_script():
    main()
    return "Processing complete."

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.environ.get("PORT", 8080))) 


import os, pickle, io
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
from google.oauth2 import service_account

SCOPES = ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/documents.readonly']
SERVICE_ACCOUNT_FILE = 'service_account.json'

PROCESSED_LABEL = 'Processed Transcripts'

def get_drive_service():
    creds = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE, scopes=SCOPES)
    return build('drive', 'v3', credentials=creds)

def extract_text_from_structural_elements(elements):
    text = ""
    for el in elements:
        if "paragraph" in el:
            for elem in el["paragraph"].get("elements", []):
                run = elem.get("textRun")
                if run and run.get("content"):
                    text += run["content"]
        elif "table" in el:
            for row in el["table"]["tableRows"]:
                for cell in row["tableCells"]:
                    text += extract_text_from_structural_elements(cell["content"])
        elif "tableOfContents" in el:
            text += extract_text_from_structural_elements(el["tableOfContents"]["content"])
    return text

def extract_doc_content(service, doc_id):
    """Extract content from Notes and Transcript tabs of the Google Doc using the new tab structure."""
    doc = service.documents().get(documentId=doc_id, includeTabsContent=True).execute()
    notes_text = ""
    transcript_text = ""
    for tab in doc.get("tabs", []):
        tab_title = tab["tabProperties"].get("title", "").lower()
        tab_body = tab["documentTab"]["body"]["content"]
        tab_text = extract_text_from_structural_elements(tab_body)
        if "notes" in tab_title:
            notes_text = tab_text
        elif "transcript" in tab_title:
            transcript_text = tab_text
    return notes_text.strip(), transcript_text.strip()

def mark_processed(service, file_id, file_parents, drive_id):
    # Find or create the 'Processed Transcripts' folder inside the shared drive
    folder_resp = service.files().list(
        q=f"name='{PROCESSED_LABEL}' and mimeType='application/vnd.google-apps.folder' and '{drive_id}' in parents",
        fields='files(id)',
        includeItemsFromAllDrives=True,
        supportsAllDrives=True
    ).execute()
    folder_id = None
    files = folder_resp.get('files', [])
    if files:
        folder_id = files[0]['id']
    else:
        # Create the folder in the shared drive root
        folder_metadata = {
            'name': PROCESSED_LABEL,
            'mimeType': 'application/vnd.google-apps.folder',
            'parents': [drive_id],
            'driveId': drive_id
        }
        folder = service.files().create(
            body=folder_metadata,
            fields='id',
            supportsAllDrives=True
        ).execute()
        folder_id = folder['id']
    # Always remove the current parent (either a folder or the drive root)
    remove_parent = file_parents if file_parents else drive_id
    update_kwargs = {
        'fileId': file_id,
        'addParents': folder_id,
        'removeParents': remove_parent,
        'supportsAllDrives': True
    }
    service.files().update(**update_kwargs).execute()

def extract_summary_paragraph(notes_structural_elements):
    found_summary = False
    for el in notes_structural_elements:
        # Look for heading 3 with text 'Summary'
        if (
            el.get('paragraph')
            and el['paragraph'].get('paragraphStyle', {}).get('namedStyleType') == 'HEADING_3'
        ):
            text = ''.join(
                elem.get('textRun', {}).get('content', '')
                for elem in el['paragraph'].get('elements', [])
            ).strip().lower()
            if text.startswith('summary'):
                found_summary = True
                continue
        # The next paragraph after 'Summary' heading is the summary paragraph
        if found_summary and el.get('paragraph'):
            para_text = ''.join(
                elem.get('textRun', {}).get('content', '')
                for elem in el['paragraph'].get('elements', [])
            ).strip()
            if para_text:
                return para_text
    return '' 

import openai
import json

def get_openai_key():
    with open('chatgptapi.json') as f:
        return json.load(f)['key']

def get_meeting_name(summary_text):
    openai.api_key = get_openai_key()
    response = openai.chat.completions.create(
        model="gpt-4.1-nano-2025-04-14",
        messages=[
            {"role": "system", "content": "Extract the meeting name from the following summary. Return only the meeting name, nothing else."},
            {"role": "user", "content": summary_text}
        ],
        max_tokens=30,
        temperature=0,
    )
    print(summary_text)
    print(response.choices[0].message.content.strip())
    return response.choices[0].message.content.strip()

def get_attendees_csv(summary_text):
    openai.api_key = get_openai_key()
    response = openai.chat.completions.create(
        model="gpt-4.1-nano-2025-04-14",
        messages=[
            {"role": "system", "content": "Extract the attendees names from the following summary. Do not include AI assistants or bot note-takers such as 'Gemini' in the attendees list. Return only the names as a comma-separated list, nothing else."},
            {"role": "user", "content": summary_text}
        ],
        max_tokens=60,
        temperature=0,
    )
    print(summary_text)
    print(response.choices[0].message.content.strip())
    return response.choices[0].message.content.strip()


import re
from datetime import datetime

def extract_metadata(filename):
    try:
        # Try to extract date/time from the filename (e.g., 'Meeting started 2025/05/19 12:20 EDT - Notes by Gemini')
        match = re.search(r'(\d{4})/(\d{2})/(\d{2}) (\d{2}):(\d{2}) (\w+)', filename)
        if match:
            year, month, day, hour, minute, tz = match.groups()
            date_obj = datetime.strptime(f'{year}-{month}-{day}', '%Y-%m-%d')
            iso_date = date_obj.strftime('%Y-%m-%d')  # ISO format for Notion
            formatted_date = date_obj.strftime('%B %d, %Y')  # Human-readable
            formatted_time = f'{int(hour)%12 or 12}:{minute} {"AM" if int(hour)<12 else "PM"} {tz}'
            return {
                'date': formatted_date,      # for display
                'date_iso': iso_date,        # for Notion property
                'time': formatted_time,
                'meeting_name': filename
            }
    except Exception as e:
        print(f"Warning: Could not parse metadata from filename: {e}")
    return {
        'date': 'Unknown Date',
        'date_iso': None,
        'time': 'Unknown Time',
        'meeting_name': filename
    }

def extract_attendees_from_notes(notes_text):
    # Look for a line that lists attendees, e.g. "Anant Aggarwal, Azha Qari, and Jonathan Razza"
    # Try to find a line with at least two full names (Firstname Lastname)
    lines = notes_text.splitlines()
    for line in lines:
        # Skip lines that are likely titles or dates
        if len(line) < 5 or line.lower().startswith("meeting") or "summary" in line.lower():
            continue
        # Look for 2+ names (Firstname Lastname, ...)
        match = re.search(r'([A-Z][a-z]+ [A-Z][a-z]+(?:, [A-Z][a-z]+ [A-Z][a-z]+)*(?:,? and [A-Z][a-z]+ [A-Z][a-z]+)?)', line)
        if match and len(match.group(1).split()) >= 4:  # At least two names
            return match.group(1)
    return 'Not specified' 

import json
from notion_client import Client
from metadata_utils import extract_metadata
from llm_utils import get_meeting_name, get_attendees_csv
from google_drive_utils import extract_summary_paragraph

def get_database_structure(notion, database_id):
    db = notion.databases.retrieve(database_id)
    return db['properties']

def upload_to_notion(title, transcript_text, notes_text, NOTION_TOKEN, DATABASE_ID, notes_structural_elements=None):
    notion = Client(auth=NOTION_TOKEN)
    print(f"\nContent lengths:")
    print(f"Title ({len(title)} chars):", title[:100])
    print(f"Transcript ({len(transcript_text)} chars):", transcript_text[:100])
    print(f"Notes ({len(notes_text)} chars):", notes_text[:100])

    # Extract metadata from filename (for date/time only)
    metadata = extract_metadata(title)

    # Extract summary paragraph from Notes tab structural elements
    summary_para = ''
    if notes_structural_elements:
        summary_para = extract_summary_paragraph(notes_structural_elements)
    if not summary_para:
        summary_para = notes_text[:300]  # fallback: first 300 chars

    # Use LLM to get meeting name and attendees
    try:
        meeting_name = get_meeting_name(summary_para)
        attendees = get_attendees_csv(summary_para)
    except Exception as e:
        print(f"LLM extraction failed: {e}")
        meeting_name = metadata['meeting_name']
        attendees = 'Not specified'

    # Create blocks for the page content
    blocks = [
        {
            'object': 'block',
            'type': 'heading_1',
            'heading_1': {
                'rich_text': [{'type': 'text', 'text': {'content': meeting_name}}]
            }
        },
        {
            'object': 'block',
            'type': 'paragraph',
            'paragraph': {
                'rich_text': [
                    {'type': 'text', 'text': {'content': '📅 ' + metadata['date']}},
                    {'type': 'text', 'text': {'content': ' | '}},
                    {'type': 'text', 'text': {'content': '🕒 ' + metadata['time']}},
                    {'type': 'text', 'text': {'content': ' | '}},
                    {'type': 'text', 'text': {'content': '👥 ' + attendees}}
                ]
            }
        },
        {
            'object': 'block',
            'type': 'divider',
            'divider': {}
        },
        {
            'object': 'block',
            'type': 'heading_2',
            'heading_2': {
                'rich_text': [{'type': 'text', 'text': {'content': 'Notes'}}]
            }
        }
    ]
    for chunk in [notes_text[i:i+2000] for i in range(0, len(notes_text), 2000)]:
        blocks.append({
            'object': 'block',
            'type': 'paragraph',
            'paragraph': {
                'rich_text': [{'type': 'text', 'text': {'content': chunk}}]
            }
        })
    blocks.extend([
        {
            'object': 'block',
            'type': 'heading_2',
            'heading_2': {
                'rich_text': [{'type': 'text', 'text': {'content': 'Transcript'}}]
            }
        }
    ])
    for chunk in [transcript_text[i:i+2000] for i in range(0, len(transcript_text), 2000)]:
        blocks.append({
            'object': 'block',
            'type': 'paragraph',
            'paragraph': {
                'rich_text': [{'type': 'text', 'text': {'content': chunk}}]
            }
        })
    properties = {
        'Name': {
            'title': [{'type': 'text', 'text': {'content': meeting_name}}]
        },
        'Date': {
            'date': {
                'start': metadata['date_iso']
            }
        },
        'Time': {
            'rich_text': [{'type': 'text', 'text': {'content': metadata['time']}}]
        },
        'Attendees': {
            'rich_text': [{'type': 'text', 'text': {'content': attendees}}]
        }
    }
    print("\nCreating Notion page with full content and metadata...")
    page = notion.pages.create(
        parent={'database_id': DATABASE_ID},
        properties=properties,
        children=blocks
    )
    print(f"✅ Created page: {meeting_name}")
    return page 