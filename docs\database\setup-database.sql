-- PostgreSQL Database Setup for MeetSync with RAG Integration
-- Run this script as a PostgreSQL superuser (usually postgres)

-- Create database if it doesn't exist
-- Note: You need to run this from psql command line or pgAdmin
-- CREATE DATABASE meetsync_db;

-- Connect to the database and enable pgvector extension
-- \c meetsync_db;

-- Enable pgvector extension for vector operations
CREATE EXTENSION IF NOT EXISTS vector;

-- Verify pgvector is installed
SELECT * FROM pg_extension WHERE extname = 'vector';

-- Create a user for the application (optional, for security)
-- CREATE USER meetsync_user WITH PASSWORD 'your_secure_password';
-- GRANT ALL PRIVILEGES ON DATABASE meetsync_db TO meetsync_user;

-- Note: After running this, you can run the Drizzle migrations:
-- npm run db:push
