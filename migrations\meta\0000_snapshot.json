{"id": "36827350-d2de-488d-a83a-362b79e2b76d", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.file_access": {"name": "file_access", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_email": {"name": "user_email", "type": "text", "primaryKey": false, "notNull": true}, "access_level": {"name": "access_level", "type": "text", "primaryKey": false, "notNull": true, "default": "'read'"}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true}, "file_content": {"name": "file_content", "type": "text", "primaryKey": false, "notNull": false}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": false}, "download_url": {"name": "download_url", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "parent_folder": {"name": "parent_folder", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "extracted_metadata": {"name": "extracted_metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": false}, "sync_item_id": {"name": "sync_item_id", "type": "integer", "primaryKey": false, "notNull": false}, "notion_page_id": {"name": "notion_page_id", "type": "text", "primaryKey": false, "notNull": false}, "last_modified": {"name": "last_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_shared": {"name": "is_shared", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "shared_with": {"name": "shared_with", "type": "text[]", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.integrations": {"name": "integrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'disconnected'"}, "credentials": {"name": "credentials", "type": "text", "primaryKey": false, "notNull": false}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "next_sync_at": {"name": "next_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "sync_schedule": {"name": "sync_schedule", "type": "text", "primaryKey": false, "notNull": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'idle'"}, "source_config": {"name": "source_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "destination_config": {"name": "destination_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_llm_enabled": {"name": "is_llm_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "sync_filters": {"name": "sync_filters", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.meeting_access": {"name": "meeting_access", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "meeting_id": {"name": "meeting_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_email": {"name": "user_email", "type": "text", "primaryKey": false, "notNull": true}, "access_level": {"name": "access_level", "type": "text", "primaryKey": false, "notNull": true, "default": "'read'"}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.meetings": {"name": "meetings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": true}, "meeting_name": {"name": "meeting_name", "type": "text", "primaryKey": false, "notNull": true}, "datetime": {"name": "datetime", "type": "timestamp", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true}, "transcript_text": {"name": "transcript_text", "type": "text", "primaryKey": false, "notNull": false}, "transcript_file_url": {"name": "transcript_file_url", "type": "text", "primaryKey": false, "notNull": false}, "attendees": {"name": "attendees", "type": "text[]", "primaryKey": false, "notNull": false}, "extracted_metadata": {"name": "extracted_metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": false}, "sync_item_id": {"name": "sync_item_id", "type": "integer", "primaryKey": false, "notNull": false}, "notion_page_id": {"name": "notion_page_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sync_items": {"name": "sync_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "integration_id": {"name": "integration_id", "type": "integer", "primaryKey": false, "notNull": true}, "sync_log_id": {"name": "sync_log_id", "type": "integer", "primaryKey": false, "notNull": false}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "destination_url": {"name": "destination_url", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sync_logs": {"name": "sync_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "integration_id": {"name": "integration_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "items_processed": {"name": "items_processed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "items_success": {"name": "items_success", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "items_failed": {"name": "items_failed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}