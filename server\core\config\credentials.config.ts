import dotenv from 'dotenv';

dotenv.config();

export const credentials = {
    "google": {
        "GOOGLE_CLIENT_ID": process.env.GOOGLE_CLIENT_ID || '',
        "GOOGLE_CLIENT_SECRET": process.env.GOOGLE_CLIENT_SECRET || '',
        "GOOGLE_REDIRECT_URI": process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/auth/google/callback'
    },
    "microsoft": {
        "MICROSOFT_CLIENT_ID": process.env.MICROSOFT_CLIENT_ID || '',
        "MICROSOFT_CLIENT_SECRET": process.env.MICROSOFT_CLIENT_SECRET || '',
        "MICROSOFT_TENANT_ID": process.env.MICROSOFT_TENANT_ID || 'common',
        "MICROSOFT_REDIRECT_URI": process.env.MICROSOFT_REDIRECT_URI || 'http://localhost:3000/api/auth/microsoft/callback'
    }
}