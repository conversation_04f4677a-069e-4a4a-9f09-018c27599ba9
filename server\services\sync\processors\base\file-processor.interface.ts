/**
 * Base interface for file processors
 * Each platform (Google Drive, Microsoft Teams, etc.) should implement this interface
 */
export interface IFileProcessor {
  /**
   * Process a file from the platform
   * @param fileData Raw file data from the platform
   * @param existingFile Existing file record in database (if any)
   * @param integration Integration configuration
   * @returns Processing result
   */
  processFile(fileData: any, existingFile: any, integration: any): Promise<ProcessResult>;

  /**
   * Check if this processor can handle the given platform and file type
   * @param platform Platform identifier (e.g., 'google_drive', 'microsoft_teams')
   * @param fileType Optional file type filter
   * @returns True if processor can handle this file
   */
  canProcess(platform: string, fileType?: string): boolean;

  /**
   * Get the platform identifier this processor handles
   */
  getPlatform(): string;
}

/**
 * Result of file processing operation
 */
export interface ProcessResult {
  success: boolean;
  skipped: boolean;
  error?: string;
  savedFile?: any;
  processingDetails?: {
    contentExtracted: boolean;
    embeddingsGenerated: boolean;
    aiMetadataExtracted: boolean;
    processingTime?: number;
  };
}

/**
 * Platform-specific metadata structure
 */
export interface PlatformMetadata {
  platform: string;
  sourceType: string;
  sourceContext: string;
  folderPath?: string;
  owner?: string;
  lastModified: Date;
  additionalMetadata?: Record<string, any>;
}

/**
 * Content extraction result
 */
export interface ContentExtractionResult {
  success: boolean;
  content?: string;
  vectorizationContent?: string;
  metadata?: Record<string, any>;
  error?: string;
  extractionMethod?: 'specialized' | 'universal' | 'fallback' | 'metadata-only' | 'failed';
}

/**
 * Base abstract class for file processors
 * Provides common functionality that all processors can use
 */
export abstract class BaseFileProcessor implements IFileProcessor {
  abstract processFile(fileData: any, existingFile: any, integration: any): Promise<ProcessResult>;
  abstract canProcess(platform: string, fileType?: string): boolean;
  abstract getPlatform(): string;

  /**
   * Check if file needs processing based on modification time
   */
  protected needsProcessing(existingFile: any, sourceModified: Date): boolean {
    if (!existingFile) {
      return true; // New file always needs processing
    }

    const existingModified = existingFile.lastModified 
      ? new Date(existingFile.lastModified) 
      : new Date(0);
    
    return sourceModified > existingModified || existingFile.status !== 'active';
  }

  /**
   * Safe file size handling for PostgreSQL integer limits
   */
  protected getSafeFileSize(fileSize: number | undefined): number {
    if (!fileSize) return 0;
    return fileSize > 2147483647 ? 2147483647 : fileSize;
  }

  /**
   * Create standardized processing result
   */
  protected createResult(
    success: boolean, 
    skipped: boolean, 
    savedFile?: any, 
    error?: string,
    processingDetails?: any
  ): ProcessResult {
    return {
      success,
      skipped,
      savedFile,
      error,
      processingDetails
    };
  }

  /**
   * Log processing information with consistent format
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const prefix = `[${this.getPlatform().toUpperCase()}]`;
    
    switch (level) {
      case 'info':
        console.log(`${prefix} ${message}`, data || '');
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, data || '');
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data || '');
        break;
    }
  }
}
