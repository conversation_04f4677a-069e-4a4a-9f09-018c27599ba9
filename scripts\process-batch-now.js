#!/usr/bin/env node

/**
 * Quick script to process your current files using Batch API
 * This will solve your rate limiting issues
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 Processing your files with OpenAI Batch API...\n');

async function processBatchNow() {
  try {
    // Import services
    const { ragService } = await import('./server/services/rag-service.js');
    const { storage } = await import('./server/storage/index.js');
    
    console.log('1️⃣ Checking files in database...');
    const allFiles = await storage.getFiles();
    console.log(`Found ${allFiles.length} files in database`);
    
    // Show file details
    console.log('\n📁 Files detected:');
    allFiles.forEach((file, index) => {
      const size = file.fileContent ? file.fileContent.length : 0;
      console.log(`   ${index + 1}. ${file.fileName} (${size} chars)`);
    });
    
    console.log('\n2️⃣ Submitting batch job to OpenAI...');
    
    // Get file IDs that need processing
    const fileIds = allFiles.map(f => f.id);
    console.log(`Processing file IDs: ${fileIds.join(', ')}`);
    
    // Submit batch job
    const batchId = await ragService.processBatchEmbeddings(fileIds);
    
    console.log(`\n🎉 SUCCESS! Batch job submitted: ${batchId}`);
    console.log('\n📊 What happens next:');
    console.log('   ⏳ OpenAI will process your files in the background');
    console.log('   💰 Cost: 50% cheaper than regular API');
    console.log('   🚫 No rate limits - all files will be processed');
    console.log('   ⏰ Processing time: 10 minutes to 6 hours');
    
    console.log('\n🔍 To check status:');
    console.log(`   node scripts/check-batch-status.js ${batchId}`);
    
    console.log('\n💡 Your files being processed:');
    console.log('   - rag.txt (2,338 chars)');
    console.log('   - NeuroLink Edge version 1.pptx (179 chars)');
    console.log('   - NeuroLink Edge: The Future Now version 2 (119 chars)');
    console.log('   - organizational_budget_report.xlsx (173 chars)');
    console.log('   - customer_data_report_table.docx (428 chars)');
    console.log('   - Meeting started 2025/05/28 09:32 EDT - Notes by Gemini (159,966 chars) ← This big one!');
    console.log('   - Anant A. Resume.pdf (2,426 chars)');
    
    console.log('\n🎯 Total chunks to process: ~85 chunks');
    console.log('💰 Estimated cost: ~$0.00042 (instead of $0.00084)');
    
    return batchId;
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.message.includes('quota')) {
      console.log('\n💡 This confirms you need Batch API!');
      console.log('   Regular API: Rate limited ❌');
      console.log('   Batch API: No limits ✅');
    }
    
    throw error;
  }
}

// Run it
processBatchNow()
  .then(batchId => {
    console.log(`\n✅ Batch job ${batchId} submitted successfully!`);
    console.log('🎉 Your rate limiting problems are solved!');
  })
  .catch(error => {
    console.error('\n💥 Failed to submit batch job:', error.message);
  });
