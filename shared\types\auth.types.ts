import { z } from "zod";

// User-related types
export interface User {
  id: number;
  username: string;
  password: string;
}

export interface InsertUser {
  username: string;
  password: string;
}

// OAuth-related types
export interface OAuthCredentials {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  scope?: string;
}

export interface GoogleOAuthData {
  code: string;
  redirectUri: string;
}

export interface MicrosoftOAuthData {
  code: string;
  redirectUri: string;
  state?: string;
}

// Authentication status types
export type AuthStatus = "authenticated" | "unauthenticated" | "pending" | "error";

export interface AuthSession {
  userId: string;
  username: string;
  isAuthenticated: boolean;
  expiresAt?: Date;
}

// OAuth validation schemas
export const googleOAuthSchema = z.object({
  code: z.string(),
  redirectUri: z.string().url(),
});

export const microsoftOAuthSchema = z.object({
  code: z.string(),
  redirectUri: z.string().url(),
  state: z.string().optional(),
});

export type GoogleOAuthRequest = z.infer<typeof googleOAuthSchema>;
export type MicrosoftOAuthRequest = z.infer<typeof microsoftOAuthSchema>;
