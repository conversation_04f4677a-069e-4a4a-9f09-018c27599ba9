> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Scheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
7:47:54 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
7:47:56 PM [express] GET /api/sync-logs 304 in 91ms :: {"logs":[]}
7:47:57 PM [express] GET /api/integrations 304 in 325ms :: {"integrations":[{"id":1,"name":"Google M…
7:48:02 PM [express] GET /api/sync-logs 304 in 76ms :: {"logs":[]}
7:48:02 PM [express] GET /api/integrations 304 in 79ms :: {"integrations":[{"id":1,"name":"Google Me…
7:48:02 PM [express] GET /api/sync-logs 304 in 78ms :: {"logs":[]}
7:48:02 PM [express] GET /api/integrations 304 in 83ms :: {"integrations":[{"id":1,"name":"Google Me…
7:48:48 PM [express] GET /api/integrations 304 in 2323ms :: {"integrations":[{"id":1,"name":"Google …
7:50:34 PM [express] GET /api/integrations 304 in 856ms :: {"integrations":[{"id":1,"name":"Google M…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_bln3aij5ffm
7:50:52 PM [express] GET /api/integrations/1/auth-url 200 in 2495ms :: {"authUrl":"https://accounts.…
7:51:00 PM [express] GET /api/integrations/oauth/callback 302 in 2ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
7:51:00 PM [express] GET /api/integrations/1/oauth/callback 302 in 277ms
7:51:00 PM [express] GET /api/integrations 200 in 76ms :: {"integrations":[{"id":1,"name":"Google Me…
7:55:23 PM [express] GET /api/sync-logs 200 in 951ms :: {"logs":[]}
7:55:26 PM [express] GET /api/integrations 304 in 3383ms :: {"integrations":[{"id":1,"name":"Google …
7:55:29 PM [express] GET /api/integrations 304 in 77ms :: {"integrations":[{"id":1,"name":"Google Me…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_6mlsk2jaoop
7:55:30 PM [express] GET /api/integrations/1/auth-url 200 in 167ms :: {"authUrl":"https://accounts.g…
7:55:54 PM [express] GET /api/integrations/oauth/callback 302 in 1ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
7:55:54 PM [express] GET /api/integrations/1/oauth/callback 302 in 489ms
7:55:55 PM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…