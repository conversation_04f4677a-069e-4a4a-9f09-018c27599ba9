import { Request, Response } from 'express';
import { BaseIntegrationController } from '../base/base-integration.controller.js';
import { googleService } from '../../../services/platform-integrations/google/index.js';
import { google } from 'googleapis';

/**
 * Controller for Google Drive integration operations
 * Handles folder management, connection testing, and drive structure
 */
export class GoogleIntegrationController extends BaseIntegrationController {
  constructor() {
    super();
    // Bind all methods to preserve 'this' context
    this.getGoogleDriveFolders = this.getGoogleDriveFolders.bind(this);
    this.getDriveStructure = this.getDriveStructure.bind(this);
    this.debugDriveFolders = this.debugDriveFolders.bind(this);
    this.testConnection = this.testConnection.bind(this);
  }

  /**
   * List Google Drive folders for integration setup
   */
  async getGoogleDriveFolders(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      this.logAction(`Fetching Google Drive folders for integration ${id}`);

      // Get the integration
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Only allow Google integrations (Meet or Drive)
      if (!this.validateIntegrationType(integration, ['google-drive', 'google_drive'])) {
        return res.status(400).json({ message: 'This endpoint is only for Google integrations.' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      // Get query parameter for parent folder ID
      const { driveId } = req.query;
      const parentFolderId = (driveId as string) || 'root';
      this.logAction(`Listing folders in parent folder: ${parentFolderId}`);

      try {
        // Get authorized client with fresh token
        const auth = await googleService.getAuthorizedClient(integration.credentials);

        this.logAction('Getting Drive API client...');

        // First make a simple API request to test connection
        this.logAction('Testing Drive API connection...');
        const drive = google.drive({ version: 'v3', auth });
        const testResult = await drive.about.get({
          fields: 'user,storageQuota'
        });

        this.logAction(`Drive API connection successful. User: ${testResult.data.user?.displayName}`);

        // Use our improved folder listing method
        const folders = await googleService.listDriveFolders(auth, parentFolderId !== 'root' ? parentFolderId : null);

        // If no folders found, provide a useful message
        if (folders.length === 0) {
          this.logAction('No folders found in Google Drive');

          return this.successResponse(res, {
            folders: [],
            user: testResult.data.user?.displayName || 'Unknown User',
            email: testResult.data.user?.emailAddress,
            message: 'No folders found in your Google Drive. You may need to create a folder first.',
            canCreateFolder: true
          });
        }

        // Return the found folders with user info
        return this.successResponse(res, {
          folders,
          user: testResult.data.user?.displayName || 'Unknown User',
          email: testResult.data.user?.emailAddress,
          message: `Found ${folders.length} folders in Google Drive`,
          canCreateFolder: true
        });

      } catch (error: any) {
        this.logError('Authentication or API error with Google Drive:', error);

        // Get detailed error information
        let errorDetails = error.message || 'Unknown error';
        let errorCode = 'API_ERROR';

        // If there's a structured API error response, extract it
        if (error.response && error.response.data) {
          errorDetails = JSON.stringify(error.response.data);
          if (error.response.data.error) {
            errorCode = error.response.data.error.code || errorCode;
          }
        }

        // Handle specific error types
        if (errorDetails.includes('invalid_grant') || errorDetails.includes('expired')) {
          return res.status(401).json({
            message: 'Your Google authentication has expired. Please reconnect your account.',
            error: 'EXPIRED_TOKEN',
            details: errorDetails
          });
        }

        // For permission errors
        if (errorDetails.includes('permission') || errorDetails.includes('scope') ||
            errorDetails.includes('403') || errorCode === '403') {
          return res.status(403).json({
            message: 'You need to grant additional permissions for accessing Google Drive folders.',
            error: 'INSUFFICIENT_PERMISSIONS',
            details: errorDetails
          });
        }

        // For Drive API not enabled
        if (errorDetails.includes('Drive API') || errorDetails.includes('not enabled') ||
            errorCode === '403' || errorCode === '404') {
          return res.status(403).json({
            message: 'Google Drive API may not be enabled for your project. Please check your Google Cloud Console.',
            error: 'API_NOT_ENABLED',
            details: errorDetails
          });
        }

        // Generic API error
        return res.status(500).json({
          message: 'Error accessing Google Drive',
          error: errorCode,
          details: errorDetails
        });
      }

    } catch (error: any) {
      return this.handleError(res, error, `Failed to list Google Drive folders for integration ${req.params.id}`);
    }
  }

  /**
   * Get complete Google Drive structure including My Drive, Shared Drives, Computers, and Shared with me
   */
  async getDriveStructure(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        // Get authorized client with fresh token
        const auth = await googleService.getAuthorizedClient(integration.credentials);

        this.logAction('Getting complete Drive structure...');

        // Get the complete drive structure
        const driveStructure = await googleService.getDriveStructure(auth);

        // Format the response for the frontend
        const response = {
          success: true,
          structure: {
            myDrive: {
              id: 'root',
              name: 'My Drive',
              type: 'root',
              folders: driveStructure.myDrive.map((folder: any) => ({
                id: folder.id,
                name: folder.name,
                type: 'folder',
                parent: 'root',
                shared: folder.shared || false,
              }))
            },
            sharedDrives: driveStructure.sharedDrives.map((drive: any) => ({
              id: drive.id,
              name: drive.name,
              type: 'shared_drive',
              capabilities: drive.capabilities,
              folders: (drive.folders || []).map((folder: any) => ({
                id: folder.id,
                name: folder.name,
                type: 'folder',
                parent: drive.id,
                shared: folder.shared || false,
              }))
            })),
            computers: driveStructure.computers.map((computer: any) => ({
              id: computer.id,
              name: computer.name,
              type: 'computer',
              shared: computer.shared || false,
            })),
            sharedWithMe: driveStructure.sharedWithMe.map((folder: any) => ({
              id: folder.id,
              name: folder.name,
              type: 'shared_folder',
              owners: folder.owners,
              shared: true,
            }))
          }
        };

        this.logAction(`Successfully retrieved Drive structure with ${driveStructure.myDrive.length + driveStructure.sharedDrives.length + driveStructure.computers.length + driveStructure.sharedWithMe.length} total items`);

        return this.successResponse(res, response);
      } catch (error: any) {
        return this.handleError(res, error, 'Failed to get Drive structure');
      }
    } catch (error: any) {
      return this.handleError(res, error, 'Internal server error in getDriveStructure');
    }
  }

  /**
   * Debug endpoint to see all folders in Google Drive (for troubleshooting computer detection)
   */
  async debugDriveFolders(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        const auth = await googleService.getAuthorizedClient(integration.credentials);
        const drive = google.drive({ version: "v3", auth });

        // Get ALL folders in the drive
        const allFoldersResponse = await drive.files.list({
          q: "mimeType='application/vnd.google-apps.folder' and trashed=false",
          fields: "files(id, name, parents, driveId, shared, owners, createdTime, modifiedTime)",
          pageSize: 1000,
          orderBy: "name",
          includeItemsFromAllDrives: true,
          supportsAllDrives: true,
        });

        const allFolders = allFoldersResponse.data.files || [];

        // Categorize folders
        const rootFolders = allFolders.filter((f: any) => f.parents?.includes('root'));
        const nonRootFolders = allFolders.filter((f: any) => !f.parents?.includes('root'));

        return this.successResponse(res, {
          debug: {
            totalFolders: allFolders.length,
            rootFolders: rootFolders.length,
            nonRootFolders: nonRootFolders.length,
            allFolders: allFolders.map((f: any) => ({
              id: f.id,
              name: f.name,
              parents: f.parents,
              isRoot: f.parents?.includes('root'),
              shared: f.shared,
              owners: f.owners?.map((o: any) => o.displayName || o.emailAddress),
              created: f.createdTime,
              modified: f.modifiedTime
            }))
          }
        });
      } catch (error: any) {
        return this.handleError(res, error, 'Failed to debug folders');
      }
    } catch (error: any) {
      return this.handleError(res, error, 'Internal server error in debugDriveFolders');
    }
  }

  /**
   * Test Google Drive connection and update integration status
   */
  async testConnection(req: Request, res: Response) {
    try {
      const { integration, success, response } = await this.validateIntegrationCredentials(req, res);
      if (!success) {
        return response;
      }

      // Validate this is a Google integration
      if (!this.validateIntegrationType(integration!, ['google_drive', 'google-drive'])) {
        return res.status(400).json({ message: 'This endpoint is only for Google Drive integrations.' });
      }

      let testSuccess = false;
      let details = {};
      let message = '';

      try {
        this.logAction('Testing Google connection for integration:', {
          id: integration!.id,
          type: integration!.type,
          hasSourceConfig: !!integration!.sourceConfig,
          requestSourceConfig: req.body.sourceConfig
        });

        const auth = await googleService.getAuthorizedClient(integration!.credentials);

        // Get folder ID(s) from request body if provided
        const { sourceConfig } = req.body;
        let folderIds: string[] = [];

        // Support both new multi-folder and legacy single folder configurations
        if (sourceConfig?.driveIds && Array.isArray(sourceConfig.driveIds)) {
          folderIds = sourceConfig.driveIds;
        } else if (sourceConfig?.driveId) {
          folderIds = [sourceConfig.driveId];
        }

        // Initialize Google Drive API
        const drive = google.drive({ version: 'v3', auth });

        // Get folder IDs from either request body or integration config
        let effectiveFolderIds: string[] = [];
        if (folderIds.length > 0) {
          effectiveFolderIds = folderIds;
        } else if (integration!.sourceConfig) {
          // Safely access sourceConfig which could be a string or object
          if (typeof integration!.sourceConfig === 'object' && integration!.sourceConfig !== null) {
            const config = integration!.sourceConfig as Record<string, any>;
            if (config.driveIds && Array.isArray(config.driveIds)) {
              effectiveFolderIds = config.driveIds;
            } else if (config.driveId) {
              effectiveFolderIds = [config.driveId];
            }
          } else if (typeof integration!.sourceConfig === 'string') {
            try {
              const parsedConfig = JSON.parse(integration!.sourceConfig);
              if (parsedConfig.driveIds && Array.isArray(parsedConfig.driveIds)) {
                effectiveFolderIds = parsedConfig.driveIds;
              } else if (parsedConfig.driveId) {
                effectiveFolderIds = [parsedConfig.driveId];
              }
            } catch (e) {
              this.logAction('Failed to parse sourceConfig string:', e);
            }
          }
        }

        if (effectiveFolderIds.length > 0) {
          // Test specific folder(s) access & check for transcript files
          this.logAction(`Testing connection to Google Drive folders: ${effectiveFolderIds.join(', ')}`);

          try {
            let totalFilesFound = 0;
            const folderResults: any[] = [];

            // Test each folder
            for (const folderId of effectiveFolderIds) {
              try {
                // First, verify folder exists and is accessible
                const folder = await drive.files.get({
                  fileId: folderId,
                  fields: 'id,name,mimeType',
                  supportsAllDrives: true
                });

                // Search for transcript files in the folder
                const response = await drive.files.list({
                  q: `'${folderId}' in parents and (mimeType='application/vnd.google-apps.document' or mimeType='text/plain')`,
                  pageSize: 20,
                  fields: 'files(id,name,mimeType)',
                  supportsAllDrives: true
                });

                const files = response.data.files || [];
                totalFilesFound += files.length;

                folderResults.push({
                  id: folderId,
                  name: folder.data.name,
                  filesFound: files.length,
                  accessible: true
                });
              } catch (folderError: any) {
                folderResults.push({
                  id: folderId,
                  name: 'Unknown',
                  filesFound: 0,
                  accessible: false,
                  error: folderError.message
                });
              }
            }

            // Update the integration with the verified source config if from request
            if (folderIds.length > 0) {
              const updateConfig = sourceConfig.driveIds ? {
                driveIds: effectiveFolderIds,
                driveNames: folderResults.filter(f => f.accessible).map(f => f.name)
              } : {
                driveId: effectiveFolderIds[0],
                driveName: folderResults.find(f => f.accessible)?.name || 'Unknown'
              };

              this.logAction('Updating integration with verified folder config:', updateConfig);

              // Update the integration with the source config and set status to connected
              const storage = await this.getStorage();
              await storage.updateIntegration(integration!.id, {
                sourceConfig: updateConfig,
                status: 'connected' // Set the status to connected
              });
            }

            const accessibleFolders = folderResults.filter(f => f.accessible);
            const inaccessibleFolders = folderResults.filter(f => !f.accessible);

            if (accessibleFolders.length === effectiveFolderIds.length) {
              testSuccess = true;
              message = `Successfully connected to ${accessibleFolders.length} folder(s)`;
            } else if (accessibleFolders.length > 0) {
              testSuccess = true;
              message = `Connected to ${accessibleFolders.length}/${effectiveFolderIds.length} folders. ${inaccessibleFolders.length} folder(s) had access issues.`;
            } else {
              testSuccess = false;
              message = `Could not access any of the ${effectiveFolderIds.length} folder(s)`;
            }

            details = {
              totalFolders: effectiveFolderIds.length,
              accessibleFolders: accessibleFolders.length,
              totalFilesFound,
              folderResults
            };
          } catch (generalError: any) {
            testSuccess = false;
            message = `Could not test folders: ${generalError.message}`;
            details = { error: generalError.message };
          }
        } else {
          // Just check general Google Drive access if no folders specified
          await drive.files.list({ pageSize: 5 });
          testSuccess = true;
          message = 'Connection to Google Drive API successful';
          details = { note: 'No specific folders tested. Please select folders to test completely.' };
        }
      } catch (error: any) {
        testSuccess = false;
        message = `Connection to Google failed: ${error.message}`;
        details = { error: error.message };
      }

      // If the test was successful, update the integration status to "connected"
      if (testSuccess) {
        await this.updateIntegrationStatus(integration!.id, 'connected');
      }

      return this.successResponse(res, { success: testSuccess, message, details });
    } catch (error: any) {
      return this.handleError(res, error, `Failed to test connection for integration ${req.params.id}`);
    }
  }
}

export const googleIntegrationController = new GoogleIntegrationController(); 