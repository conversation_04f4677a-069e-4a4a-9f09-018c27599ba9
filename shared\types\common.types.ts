// Common API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Common pagination types
export interface PaginationOptions {
  page?: number;
  limit?: number;
  offset?: number;
}

// Common filter types
export interface FilterOptions {
  search?: string;
  status?: string;
  type?: string;
  platform?: string;
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

// Common sort types
export interface SortOptions {
  field: string;
  direction: "asc" | "desc";
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
}

// Status types
export type ProcessingStatus = "pending" | "processing" | "completed" | "failed" | "cancelled";
export type ConnectionStatus = "connected" | "disconnected" | "connecting" | "error";

// Configuration types
export interface AppConfig {
  openai: {
    apiKey: string;
    model: string;
    embeddingModel: string;
    maxTokens: number;
    temperature: number;
  };
  database: {
    url: string;
    maxConnections: number;
  };
  server: {
    port: number;
    host: string;
    environment: "development" | "production" | "test";
  };
}

// Utility types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Event types
export interface AppEvent {
  type: string;
  payload: Record<string, any>;
  timestamp: Date;
  source: string;
}

// Metrics types
export interface PerformanceMetrics {
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  activeConnections: number;
}

// Health check types
export interface HealthStatus {
  status: "healthy" | "unhealthy" | "degraded";
  services: {
    database: ConnectionStatus;
    openai: ConnectionStatus;
    storage: ConnectionStatus;
  };
  uptime: number;
  version: string;
}
