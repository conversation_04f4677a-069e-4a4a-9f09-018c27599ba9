import type { 
  User, InsertUser,
  Integration, InsertIntegration,
  SyncLog, InsertSyncLog,
  SyncItem, InsertSyncItem,
  File, InsertFile,
  FileChunk, InsertFileChunk,
  ChatSession, InsertChatSession,
  ChatMessage, InsertChatMessage,
  Project, InsertProject
} from "../../../shared/index.js";

/**
 * Base storage interface defining all storage operations
 * This interface ensures consistency across different storage implementations
 */
export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Integration methods
  getIntegrations(): Promise<Integration[]>;
  getIntegration(id: number): Promise<Integration | undefined>;
  getIntegrationsByType(type: string): Promise<Integration[]>;
  createIntegration(integration: InsertIntegration): Promise<Integration>;
  updateIntegration(id: number, data: Partial<Integration>): Promise<Integration | undefined>;
  deleteIntegration(id: number): Promise<boolean>;
  updateIntegrationStatus(id: number, status: string): Promise<Integration | undefined>;

  // Sync Log methods
  getSyncLogs(integrationId?: number, limit?: number): Promise<SyncLog[]>;
  getSyncLog(id: number): Promise<SyncLog | undefined>;
  createSyncLog(log: InsertSyncLog): Promise<SyncLog>;
  updateSyncLog(id: number, data: Partial<SyncLog>): Promise<SyncLog | undefined>;

  // Sync Item methods
  getSyncItems(syncLogId?: number, status?: string): Promise<SyncItem[]>;
  getSyncItem(id: number): Promise<SyncItem | undefined>;
  getSyncItemByExternalId(externalId: string, integrationId: number): Promise<SyncItem | undefined>;
  createSyncItem(item: InsertSyncItem): Promise<SyncItem>;
  updateSyncItem(id: number, data: Partial<SyncItem>): Promise<SyncItem | undefined>;

  // File methods
  getFiles(platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string): Promise<File[]>;
  getFile(id: number): Promise<File | undefined>;
  getFileByExternalId(externalId: string, platform: string): Promise<File | undefined>;
  createFile(file: InsertFile): Promise<File>;
  updateFile(id: number, data: Partial<File>): Promise<File | undefined>;
  deleteFile(id: number): Promise<boolean>;
  markFileAsDeleted(id: number): Promise<File | undefined>;
  searchFiles(query: string, platform?: string, fileType?: string, folderId?: string): Promise<File[]>;

  // Incremental sync methods
  getFilesForIntegration(integrationId: number, platform: string): Promise<File[]>;
  markFilesAsDeleted(externalIds: string[], platform: string): Promise<void>;
  updateFileStatus(id: number, status: string): Promise<File | undefined>;

  // RAG and Chat methods
  // File Chunks
  getFileChunks(fileId: number): Promise<FileChunk[]>;
  createFileChunk(chunk: InsertFileChunk): Promise<FileChunk>;
  deleteFileChunks(fileId: number): Promise<void>;
  deleteChunk(chunkId: number): Promise<void>;
  searchSimilarChunks(queryEmbedding: number[], enabledSources: string[], limit: number): Promise<any[]>;

  // Chat Sessions
  getChatSessions(userId?: string, limit?: number): Promise<ChatSession[]>;
  getChatSession(id: string): Promise<ChatSession | undefined>;
  createChatSession(session: InsertChatSession): Promise<ChatSession>;
  updateChatSession(id: string, data: Partial<ChatSession>): Promise<ChatSession | undefined>;
  deleteChatSession(id: string): Promise<boolean>;

  // Chat Messages
  getChatMessages(sessionId: string, limit?: number): Promise<ChatMessage[]>;
  createChatMessage(message: InsertChatMessage): Promise<ChatMessage>;

  // Projects
  getProjects(userId?: string): Promise<Project[]>;
  getProject(id: number): Promise<Project | undefined>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: number, data: Partial<Project>): Promise<Project | undefined>;
  deleteProject(id: number): Promise<boolean>;

  // Development utility methods
  clearAllData?(): Promise<void>;
}

/**
 * Base storage class with common functionality
 */
export abstract class BaseStorage implements IStorage {
  protected db: any;
  protected useDatabase: boolean = false;
  // Abstract methods that must be implemented by subclasses
  abstract getUser(id: number): Promise<User | undefined>;
  abstract getUserByUsername(username: string): Promise<User | undefined>;
  abstract createUser(user: InsertUser): Promise<User>;
  
  abstract getIntegrations(): Promise<Integration[]>;
  abstract getIntegration(id: number): Promise<Integration | undefined>;
  abstract getIntegrationsByType(type: string): Promise<Integration[]>;
  abstract createIntegration(integration: InsertIntegration): Promise<Integration>;
  abstract updateIntegration(id: number, data: Partial<Integration>): Promise<Integration | undefined>;
  abstract deleteIntegration(id: number): Promise<boolean>;
  abstract updateIntegrationStatus(id: number, status: string): Promise<Integration | undefined>;
  
  abstract getSyncLogs(integrationId?: number, limit?: number): Promise<SyncLog[]>;
  abstract getSyncLog(id: number): Promise<SyncLog | undefined>;
  abstract createSyncLog(log: InsertSyncLog): Promise<SyncLog>;
  abstract updateSyncLog(id: number, data: Partial<SyncLog>): Promise<SyncLog | undefined>;
  
  abstract getSyncItems(syncLogId?: number, status?: string): Promise<SyncItem[]>;
  abstract getSyncItem(id: number): Promise<SyncItem | undefined>;
  abstract getSyncItemByExternalId(externalId: string, integrationId: number): Promise<SyncItem | undefined>;
  abstract createSyncItem(item: InsertSyncItem): Promise<SyncItem>;
  abstract updateSyncItem(id: number, data: Partial<SyncItem>): Promise<SyncItem | undefined>;
  
  abstract getFiles(platform?: string, userId?: string, limit?: number, offset?: number, folderId?: string): Promise<File[]>;
  abstract getFile(id: number): Promise<File | undefined>;
  abstract getFileByExternalId(externalId: string, platform: string): Promise<File | undefined>;
  abstract createFile(file: InsertFile): Promise<File>;
  abstract updateFile(id: number, data: Partial<File>): Promise<File | undefined>;
  abstract deleteFile(id: number): Promise<boolean>;
  abstract markFileAsDeleted(id: number): Promise<File | undefined>;
  abstract searchFiles(query: string, platform?: string, fileType?: string, folderId?: string): Promise<File[]>;
  
  abstract getFilesForIntegration(integrationId: number, platform: string): Promise<File[]>;
  abstract markFilesAsDeleted(externalIds: string[], platform: string): Promise<void>;
  abstract updateFileStatus(id: number, status: string): Promise<File | undefined>;
  
  abstract getFileChunks(fileId: number): Promise<FileChunk[]>;
  abstract createFileChunk(chunk: InsertFileChunk): Promise<FileChunk>;
  abstract deleteFileChunks(fileId: number): Promise<void>;
  abstract deleteChunk(chunkId: number): Promise<void>;
  abstract searchSimilarChunks(queryEmbedding: number[], enabledSources: string[], limit: number): Promise<any[]>;
  
  abstract getChatSessions(userId?: string, limit?: number): Promise<ChatSession[]>;
  abstract getChatSession(id: string): Promise<ChatSession | undefined>;
  abstract createChatSession(session: InsertChatSession): Promise<ChatSession>;
  abstract updateChatSession(id: string, data: Partial<ChatSession>): Promise<ChatSession | undefined>;
  abstract deleteChatSession(id: string): Promise<boolean>;
  
  abstract getChatMessages(sessionId: string, limit?: number): Promise<ChatMessage[]>;
  abstract createChatMessage(message: InsertChatMessage): Promise<ChatMessage>;
  
  abstract getProjects(userId?: string): Promise<Project[]>;
  abstract getProject(id: number): Promise<Project | undefined>;
  abstract createProject(project: InsertProject): Promise<Project>;
  abstract updateProject(id: number, data: Partial<Project>): Promise<Project | undefined>;
  abstract deleteProject(id: number): Promise<boolean>;
  
  abstract clearAllData?(): Promise<void>;

  // Common utility methods that can be shared
  protected validateId(id: number): void {
    if (!id || id <= 0) {
      throw new Error('Invalid ID provided');
    }
  }

  protected validateString(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(`Invalid ${fieldName} provided`);
    }
  }

  protected async ensureDatabase(): Promise<void> {
    if (this.useDatabase && !this.db) {
      try {
        const { getDatabase } = await import('../../core/config/database.config');
        this.db = getDatabase();
      } catch (error) {
        console.warn('Database not available, falling back to memory storage');
        this.useDatabase = false;
      }
    }
  }
}
