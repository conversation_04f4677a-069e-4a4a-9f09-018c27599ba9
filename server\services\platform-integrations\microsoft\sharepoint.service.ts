import { Client } from '@microsoft/microsoft-graph-client';
import { BaseService } from "../../base/service.interface";
import type { MicrosoftCredentials } from './oauth.service';
import type { TeamsFileMetadata } from './teams.service';

/**
 * Microsoft SharePoint Service - handles SharePoint operations and file management
 */
export class MicrosoftSharePointService extends BaseService {
  constructor() {
    super('MicrosoftSharePoint', '1.0.0', 'Microsoft SharePoint operations and file management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Microsoft SharePoint service initialized');
  }

  protected getDependencies(): string[] {
    return ['MicrosoftOAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      apiVersion: 'v1.0',
      supportedOperations: ['sites', 'drives', 'files', 'search'],
    };
  }

  /**
   * Get available SharePoint sites
   */
  async getSharePointSites(client: Client, limit: number = 10): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateNumber(limit, 'limit', 1, 50);

    try {
      const response = await client.api('/sites?search=*').get();
      const sites = response.value?.slice(0, limit) || [];
      
      this.log('info', `Found ${sites.length} SharePoint sites`);
      return sites;
    } catch (error: any) {
      this.log('error', 'Error getting SharePoint sites', error);
      return [];
    }
  }

  /**
   * Get drives for a SharePoint site
   */
  async getSiteDrives(client: Client, siteId: string): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(siteId, 'site ID');

    try {
      const response = await client.api(`/sites/${siteId}/drives`).get();
      return response.value || [];
    } catch (error: any) {
      this.log('error', `Error getting drives for site ${siteId}`, error);
      return [];
    }
  }

  /**
   * Sync files from a SharePoint site with comprehensive file retrieval
   */
  async syncSharePointSite(
    client: Client,
    siteId: string
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(siteId, 'site ID');

    try {
      this.log('info', `Starting comprehensive sync for SharePoint site ${siteId}`);
      
      // Get the drives for the site
      const drives = await this.getSiteDrives(client, siteId);
      const allFiles: any[] = [];
      
      for (const drive of drives) {
        try {
          this.log('info', `Syncing SharePoint drive: ${drive.name} (${drive.id})`);
          
          // Get ALL files recursively from the drive root
          const driveFiles = await this.getAllFilesRecursively(
            client,
            drive.id,
            'root',
            siteId
          );
          
          // Add drive context to metadata
          driveFiles.forEach(file => {
            const currentMetadata = file.extractedMetadata as TeamsFileMetadata;
            file.extractedMetadata = {
              ...currentMetadata,
              _driveName: drive.name,
              _driveId: drive.id,
              _sourceContext: `SharePoint Drive: ${drive.name}`
            } as TeamsFileMetadata;
          });
          
          allFiles.push(...driveFiles);
          this.log('info', `Found ${driveFiles.length} files in drive ${drive.name}`);
        } catch (driveError) {
          this.log('error', `Error syncing drive ${drive.name} in site ${siteId}`, driveError);
          // Continue with other drives
        }
      }

      this.log('info', `Found ${allFiles.length} total files in SharePoint site ${siteId}`);
      return allFiles;
    } catch (error: any) {
      this.handleError(error, `syncing SharePoint site ${siteId}`);
    }
  }

  /**
   * Get SharePoint site structure for selection
   */
  async getSharePointStructure(client: Client): Promise<{
    sites: Array<{
      id: string;
      name: string;
      webUrl: string;
      drives: Array<{
        id: string;
        name: string;
        itemCount?: number;
      }>;
    }>;
  }> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');

    try {
      const sites = await this.getSharePointSites(client);
      const siteStructure = [];

      for (const site of sites) {
        try {
          // Get drives for this site
          const drives = await this.getSiteDrives(client, site.id);
          const drivesWithCount = [];

          for (const drive of drives) {
            let itemCount = 0;
            try {
              const filesResponse = await client.api(`/drives/${drive.id}/root/children`).get();
              itemCount = filesResponse.value?.length || 0;
            } catch (error) {
              // Continue if we can't get item count
            }

            drivesWithCount.push({
              id: drive.id,
              name: drive.name,
              itemCount,
            });
          }

          siteStructure.push({
            id: site.id,
            name: site.displayName || site.name,
            webUrl: site.webUrl,
            drives: drivesWithCount,
          });
        } catch (error) {
          this.log('error', `Error processing site ${site.displayName || site.name}`, error);
        }
      }

      return { sites: siteStructure };
    } catch (error: any) {
      this.handleError(error, 'getting SharePoint structure');
    }
  }

  /**
   * Search files in SharePoint sites
   */
  async searchFiles(
    client: Client,
    query: string,
    siteId?: string
  ): Promise<any[]> {
    this.ensureInitialized();
    this.validateRequired(client, 'Graph client');
    this.validateString(query, 'search query');

    try {
      let searchResults: any[] = [];

      if (siteId) {
        // Search in specific site
        const drives = await this.getSiteDrives(client, siteId);
        
        for (const drive of drives) {
          try {
            const response = await client.api(`/drives/${drive.id}/root/search(q='${encodeURIComponent(query)}')`)
              .select('id,name,file,folder,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference')
              .get();

            const items = response.value || [];
            const files = items.filter((item: any) => item.file); // Only return files, not folders

            searchResults.push(...files.map((item: any) => this.mapSharePointFileToStandardFormat(item, drive.id, siteId)));
          } catch (driveError) {
            this.log('error', `Error searching in drive ${drive.name}`, driveError);
          }
        }
      } else {
        // Search across all accessible sites
        const sites = await this.getSharePointSites(client, 5); // Limit to 5 sites for performance
        
        for (const site of sites) {
          try {
            const siteFiles = await this.searchFiles(client, query, site.id);
            searchResults.push(...siteFiles);
          } catch (siteError) {
            this.log('error', `Error searching in site ${site.displayName || site.name}`, siteError);
          }
        }
      }

      return searchResults;
    } catch (error: any) {
      this.handleError(error, `searching SharePoint files with query: ${query}`);
    }
  }

  /**
   * Get all files recursively from a SharePoint drive folder
   */
  private async getAllFilesRecursively(
    client: Client,
    driveId: string,
    folderId: string,
    siteId: string,
    folderPath: string = ''
  ): Promise<any[]> {
    const allFiles: any[] = [];
    
    try {
      this.log('info', `Scanning SharePoint folder: ${folderId} at path: ${folderPath || 'root'}`);
      
      // Get items in current folder with expanded properties
      const response = await client.api(`/drives/${driveId}/items/${folderId}/children`)
        .select('id,name,file,folder,size,createdDateTime,lastModifiedDateTime,webUrl,createdBy,lastModifiedBy,parentReference')
        .expand('thumbnails')
        .get();
      
      const items = response.value || [];
      this.log('info', `Found ${items.length} items in SharePoint folder ${folderId}`);
      
      for (const item of items) {
        if (item.file) {
          // This is a file - process it
          this.log('info', `Processing SharePoint file: ${item.name} (${item.id})`);
          
          const file = this.mapSharePointFileToStandardFormat(item, driveId, siteId, folderPath);
          allFiles.push(file);
        } else if (item.folder) {
          // This is a folder - recurse into it
          const subFolderPath = folderPath ? `${folderPath}/${item.name}` : item.name;
          this.log('info', `Recursing into SharePoint subfolder: ${item.name} (${item.id})`);
          
          try {
            const subFiles = await this.getAllFilesRecursively(
              client,
              driveId,
              item.id,
              siteId,
              subFolderPath
            );
            allFiles.push(...subFiles);
          } catch (subFolderError) {
            this.log('error', `Error scanning SharePoint subfolder ${item.name}`, subFolderError);
            // Continue with other folders
          }
        }
      }
      
      return allFiles;
    } catch (error: any) {
      this.handleError(error, `getting SharePoint files from folder ${folderId}`);
    }
  }

  /**
   * Map SharePoint file item to standard format
   */
  private mapSharePointFileToStandardFormat(
    item: any,
    driveId: string,
    siteId: string,
    folderPath: string = ''
  ): any {
    return {
      externalId: item.id,
      fileName: item.name,
      fileType: this.determineFileType(item),
      platform: 'microsoft_sharepoint',
      sourceUrl: item.webUrl,
      userId: item.createdBy?.user?.id || item.lastModifiedBy?.user?.id || 'unknown',
      lastModified: item.lastModifiedDateTime ? new Date(item.lastModifiedDateTime) : new Date(),
      extractedMetadata: {
        _sourceType: 'sharepoint_site',
        _sourceFolderId: item.parentReference?.id || 'root',
        _siteId: siteId,
        webUrl: item.webUrl,
        driveId: driveId,
        parentReference: item.parentReference,
        folderPath: folderPath,
        folderName: folderPath ? folderPath.split('/').pop() || 'Unknown Folder' : 'Root',
        ownerName: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
        ownerEmail: item.createdBy?.user?.userPrincipalName || item.lastModifiedBy?.user?.userPrincipalName || null,
        fileExtension: this.getFileExtension(item.name),
        mimeType: item.file?.mimeType || 'unknown',
        shared: item.shared !== undefined,
        lastModifiedDateTime: item.lastModifiedDateTime,
        createdDateTime: item.createdDateTime,
        size: item.size || 0,
        createdBy: item.createdBy?.user?.displayName,
        lastModifiedBy: item.lastModifiedBy?.user?.displayName,
        owner: item.createdBy?.user?.displayName || item.lastModifiedBy?.user?.displayName || 'Unknown',
        hasContent: true,
        isDownloadable: true,
      } as TeamsFileMetadata
    };
  }

  /**
   * Determine file type based on file properties
   */
  private determineFileType(item: any): string {
    const name = item.name?.toLowerCase() || '';
    const mimeType = item.file?.mimeType?.toLowerCase() || '';
    const extension = this.getFileExtension(name);

    // Documents
    if (mimeType.includes('document') || mimeType.includes('text') || 
        ['docx', 'doc', 'pdf', 'txt', 'rtf', 'odt'].includes(extension)) {
      return 'document';
    }

    // Presentations
    if (mimeType.includes('presentation') || 
        ['pptx', 'ppt', 'odp', 'key'].includes(extension)) {
      return 'presentation';
    }

    // Spreadsheets
    if (mimeType.includes('sheet') || 
        ['xlsx', 'xls', 'csv', 'ods', 'numbers'].includes(extension)) {
      return 'spreadsheet';
    }

    // Images
    if (mimeType.startsWith('image/') || 
        ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      return 'image';
    }

    // Video files
    if (mimeType.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'webm', 'mkv', 'flv'].includes(extension)) {
      return 'video';
    }

    // Audio files
    if (mimeType.startsWith('audio/') || ['mp3', 'wav', 'm4a', 'wma', 'aac', 'flac', 'ogg'].includes(extension)) {
      return 'audio';
    }

    return extension || 'unknown';
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
  }
}
