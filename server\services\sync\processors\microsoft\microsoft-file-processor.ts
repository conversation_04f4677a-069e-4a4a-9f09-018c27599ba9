import { storage } from "../../../../storage/index.js";
import { openaiService } from "../../../ai/openai-service.js";
import { BaseFileProcessor, type ProcessResult } from '../base/file-processor.interface';
import { MicrosoftEmbeddingProcessor } from './microsoft-embedding-processor';
import { unifiedContentExtractor } from '../../../ai/unified-content-extractor.service';
import { simpleEmbeddingService as embeddingService } from '../../../ai/simple-embedding-service.js';

/**
 * Microsoft Teams file processor
 * Handles processing files from Microsoft Teams platform
 */
export class MicrosoftFileProcessor extends BaseFileProcessor {
  private embeddingProcessor: MicrosoftEmbeddingProcessor;

  constructor() {
    super();
    this.embeddingProcessor = new MicrosoftEmbeddingProcessor();
  }

  getPlatform(): string {
    return 'microsoft_teams';
  }

  canProcess(platform: string, fileType?: string): boolean {
    return platform === 'microsoft_teams';
  }

  /**
   * Process a Microsoft Teams file
   */
  async processFile(
    file: any,
    existingFile: any,
    integration: any
  ): Promise<ProcessResult> {
    const startTime = Date.now();
    
    try {
      this.log('info', `Processing Microsoft Teams file: ${file.fileName}`);

      // Check if file has changed
      const sourceModified = file.lastModified ? new Date(file.lastModified) : new Date();
      const needsAIProcessing = this.needsProcessing(existingFile, sourceModified);

      if (!needsAIProcessing && existingFile?.status === 'active') {
        this.log('info', `Skipping unchanged file: ${file.fileName}`);
        return this.createResult(true, true);
      }

      // Enhanced metadata for meeting transcripts using AI (only if needed)
      if (needsAIProcessing && file.fileType === 'transcript' && integration.isLlmEnabled && openaiService.isInitialized()) {
        await this.extractAIMetadata(file, integration);
      }

      // Save or update file record
      const savedFile = await this.saveFileRecord(file, existingFile);

      // Generate embeddings for RAG functionality using unified content extractor
      let embeddingsGenerated = false;
      if (needsAIProcessing && embeddingService.isInitialized()) {
        try {
          // Create unified platform metadata
          const platformMetadata = {
            platform: 'microsoft_teams',
            fileName: file.fileName,
            fileType: file.fileType,
            mimeType: file.mimeType,
            sourceUrl: file.sourceUrl,
            lastModified: file.lastModified ? new Date(file.lastModified) : new Date(),
            owner: file.extractedMetadata?.owner || file.extractedMetadata?.ownerName || 'Unknown',
            folderPath: file.extractedMetadata?.folderPath || 'Unknown',
            sourceContext: file.extractedMetadata?._sourceContext || 'Microsoft Teams'
          };

          // Try to download file buffer for content extraction
          let fileBuffer: Buffer | undefined;
          try {
            const { microsoftService } = await import('../../../platform-integrations/microsoft');
            const { cryptoService } = await import('../../../core/crypto-service.js');
            
            // Get drive ID and file ID from metadata
            const metadata = file.extractedMetadata as any;
            const driveId = metadata?.driveId;
            const fileId = file.externalId;

            if (driveId && fileId) {
              const credentials = JSON.parse(await cryptoService.decrypt(integration.credentials));
              const client = (microsoftService as any).createGraphClient(credentials);
              
              // Download the file buffer
              const downloadResponse = await client.api(`/drives/${driveId}/items/${fileId}/content`)
                .responseType('arraybuffer' as any)
                .get();

              if (downloadResponse) {
                // Convert response to buffer (handle various response types)
                if (Buffer.isBuffer(downloadResponse)) {
                  fileBuffer = downloadResponse;
                } else if (downloadResponse instanceof ArrayBuffer) {
                  fileBuffer = Buffer.from(downloadResponse);
                } else if (downloadResponse instanceof Uint8Array) {
                  fileBuffer = Buffer.from(downloadResponse);
                } else if (typeof downloadResponse === 'string') {
                  fileBuffer = Buffer.from(downloadResponse, 'binary');
                } else if (downloadResponse && typeof downloadResponse === 'object' && downloadResponse.buffer) {
                  fileBuffer = Buffer.from(downloadResponse.buffer);
                }
              }
            }
          } catch (downloadError) {
            this.log('warn', `Could not download file content for ${file.fileName}, using metadata only`, downloadError);
          }

          // Extract content using unified extractor
          const contentResult = await unifiedContentExtractor.extractContent(
            platformMetadata,
            fileBuffer
          );

          if (contentResult.success && contentResult.content) {
            // Generate embeddings using the simple embedding service
            await embeddingService.processFileForEmbeddings(savedFile.id, contentResult.content);
            embeddingsGenerated = true;
            this.log('info', `Generated embeddings for ${file.fileName} using ${contentResult.metadata?.extractionMethod}`);
          } else {
            this.log('warn', `Content extraction failed for ${file.fileName}: ${contentResult.error}`);
          }
        } catch (embeddingError) {
          this.log('error', `Error generating embeddings for ${file.fileName}:`, embeddingError);
        }
      }

      const processingTime = Date.now() - startTime;
      this.log('info', `Successfully processed ${file.fileName} in ${processingTime}ms`);

      return this.createResult(true, false, savedFile, undefined, {
        contentExtracted: true,
        embeddingsGenerated,
        aiMetadataExtracted: needsAIProcessing && integration.isLlmEnabled,
        processingTime
      });

    } catch (error: any) {
      this.log('error', `Error processing Microsoft file ${file.fileName}:`, error);
      return this.createResult(false, false, undefined, error.message);
    }
  }

  /**
   * Extract AI metadata for Microsoft Teams transcripts
   */
  private async extractAIMetadata(file: any, integration: any): Promise<void> {
    try {
      this.log('info', `Extracting enhanced AI metadata for Microsoft Teams file: ${file.fileName}`);
      
      // For Microsoft Teams, we now have enhanced metadata from Graph API
      // Pass this to the OpenAI service for better extraction
      const aiMetadata = await openaiService.extractMetadata(
        '', // We don't have the file content, but metadata is more accurate now
        file.fileName,
        file.extractedMetadata // Pass the enhanced Teams metadata
      );
      
      // Merge AI metadata with existing Teams metadata
      file.extractedMetadata = {
        ...file.extractedMetadata,
        aiExtractedTitle: aiMetadata.title,
        aiExtractedAttendees: aiMetadata.attendees,
        aiExtractedTopics: aiMetadata.topics,
        aiExtractedDate: aiMetadata.date,
        aiExtractedTime: aiMetadata.time,
        aiProcessed: true,
        aiProcessedAt: new Date().toISOString(),
        enhancedWithGraphAPI: !!(file.extractedMetadata as any)?.meetingAttendees || !!(file.extractedMetadata as any)?.calendarAttendees
      };
      
      this.log('info', `Enhanced AI metadata extracted for ${file.fileName}:`, {
        title: aiMetadata.title,
        attendeeCount: aiMetadata.attendees.length,
        topics: aiMetadata.topics,
        hasGraphAPIData: !!(file.extractedMetadata as any)?.meetingAttendees || !!(file.extractedMetadata as any)?.calendarAttendees
      });
    } catch (aiError) {
      this.log('error', `Error extracting AI metadata for ${file.fileName}:`, aiError);
      // Continue without AI metadata but mark as attempted
      file.extractedMetadata = {
        ...file.extractedMetadata,
        aiProcessed: false,
        aiError: (aiError as any)?.message || 'Unknown AI processing error',
        aiProcessedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Save or update file record in database
   */
  private async saveFileRecord(file: any, existingFile: any): Promise<any> {
    if (existingFile) {
      const savedFile = await storage.updateFile(existingFile.id, {
        fileName: file.fileName,
        lastModified: file.lastModified ? new Date(file.lastModified) : new Date(),
        extractedMetadata: file.extractedMetadata,
        mimeType: file.mimeType,
        fileSize: this.getSafeFileSize(file.fileSize),
        downloadUrl: file.downloadUrl,
        thumbnailUrl: file.thumbnailUrl,
        isShared: file.isShared,
        sharedWith: file.sharedWith,
        status: 'active',
        updatedAt: new Date(),
      });
      
      this.log('info', `Updated file reference: ${file.fileName}`);
      return savedFile;
    } else {
      const savedFile = await storage.createFile({
        externalId: file.externalId,
        fileName: file.fileName,
        fileType: file.fileType,
        mimeType: file.mimeType,
        fileSize: this.getSafeFileSize(file.fileSize),
        platform: file.platform,
        sourceUrl: file.sourceUrl,
        downloadUrl: file.downloadUrl,
        thumbnailUrl: file.thumbnailUrl,
        userId: file.userId,
        lastModified: file.lastModified ? new Date(file.lastModified) : new Date(),
        isShared: file.isShared,
        sharedWith: file.sharedWith,
        extractedMetadata: file.extractedMetadata as any,
        status: 'active',
      });
      
      this.log('info', `Created new file reference: ${file.fileName}`);
      return savedFile;
    }
  }
}
