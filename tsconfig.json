{"include": ["client/src/**/*", "shared/**/*", "server/**/*", "mcp-servers"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts", "deprecated/**/*"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "vite/client"], "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"], "@shared/schema": ["./shared/schema.ts"]}}}