openapi: 3.0.3
info:
  title: MeetSync Web App API
  description: |
    A comprehensive API for the MeetSync Web Application that provides integration management, 
    file synchronization, chat functionality, and RAG (Retrieval Augmented Generation) capabilities.
  version: "1.0.0"
  contact:
    name: MeetSync Support
  
servers:
  - url: /api
    description: Main API server

security:
  - ApiKeyAuth: []

paths:
  # System & Health Endpoints
  /info:
    $ref: './paths/system.yaml#/Info'
  /health:
    $ref: './paths/system.yaml#/Health'
  /health/live:
    $ref: './paths/system.yaml#/HealthLive'
  /health/ready:
    $ref: './paths/system.yaml#/HealthReady'
  /readiness:
    $ref: './paths/system.yaml#/Readiness'
  /liveness:
    $ref: './paths/system.yaml#/Liveness'

  # Debug & Diagnostic Endpoints
  /debug/database-state:
    $ref: './paths/debug.yaml#/DatabaseState'
  /diagnostic/health:
    $ref: './paths/debug.yaml#/DiagnosticHealth'
  /diagnostic/test-encryption:
    $ref: './paths/debug.yaml#/TestEncryption'
  /diagnostic/test-google-credentials/{id}:
    $ref: './paths/debug.yaml#/TestGoogleCredentials'
  /diagnostic/test-integration-creation:
    $ref: './paths/debug.yaml#/TestIntegrationCreation'
  /diagnostic/seed-data:
    $ref: './paths/debug.yaml#/SeedData'
  /diagnostic/clear-integrations:
    $ref: './paths/debug.yaml#/ClearIntegrations'
  /diagnostic/integration/{id}:
    $ref: './paths/debug.yaml#/DiagnosticIntegration'
  /diagnostic/test-schema:
    $ref: './paths/debug.yaml#/TestSchema'
  /diagnostic/test-credential-flow:
    $ref: './paths/debug.yaml#/TestCredentialFlow'
  /diagnostic/clear-credentials/{id}:
    $ref: './paths/debug.yaml#/ClearCredentials'
  /diagnostic/test-sync/{id}:
    $ref: './paths/debug.yaml#/TestSync'
  /diagnostic/storage:
    $ref: './paths/debug.yaml#/DiagnosticStorage'
  /diagnostic/database:
    $ref: './paths/debug.yaml#/DiagnosticDatabase'
  /diagnostic/services:
    $ref: './paths/debug.yaml#/DiagnosticServices'

  # Integration Management
  /integrations:
    $ref: './paths/integrations.yaml#/Integrations'
  /integrations/{id}:
    $ref: './paths/integrations.yaml#/IntegrationById'
  /integrations/{id}/auth-url:
    $ref: './paths/integrations.yaml#/AuthUrl'
  /integrations/{id}/oauth/callback:
    $ref: './paths/integrations.yaml#/OAuthCallback'
  /integrations/{id}/folders:
    $ref: './paths/integrations.yaml#/GoogleDriveFolders'
  /integrations/{id}/drive-structure:
    $ref: './paths/integrations.yaml#/DriveStructure'
  /integrations/{id}/debug-folders:
    $ref: './paths/integrations.yaml#/DebugFolders'
  /integrations/{id}/teams-auth-url:
    $ref: './paths/integrations.yaml#/TeamsAuthUrl'
  /integrations/{id}/teams/oauth/callback:
    $ref: './paths/integrations.yaml#/TeamsOAuthCallback'
  /integrations/{id}/teams-sources:
    $ref: './paths/integrations.yaml#/TeamsSources'
  /integrations/{id}/teams-folders:
    $ref: './paths/integrations.yaml#/TeamsFolders'
  /integrations/{id}/teams-channels/{teamId}:
    $ref: './paths/integrations.yaml#/TeamsChannels'
  /integrations/{id}/test-teams-connection:
    $ref: './paths/integrations.yaml#/TestTeamsConnection'
  /integrations/oauth/callback:
    $ref: './paths/integrations.yaml#/GlobalOAuthCallback'

  # Scheduling
  /schedules:
    $ref: './paths/sync.yaml#/Schedules'

  # Sync Operations
  /sync-logs:
    $ref: './paths/sync.yaml#/SyncLogs'
  /sync-items/{syncLogId}:
    $ref: './paths/sync.yaml#/SyncItems'
  /sync-now:
    $ref: './paths/sync.yaml#/SyncNow'
  /sync/re-vectorize:
    $ref: './paths/sync.yaml#/ReVectorize'

  # Chat System
  /chat/sessions:
    $ref: './paths/chat.yaml#/ChatSessions'
  /chat/sessions/{id}:
    $ref: './paths/chat.yaml#/ChatSessionById'
  /chat/sessions/{id}/messages:
    $ref: './paths/chat.yaml#/ChatMessages'
  /chat/sources:
    $ref: './paths/chat.yaml#/ChatSources'
  /chat/search:
    $ref: './paths/chat.yaml#/ChatSearch'
  /chat/search-files:
    $ref: './paths/chat.yaml#/ChatSearchFiles'

  # File Management
  /files:
    $ref: './paths/files.yaml#/Files'
  /files/search:
    $ref: './paths/files.yaml#/FilesSearch'
  /files/{id}:
    $ref: './paths/files.yaml#/FileById'
  /files/{fileId}/embeddings:
    $ref: './paths/files.yaml#/FileEmbeddings'
  /files/upload:
    $ref: './paths/files.yaml#/FileUpload'
  /files/uploaded:
    $ref: './paths/files.yaml#/UploadedFiles'
  /files/uploaded/{id}:
    $ref: './paths/files.yaml#/UploadedFileById'
  /files/uploaded/{id}/download:
    $ref: './paths/files.yaml#/DownloadFile'
  /files/upload-stats:
    $ref: './paths/files.yaml#/UploadStats'
  /files/reprocess-google-drive:
    $ref: './paths/files.yaml#/ReprocessGoogleDrive'

  # Legacy & Compatibility Endpoints
  /chat-sessions:
    $ref: './paths/legacy.yaml#/ChatSessions'
  /projects:
    $ref: './paths/legacy.yaml#/Projects'

  # RAG & Search
  /rag/search:
    $ref: './paths/rag.yaml#/RagSearch'
  /rag/status:
    $ref: './paths/rag.yaml#/RagStatus'

  # Upload Status
  /upload/status:
    $ref: './paths/upload.yaml#/UploadStatus'

  # Test Endpoints
  /test:
    $ref: './paths/test.yaml#/Test'
  /test/storage:
    $ref: './paths/test.yaml#/TestStorage'

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for authentication
    
  schemas:
    $ref: './components/schemas.yaml'
  
  parameters:
    $ref: './components/parameters.yaml'
  
  responses:
    $ref: './components/responses.yaml'

tags:
  - name: System
    description: System health and information endpoints
  - name: Debug
    description: Diagnostic and debugging endpoints
  - name: Integrations
    description: Integration management and OAuth operations
  - name: Sync
    description: Synchronization and scheduling operations
  - name: Chat
    description: Chat sessions and messaging
  - name: Files
    description: File management and upload operations
  - name: RAG
    description: Retrieval Augmented Generation search
  - name: Test
    description: Testing and development endpoints
  - name: Legacy
    description: Legacy compatibility endpoints 