#!/bin/bash
# =============================================================================
# MeetSync Database Backup Script
# =============================================================================

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/app/backups}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_NAME="meetsync_backup_${TIMESTAMP}"

# Database configuration
DB_HOST="${POSTGRES_HOST:-postgres}"
DB_PORT="${POSTGRES_PORT:-5432}"
DB_NAME="${POSTGRES_DB:-meetsync}"
DB_USER="${POSTGRES_USER:-meetsync}"
DB_PASSWORD="${POSTGRES_PASSWORD:-meetsync123}"

# S3 Configuration (optional)
S3_BUCKET="${BACKUP_S3_BUCKET:-}"
S3_PREFIX="${BACKUP_S3_PREFIX:-meetsync/backups}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}$(date '+%Y-%m-%d %H:%M:%S')${NC} [BACKUP] $1"
}

error() {
    echo -e "${RED}$(date '+%Y-%m-%d %H:%M:%S')${NC} [ERROR] $1" >&2
}

success() {
    echo -e "${GREEN}$(date '+%Y-%m-%d %H:%M:%S')${NC} [SUCCESS] $1"
}

warn() {
    echo -e "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S')${NC} [WARNING] $1"
}

# Create backup directory
create_backup_dir() {
    log "Creating backup directory: ${BACKUP_DIR}"
    mkdir -p "${BACKUP_DIR}"
    
    if [ ! -w "${BACKUP_DIR}" ]; then
        error "Backup directory ${BACKUP_DIR} is not writable"
        exit 1
    fi
}

# Check database connectivity
check_database() {
    log "Checking database connectivity..."
    
    export PGPASSWORD="${DB_PASSWORD}"
    
    if ! pg_isready -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" >/dev/null 2>&1; then
        error "Database is not accessible"
        exit 1
    fi
    
    success "Database is accessible"
}

# Perform database backup
backup_database() {
    log "Starting database backup..."
    
    local backup_file="${BACKUP_DIR}/${BACKUP_NAME}.sql"
    local backup_file_gz="${backup_file}.gz"
    
    export PGPASSWORD="${DB_PASSWORD}"
    
    # Create database dump
    if pg_dump \
        -h "${DB_HOST}" \
        -p "${DB_PORT}" \
        -U "${DB_USER}" \
        -d "${DB_NAME}" \
        --verbose \
        --no-password \
        --format=custom \
        --compress=9 \
        --file="${backup_file}" 2>/dev/null; then
        
        success "Database backup completed: ${backup_file}"
        
        # Compress backup
        log "Compressing backup..."
        if gzip "${backup_file}"; then
            success "Backup compressed: ${backup_file_gz}"
            echo "${backup_file_gz}"
        else
            warn "Failed to compress backup, keeping uncompressed version"
            echo "${backup_file}"
        fi
    else
        error "Database backup failed"
        exit 1
    fi
}

# Backup uploaded files
backup_files() {
    log "Starting file backup..."
    
    local files_backup="${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz"
    local uploads_dir="/app/uploads"
    
    if [ -d "${uploads_dir}" ] && [ "$(ls -A ${uploads_dir})" ]; then
        if tar -czf "${files_backup}" -C "${uploads_dir}" . 2>/dev/null; then
            success "Files backup completed: ${files_backup}"
            echo "${files_backup}"
        else
            warn "Files backup failed"
        fi
    else
        log "No files to backup in ${uploads_dir}"
    fi
}

# Upload to S3 (if configured)
upload_to_s3() {
    local backup_file="$1"
    
    if [ -z "${S3_BUCKET}" ]; then
        log "S3 backup not configured, skipping upload"
        return 0
    fi
    
    log "Uploading backup to S3..."
    
    local s3_path="s3://${S3_BUCKET}/${S3_PREFIX}/$(basename ${backup_file})"
    
    if command -v aws >/dev/null 2>&1; then
        if aws s3 cp "${backup_file}" "${s3_path}"; then
            success "Backup uploaded to S3: ${s3_path}"
        else
            warn "Failed to upload backup to S3"
        fi
    else
        warn "AWS CLI not available, skipping S3 upload"
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups (keeping ${RETENTION_DAYS} days)..."
    
    local deleted_count=0
    
    # Clean local backups
    if [ -d "${BACKUP_DIR}" ]; then
        while IFS= read -r -d '' backup_file; do
            rm -f "${backup_file}"
            deleted_count=$((deleted_count + 1))
        done < <(find "${BACKUP_DIR}" -name "meetsync_backup_*.sql*" -type f -mtime +${RETENTION_DAYS} -print0)
        
        while IFS= read -r -d '' backup_file; do
            rm -f "${backup_file}"
            deleted_count=$((deleted_count + 1))
        done < <(find "${BACKUP_DIR}" -name "meetsync_backup_*_files.tar.gz" -type f -mtime +${RETENTION_DAYS} -print0)
    fi
    
    if [ ${deleted_count} -gt 0 ]; then
        success "Deleted ${deleted_count} old backup files"
    else
        log "No old backups to clean up"
    fi
    
    # Clean S3 backups (if configured)
    if [ -n "${S3_BUCKET}" ] && command -v aws >/dev/null 2>&1; then
        log "Cleaning up old S3 backups..."
        
        local cutoff_date=$(date -d "${RETENTION_DAYS} days ago" '+%Y-%m-%d')
        
        aws s3 ls "s3://${S3_BUCKET}/${S3_PREFIX}/" | while read -r line; do
            local file_date=$(echo "$line" | awk '{print $1}')
            local file_name=$(echo "$line" | awk '{print $4}')
            
            if [[ "${file_date}" < "${cutoff_date}" ]] && [[ "${file_name}" == meetsync_backup_* ]]; then
                aws s3 rm "s3://${S3_BUCKET}/${S3_PREFIX}/${file_name}"
                log "Deleted old S3 backup: ${file_name}"
            fi
        done
    fi
}

# Verify backup integrity
verify_backup() {
    local backup_file="$1"
    
    log "Verifying backup integrity..."
    
    if [ ! -f "${backup_file}" ]; then
        error "Backup file not found: ${backup_file}"
        return 1
    fi
    
    # Check if it's a gzipped file
    if [[ "${backup_file}" == *.gz ]]; then
        if gzip -t "${backup_file}" 2>/dev/null; then
            success "Backup file integrity verified (gzip)"
        else
            error "Backup file is corrupted (gzip check failed)"
            return 1
        fi
    fi
    
    # Check file size
    local file_size=$(stat -f%z "${backup_file}" 2>/dev/null || stat -c%s "${backup_file}" 2>/dev/null)
    if [ "${file_size}" -lt 1024 ]; then
        error "Backup file is too small (${file_size} bytes), likely incomplete"
        return 1
    fi
    
    success "Backup verification completed"
    return 0
}

# Generate backup report
generate_report() {
    local db_backup="$1"
    local files_backup="$2"
    
    local report_file="${BACKUP_DIR}/${BACKUP_NAME}_report.txt"
    
    cat > "${report_file}" << EOF
MeetSync Backup Report
======================
Date: $(date)
Backup Name: ${BACKUP_NAME}
Database Host: ${DB_HOST}
Database Name: ${DB_NAME}

Database Backup:
- File: $(basename "${db_backup}")
- Size: $(ls -lh "${db_backup}" | awk '{print $5}')
- Status: $([ -f "${db_backup}" ] && echo "Success" || echo "Failed")

Files Backup:
- File: $(basename "${files_backup}")
- Size: $([ -f "${files_backup}" ] && ls -lh "${files_backup}" | awk '{print $5}' || echo "N/A")
- Status: $([ -f "${files_backup}" ] && echo "Success" || echo "Skipped")

S3 Upload: $([ -n "${S3_BUCKET}" ] && echo "Enabled" || echo "Disabled")
Retention: ${RETENTION_DAYS} days

Backup completed at: $(date)
EOF

    success "Backup report generated: ${report_file}"
}

# Send notification (if configured)
send_notification() {
    local status="$1"
    local message="$2"
    
    if [ -n "${WEBHOOK_URL}" ]; then
        local payload="{\"text\":\"MeetSync Backup ${status}: ${message}\"}"
        
        if command -v curl >/dev/null 2>&1; then
            curl -X POST -H 'Content-type: application/json' \
                --data "${payload}" \
                "${WEBHOOK_URL}" >/dev/null 2>&1 || true
        fi
    fi
    
    if [ -n "${EMAIL_TO}" ] && command -v mail >/dev/null 2>&1; then
        echo "${message}" | mail -s "MeetSync Backup ${status}" "${EMAIL_TO}" || true
    fi
}

# Main backup function
main() {
    log "Starting MeetSync backup process..."
    
    local start_time=$(date +%s)
    local db_backup=""
    local files_backup=""
    
    trap 'error "Backup process interrupted"; exit 1' INT TERM
    
    # Create backup directory
    create_backup_dir
    
    # Check database connectivity
    check_database
    
    # Perform database backup
    db_backup=$(backup_database)
    
    # Verify database backup
    if ! verify_backup "${db_backup}"; then
        error "Database backup verification failed"
        send_notification "FAILED" "Database backup verification failed"
        exit 1
    fi
    
    # Backup files
    files_backup=$(backup_files)
    
    # Upload to S3
    upload_to_s3 "${db_backup}"
    if [ -n "${files_backup}" ]; then
        upload_to_s3 "${files_backup}"
    fi
    
    # Clean old backups
    cleanup_old_backups
    
    # Generate report
    generate_report "${db_backup}" "${files_backup}"
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    success "Backup process completed in ${duration} seconds"
    send_notification "SUCCESS" "Backup completed successfully in ${duration} seconds"
}

# Help function
show_help() {
    cat << EOF
MeetSync Database Backup Script

Usage: $0 [OPTIONS]

Options:
    -h, --help          Show this help message
    -d, --dir DIR       Backup directory (default: /app/backups)
    -r, --retention N   Retention period in days (default: 30)
    --verify-only FILE  Only verify an existing backup file
    --restore FILE      Restore from backup file (use with caution)

Environment Variables:
    BACKUP_DIR          Backup directory
    RETENTION_DAYS      Backup retention period
    BACKUP_S3_BUCKET    S3 bucket for remote backup
    BACKUP_S3_PREFIX    S3 prefix for backup files
    WEBHOOK_URL         Webhook URL for notifications
    EMAIL_TO            Email address for notifications

Examples:
    $0                          # Run full backup
    $0 -d /backups -r 7        # Backup to /backups, keep 7 days
    $0 --verify-only backup.sql # Verify backup file
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -r|--retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        --verify-only)
            verify_backup "$2"
            exit $?
            ;;
        --restore)
            error "Restore functionality should be used with restore.sh script"
            exit 1
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main backup process
main 