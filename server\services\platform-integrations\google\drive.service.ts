import { google, drive_v3 } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { BaseService } from "../../base/service.interface";

/**
 * Google Drive Service - handles file operations and folder management
 */
export class GoogleDriveService extends BaseService {
  constructor() {
    super('GoogleDrive', '1.0.0', 'Google Drive file operations and folder management');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Google Drive service initialized');
  }

  protected getDependencies(): string[] {
    return ['GoogleOAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      apiVersion: 'v3',
      supportedMimeTypes: this.getSupportedMimeTypes().length,
    };
  }

  /**
   * List files in a Google Drive folder
   * @param auth Authorized OAuth2 client
   * @param folderId Google Drive folder ID
   * @param processedFolderId Optional processed folder ID to exclude
   */
  async listFiles(
    auth: OAuth2Client,
    folderId: string,
    processedFolderId?: string,
  ): Promise<drive_v3.Schema$File[]> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(folderId, 'folder ID');

    try {
      const drive = google.drive({ version: "v3", auth });

      // Build a comprehensive query for all business file types
      const mimeTypeConditions = this.getSupportedMimeTypes()
        .map(mimeType => {
          if (mimeType.includes('*')) {
            return `mimeType contains '${mimeType.replace('*', '')}'`;
          }
          return `mimeType = '${mimeType}'`;
        })
        .join(' or ');

      const query = `'${folderId}' in parents and trashed = false and (${mimeTypeConditions})`;

      this.log('info', `Google Drive query: ${query}`);

      const response = await drive.files.list({
        q: query,
        fields: "files(id, name, mimeType, modifiedTime, parents)",
        orderBy: "modifiedTime desc",
      });

      let files = response.data.files || [];

      // If we have a processedFolderId, filter out any files that are already in the processed folder
      if (processedFolderId && files.length > 0) {
        this.log('info', `Filtering out files that are in the processed folder: ${processedFolderId}`);

        // Get list of files in the processed folder
        const processedFilesResponse = await drive.files.list({
          q: `'${processedFolderId}' in parents and trashed = false`,
          fields: "files(id)",
        });

        const processedFileIds = new Set(
          (processedFilesResponse.data.files || []).map((file) => file.id),
        );
        this.log('info', `Found ${processedFileIds.size} files in processed folder`);

        // Filter out files that are in the processed folder
        files = files.filter((file) => !processedFileIds.has(file.id));
        this.log('info', `After filtering: ${files.length} files remaining to process`);
      }

      return files;
    } catch (error) {
      this.handleError(error, `listing files in folder ${folderId}`);
    }
  }

  /**
   * Get or create a folder for processed documents
   * @param auth Authorized OAuth2 client
   * @param parentFolderId Parent folder ID
   */
  async getOrCreateProcessedFolder(
    auth: OAuth2Client,
    parentFolderId: string,
  ): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(parentFolderId, 'parent folder ID');

    try {
      const drive = google.drive({ version: "v3", auth });

      // Check if "Processed" folder already exists
      const query = `'${parentFolderId}' in parents and name = 'Processed' and mimeType = 'application/vnd.google-apps.folder' and trashed = false`;
      const response = await drive.files.list({
        q: query,
        fields: "files(id, name)",
      });

      if (response.data.files && response.data.files.length > 0) {
        return response.data.files[0].id || "";
      }

      // Create the folder if it doesn't exist
      const fileMetadata = {
        name: "Processed",
        mimeType: "application/vnd.google-apps.folder",
        parents: [parentFolderId],
      };

      const folder = await drive.files.create({
        requestBody: fileMetadata,
        fields: "id",
      });

      if (!folder.data.id) {
        throw new Error("Failed to create processed folder");
      }

      this.log('info', `Created "Processed" folder: ${folder.data.id}`);

      return folder.data.id;
    } catch (error) {
      this.handleError(error, 'getting or creating processed folder');
    }
  }

  /**
   * Move a file to the processed folder
   * @param auth Authorized OAuth2 client
   * @param fileId File ID to move
   * @param sourceFolderId Source folder ID
   * @param processedFolderId Processed folder ID
   */
  async moveFileToProcessedFolder(
    auth: OAuth2Client,
    fileId: string,
    sourceFolderId: string,
    processedFolderId: string,
  ): Promise<void> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(fileId, 'file ID');
    this.validateString(sourceFolderId, 'source folder ID');
    this.validateString(processedFolderId, 'processed folder ID');

    try {
      const drive = google.drive({ version: "v3", auth });

      // Remove the file from the source folder and add to processed folder
      await drive.files.update({
        fileId,
        addParents: processedFolderId,
        removeParents: sourceFolderId,
        fields: "id, parents",
      });

      this.log('info', `Moved file ${fileId} to processed folder ${processedFolderId}`);
    } catch (error) {
      this.handleError(error, `moving file ${fileId} to processed folder`);
    }
  }

  /**
   * Get the complete Google Drive structure including root, shared drives, and computers
   * @param auth Authorized OAuth2 client
   * @returns Structured drive information
   */
  async getDriveStructure(auth: OAuth2Client): Promise<{
    myDrive: drive_v3.Schema$File[];
    sharedDrives: any[];
    computers: drive_v3.Schema$File[];
    sharedWithMe: drive_v3.Schema$File[];
  }> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');

    try {
      const drive = google.drive({ version: "v3", auth });

      // Get user info
      let userEmail = 'Unknown';
      try {
        const oauth2 = google.oauth2({ version: "v2", auth });
        const userInfo = await oauth2.userinfo.get();
        userEmail = userInfo.data.email || 'Unknown';
        this.log('info', `Getting Drive structure for: ${userEmail}`);
      } catch (userError) {
        this.log('warn', 'Could not get user info', userError);
      }

      // Get My Drive folders (root level)
      const myDriveResponse = await drive.files.list({
        q: "parents in 'root' and mimeType = 'application/vnd.google-apps.folder' and trashed = false",
        fields: "files(id, name, mimeType, modifiedTime, parents, owners)",
        orderBy: "name",
      });

      // Get Shared Drives
      const sharedDrivesResponse = await drive.drives.list({
        fields: "drives(id, name, backgroundImageFile, capabilities)",
      });

      // Get Computer backups (Google Drive desktop app synced folders)
      const computersResponse = await drive.files.list({
        q: "mimeType = 'application/vnd.google-apps.folder' and 'appDataFolder' in parents and trashed = false",
        fields: "files(id, name, mimeType, modifiedTime, parents)",
        orderBy: "name",
      });

      // Get Shared with Me files/folders
      const sharedWithMeResponse = await drive.files.list({
        q: "sharedWithMe = true and trashed = false",
        fields: "files(id, name, mimeType, modifiedTime, parents, owners, sharingUser)",
        orderBy: "modifiedTime desc",
        pageSize: 50, // Limit to avoid too many results
      });

      return {
        myDrive: myDriveResponse.data.files || [],
        sharedDrives: sharedDrivesResponse.data.drives || [],
        computers: computersResponse.data.files || [],
        sharedWithMe: sharedWithMeResponse.data.files || [],
      };
    } catch (error) {
      this.handleError(error, 'getting drive structure');
    }
  }

  /**
   * List ALL files in a Google Drive folder for reference aggregation
   * @param auth Authorized OAuth2 client
   * @param folderId Folder ID to scan (can be root or specific folder)
   * @param recursive Whether to scan subfolders recursively
   * @returns Array of all files with comprehensive metadata
   */
  async listAllFiles(
    auth: OAuth2Client,
    folderId: string = 'root',
    recursive: boolean = true,
  ): Promise<drive_v3.Schema$File[]> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');

    try {
      const drive = google.drive({ version: "v3", auth });
      let allFiles: drive_v3.Schema$File[] = [];

      // Get files in current folder
      const filesResponse = await drive.files.list({
        q: `'${folderId}' in parents and trashed = false`,
        fields: "files(id, name, mimeType, modifiedTime, parents, size, owners, sharingUser, webViewLink)",
        orderBy: "modifiedTime desc",
        pageSize: 1000, // Get more files per request
      });

      const files = filesResponse.data.files || [];
      
      // Separate files and folders
      const actualFiles = files.filter(file => file.mimeType !== 'application/vnd.google-apps.folder');
      const folders = files.filter(file => file.mimeType === 'application/vnd.google-apps.folder');

      allFiles.push(...actualFiles);

      // If recursive, process subfolders
      if (recursive) {
        for (const folder of folders) {
          if (folder.id) {
            try {
              const subFiles = await this.listAllFiles(auth, folder.id, true);
              allFiles.push(...subFiles);
            } catch (subError) {
              this.log('warn', `Error processing subfolder ${folder.name}`, subError);
            }
          }
        }
      }

      return allFiles;
    } catch (error) {
      this.handleError(error, `listing all files in folder ${folderId}`);
    }
  }

  /**
   * Get supported MIME types for file processing
   */
  private getSupportedMimeTypes(): string[] {
    return [
      'application/vnd.google-apps.document',
      'application/vnd.google-apps.spreadsheet',
      'application/vnd.google-apps.presentation',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/pdf',
      'text/plain',
      'text/csv',
      'image/*',
      'video/*',
      'audio/*',
    ];
  }

  /**
   * List Google Drive folders using the correct API parameters
   * @param auth Authorized OAuth2 client
   * @param parentFolderId Optional parent folder ID to filter results
   * @returns List of folder objects with id, name, and parents
   */
  async listDriveFolders(
    auth: OAuth2Client,
    parentFolderId: string | null = null,
  ): Promise<drive_v3.Schema$File[]> {
    try {
      const drive = google.drive({ version: "v3", auth });

      let query = "mimeType='application/vnd.google-apps.folder' and trashed=false";

      if (parentFolderId) {
        query += ` and '${parentFolderId}' in parents`;
      }

      const response = await drive.files.list({
        q: query,
        fields: "files(id,name,parents,createdTime,modifiedTime,webViewLink)",
        orderBy: "name",
        pageSize: 1000,
      });

      return response.data.files || [];
    } catch (error: any) {
      this.log('error', `Failed to list Drive folders: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if a file type is supported
   */
  isFileTypeSupported(mimeType: string): boolean {
    const supportedTypes = this.getSupportedMimeTypes();
    return supportedTypes.some(type => {
      if (type.includes('*')) {
        return mimeType.startsWith(type.replace('*', ''));
      }
      return mimeType === type;
    });
  }
}
