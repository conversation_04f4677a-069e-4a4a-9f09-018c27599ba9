import { z } from "zod";

// Project types
export interface Project {
  id: number;
  name: string;
  description: string | null;
  userId: string | null;
  enabledSources: Record<string, any> | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface InsertProject {
  name: string;
  description?: string | null;
  userId?: string | null;
  enabledSources?: Record<string, any> | null;
}

// Project configuration types
export interface ProjectConfig {
  defaultSources: string[];
  aiSettings: {
    model: string;
    temperature: number;
    maxTokens: number;
  };
  searchSettings: {
    topK: number;
    similarityThreshold: number;
  };
}

// Project validation schema
export const createProjectSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  enabledSources: z.array(z.string()).optional(),
  userId: z.string().optional(),
});

export const updateProjectSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  enabledSources: z.array(z.string()).optional(),
});

export type CreateProjectRequest = z.infer<typeof createProjectSchema>;
export type UpdateProjectRequest = z.infer<typeof updateProjectSchema>;
