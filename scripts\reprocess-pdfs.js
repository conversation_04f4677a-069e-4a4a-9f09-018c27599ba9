// Script to reprocess existing PDF files for text extraction
import { storage } from './server/storage/index.js';
import { embeddingService } from './server/services/embedding-service.js';
import fs from 'fs';
import path from 'path';

async function extractTextFromPDF(filePath) {
  try {
    console.log(`Extracting text from PDF: ${filePath}`);
    
    // Dynamic import to avoid module loading issues
    const pdfjsLib = await import('pdfjs-dist');
    
    const data = new Uint8Array(fs.readFileSync(filePath));
    const pdf = await pdfjsLib.getDocument({ data }).promise;
    
    let fullText = '';
    
    // Extract text from each page
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      
      const pageText = textContent.items
        .map((item) => item.str)
        .join(' ');
      
      fullText += `Page ${pageNum}:\n${pageText}\n\n`;
    }
    
    return fullText.trim();
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return null;
  }
}

async function downloadAndProcessPDF(file) {
  try {
    console.log(`Processing PDF: ${file.fileName}`);
    
    // For Google Drive files, we need to download them first
    // This is a simplified approach - in production you'd use the Google Drive API
    
    // For now, let's just update the file with a placeholder that indicates it needs processing
    const placeholderContent = `PDF Document: ${file.fileName}
    
This is a PDF file that contains text content. The file has been identified but text extraction is pending.
File details:
- Name: ${file.fileName}
- Platform: ${file.platform}
- Type: ${file.fileType}
- Size: ${file.fileSize || 'Unknown'} bytes

To fully extract and search the content of this PDF, please re-upload it using the file upload feature in the chat widget.`;

    // Process the placeholder content for embeddings
    await embeddingService.processFileForEmbeddings(file.id, placeholderContent);
    
    console.log(`✅ Processed placeholder content for: ${file.fileName}`);
    return true;
  } catch (error) {
    console.error(`❌ Error processing ${file.fileName}:`, error);
    return false;
  }
}

async function reprocessPDFs() {
  console.log('🔄 Starting PDF reprocessing...\n');

  try {
    // Get all files
    const filesResult = await storage.getFiles();
    const files = filesResult.files || [];
    
    console.log(`Found ${files.length} total files`);
    
    // Filter for PDF files
    const pdfFiles = files.filter(file => 
      file.fileType === 'pdf' || 
      file.fileName?.toLowerCase().endsWith('.pdf') ||
      file.mimeType?.includes('pdf')
    );
    
    console.log(`Found ${pdfFiles.length} PDF files to process`);
    
    if (pdfFiles.length === 0) {
      console.log('No PDF files found to process');
      return;
    }
    
    // Process each PDF file
    let processed = 0;
    for (const file of pdfFiles) {
      console.log(`\n📄 Processing: ${file.fileName}`);
      
      // Check if file already has chunks
      const existingChunks = await storage.getFileChunks(file.id);
      
      if (existingChunks.length > 0) {
        console.log(`  ✅ File already has ${existingChunks.length} chunks, skipping`);
        continue;
      }
      
      const success = await downloadAndProcessPDF(file);
      if (success) {
        processed++;
      }
      
      // Add a small delay to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n🎉 Reprocessing complete!`);
    console.log(`📊 Results:`);
    console.log(`  - Total PDF files: ${pdfFiles.length}`);
    console.log(`  - Successfully processed: ${processed}`);
    console.log(`  - Skipped (already processed): ${pdfFiles.length - processed}`);
    
  } catch (error) {
    console.error('❌ Error during PDF reprocessing:', error);
  }
}

// Run the reprocessing
reprocessPDFs().then(() => {
  console.log('\n✅ PDF reprocessing script completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});
