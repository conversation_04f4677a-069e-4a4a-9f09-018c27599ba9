[media pointer="file-service://file-Ag982PsRejY1z6TJTaQEoc"]
The dashboard UI now shows my integration as "Connected" with the "Sync Now" button available. it gives me a sync failed erroor popu up when i click sync now. 

chunk-RPCDYKBN.js?v=a45db685:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
setup.tsx:104 Fetching folders for integration 1...
setup.tsx:110 Fetched folders data: ObjectcanCreateFolder: trueemail: "<EMAIL>"folders: Array(9)0: {parents: Array(1), id: '1KjEsp9ercpnG4USyrppDj6tzjdwwYIAd', name: '__pycache__'}1: {parents: Array(1), id: '1mkHZeRGOC0EWVRzzsf3HebLbB0pIpEg4', name: 'All Meet Sources'}2: {parents: <PERSON><PERSON><PERSON>(1), id: '1w_RHiAH3e0Q92khy099DuXQ6uTFICWNs', name: 'backups'}3: {id: '1Wig7EP6-iqZiZDbfwTRwzpSTNEig81Xd', name: 'Meet Recordings'}4: {id: '1PeHfFpfJp8Me_mCzYq4BrpfGGzwn0aqI', name: 'Meet Recordings'}5: {parents: Array(1), id: '1Vg0-3X_0V4MLH6Ipmx8T_GU8SN6LR56h', name: 'Meet Recordings'}id: "1Vg0-3X_0V4MLH6Ipmx8T_GU8SN6LR56h"name: "Meet Recordings"parents: ['0AHILwkzTG3mUUk9PVA'][[Prototype]]: Object6: id: "1U_QJywx7qhlTOLXg4kEnIN2c0OGN90hB"name: "meet_transcript"[[Prototype]]: Object7: {parents: Array(1), id: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2', name: 'MeetSync'}8: {parents: Array(1), id: '1Sb-jrm3N3ETWSvLb10uwhsADr64QzIx8', name: 'Processed Transcripts', driveId: '0ADjOv-Fl7-EBUk9PVA'}length: 9[[Prototype]]: Array(0)message: "Found 9 folders in Google Drive"user: "Azha Qari"[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
setup.tsx:169 Saving integration configuration with folder: Object
api/sync-now:1 
            
            
           Failed to load resource: the server responded with a status of 500 (Internal Server Error)
queryClient.ts:15 
            
            
           POST https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/sync-now 500 (Internal Server Error)
apiRequest @ queryClient.ts:15
startSync @ api.ts:61
mutationFn @ IntegrationCard.tsx:69
fn @ @tanstack_react-query.js?v=a45db685:1189
run @ @tanstack_react-query.js?v=a45db685:494
start @ @tanstack_react-query.js?v=a45db685:536
execute @ @tanstack_react-query.js?v=a45db685:1225
await in execute
mutate @ @tanstack_react-query.js?v=a45db685:2630
(anonymous) @ @tanstack_react-query.js?v=a45db685:3295
onClick @ IntegrationCard.tsx:202
callCallback2 @ chunk-RPCDYKBN.js?v=a45db685:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=a45db685:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=a45db685:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=a45db685:3736
executeDispatch @ chunk-RPCDYKBN.js?v=a45db685:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=a45db685:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=a45db685:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=a45db685:7051
(anonymous) @ chunk-RPCDYKBN.js?v=a45db685:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=a45db685:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=a45db685:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=a45db685:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=a45db685:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=a45db685:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=a45db685:5449

> rest-express@1.0.0 dev
Scheduled integration 1 with cron: 0 9 * * * (next run: Thu May 22 2025 09:00:00 GMT+0000 (Coordinated Universal Time))
Initialized scheduler with 1 jobs
9:46:02 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
9:46:06 PM [express] GET /api/sync-logs 304 in 104ms :: {"logs":[]}
9:46:06 PM [express] GET /api/integrations 200 in 375ms :: {"integrations":[{"id":1,"name":"Google M…
9:46:07 PM [express] GET /api/integrations 200 in 782ms :: {"integrations":[{"id":1,"name":"Google M…
9:46:07 PM [express] GET /api/sync-logs 304 in 786ms :: {"logs":[]}
9:46:59 PM [express] GET /api/integrations 200 in 331ms :: {"integrations":[{"id":1,"name":"Google M…
9:47:24 PM [express] GET /api/integrations 304 in 318ms :: {"integrations":[{"id":1,"name":"Google M…
9:47:24 PM [express] GET /api/integrations 304 in 417ms :: {"integrations":[{"id":1,"name":"Google M…
9:49:20 PM [express] GET /api/integrations 304 in 2800ms :: {"integrations":[{"id":1,"name":"Google …
9:49:24 PM [express] GET /api/integrations 304 in 76ms :: {"integrations":[{"id":1,"name":"Google Me…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_1rzzt5gp99x
9:49:25 PM [express] GET /api/integrations/1/auth-url 200 in 164ms :: {"authUrl":"https://accounts.g…
9:49:30 PM [express] GET /api/integrations/oauth/callback 302 in 3ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
9:49:30 PM [express] GET /api/integrations/1/oauth/callback 302 in 240ms
9:49:31 PM [express] GET /api/integrations 200 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 9 folders
9:49:33 PM [express] GET /api/integrations/1/folders 304 in 618ms :: {"folders":[{"parents":["1U_QJy…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2',
    driveName: 'MeetSync'
  }
}
Testing connection to Google Drive folder: 1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2
Updating integration with verified folder config: { driveId: '1pq2rGoOuHNqvdNMily1eDgfUNMCwenZ2', driveName: 'MeetSync' }
9:49:36 PM [express] POST /api/integrations/1/test-connection 200 in 472ms :: {"success":true,"messa…
Cancelled schedule for integration 1
9:49:37 PM [express] PUT /api/integrations/1 200 in 223ms :: {"message":"Integration updated success…
9:49:38 PM [express] GET /api/integrations 200 in 73ms :: {"integrations":[{"id":1,"name":"Google Me…
9:49:39 PM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
9:49:56 PM [express] GET /api/integrations 304 in 2342ms :: {"integrations":[{"id":1,"name":"Google …
9:49:59 PM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_cur8yy577fg
9:50:00 PM [express] GET /api/integrations/1/auth-url 200 in 155ms :: {"authUrl":"https://accounts.g…
9:50:05 PM [express] GET /api/integrations/oauth/callback 302 in 1ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
9:50:05 PM [express] GET /api/integrations/1/oauth/callback 302 in 246ms
9:50:05 PM [express] GET /api/integrations 200 in 76ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 9 folders
9:50:07 PM [express] GET /api/integrations/1/folders 304 in 527ms :: {"folders":[{"parents":["1U_QJy…
9:50:10 PM [express] PUT /api/integrations/1 200 in 247ms :: {"message":"Integration updated success…
9:50:10 PM [express] GET /api/integrations 200 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
9:50:11 PM [express] GET /api/integrations 304 in 75ms :: {"integrations":[{"id":1,"name":"Google Me…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_22y7tripnqw
9:52:07 PM [express] GET /api/integrations/1/auth-url 200 in 2958ms :: {"authUrl":"https://accounts.…
9:52:19 PM [express] GET /api/integrations 200 in 322ms :: {"integrations":[{"id":1,"name":"Google M…
9:52:20 PM [express] GET /api/integrations 304 in 73ms :: {"integrations":[{"id":1,"name":"Google Me…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_mv1hgeu73i
9:52:22 PM [express] GET /api/integrations/1/auth-url 200 in 153ms :: {"authUrl":"https://accounts.g…
9:52:28 PM [express] GET /api/integrations/oauth/callback 302 in 1ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
9:52:29 PM [express] GET /api/integrations/1/oauth/callback 302 in 227ms
9:52:29 PM [express] GET /api/integrations 200 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 60 folders
9:52:38 PM [express] GET /api/integrations/1/folders 200 in 6810ms :: {"folders":[{"parents":["0AKDV…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: { driveId: '15WC2peFXdh7J8FFs8Vb0WhDRcXGbtMix', driveName: 'Aditi' }
}
Testing connection to Google Drive folder: 15WC2peFXdh7J8FFs8Vb0WhDRcXGbtMix
Updating integration with verified folder config: { driveId: '15WC2peFXdh7J8FFs8Vb0WhDRcXGbtMix', driveName: 'Aditi' }
9:53:11 PM [express] POST /api/integrations/1/test-connection 200 in 664ms :: {"success":true,"messa…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '1kusm3bnAY8S_BegYcd5rmRAEp5B-Lf4r',
    driveName: 'AlgoExpert'
  }
}
Testing connection to Google Drive folder: 1kusm3bnAY8S_BegYcd5rmRAEp5B-Lf4r
Updating integration with verified folder config: {
  driveId: '1kusm3bnAY8S_BegYcd5rmRAEp5B-Lf4r',
  driveName: 'AlgoExpert'
}
9:53:14 PM [express] POST /api/integrations/1/test-connection 200 in 367ms :: {"success":true,"messa…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '14rI_4ZJIHz7_FCuAGvrdcUaLwp6DskUr',
    driveName: 'APPH 1040'
  }
}
Testing connection to Google Drive folder: 14rI_4ZJIHz7_FCuAGvrdcUaLwp6DskUr
Updating integration with verified folder config: {
  driveId: '14rI_4ZJIHz7_FCuAGvrdcUaLwp6DskUr',
  driveName: 'APPH 1040'
}
9:53:16 PM [express] POST /api/integrations/1/test-connection 200 in 399ms :: {"success":true,"messa…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '1M_W9HTFlkSWz1PWi4kuqxBPX-DMCw9Cb',
    driveName: 'Centennial Apartment'
  }
}
Testing connection to Google Drive folder: 1M_W9HTFlkSWz1PWi4kuqxBPX-DMCw9Cb
Updating integration with verified folder config: {
  driveId: '1M_W9HTFlkSWz1PWi4kuqxBPX-DMCw9Cb',
  driveName: 'Centennial Apartment'
}
9:53:20 PM [express] POST /api/integrations/1/test-connection 200 in 325ms :: {"success":true,"messa…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '0B6DVoRoVOqauflVXVEtGYnllM0t5RllVWlhmRmJueWltSGF0aHpxMHNuMEpRWXhzS2syLVU',
    driveName: 'Classroom'
  }
}
Testing connection to Google Drive folder: 0B6DVoRoVOqauflVXVEtGYnllM0t5RllVWlhmRmJueWltSGF0aHpxMHNuMEpRWXhzS2syLVU
Updating integration with verified folder config: {
  driveId: '0B6DVoRoVOqauflVXVEtGYnllM0t5RllVWlhmRmJueWltSGF0aHpxMHNuMEpRWXhzS2syLVU',
  driveName: 'Classroom'
}
9:53:23 PM [express] POST /api/integrations/1/test-connection 200 in 342ms :: {"success":true,"messa…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '1lLe7yVRCkGEav8R9T3nukyhT8oF1ASEb',
    driveName: 'CS 1332'
  }
}
Testing connection to Google Drive folder: 1lLe7yVRCkGEav8R9T3nukyhT8oF1ASEb
Updating integration with verified folder config: { driveId: '1lLe7yVRCkGEav8R9T3nukyhT8oF1ASEb', driveName: 'CS 1332' }
9:53:25 PM [express] POST /api/integrations/1/test-connection 200 in 335ms :: {"success":true,"messa…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '0B_uJsdsot1tjLVFkRlhfVW1Gelk',
    driveName: 'HOPE Soccer Tournament'
  }
}
Testing connection to Google Drive folder: 0B_uJsdsot1tjLVFkRlhfVW1Gelk
Updating integration with verified folder config: {
  driveId: '0B_uJsdsot1tjLVFkRlhfVW1Gelk',
  driveName: 'HOPE Soccer Tournament'
}
9:53:29 PM [express] POST /api/integrations/1/test-connection 200 in 415ms :: {"success":true,"messa…
Testing Google connection for integration: {
  id: 1,
  type: 'google-meet',
  hasSourceConfig: true,
  requestSourceConfig: {
    driveId: '14rF4UsFugL1uCIWuldTFL-mK3X8PFwzl',
    driveName: 'ID 2202'
  }
}
Testing connection to Google Drive folder: 14rF4UsFugL1uCIWuldTFL-mK3X8PFwzl
Updating integration with verified folder config: { driveId: '14rF4UsFugL1uCIWuldTFL-mK3X8PFwzl', driveName: 'ID 2202' }
9:53:31 PM [express] POST /api/integrations/1/test-connection 200 in 312ms :: {"success":true,"messa…
9:53:40 PM [express] PUT /api/integrations/1 200 in 220ms :: {"message":"Integration updated success…
9:53:40 PM [express] GET /api/integrations 200 in 73ms :: {"integrations":[{"id":1,"name":"Google Me…
9:53:41 PM [express] GET /api/integrations 304 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
9:55:57 PM [express] GET /api/integrations 200 in 2827ms :: {"integrations":[{"id":1,"name":"Google …
9:56:12 PM [vite] hmr update /src/components/integrations/IntegrationCard.tsx, /src/index.css?v=iU0SMl4IQq_Sbqf__p03_
9:56:21 PM [express] GET /api/integrations 200 in 338ms :: {"integrations":[{"id":1,"name":"Google M…
9:56:21 PM [express] GET /api/integrations 200 in 78ms :: {"integrations":[{"id":1,"name":"Google Me…
9:56:56 PM [express] GET /api/integrations 304 in 311ms :: {"integrations":[{"id":1,"name":"Google M…
Error starting sync: TypeError: Cannot read properties of undefined (reading 'startSync')
    at syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:34)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:36:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:85:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at /home/<USER>/workspace/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)
    at invokeCallback (/home/<USER>/workspace/node_modules/raw-body/index.js:238:16)
    at done (/home/<USER>/workspace/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/home/<USER>/workspace/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:518:28)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
9:57:05 PM [express] POST /api/sync-now 500 in 10ms :: {"message":"Failed to start sync","error":"Ca…
9:57:11 PM [express] GET /api/integrations 304 in 401ms :: {"integrations":[{"id":1,"name":"Google M…
Processing Google OAuth for integration type: google-meet
Generating OAuth URL for integration ID: 1
Using global redirect URI: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Generated OAuth state token: 1_3kc3j7wv508
9:57:12 PM [express] GET /api/integrations/1/auth-url 200 in 178ms :: {"authUrl":"https://accounts.g…
9:57:17 PM [express] GET /api/integrations/oauth/callback 302 in 1ms
Using global redirect URI for token exchange: https://811bc24a-1dca-4d34-8d69-b5c4d97212d5-00-1sqt99a575c7g.janeway.replit.dev/api/integrations/oauth/callback
Exchanging authorization code for tokens...
Successfully obtained tokens from Google
9:57:17 PM [express] GET /api/integrations/1/oauth/callback 302 in 260ms
9:57:18 PM [express] GET /api/integrations 200 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
Fetching Google Drive folders for integration 1
Listing folders in parent folder: root
Getting Drive API client...
Testing Drive API connection...
Drive API connection successful. User: Azha Qari
Listing folders for Google user: <EMAIL>
Using Drive API query: mimeType='application/vnd.google-apps.folder' and trashed=false
Drive API request params: {
  "q": "mimeType='application/vnd.google-apps.folder' and trashed=false",
  "fields": "files(id, name, parents, driveId), nextPageToken",
  "pageSize": 100,
  "orderBy": "name",
  "includeItemsFromAllDrives": true,
  "supportsAllDrives": true
}
Drive API response status: 200
Found 9 folders
9:57:20 PM [express] GET /api/integrations/1/folders 200 in 592ms :: {"folders":[{"parents":["1U_QJy…
9:57:22 PM [express] PUT /api/integrations/1 200 in 226ms :: {"message":"Integration updated success…
9:57:23 PM [express] GET /api/integrations 200 in 77ms :: {"integrations":[{"id":1,"name":"Google Me…
9:57:24 PM [express] GET /api/integrations 304 in 74ms :: {"integrations":[{"id":1,"name":"Google Me…
Error starting sync: TypeError: Cannot read properties of undefined (reading 'startSync')
    at syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:34)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:36:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:85:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at /home/<USER>/workspace/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)
    at invokeCallback (/home/<USER>/workspace/node_modules/raw-body/index.js:238:16)
    at done (/home/<USER>/workspace/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/home/<USER>/workspace/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:518:28)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
9:57:28 PM [express] POST /api/sync-now 500 in 1ms :: {"message":"Failed to start sync","error":"Can…
Error starting sync: TypeError: Cannot read properties of undefined (reading 'startSync')
    at syncNow (/home/<USER>/workspace/server/controllers/sync.ts:406:34)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:36:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:85:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at /home/<USER>/workspace/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)
    at invokeCallback (/home/<USER>/workspace/node_modules/raw-body/index.js:238:16)
    at done (/home/<USER>/workspace/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/home/<USER>/workspace/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:518:28)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
9:59:17 PM [express] POST /api/sync-now 500 in 2ms :: {"message":"Failed to start sync","error":"Can…

