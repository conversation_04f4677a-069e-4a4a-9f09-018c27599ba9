require("dotenv").config(); const { google } = require("googleapis"); async function testAdvancedPDFProcessing() { try { console.log("🔍 Testing Advanced PDF Processing..."); const fileId = "1Zk6ZgZHomhCXxjvHx31a8DMIoiVzrSqx"; const fileName = "Anant A. Resume.pdf"; console.log(`📄 File ID: ${fileId}`); console.log(`📄 File Name: ${fileName}`); const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID; const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET; if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) { console.log("❌ Google OAuth credentials not found in environment"); return; } console.log("✅ Google OAuth credentials found"); const { GoogleServiceFacade } = await import("../../server/services/google/google.facade.js"); const { storage } = await import("../../server/storage/index.js"); const { AdvancedPDFProcessor } = await import("../../server/services/pdf-service-advanced.js"); console.log("📋 Loading Google integrations..."); const integrations = await storage.getIntegrations(); const googleIntegrations = integrations.filter(int => int.type === "google-drive"); if (googleIntegrations.length === 0) { console.log("❌ No Google Drive integrations found. Please connect Google Drive first."); return; } const integration = googleIntegrations[0]; console.log(`📋 Using integration: ${integration.name} (ID: ${integration.id})`); if (!integration.credentials) { console.log("❌ Integration has no credentials"); return; } const googleService = new GoogleServiceFacade(); await googleService.initialize(); const auth = await googleService.getAuthorizedClient(integration.credentials); const drive = google.drive({ version: "v3", auth }); console.log("📥 Downloading PDF..."); const response = await drive.files.get({ fileId: fileId, alt: "media", }, { responseType: "arraybuffer", }); if (!response.data) { console.log("❌ No data received from Google Drive"); return; } const buffer = Buffer.from(response.data); console.log(`✅ PDF downloaded successfully: ${buffer.length} bytes`); console.log("\n🚀 Testing Advanced PDF Processing..."); const processor = new AdvancedPDFProcessor(); const result = await processor.processPDF(buffer, fileName); if (result.success) { console.log("🎉 SUCCESS: Advanced PDF processing worked!"); console.log(`📊 Processing method: ${result.metadata?.processingMethod}`); console.log(`📊 Confidence: ${result.metadata?.confidence}`); console.log(`📊 Page count: ${result.metadata?.pageCount}`); console.log(`📊 Word count: ${result.metadata?.wordCount}`); console.log(`📊 Has tables: ${result.metadata?.hasTables}`); console.log(`📊 Chunks created: ${result.chunks?.length || 0}`); if (result.extractedText) { console.log("\n📄 First 500 characters of extracted text:"); console.log("---"); console.log(result.extractedText.substring(0, 500)); console.log("---"); if (result.structuredMarkdown) { console.log("\n📝 First 300 characters of structured markdown:"); console.log("---"); console.log(result.structuredMarkdown.substring(0, 300)); console.log("---"); } if (result.chunks && result.chunks.length > 0) { console.log("\n🧩 Sample chunks:"); result.chunks.slice(0, 3).forEach((chunk, index) => { console.log(`Chunk ${index + 1} (${chunk.type}, page ${chunk.page}, confidence: ${chunk.confidence}):`); console.log(chunk.content.substring(0, 150) + "..."); console.log(""); }); } } } else { console.log("❌ Advanced PDF processing failed:", result.error); } } catch (error) { console.error("❌ Error in advanced PDF processing test:", error); } } testAdvancedPDFProcessing();
