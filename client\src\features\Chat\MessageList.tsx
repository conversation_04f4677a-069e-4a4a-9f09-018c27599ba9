import React from 'react';
import { ChatMessage } from '../../store/chat/store';
import { ToolUsageBadge } from '../../components/chat/ToolUsageBadge';
import { ToolResultsPanel } from '../../components/chat/ToolResultsPanel';
import { MockToolUsageGenerator } from '../../lib/mock-tool-usage';
import { cn } from '../../lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface MessageListProps {
  messages: ChatMessage[];
  isGenerating: boolean;
  onDeleteMessage: (messageId: string) => void;
}

export function MessageList({ messages, isGenerating, onDeleteMessage }: MessageListProps) {
  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.map((message) => (
        <MessageItem
          key={message.id}
          message={message}
          onDelete={() => onDeleteMessage(message.id)}
        />
      ))}
      
      {/* Loading indicator when generating */}
      {isGenerating && (
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div className="flex-1 bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <div className="animate-pulse flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-sm text-gray-500">AI is thinking...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface MessageItemProps {
  message: ChatMessage;
  onDelete: () => void;
}

function MessageItem({ message, onDelete }: MessageItemProps) {
  const isUser = message.role === 'user';
  const isError = message.error !== undefined;
  const isLoading = message.loading;

  // Generate mock tool usage for AI messages (for demonstration)
  const toolUsages = React.useMemo(() => {
    return MockToolUsageGenerator.generateToolUsage(message.id, !isUser);
  }, [message.id, isUser]);

  return (
    <div className={cn("flex items-start space-x-3", isUser && "flex-row-reverse space-x-reverse")}>
      {/* Avatar */}
      <div className={cn(
        "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
        isUser 
          ? "bg-blue-600" 
          : "bg-gradient-to-br from-purple-500 to-blue-600"
      )}>
        {isUser ? (
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        ) : (
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        )}
      </div>

      {/* Message Content */}
      <div className={cn("flex-1 group", isUser && "flex flex-col items-end")}>
        <div className={cn(
          "max-w-3xl rounded-lg p-4 shadow-sm relative",
          isUser 
            ? "bg-blue-600 text-white" 
            : isError 
              ? "bg-red-50 border border-red-200"
              : "bg-white border border-gray-200"
        )}>
          {/* Loading state */}
          {isLoading && (
            <div className="flex items-center space-x-2">
              <div className="animate-pulse flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-sm text-gray-500">Generating response...</span>
            </div>
          )}

          {/* Error state */}
          {isError && (
            <div className="mb-2">
              <div className="flex items-center space-x-2 text-red-600 mb-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium">Error</span>
              </div>
                             <p className="text-sm text-red-700">{message.error?.message}</p>
            </div>
          )}

          {/* Message content */}
          {message.content && (
            <div className={cn(
              "prose prose-sm max-w-none",
              isUser ? "text-white prose-invert" : "text-gray-900",
              isError && "text-red-700"
            )}>
              <MessageContent content={message.content} />
            </div>
          )}

          {/* Files */}
          {message.files && message.files.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="text-xs text-gray-500 mb-2">
                {message.files.length} file{message.files.length > 1 ? 's' : ''} attached
              </div>
              <div className="flex flex-wrap gap-2">
                {message.files.map((file) => (
                  <div
                    key={file.id}
                    className="px-2 py-1 bg-gray-100 rounded text-xs text-gray-700 flex items-center space-x-1"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>{file.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Sources */}
          {message.sources && message.sources.length > 0 && !isUser && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="text-xs text-gray-500 mb-2">Sources used:</div>
              <div className="flex flex-wrap gap-1">
                {message.sources.map((source, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs font-medium"
                  >
                    {source.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Message actions (on hover) */}
          <div className={cn(
            "absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",
            isUser && "left-2 right-auto"
          )}>
            <div className="flex items-center space-x-1">
              {/* Copy button */}
              <button
                onClick={() => navigator.clipboard.writeText(message.content)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="Copy message"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>

              {/* Delete button */}
              <button
                onClick={onDelete}
                className="p-1 text-gray-400 hover:text-red-500 rounded"
                title="Delete message"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Timestamp and metadata */}
        <div className={cn(
          "mt-1 text-xs text-gray-500 flex items-center space-x-2",
          isUser && "flex-row-reverse space-x-reverse"
        )}>
          <span>{formatDistanceToNow(message.timestamp, { addSuffix: true })}</span>
          {message.model && !isUser && (
            <>
              <span>•</span>
              <span>{message.model}</span>
            </>
          )}
          {message.tokenCount && (
            <>
              <span>•</span>
              <span>{message.tokenCount} tokens</span>
            </>
          )}
          {/* Tool usage badge for AI messages */}
          {!isUser && toolUsages.length > 0 && (
            <>
              <span>•</span>
              <ToolUsageBadge toolUsages={toolUsages} />
            </>
          )}
        </div>

        {/* Tool Results Panel for AI messages */}
        {!isUser && toolUsages.length > 0 && (
          <ToolResultsPanel toolUsages={toolUsages} className="mt-2" />
        )}
      </div>
    </div>
  );
}

// Component to render message content with proper formatting
function MessageContent({ content }: { content: string }) {
  // Simple markdown-like formatting
  const formatContent = (text: string) => {
    // Convert markdown-style formatting to HTML
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded text-sm">$1</code>')
      .replace(/\n/g, '<br />');
  };

  return (
    <div 
      dangerouslySetInnerHTML={{ 
        __html: formatContent(content) 
      }} 
    />
  );
} 