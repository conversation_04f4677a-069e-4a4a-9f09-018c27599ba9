import { z } from "zod";

// Integration platform types
export type IntegrationType = "google-drive" | "google_drive" | "microsoft_teams" | "slack" | "notion" | "gmail" | "google_calendar" | "google-calendar";
export type IntegrationStatus = "connected" | "disconnected" | "error" | "configured" | "pending";
export type SyncStatus = "idle" | "running" | "success" | "failed" | "partial";

// Core integration interface
export interface Integration {
  id: number;
  name: string;
  type: string;
  status: string;
  credentials: string | null;
  config: Record<string, any> | null;
  lastSyncAt: Date | null;
  nextSyncAt: Date | null;
  syncSchedule: string | null;
  syncStatus: string;
  sourceConfig: Record<string, any> | null;
  destinationConfig: Record<string, any> | null;
  createdAt: Date;
  updatedAt: Date;
  isLlmEnabled: boolean | null;
  syncFilters: Record<string, any> | null;
}

export interface InsertIntegration {
  name: string;
  type: string;
  status?: string;
  credentials?: string | null;
  config?: Record<string, any> | null;
  syncSchedule?: string | null;
  syncStatus?: string;
  sourceConfig?: Record<string, any> | null;
  destinationConfig?: Record<string, any> | null;
  isLlmEnabled?: boolean | null;
  syncFilters?: Record<string, any> | null;
}

// Sync-related types
export interface SyncLog {
  id: number;
  integrationId: number;
  startTime: Date;
  endTime: Date | null;
  status: string;
  itemsProcessed: number | null;
  itemsSuccess: number | null;
  itemsFailed: number | null;
  details: Record<string, any> | null;
  error: string | null;
}

export interface InsertSyncLog {
  integrationId: number;
  startTime?: Date;
  status: string;
  itemsProcessed?: number | null;
  itemsSuccess?: number | null;
  itemsFailed?: number | null;
  details?: Record<string, any> | null;
  error?: string | null;
}

export interface SyncItem {
  id: number;
  integrationId: number;
  syncLogId: number | null;
  externalId: string;
  title: string;
  type: string;
  sourceUrl: string | null;
  destinationUrl: string | null;
  metadata: Record<string, any> | null;
  status: string;
  processedAt: Date | null;
  error: string | null;
  createdAt: Date;
}

export interface InsertSyncItem {
  integrationId: number;
  syncLogId?: number | null;
  externalId: string;
  title: string;
  type: string;
  sourceUrl?: string | null;
  destinationUrl?: string | null;
  metadata?: Record<string, any> | null;
  status: string;
  error?: string | null;
}

// Configuration schemas
export const integrationConfigSchema = z.object({
  name: z.string().min(3),
  type: z.enum(["google-drive", "google_drive", "microsoft_teams", "slack", "gmail", "google_calendar", "google-calendar"]),
  config: z.record(z.any()).optional(),
  sourceConfig: z.record(z.any()).optional(),
  destinationConfig: z.record(z.any()).optional(),
  isLlmEnabled: z.boolean().default(true),
  syncFilters: z.record(z.any()).optional(),
  syncSchedule: z.union([z.string(), z.null()]).optional(),
  status: z.string().optional(),
});

export const scheduleUpdateSchema = z.object({
  integrationId: z.number(),
  schedule: z.string(),
  enabled: z.boolean().default(true),
});

export const syncNowSchema = z.object({
  integrationId: z.number(),
});

export type IntegrationConfigRequest = z.infer<typeof integrationConfigSchema>;
export type ScheduleUpdateRequest = z.infer<typeof scheduleUpdateSchema>;
export type SyncNowRequest = z.infer<typeof syncNowSchema>;
