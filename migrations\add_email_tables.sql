-- Create email tables
CREATE TABLE IF NOT EXISTS emails (
    id SERIAL PRIMARY KEY,
    external_id TEXT NOT NULL,
    platform TEXT NOT NULL,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    sender TEXT NOT NULL,
    recipients JSONB NOT NULL DEFAULT '[]'::jsonb,
    cc JSONB DEFAULT '[]'::jsonb,
    bcc JSONB DEFAULT '[]'::jsonb,
    thread_id TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    metadata JSONB DEFAULT '{}'::jsonb,
    extracted_metadata JSONB DEFAULT '{}'::jsonb,
    user_id TEXT,
    organization_id TEXT,
    sync_item_id INTEGER,
    is_read BOOLEAN DEFAULT false,
    is_starred BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    labels JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    received_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS email_chunks (
    id SERIAL PRIMARY KEY,
    email_id INTEGER NOT NULL REFERENCES emails(id),
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    embedding TEXT,
    token_count INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_emails_external_id_platform ON emails(external_id, platform);
CREATE INDEX IF NOT EXISTS idx_emails_platform ON emails(platform);
CREATE INDEX IF NOT EXISTS idx_emails_user_id ON emails(user_id);
CREATE INDEX IF NOT EXISTS idx_emails_thread_id ON emails(thread_id);
CREATE INDEX IF NOT EXISTS idx_emails_status ON emails(status);
CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);
CREATE INDEX IF NOT EXISTS idx_email_chunks_email_id ON email_chunks(email_id);
CREATE INDEX IF NOT EXISTS idx_email_chunks_chunk_index ON email_chunks(chunk_index); 