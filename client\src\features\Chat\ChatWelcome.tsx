import { useState } from "react";
import { <PERSON><PERSON>les, Bot, Database, Upload, MessageCircle, Zap, Brain, FileText, Users, Search } from "lucide-react";
import { cn } from "../../lib/utils";

interface ChatWelcomeProps {
  onStartChat: () => void;
  onQuickAction: (prompt: string) => void;
  sources: any[];
  fileStats?: { totalFiles: number; indexedFiles: number; };
  className?: string;
}

export function ChatWelcome({ onStartChat, onQuickAction, sources, fileStats, className = "" }: ChatWelcomeProps) {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  const quickActions = [
    {
      id: "summarize",
      title: "Summarize my meetings",
      description: "Get a quick overview of recent meeting notes and transcripts",
      icon: Users,
      prompt: "Please summarize my recent meetings and highlight the key decisions and action items",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      id: "analyze",
      title: "Analyze documents",
      description: "Deep dive into your uploaded documents and files",
      icon: FileText,
      prompt: "Analyze my uploaded documents and provide insights on the main themes and topics",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      id: "search",
      title: "Search knowledge",
      description: "Find specific information across all your connected sources",
      icon: Search,
      prompt: "Help me find information about [specify your topic] across all my documents",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      id: "insights",
      title: "Generate insights",
      description: "Discover patterns and connections in your data",
      icon: Brain,
      prompt: "What insights and patterns can you identify from my recent documents and meetings?",
      gradient: "from-orange-500 to-red-500"
    }
  ];

  const connectedSources = sources?.filter((s: any) => s.connected) || [];
  const totalFiles = fileStats?.totalFiles || connectedSources.reduce((sum: number, source: any) => sum + (source.fileCount || 0), 0);

  return (
    <div className={cn("h-full bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex p-8 overflow-hidden", className)}>
      <div className="w-full flex gap-12 items-start">
        
        {/* Left Column - Hero Section */}
        <div className="flex-1 space-y-6">
          <div className="flex items-center gap-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-2xl opacity-20 animate-pulse"></div>
              <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-full w-16 h-16 flex items-center justify-center">
                <Bot className="w-8 h-8 text-white" />
              </div>
            </div>
            
            <div className="space-y-3">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                Welcome to GPT Unify
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed max-w-2xl">
                Your intelligent AI assistant powered by GPT-4.1 Nano. Ask questions, get insights, 
                and unlock the knowledge in your connected platforms.
              </p>
            </div>
          </div>

          {/* Stats */}
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <Database className="w-4 h-4 text-blue-500" />
              <span className="text-gray-600">
                {connectedSources.length} source{connectedSources.length !== 1 ? 's' : ''} connected
              </span>
            </div>
            <div className="w-px h-4 bg-gray-300"></div>
            <div className="flex items-center gap-2">
              <FileText className="w-4 h-4 text-green-500" />
              <span className="text-gray-600">
                {totalFiles} files indexed
              </span>
            </div>
            <div className="w-px h-4 bg-gray-300"></div>
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-purple-500" />
              <span className="text-gray-600">
                AI-powered search
              </span>
            </div>
          </div>

          {/* Connected Sources */}
          {connectedSources.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Connected Sources</h3>
              <div className="flex flex-wrap gap-3">
                {connectedSources.map((source: any) => (
                  <div 
                    key={source.id} 
                    className="px-4 py-2 text-sm bg-white border border-gray-200 rounded-full hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="font-medium">{source.name}</span>
                      {source.driveInfo && source.driveInfo.driveNames && (
                        <span className="text-xs text-gray-500">
                          • {source.driveInfo.driveNames.join(', ')}
                        </span>
                      )}
                      {source.fileCount && source.fileCount > 0 && (
                        <span className="text-xs text-gray-500">
                          ({source.fileCount} files)
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Start Chat Button and Footer */}
          <div className="space-y-4">
            <button 
              type="button"
              onClick={onStartChat}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 text-lg rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              aria-label="Start a new chat conversation"
            >
              <div className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5" aria-hidden="true" />
                Start Chatting
                <Sparkles className="w-5 h-5" aria-hidden="true" />
              </div>
            </button>
            
            <p className="text-sm text-gray-500">
              Powered by GPT-4.1 Nano (2025-04-14) • RAG-enabled • Real-time document analysis
            </p>
          </div>
        </div>

        {/* Right Column - Quick Actions */}
        <div className="flex-1 space-y-4">
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Get started with these quick actions</h2>
            <p className="text-gray-600 mb-4">Choose an action below or start typing your own question</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <div 
                  key={action.id}
                  className={cn(
                    "relative overflow-hidden border-2 cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 rounded-lg bg-white",
                    hoveredCard === action.id ? 'border-blue-500 shadow-lg' : 'border-gray-200 hover:border-blue-300'
                  )}
                  onMouseEnter={() => setHoveredCard(action.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                  onClick={() => onQuickAction(action.prompt)}
                >
                  <div className={cn(
                    `absolute inset-0 bg-gradient-to-br ${action.gradient} opacity-5`,
                    hoveredCard === action.id ? 'opacity-10' : '',
                    'transition-opacity duration-300'
                  )}></div>
                  
                  <div className="relative p-5">
                    <div className="flex items-start gap-3">
                      <div className={cn(`p-2.5 rounded-lg bg-gradient-to-br ${action.gradient} text-white shadow-lg`)}>
                        <Icon className="w-5 h-5" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <h3 className="font-semibold text-base text-gray-900">{action.title}</h3>
                        <p className="text-sm text-gray-600 leading-relaxed">{action.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
} 