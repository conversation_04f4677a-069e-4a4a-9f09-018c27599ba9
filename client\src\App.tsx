import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ui/theme-provider";
import NotFound from "@/pages/not-found";

// Import Pages
import Dashboard from "@/pages/dashboard";
import ChatPage from "@/pages/chat";
import Integrations from "@/pages/integrations";
import IntegrationSetupPage from "@/pages/integrations/setup";
import GoogleIntegrationSetup from "@/pages/integrations/google-setup";
import Schedules from "@/pages/schedules";
import Logs from "@/pages/logs";
import Settings from "@/pages/settings";
import DebugPage from "@/pages/debug";
import FilesPage from "@/pages/files";
import EmailsPage from "@/pages/emails";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Dashboard} />
      <Route path="/dashboard" component={Dashboard} />
      <Route path="/chat" component={ChatPage} />
      <Route path="/integrations" component={Integrations} />
      <Route path="/integrations/new/setup" component={IntegrationSetupPage} />
      <Route path="/integrations/google/setup" component={GoogleIntegrationSetup} />
      <Route path="/integrations/:id/setup" component={IntegrationSetupPage} />
      <Route path="/files" component={FilesPage} />
      <Route path="/emails" component={EmailsPage} />
      <Route path="/schedules" component={Schedules} />
      <Route path="/logs" component={Logs} />
      <Route path="/settings" component={Settings} />
      <Route path="/debug" component={DebugPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <ThemeProvider>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
}

export default App;
