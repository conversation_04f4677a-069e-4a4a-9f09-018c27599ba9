Files:
  get:
    tags:
      - Files
    summary: Get all files
    description: Retrieve a list of files with optional filtering and pagination
    operationId: getFiles
    parameters:
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/OffsetParam'
      - $ref: '../components/parameters.yaml#/PlatformFilter'
      - $ref: '../components/parameters.yaml#/FileTypeFilter'
      - $ref: '../components/parameters.yaml#/SortBy'
      - $ref: '../components/parameters.yaml#/SortOrder'
    responses:
      '200':
        $ref: '../components/responses.yaml#/FilesListResponse'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

FilesSearch:
  get:
    tags:
      - Files
    summary: Search files
    description: Search files by query with optional filtering
    operationId: searchFiles
    parameters:
      - $ref: '../components/parameters.yaml#/SearchQuery'
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/PlatformFilter'
      - $ref: '../components/parameters.yaml#/FileTypeFilter'
    responses:
      '200':
        $ref: '../components/responses.yaml#/SearchResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

FileById:
  get:
    tags:
      - Files
    summary: Get file by ID
    description: Retrieve a specific file by its ID
    operationId: getFileById
    parameters:
      - $ref: '../components/parameters.yaml#/FileId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/FileResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  
  delete:
    tags:
      - Files
    summary: Delete file
    description: Delete a file and all associated data
    operationId: deleteFile
    parameters:
      - $ref: '../components/parameters.yaml#/FileId'
    responses:
      '204':
        $ref: '../components/responses.yaml#/NoContent'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

FileEmbeddings:
  post:
    tags:
      - Files
    summary: Process file for embeddings
    description: Process a file to generate embeddings for RAG functionality
    operationId: processFileForEmbeddings
    parameters:
      - name: fileId
        in: path
        required: true
        description: The unique identifier of the file
        schema:
          type: integer
    responses:
      '200':
        description: File processing started successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                jobId:
                  type: string
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

FileUpload:
  post:
    tags:
      - Files
    summary: Upload files
    description: Upload multiple files to the system
    operationId: uploadFiles
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              files:
                type: array
                items:
                  type: string
                  format: binary
                maxItems: 10
    responses:
      '200':
        description: Files uploaded successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                uploadedFiles:
                  type: array
                  items:
                    type: object
      '400':
        $ref: '../components/responses.yaml#/BadRequest'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

UploadedFiles:
  get:
    tags:
      - Files
    summary: Get uploaded files
    description: Retrieve list of uploaded files
    operationId: getUploadedFiles
    responses:
      '200':
        description: Uploaded files retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                files:
                  type: array
                  items:
                    type: object
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

UploadedFileById:
  delete:
    tags:
      - Files
    summary: Delete uploaded file
    description: Delete a specific uploaded file
    operationId: deleteUploadedFile
    parameters:
      - $ref: '../components/parameters.yaml#/UploadedFileId'
    responses:
      '204':
        $ref: '../components/responses.yaml#/NoContent'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

DownloadFile:
  get:
    tags:
      - Files
    summary: Download file
    description: Download a specific uploaded file
    operationId: downloadFile
    parameters:
      - $ref: '../components/parameters.yaml#/UploadedFileId'
    responses:
      '200':
        $ref: '../components/responses.yaml#/FileDownloadResponse'
      '404':
        $ref: '../components/responses.yaml#/NotFound'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

UploadStats:
  get:
    tags:
      - Files
    summary: Get upload statistics
    description: Retrieve statistics about uploaded files
    operationId: getUploadStats
    responses:
      '200':
        $ref: '../components/responses.yaml#/UploadStatsResponse'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

ReprocessGoogleDrive:
  post:
    tags:
      - Files
    summary: Reprocess Google Drive files
    description: Reprocess files from Google Drive integrations
    operationId: reprocessGoogleDrive
    requestBody:
      required: false
      content:
        application/json:
          schema:
            type: object
            properties:
              integrationId:
                type: integer
                description: Specific integration ID to reprocess
              forceReprocess:
                type: boolean
                default: false
    responses:
      '200':
        description: Reprocessing started successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                jobId:
                  type: string
      '500':
        $ref: '../components/responses.yaml#/InternalServerError' 