import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { useToast } from '../../hooks/use-toast';
import { 
  MailIcon, 
  RefreshCwIcon, 
  CheckCircleIcon, 
  AlertCircleIcon,
  InboxIcon,
  StarIcon,
  UserIcon,
  CalendarIcon
} from 'lucide-react';

interface GmailProfile {
  emailAddress: string;
  messagesTotal: number;
  threadsTotal: number;
}

interface EmailStats {
  totalEmails: number;
  recentEmails: number;
  unreadEmails: number;
  starredEmails: number;
  lastSyncAt?: string;
}

interface GmailIntegrationCardProps {
  integrationId: number;
  isConnected: boolean;
  onStatusChange?: (connected: boolean) => void;
}

export function GmailIntegrationCard({ 
  integrationId, 
  isConnected, 
  onStatusChange 
}: GmailIntegrationCardProps) {
  const [profile, setProfile] = useState<GmailProfile | null>(null);
  const [stats, setStats] = useState<EmailStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const { toast } = useToast();

  // Load Gmail profile and stats when connected
  useEffect(() => {
    if (isConnected) {
      loadGmailData();
    }
  }, [isConnected, integrationId]);

  const loadGmailData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadGmailProfile(),
        loadEmailStats()
      ]);
    } catch (error) {
      console.error('Error loading Gmail data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadGmailProfile = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/gmail/profile`);
      if (response.ok) {
        const data = await response.json();
        setProfile(data.profile);
      }
    } catch (error) {
      console.error('Error loading Gmail profile:', error);
    }
  };

  const loadEmailStats = async () => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/gmail/stats`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error loading email stats:', error);
    }
  };

  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      const response = await fetch(`/api/integrations/${integrationId}/gmail/test`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok && data.connected) {
        toast({
          title: "Gmail Connection Successful",
          description: `Connected to ${data.profile?.emailAddress}`,
        });
        setProfile(data.profile);
        onStatusChange?.(true);
      } else {
        toast({
          title: "Gmail Connection Failed",
          description: data.message || "Failed to connect to Gmail",
          variant: "destructive",
        });
        onStatusChange?.(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test Gmail connection",
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const syncRecentEmails = async () => {
    setIsSyncing(true);
    try {
      const response = await fetch(`/api/integrations/${integrationId}/gmail/sync-recent`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Email Sync Started",
          description: `Syncing recent emails (${data.daysBack} days)`,
        });
        
        // Reload stats after sync
        setTimeout(() => {
          loadEmailStats();
        }, 2000);
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to sync emails",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sync recent emails",
        variant: "destructive",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const syncAllEmails = async () => {
    setIsSyncing(true);
    try {
      const response = await fetch(`/api/integrations/${integrationId}/gmail/sync-all`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "Full Email Sync Started",
          description: "This may take several minutes for large inboxes",
        });
        
        // Reload stats after some time
        setTimeout(() => {
          loadEmailStats();
        }, 5000);
      } else {
        toast({
          title: "Sync Failed",
          description: data.message || "Failed to start full sync",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start full email sync",
        variant: "destructive",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  if (!isConnected) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <MailIcon className="h-5 w-5" />
            <CardTitle>Gmail Integration</CardTitle>
          </div>
          <CardDescription>
            Connect your Gmail account to enable email-based conversations and search
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <AlertCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">Gmail is not connected</p>
            <Button onClick={testConnection} disabled={isTestingConnection}>
              {isTestingConnection ? (
                <>
                  <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                  Testing Connection...
                </>
              ) : (
                <>
                  <MailIcon className="h-4 w-4 mr-2" />
                  Test Gmail Connection
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MailIcon className="h-5 w-5" />
            <CardTitle>Gmail Integration</CardTitle>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <CheckCircleIcon className="h-3 w-3 mr-1" />
              Connected
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={testConnection}
            disabled={isTestingConnection}
          >
            {isTestingConnection ? (
              <RefreshCwIcon className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCwIcon className="h-4 w-4" />
            )}
          </Button>
        </div>
        <CardDescription>
          Your Gmail account is connected and ready for AI conversations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Gmail Profile Info */}
        {profile && (
          <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
            <UserIcon className="h-8 w-8 text-gray-600" />
            <div>
              <p className="font-medium">{profile.emailAddress}</p>
              <p className="text-sm text-gray-600">
                {profile.messagesTotal?.toLocaleString()} total messages
              </p>
            </div>
          </div>
        )}

        {/* Email Statistics */}
        {stats && (
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <InboxIcon className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">{stats.totalEmails}</p>
                <p className="text-xs text-gray-600">Synced Emails</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <StarIcon className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm font-medium">{stats.starredEmails}</p>
                <p className="text-xs text-gray-600">Starred</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <MailIcon className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">{stats.unreadEmails}</p>
                <p className="text-xs text-gray-600">Unread</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">{stats.recentEmails}</p>
                <p className="text-xs text-gray-600">Recent (7d)</p>
              </div>
            </div>
          </div>
        )}

        {/* Last Sync Info */}
        {stats?.lastSyncAt && (
          <div className="text-xs text-gray-600 text-center">
            Last synced: {new Date(stats.lastSyncAt).toLocaleString()}
          </div>
        )}

        {/* Sync Actions */}
        <div className="flex space-x-2">
          <Button
            onClick={syncRecentEmails}
            disabled={isSyncing || isLoading}
            className="flex-1"
            variant="outline"
          >
            {isSyncing ? (
              <>
                <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Sync Recent
              </>
            )}
          </Button>
          <Button
            onClick={syncAllEmails}
            disabled={isSyncing || isLoading}
            className="flex-1"
          >
            {isSyncing ? (
              <>
                <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <MailIcon className="h-4 w-4 mr-2" />
                Full Sync
              </>
            )}
          </Button>
        </div>

        {/* Status Messages */}
        {stats && stats.totalEmails === 0 && (
          <div className="text-center py-4 text-gray-600">
            <p>No emails have been synced yet.</p>
            <p className="text-sm">Click "Sync Recent" to get started.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 