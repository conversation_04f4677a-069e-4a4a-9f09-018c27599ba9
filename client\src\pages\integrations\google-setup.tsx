import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import AppLayout from '@/components/layout/AppLayout';
import { 
  HardDriveIcon, 
  MailIcon, 
  CalendarIcon, 
  CheckCircleIcon, 
  InfoIcon,
  Loader2Icon,
  ArrowLeftIcon
} from 'lucide-react';
import { IntegrationIcon } from '@/components/ui/integration-icons';

interface GoogleService {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  scopes: string[];
}

interface GoogleAccount {
  isConnected: boolean;
  email?: string;
  displayName?: string;
  connectedServices: string[];
}

const GOOGLE_SERVICES: GoogleService[] = [
  {
    id: 'google_drive',
    name: 'Google Drive',
    description: 'Sync and search your Google Drive files, documents, and folders',
    icon: <IntegrationIcon type="google-drive" size={24} />,
    scopes: [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/documents.readonly'
    ]
  },
  {
    id: 'gmail',
    name: 'Gmail',
    description: 'Sync and search your Gmail emails and conversations',
    icon: <IntegrationIcon type="gmail" size={24} />,
    scopes: [
      'https://www.googleapis.com/auth/gmail.readonly',
      'https://www.googleapis.com/auth/gmail.metadata'
    ]
  },
  {
    id: 'google_calendar',
    name: 'Google Calendar',
    description: 'Sync and search your Google Calendar events and meetings',
    icon: <IntegrationIcon type="google-calendar" size={24} />,
    scopes: [
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.readonly',
      'https://www.googleapis.com/auth/calendar.events'
    ]
  }
];

export default function GoogleIntegrationSetup() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [googleAccount, setGoogleAccount] = useState<GoogleAccount | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Check if user already has a Google account connected
  useEffect(() => {
    checkGoogleAccountStatus();
  }, []);

  const checkGoogleAccountStatus = async () => {
    try {
      const response = await fetch('/api/integrations/google/status');
      if (response.ok) {
        const data = await response.json();
        setGoogleAccount(data.account);
        
        // If account is connected, pre-select services that aren't already connected
        if (data.account?.isConnected) {
          const availableServices = GOOGLE_SERVICES
            .filter(service => !data.account.connectedServices?.includes(service.id))
            .map(service => service.id);
          setSelectedServices(availableServices);
        }
      }
    } catch (error) {
      console.error('Error checking Google account status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const handleConnectGoogleAccount = async () => {
    setIsConnecting(true);
    try {
      // Create a temporary Google Drive integration to trigger OAuth
      const createResponse = await fetch('/api/integrations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'google_drive',
          name: 'Google Account Connection',
          status: 'disconnected',
        }),
      });

      if (createResponse.ok) {
        const createData = await createResponse.json();
        const integrationId = createData.integration.id;

        // Get OAuth URL with all required scopes
        const authResponse = await fetch(`/api/integrations/${integrationId}/auth-url?redirectUri=${encodeURIComponent(window.location.origin + '/integrations/google/setup')}`);
        if (authResponse.ok) {
          const authData = await authResponse.json();
          // Store the integration ID in sessionStorage for cleanup after OAuth
          sessionStorage.setItem('tempGoogleIntegrationId', integrationId.toString());
          // Redirect to Google OAuth
          window.location.href = authData.authUrl;
        } else {
          throw new Error('Failed to get OAuth URL');
        }
      } else {
        throw new Error('Failed to create temporary integration');
      }
    } catch (error) {
      console.error('Error connecting Google account:', error);
      toast({
        title: "Connection Failed",
        description: "Failed to connect Google account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const handleCreateIntegrations = async () => {
    if (selectedServices.length === 0) {
      toast({
        title: "No Services Selected",
        description: "Please select at least one Google service to integrate.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const createdIntegrations = [];

      for (const serviceId of selectedServices) {
        const service = GOOGLE_SERVICES.find(s => s.id === serviceId);
        if (!service) continue;

        const response = await fetch('/api/integrations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: serviceId,
            name: service.name,
            status: googleAccount?.isConnected ? 'connected' : 'disconnected',
            useExistingGoogleAuth: googleAccount?.isConnected, // Flag to use existing Google tokens
          }),
        });

        if (response.ok) {
          const data = await response.json();
          createdIntegrations.push(data.integration);
        } else {
          const errorData = await response.json();
          throw new Error(`Failed to create ${service.name}: ${errorData.message}`);
        }
      }

      toast({
        title: "Integrations Created",
        description: `Successfully created ${createdIntegrations.length} Google integration${createdIntegrations.length > 1 ? 's' : ''}.`,
        variant: "default",
      });

      // Redirect to integrations page
      setLocation('/integrations');
    } catch (error: any) {
      console.error('Error creating integrations:', error);
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create integrations. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  if (isLoading) {
    return (
      <AppLayout title="Google Integration Setup">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="h-32 bg-gray-200 rounded-lg animate-pulse" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-48 bg-gray-200 rounded-lg animate-pulse" />
            ))}
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title="Google Integration Setup">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            onClick={() => setLocation('/integrations')}
            className="p-2"
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Connect Google Services</h1>
            <p className="text-gray-600">
              Select the Google services you want to integrate with MeetSync
            </p>
          </div>
        </div>

        {/* Google Account Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-red-100 rounded-lg flex items-center justify-center">
                <div className="h-4 w-4 bg-red-500 rounded-full" />
              </div>
              <span>Google Account</span>
              {googleAccount?.isConnected && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                  Connected
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {googleAccount?.isConnected ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{googleAccount.displayName || googleAccount.email}</p>
                    {googleAccount.displayName && (
                      <p className="text-sm text-gray-500">{googleAccount.email}</p>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-green-600">
                      Account Connected
                    </p>
                    <p className="text-xs text-gray-500">
                      Ready to add services
                    </p>
                  </div>
                </div>
                
                <Alert>
                  <InfoIcon className="h-4 w-4" />
                  <AlertDescription>
                    Your Google account is connected. You can now select additional services 
                    to integrate without re-authenticating.
                  </AlertDescription>
                </Alert>
              </div>
            ) : (
              <div className="space-y-4">
                <Alert>
                  <InfoIcon className="h-4 w-4" />
                  <AlertDescription>
                    Connect your Google account once to access all Google services. 
                    This will grant permissions for Drive, Gmail, Calendar, and other Google services.
                  </AlertDescription>
                </Alert>
                
                <Button 
                  onClick={handleConnectGoogleAccount}
                  disabled={isConnecting}
                  size="lg"
                  className="w-full"
                >
                  {isConnecting ? (
                    <>
                      <Loader2Icon className="h-4 w-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    "Connect Google Account"
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Service Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Select Google Services</CardTitle>
            <CardDescription>
              Choose which Google services you want to integrate. You can always add more later.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {GOOGLE_SERVICES.map((service) => {
                const isAlreadyConnected = googleAccount?.connectedServices?.includes(service.id);
                const isSelected = selectedServices.includes(service.id);
                
                return (
                  <div
                    key={service.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      isAlreadyConnected 
                        ? 'border-green-200 bg-green-50' 
                        : isSelected 
                        ? 'border-blue-200 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => !isAlreadyConnected && handleServiceToggle(service.id)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {isAlreadyConnected ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-600" />
                        ) : (
                          <Checkbox 
                            checked={isSelected}
                            onChange={() => handleServiceToggle(service.id)}
                          />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          {service.icon}
                          <h3 className="font-medium">{service.name}</h3>
                          {isAlreadyConnected && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                              Connected
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          {service.description}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button 
            variant="outline" 
            onClick={() => setLocation('/integrations')}
          >
            Cancel
          </Button>
          
          <Button
            onClick={handleCreateIntegrations}
            disabled={!googleAccount?.isConnected || selectedServices.length === 0 || isCreating}
            size="lg"
          >
            {isCreating ? (
              <>
                <Loader2Icon className="h-4 w-4 mr-2 animate-spin" />
                Creating Integrations...
              </>
            ) : (
              `Create ${selectedServices.length} Integration${selectedServices.length !== 1 ? 's' : ''}`
            )}
          </Button>
        </div>
      </div>
    </AppLayout>
  );
} 