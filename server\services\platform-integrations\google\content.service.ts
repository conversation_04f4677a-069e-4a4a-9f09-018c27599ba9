import { google } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { BaseService } from "../../base/service.interface";

/**
 * Google Content Extraction Service - handles content extraction from various Google file types
 */
export class GoogleContentService extends BaseService {
  constructor() {
    super('GoogleContent', '1.0.0', 'Google content extraction from Docs, Sheets, and other file types');
  }

  protected async onInitialize(): Promise<void> {
    this.log('info', 'Google Content service initialized');
  }

  protected getDependencies(): string[] {
    return ['GoogleOAuth'];
  }

  protected async checkHealth(): Promise<Record<string, any>> {
    return {
      supportedDocTypes: ['docs', 'sheets', 'slides'],
      extractionMethods: ['text', 'structured', 'metadata'],
    };
  }

  /**
   * Extract metadata from a Google Meet transcript filename
   * @param filename Filename to extract metadata from
   * @returns Extracted metadata
   */
  extractMetadataFromFilename(filename: string): {
    title: string;
    date: string | null;
    time: string | null;
  } {
    this.ensureInitialized();
    this.validateString(filename, 'filename');

    try {
      // Extract date/time from filename (e.g., "Meeting started 2025/05/19 12:20 EDT - Notes by Gemini")
      const match = filename.match(
        /(\d{4})\/(\d{2})\/(\d{2}) (\d{2}):(\d{2}) (\w+)/,
      );

      if (match) {
        const [_, year, month, day, hour, minute, tz] = match;

        // Create date object for ISO format
        const dateObj = new Date(`${year}-${month}-${day}`);
        const isoDate = dateObj.toISOString().split("T")[0]; // ISO format for Notion

        // Format time (12-hour with AM/PM)
        const hour12 = parseInt(hour) % 12 || 12;
        const ampm = parseInt(hour) < 12 ? "AM" : "PM";
        const formattedTime = `${hour12}:${minute} ${ampm} ${tz}`;

        // Try to extract a title from the filename
        // Remove the "Copy of " prefix and date/time parts
        let title = filename.replace(/^Copy of /, "");
        title = title.replace(
          /Meeting started \d{4}\/\d{2}\/\d{2} \d{2}:\d{2} \w+ - /,
          "",
        );
        title = title.replace(/Notes by Gemini$/, "").trim();

        // If we couldn't extract a meaningful title, use a default with the date
        if (!title || title === "") {
          const monthNames = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December",
          ];
          const monthName = monthNames[parseInt(month) - 1];
          title = `Meeting on ${monthName} ${parseInt(day)}, ${year}`;
        }

        return {
          title,
          date: isoDate,
          time: formattedTime,
        };
      }
    } catch (error) {
      this.log('error', `Error extracting metadata from filename ${filename}`, error);
    }

    return {
      title: filename.replace(/^Copy of /, ""),
      date: null,
      time: null,
    };
  }

  /**
   * Extract content from a Google Doc with tabs (Notes and Transcript)
   * @param auth Authorized OAuth2 client
   * @param docId Google Doc ID
   */
  async extractDocContent(
    auth: OAuth2Client,
    docId: string,
  ): Promise<{
    transcriptText: string;
    notesText: string;
    summaryParagraph: string;
  }> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(docId, 'document ID');

    try {
      const docs = google.docs({ version: "v1", auth });

      // First, try to get the document content with tabs included
      // Some documents may not support includeTabsContent parameter
      let response;
      try {
        response = await docs.documents.get({
          documentId: docId,
          includeTabsContent: true,
        });
      } catch (tabError: any) {
        this.log('info', `Document ${docId} doesn't support tabs, trying without includeTabsContent`);
        // Fallback to basic document retrieval
        response = await docs.documents.get({
          documentId: docId,
        });
      }

      const document = response.data;

      if (!document.body && !document.tabs) {
        this.log('warn', `Document ${docId} has no body or tabs, creating minimal content`);
        return {
          transcriptText: `Document: ${document.title || 'Untitled'}`,
          notesText: "",
          summaryParagraph: `Google Docs file: ${document.title || 'Untitled'}`
        };
      }

      // Initialize content variables
      let transcriptText = "";
      let notesText = "";
      let summaryParagraph = "";

      // Check if document has tabs
      if (document.tabs && document.tabs.length > 0) {
        this.log('info', `Document has ${document.tabs.length} tabs`);

        // Process each tab
        for (const tab of document.tabs) {
          if (!tab.documentTab) continue;

          const tabTitle = (tab.documentTab as any).title || "";
          const tabBody = tab.documentTab.body;

          if (!tabBody) continue;

          const tabText = this.extractTextFromBody(tabBody);

          this.log('info', `Tab "${tabTitle}" has ${tabText.length} characters`);

          // Determine content type based on tab title
          if (tabTitle.toLowerCase().includes("transcript")) {
            transcriptText = tabText;
          } else if (tabTitle.toLowerCase().includes("notes")) {
            notesText = tabText;
          } else {
            // If we can't determine the type, add to notes
            notesText += (notesText ? "\n\n" : "") + `${tabTitle}:\n${tabText}`;
          }
        }
      } else {
        // No tabs - extract all content as a single document
        this.log('info', 'Document has no tabs, extracting all content');
        const fullText = document.body ? this.extractTextFromBody(document.body) : '';

        if (fullText.trim().length > 0) {
          // Try to split content based on common patterns
          const sections = this.splitDocumentContent(fullText);
          transcriptText = sections.transcript;
          notesText = sections.notes;
          summaryParagraph = sections.summary;
        } else {
          // Document exists but is empty - use document title and metadata
          this.log('warn', `Document ${docId} has empty body, using title and metadata`);
          transcriptText = `Document: ${document.title || 'Untitled Google Doc'}`;
          if (document.documentStyle) {
            transcriptText += `\nDocument created in Google Docs`;
          }
        }
      }

      // If we still don't have content, try to extract from the main body
      if (!transcriptText && !notesText) {
        if (document.body) {
          const fullText = this.extractTextFromBody(document.body);
          if (fullText.trim().length > 0) {
            // Default to putting everything in transcript
            transcriptText = fullText;
          } else {
            // Even the body is empty, use document metadata
            transcriptText = `Google Docs file: ${document.title || 'Untitled'}`;
            notesText = `This document appears to be empty or contains only formatting. Document ID: ${docId}`;
          }
        } else {
          // No body at all
          transcriptText = `Google Docs file: ${document.title || 'Untitled'}`;
          notesText = `Document structure not accessible via API. Document ID: ${docId}`;
        }
      }

      // Extract summary paragraph (first substantial paragraph)
      if (!summaryParagraph) {
        summaryParagraph = this.extractSummaryParagraph(transcriptText || notesText);
      }

      this.log('info', `Extracted content - Transcript: ${transcriptText.length} chars, Notes: ${notesText.length} chars, Summary: ${summaryParagraph.length} chars`);

      return {
        transcriptText: transcriptText.trim(),
        notesText: notesText.trim(),
        summaryParagraph: summaryParagraph.trim(),
      };
    } catch (error) {
      this.handleError(error, `extracting content from document ${docId}`);
    }
  }

  /**
   * Extract text content from a Google Docs body structure
   * @param body Document body from Google Docs API
   */
  private extractTextFromBody(body: any): string {
    if (!body || !body.content) {
      return "";
    }

    let text = "";

    for (const element of body.content) {
      if (element.paragraph) {
        // Extract text from paragraph elements
        if (element.paragraph.elements) {
          for (const paragraphElement of element.paragraph.elements) {
            if (paragraphElement.textRun && paragraphElement.textRun.content) {
              text += paragraphElement.textRun.content;
            }
          }
        }
      } else if (element.table) {
        // Extract text from table elements
        if (element.table.tableRows) {
          for (const row of element.table.tableRows) {
            if (row.tableCells) {
              for (const cell of row.tableCells) {
                if (cell.content) {
                  text += this.extractTextFromBody({ content: cell.content });
                  text += "\t"; // Add tab separator between cells
                }
              }
              text += "\n"; // Add newline after each row
            }
          }
        }
      }
    }

    return text;
  }

  /**
   * Split document content into logical sections
   * @param fullText Complete document text
   */
  private splitDocumentContent(fullText: string): {
    transcript: string;
    notes: string;
    summary: string;
  } {
    // Try to identify sections based on common patterns
    const lines = fullText.split('\n');
    let transcript = "";
    let notes = "";
    let summary = "";

    let currentSection = "unknown";
    let summaryExtracted = false;

    for (const line of lines) {
      const lowerLine = line.toLowerCase().trim();

      // Identify section headers
      if (lowerLine.includes("transcript") || lowerLine.includes("recording")) {
        currentSection = "transcript";
        continue;
      } else if (lowerLine.includes("notes") || lowerLine.includes("summary")) {
        currentSection = "notes";
        continue;
      }

      // Extract summary from the first substantial paragraph
      if (!summaryExtracted && line.trim().length > 50) {
        summary = line.trim();
        summaryExtracted = true;
      }

      // Add content to appropriate section
      if (currentSection === "transcript") {
        transcript += line + "\n";
      } else if (currentSection === "notes") {
        notes += line + "\n";
      } else {
        // Default to transcript if we can't determine
        transcript += line + "\n";
      }
    }

    return {
      transcript: transcript.trim(),
      notes: notes.trim(),
      summary: summary.trim(),
    };
  }

  /**
   * Extract a summary paragraph from text content
   * @param text Text to extract summary from
   */
  private extractSummaryParagraph(text: string): string {
    if (!text) return "";

    // Split into paragraphs and find the first substantial one
    const paragraphs = text.split('\n\n').filter(p => p.trim().length > 0);

    for (const paragraph of paragraphs) {
      const trimmed = paragraph.trim();
      // Look for a paragraph that's substantial but not too long
      if (trimmed.length >= 50 && trimmed.length <= 500) {
        return trimmed;
      }
    }

    // If no good paragraph found, take the first 200 characters
    return text.substring(0, 200).trim() + (text.length > 200 ? "..." : "");
  }

  /**
   * Extract content from Google Sheets
   * @param auth Authorized OAuth2 client
   * @param spreadsheetId Google Sheets ID
   */
  async extractSheetsContent(
    auth: OAuth2Client,
    spreadsheetId: string,
  ): Promise<{
    content: string;
    metadata: Record<string, any>;
  }> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(spreadsheetId, 'spreadsheet ID');

    try {
      const sheets = google.sheets({ version: "v4", auth });

      // Get spreadsheet metadata
      const spreadsheet = await sheets.spreadsheets.get({
        spreadsheetId,
      });

      let content = "";
      const metadata: Record<string, any> = {
        title: spreadsheet.data.properties?.title || "Untitled Spreadsheet",
        sheetCount: spreadsheet.data.sheets?.length || 0,
        sheets: [],
      };

      // Extract content from each sheet
      if (spreadsheet.data.sheets) {
        for (const sheet of spreadsheet.data.sheets) {
          const sheetTitle = sheet.properties?.title || "Untitled Sheet";
          
          try {
            // Get values from the sheet
            const values = await sheets.spreadsheets.values.get({
              spreadsheetId,
              range: sheetTitle,
            });

            const rows = values.data.values || [];
            let sheetContent = `Sheet: ${sheetTitle}\n`;

            // Convert rows to text
            for (const row of rows) {
              sheetContent += row.join('\t') + '\n';
            }

            content += sheetContent + '\n\n';
            metadata.sheets.push({
              title: sheetTitle,
              rowCount: rows.length,
              columnCount: rows[0]?.length || 0,
            });

          } catch (sheetError) {
            this.log('warn', `Error extracting sheet ${sheetTitle}`, sheetError);
          }
        }
      }

      return {
        content: content.trim(),
        metadata,
      };
    } catch (error) {
      this.handleError(error, `extracting content from spreadsheet ${spreadsheetId}`);
    }
  }

  /**
   * Extract content from Google Slides
   * @param auth Authorized OAuth2 client
   * @param presentationId Google Slides ID
   */
  async extractSlidesContent(
    auth: OAuth2Client,
    presentationId: string,
  ): Promise<{
    content: string;
    metadata: Record<string, any>;
  }> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(presentationId, 'presentation ID');

    try {
      const slides = google.slides({ version: "v1", auth });

      // Get presentation
      const presentation = await slides.presentations.get({
        presentationId,
      });

      let content = "";
      const metadata: Record<string, any> = {
        title: presentation.data.title || "Untitled Presentation",
        slideCount: presentation.data.slides?.length || 0,
        slides: [],
      };

      // Extract content from each slide
      if (presentation.data.slides) {
        for (let i = 0; i < presentation.data.slides.length; i++) {
          const slide = presentation.data.slides[i];
          let slideContent = `Slide ${i + 1}:\n`;

          // Extract text from slide elements
          if (slide.pageElements) {
            for (const element of slide.pageElements) {
              if (element.shape && element.shape.text) {
                const textElements = element.shape.text.textElements || [];
                for (const textElement of textElements) {
                  if (textElement.textRun) {
                    slideContent += textElement.textRun.content || "";
                  }
                }
              }
            }
          }

          content += slideContent + '\n\n';
          metadata.slides.push({
            slideNumber: i + 1,
            objectId: slide.objectId,
          });
        }
      }

      return {
        content: content.trim(),
        metadata,
      };
    } catch (error) {
      this.handleError(error, `extracting content from presentation ${presentationId}`);
    }
  }

  /**
   * Extract comprehensive metadata from a Google Drive file
   * @param file Google Drive file object
   * @returns Standardized file metadata for our files table
   */
  extractFileMetadata(file: any): any {
    try {
      // Determine file type based on mimeType and extension
      let fileType = 'document'; // default

      if (file.mimeType) {
        if (file.mimeType.includes('spreadsheet')) {
          fileType = 'spreadsheet';
        } else if (file.mimeType.includes('presentation')) {
          fileType = 'presentation';
        } else if (file.mimeType.includes('pdf')) {
          fileType = 'pdf';
        } else if (file.mimeType.includes('word')) {
          fileType = 'word';
        } else if (file.mimeType.includes('excel')) {
          fileType = 'excel';
        } else if (file.mimeType.includes('powerpoint')) {
          fileType = 'powerpoint';
        } else if (file.mimeType.includes('image')) {
          fileType = 'image';
        } else if (file.mimeType.includes('video')) {
          fileType = 'video';
        } else if (file.mimeType.includes('audio')) {
          fileType = 'audio';
        } else if (file.mimeType.includes('text')) {
          fileType = 'text';
        }
      }

      return {
        externalId: file.id || `google-drive-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        fileName: file.name || 'Untitled',
        fileType,
        mimeType: file.mimeType || 'application/octet-stream',
        fileSize: parseInt(file.size) || 0,
        platform: 'google_drive',
        sourceUrl: file.webViewLink || null,
        downloadUrl: file.webContentLink || null,
        thumbnailUrl: file.thumbnailLink || null,
        userId: file.owners?.[0]?.emailAddress || null,
        lastModified: file.modifiedTime ? new Date(file.modifiedTime) : new Date(),
        isShared: file.shared || false,
        sharedWith: file.permissions?.map((p: any) => p.emailAddress).filter(Boolean) || [],
        extractedMetadata: {
          driveFileId: file.id,
          webViewLink: file.webViewLink,
          parents: file.parents || [],
          owners: file.owners || [],
          permissions: file.permissions || [],
          modifiedTime: file.modifiedTime,
        },
      };
    } catch (error) {
      this.log('error', 'Error extracting file metadata', error);
      return {
        externalId: file.id || `google-drive-fallback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        fileName: file.name || 'Untitled',
        fileType: 'document',
        mimeType: 'application/octet-stream',
        fileSize: 0,
        platform: 'google_drive',
        sourceUrl: null,
        downloadUrl: null,
        thumbnailUrl: null,
        userId: null,
        lastModified: new Date(),
        isShared: false,
        sharedWith: [],
        extractedMetadata: {
          driveFileId: file.id,
          error: 'Failed to extract metadata properly',
        },
      };
    }
  }

  /**
   * Download PDF content from Google Drive
   */
  async downloadPDFContent(auth: OAuth2Client, fileId: string, fileName: string): Promise<Buffer | null> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(fileId, 'file ID');

    try {
      const drive = google.drive({ version: 'v3', auth });

      this.log('info', `Downloading PDF content for: ${fileName} (ID: ${fileId})`);

      const response = await drive.files.get({
        fileId,
        alt: 'media',
      }, {
        responseType: 'arraybuffer',
      });

      if (response.data) {
        return Buffer.from(response.data as ArrayBuffer);
      }

      return null;
    } catch (error) {
      this.handleError(error, `downloading PDF content for ${fileName}`);
      return null;
    }
  }

  /**
   * Download Word document content from Google Drive
   */
  async downloadWordContent(auth: OAuth2Client, fileId: string, fileName: string): Promise<Buffer | null> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateString(fileId, 'file ID');

    try {
      const drive = google.drive({ version: 'v3', auth });

      this.log('info', `Downloading Word document content for: ${fileName} (ID: ${fileId})`);

      const response = await drive.files.get({
        fileId,
        alt: 'media',
      }, {
        responseType: 'arraybuffer',
      });

      if (response.data) {
        return Buffer.from(response.data as ArrayBuffer);
      }

      return null;
    } catch (error) {
      this.handleError(error, `downloading Word content for ${fileName}`);
      return null;
    }
  }

  /**
   * Extract content from any supported file type
   * @param auth Authorized OAuth2 client
   * @param file Google Drive file object
   * @returns Extracted text content
   */
  async extractFileContent(auth: OAuth2Client, file: any): Promise<string> {
    this.ensureInitialized();
    this.validateRequired(auth, 'OAuth2 client');
    this.validateRequired(file, 'file object');

    try {
      const mimeType = file.mimeType || '';
      const fileId = file.id || '';

      // Handle Google Docs
      if (mimeType === 'application/vnd.google-apps.document') {
        const docContent = await this.extractDocContent(auth, fileId);
        return `${docContent.transcriptText}\n\n${docContent.notesText}`.trim();
      }

      // Handle Google Sheets
      if (mimeType === 'application/vnd.google-apps.spreadsheet') {
        const sheetsContent = await this.extractSheetsContent(auth, fileId);
        return sheetsContent.content;
      }

      // Handle Google Slides
      if (mimeType === 'application/vnd.google-apps.presentation') {
        const slidesContent = await this.extractSlidesContent(auth, fileId);
        return slidesContent.content;
      }

      // For other file types, return basic info
      return `File: ${file.name}\nType: ${mimeType}\nSize: ${file.size || 'Unknown'} bytes`;

    } catch (error) {
      this.handleError(error, `extracting content from file ${file.name}`);
      return '';
    }
  }
}
