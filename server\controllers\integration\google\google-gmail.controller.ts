import { Request, Response } from 'express';
import { BaseIntegrationController } from '../base/base-integration.controller.js';
import { gmailService } from '../../../services/platform-integrations/google/gmail.service.js';
import { GoogleOAuthService } from '../../../services/platform-integrations/google/oauth.service.js';

/**
 * Gmail Integration Controller - handles Gmail-specific operations
 */
export class GoogleGmailController extends BaseIntegrationController {
  private googleOAuthService: GoogleOAuthService;

  constructor() {
    super();
    this.googleOAuthService = new GoogleOAuthService();
  }

  /**
   * Get Gmail profile information
   */
  async getGmailProfile(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      this.logAction(`Getting Gmail profile for integration ${id}`);

      // Get the integration
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Validate this is a Google integration with Gmail access
      if (!this.validateIntegrationType(integration, ['google-gmail', 'google_gmail', 'google-drive', 'google_drive'])) {
        return res.status(400).json({ message: 'This endpoint requires a Google integration with Gmail access.' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        // Get authorized client
        const auth = await this.googleOAuthService.getAuthorizedClient(integration.credentials);

        // Get Gmail profile
        const profile = await gmailService.getGmailProfile(auth);

        this.logAction(`Successfully retrieved Gmail profile for ${profile.emailAddress}`);

        return this.successResponse(res, {
          profile,
          integration: {
            id: integration.id,
            name: integration.name,
            type: integration.type,
            status: integration.status,
          }
        });

      } catch (error: any) {
        this.logAction(`Error getting Gmail profile: ${error.message}`);
        return this.handleError(res, error, 'Failed to get Gmail profile');
      }

    } catch (error: any) {
      this.logAction(`Error in getGmailProfile: ${error.message}`);
      return this.handleError(res, error, 'Internal server error');
    }
  }

  /**
   * Sync all emails from Gmail
   */
  async syncAllEmails(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      this.logAction(`Starting full Gmail sync for integration ${id}`);

      // Get the integration
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Validate this is a Google integration with Gmail access
      if (!this.validateIntegrationType(integration, ['google-gmail', 'google_gmail', 'google-drive', 'google_drive'])) {
        return res.status(400).json({ message: 'This endpoint requires a Google integration with Gmail access.' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        // Get authorized client
        const auth = await this.googleOAuthService.getAuthorizedClient(integration.credentials);

        // Start email sync in background (don't wait for completion)
        const userId = 'anonymous'; // Use default user ID for now
        const organizationId = 'default'; // Use default organization ID for now

        // Start sync process
        gmailService.fetchAllEmails(auth, userId, organizationId)
          .then(() => {
            this.logAction(`Completed full Gmail sync for integration ${id}`);
          })
          .catch((error) => {
            this.logAction(`Error in full Gmail sync for integration ${id}: ${error.message}`);
          });

        return this.successResponse(res, {
          message: 'Gmail sync started successfully',
          integration: {
            id: integration.id,
            name: integration.name,
            type: integration.type,
            status: integration.status,
          },
          syncStatus: 'started'
        });

      } catch (error: any) {
        this.logAction(`Error starting Gmail sync: ${error.message}`);
        return this.handleError(res, error, 'Failed to start Gmail sync');
      }

    } catch (error: any) {
      this.logAction(`Error in syncAllEmails: ${error.message}`);
      return this.handleError(res, error, 'Internal server error');
    }
  }

  /**
   * Sync recent emails from Gmail (last 7 days)
   */
  async syncRecentEmails(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      // Get days parameter from query string
      const daysBack = parseInt(req.query.days as string) || 7;

      this.logAction(`Starting recent Gmail sync (${daysBack} days) for integration ${id}`);

      // Get the integration
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      // Validate this is a Google integration with Gmail access
      if (!this.validateIntegrationType(integration, ['google-gmail', 'google_gmail', 'google-drive', 'google_drive'])) {
        return res.status(400).json({ message: 'This endpoint requires a Google integration with Gmail access.' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        // Get authorized client
        const auth = await this.googleOAuthService.getAuthorizedClient(integration.credentials);

        // Get user info
        const userId = 'anonymous'; // Use default user ID for now
        const organizationId = 'default'; // Use default organization ID for now

        // Start recent sync
        await gmailService.syncRecentEmails(auth, userId, organizationId, daysBack);

        this.logAction(`Completed recent Gmail sync for integration ${id}`);

        return this.successResponse(res, {
          message: `Recent Gmail sync completed (${daysBack} days)`,
          integration: {
            id: integration.id,
            name: integration.name,
            type: integration.type,
            status: integration.status,
          },
          syncStatus: 'completed',
          daysBack
        });

      } catch (error: any) {
        this.logAction(`Error in recent Gmail sync: ${error.message}`);
        return this.handleError(res, error, 'Failed to sync recent emails');
      }

    } catch (error: any) {
      this.logAction(`Error in syncRecentEmails: ${error.message}`);
      return this.handleError(res, error, 'Internal server error');
    }
  }

  /**
   * Get Gmail sync status and statistics
   */
  async getEmailStats(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      this.logAction(`Getting email statistics for integration ${id}`);

      // Get the integration
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      try {
        // Get email statistics from storage
        const { EmailStorage } = await import('../../../storage/features/email.storage.js');
        const emailStorage = new EmailStorage();

        const userId = 'anonymous'; // Use default user ID for now
        
        // Get total email count for this user
        const allEmails = await emailStorage.getEmails('gmail', userId, 1, 1000);
        const totalEmails = allEmails.length;

        // Get recent emails (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        
        const recentEmails = allEmails.filter(email => 
          email.receivedAt && new Date(email.receivedAt) > sevenDaysAgo
        );

        // Get email statistics
        const stats = {
          totalEmails,
          recentEmails: recentEmails.length,
          unreadEmails: allEmails.filter(email => !email.isRead).length,
          starredEmails: allEmails.filter(email => email.isStarred).length,
          lastSyncAt: integration.lastSyncAt,
          integration: {
            id: integration.id,
            name: integration.name,
            type: integration.type,
            status: integration.status,
          }
        };

        return this.successResponse(res, stats);

      } catch (error: any) {
        this.logAction(`Error getting email statistics: ${error.message}`);
        return this.handleError(res, error, 'Failed to get email statistics');
      }

    } catch (error: any) {
      this.logAction(`Error in getEmailStats: ${error.message}`);
      return this.handleError(res, error, 'Internal server error');
    }
  }

  /**
   * Test Gmail connection
   */
  async testGmailConnection(req: Request, res: Response) {
    try {
      const { isValid, id, error } = this.validateIntegrationId(req);
      if (!isValid) {
        return res.status(400).json({ message: error });
      }

      this.logAction(`Testing Gmail connection for integration ${id}`);

      // Get the integration
      const integration = await this.getIntegrationById(id!);
      if (!integration) {
        return res.status(404).json({ message: 'Integration not found' });
      }

      if (!integration.credentials) {
        return res.status(400).json({ message: 'Integration is not connected' });
      }

      try {
        // Get authorized client
        const auth = await this.googleOAuthService.getAuthorizedClient(integration.credentials);

        // Test Gmail access by getting profile
        const profile = await gmailService.getGmailProfile(auth);

        this.logAction(`Gmail connection test successful for ${profile.emailAddress}`);

        return this.successResponse(res, {
          message: 'Gmail connection successful',
          connected: true,
          profile: {
            emailAddress: profile.emailAddress,
            totalMessages: profile.messagesTotal,
            totalThreads: profile.threadsTotal,
          },
          integration: {
            id: integration.id,
            name: integration.name,
            type: integration.type,
            status: integration.status,
          }
        });

      } catch (error: any) {
        this.logAction(`Gmail connection test failed: ${error.message}`);
        return this.handleError(res, error, 'Gmail connection failed');
      }

    } catch (error: any) {
      this.logAction(`Error in testGmailConnection: ${error.message}`);
      return this.handleError(res, error, 'Internal server error');
    }
  }
} 