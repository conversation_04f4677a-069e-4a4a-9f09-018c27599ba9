// MCP Types and Interfaces

export interface MCPServer {
  id: string;
  name: string;
  type: string;
  status: MCPServerStatus;
  url?: string;
  lastConnected?: Date;
  error?: string;
  tools: MCPTool[];
  config: MCPServerConfig;
}

export interface MCPTool {
  name: string;
  description: string;
  parameters?: Record<string, any>;
  lastUsed?: Date;
  usageCount?: number;
}

export interface MCPServerStatus {
  connected: boolean;
  healthy: boolean;
  lastPing?: Date;
  responseTime?: number;
  error?: string;
}

export interface MCPServerConfig {
  enabled: boolean;
  autoReconnect: boolean;
  timeout: number;
  retryAttempts: number;
  [key: string]: any;
}

export interface MCPStatusResponse {
  servers: MCPServer[];
  totalTools: number;
  connectedServers: number;
  lastUpdate: Date;
}

export interface MCPToolUsage {
  toolName: string;
  serverName: string;
  timestamp: Date;
  duration?: number;
  success: boolean;
  result?: any;
  error?: string;
}

export interface MCPLogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  serverId?: string;
  toolName?: string;
  details?: Record<string, any>;
}
